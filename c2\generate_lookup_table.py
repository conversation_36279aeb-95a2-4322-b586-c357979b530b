"""
混凝土本构"查表法"VUMAT - 离线数据生成脚本
从PINN训练结果生成lookup_table_1d.dat文件，供Fortran VUMAT使用

功能：
1. 加载PINN训练的物理参数
2. 生成不同损伤历史等级下的滞回曲线数据
3. 输出格式化的查找表文件

作者：AI Assistant
日期：2024
"""

import numpy as np
import json
import os
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime
import sys

# 导入项目模块
try:
    from predict_hysteresis import HysteresisPredictor
    from font_config import setup_font
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在c2目录下运行此脚本")
    sys.exit(1)


class LookupTableGenerator:
    """查找表生成器"""
    
    def __init__(self, config=None):
        """
        初始化生成器
        
        Args:
            config: 配置字典，可包含：
                - r_max_levels: r_max等级数组
                - points_per_segment: 每段采样点数
                - model_path: 模型文件路径
        """
        # 默认配置
        self.config = {
            'r_max_min': 0.0002,        # 最小r_max (0.02%应变)
            'r_max_max': 0.004,         # 最大r_max (0.4%应变)  
            'num_r_max_levels': 20,     # r_max等级数
            'points_per_segment': 200,  # 每个加载段的采样点数
            'model_path': None,         # 自动找最新模型
            'output_file': 'lookup_table_1d.dat',
            'generate_plots': True,     # 是否生成验证图表
        }
        
        if config:
            self.config.update(config)
            
        # 设置字体
        setup_font()
        
        # 自动寻找最新的模型文件
        if self.config['model_path'] is None:
            self.config['model_path'] = self._find_latest_model()
            
        print(f"使用模型文件: {self.config['model_path']}")
        
        # 初始化预测器
        self.predictor = HysteresisPredictor(self.config['model_path'])
        
        # 生成r_max等级
        self.r_max_levels = np.linspace(
            self.config['r_max_min'], 
            self.config['r_max_max'], 
            self.config['num_r_max_levels']
        )
        
        print(f"将生成 {len(self.r_max_levels)} 个损伤等级的查找表")
        print(f"r_max范围: {self.r_max_levels[0]:.4f} 到 {self.r_max_levels[-1]:.4f}")
        
    def _find_latest_model(self):
        """自动寻找最新的训练模型"""
        results_dir = Path('results')
        if not results_dir.exists():
            raise FileNotFoundError("找不到results目录，请先进行PINN训练")
            
        # 寻找最新的session目录
        session_dirs = [d for d in results_dir.iterdir() 
                       if d.is_dir() and d.name.startswith('session_')]
        
        if not session_dirs:
            raise FileNotFoundError("找不到训练session目录")
            
        # 按时间戳排序，选择最新的
        latest_session = sorted(session_dirs, key=lambda x: x.name)[-1]
        
        # 寻找最终模型文件
        model_files = [
            latest_session / 'training' / 'final_model.pth',
            latest_session / 'training' / 'best_model.pth',
        ]
        
        for model_file in model_files:
            if model_file.exists():
                return str(model_file)
                
        raise FileNotFoundError(f"在{latest_session}中找不到模型文件")
    
    def generate_symmetric_cycle(self, r_max_level):
        """
        生成对称的循环加载路径
        
        Args:
            r_max_level: 最大应变幅值
            
        Returns:
            strain_path: 应变路径数组
        """
        points = self.config['points_per_segment']
        
        # 生成对称循环：0 → +r_max → -r_max → 0
        # 使用更精细的控制避免重复点
        
        # 第一段：0到拉伸峰值
        segment1 = np.linspace(0.0, r_max_level, points, endpoint=True)
        
        # 第二段：拉伸峰值到压缩峰值（排除起始点避免重复）
        segment2 = np.linspace(r_max_level, -r_max_level, 2*points, endpoint=True)[1:]
        
        # 第三段：压缩峰值回到0（排除起始点避免重复）
        segment3 = np.linspace(-r_max_level, 0.0, points, endpoint=True)[1:]
        
        # 连接各段
        strain_path = np.concatenate([segment1, segment2, segment3])
        
        # 最终清理：确保没有重复值
        strain_path_unique = []
        prev_strain = None
        tolerance = 1e-10
        
        for strain in strain_path:
            if prev_strain is None or abs(strain - prev_strain) > tolerance:
                strain_path_unique.append(strain)
                prev_strain = strain
        
        return np.array(strain_path_unique)
    
    def generate_lookup_data(self):
        """生成完整的查找表数据"""
        print("\n开始生成查找表数据...")
        
        # 存储所有数据
        all_data = []
        
        # 为每个r_max等级生成数据
        for i, r_max in enumerate(self.r_max_levels):
            print(f"处理 r_max = {r_max:.4f} ({i+1}/{len(self.r_max_levels)})")
            
            # 生成加载路径
            strain_path = self.generate_symmetric_cycle(r_max)
            
            # 计算响应
            try:
                response = self.predictor.predict_response(strain_path)
                
                # 收集当前r_max等级的数据
                level_data = []
                for strain, stress in zip(response['strain'], response['stress']):
                    level_data.append([r_max, strain, stress])
                
                # 转换为numpy数组进行处理
                level_data = np.array(level_data)
                
                # 按应变值排序
                sort_indices = np.argsort(level_data[:, 1])
                level_data = level_data[sort_indices]
                
                # 去除重复的应变点（彻底的去重逻辑）
                # 使用更严格的容差和去重方法
                if len(level_data) > 0:
                    # 先排序
                    sort_indices = np.argsort(level_data[:, 1])
                    level_data = level_data[sort_indices]
                    
                    # 使用逐行比较去重，设置合理的容差
                    unique_data = [level_data[0]]  # 保留第一个点
                    tolerance = 1e-6  # 增大容差，确保去除真正的重复点
                    removed_count = 0
                    
                    for i in range(1, len(level_data)):
                        # 检查当前应变与上一个唯一点的差异
                        strain_diff = abs(level_data[i, 1] - unique_data[-1][1])
                        if strain_diff > tolerance:
                            unique_data.append(level_data[i])
                        else:
                            removed_count += 1
                    
                    level_data = np.array(unique_data)
                    
                    # 输出详细的去重信息
                    if removed_count > 1:
                        print(f"    实际去除重复点: {removed_count}")
                    elif removed_count == 0:
                        print(f"    无重复点需要去除")
                else:
                    level_data = np.array([])
                
                # 添加到总数据中
                all_data.extend(level_data.tolist())
                
                print(f"  处理后数据点数: {len(level_data)} (去重前: {len(response['strain'])})")
                    
            except Exception as e:
                print(f"警告：r_max={r_max:.4f}计算失败: {e}")
                continue
                
        print(f"生成完成，共 {len(all_data)} 个数据点")
        
        return np.array(all_data)
    
    def write_lookup_table(self, data):
        """写入查找表文件"""
        output_path = self.config['output_file']
        
        print(f"\n写入查找表到: {output_path}")
        
        with open(output_path, 'w') as f:
            # 写入文件头
            f.write("# 混凝土本构查找表 - 供VUMAT使用\n")
            f.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 模型文件: {self.config['model_path']}\n")
            f.write("#\n")
            f.write("# 数据格式: r_max(应变幅值包络), strain(应变), stress(应力,MPa)\n")
            f.write("# 每个r_max等级包含一个完整的对称循环\n")
            f.write("#\n")
            f.write(f"# 参数信息:\n")
            
            # 写入物理参数信息
            params = self.predictor.physics_params
            constants = self.predictor.material_constants
            f.write(f"# E0 = {constants['E0']:.1f} MPa\n")
            f.write(f"# f_t = {constants['f_t']:.2f} MPa\n") 
            f.write(f"# f_c = {constants['f_c']:.2f} MPa\n")
            f.write(f"# A+ = {params['A_plus']:.4f}, B+ = {params['B_plus']:.4f}, xi+ = {params['xi_plus']:.4f}\n")
            f.write(f"# A- = {params['A_minus']:.4f}, B- = {params['B_minus']:.4f}, xi- = {params['xi_minus']:.4f}\n")
            f.write("#\n")
            
            # 写入数据
            for row in data:
                f.write(f"{row[0]:.6f}, {row[1]:.6f}, {row[2]:.6f}\n")
                
        print(f"查找表写入完成，共 {len(data)} 行数据")
        
        # 输出统计信息
        print(f"\n数据统计:")
        print(f"  r_max范围: {data[:, 0].min():.4f} 到 {data[:, 0].max():.4f}")
        print(f"  应变范围: {data[:, 1].min():.4f} 到 {data[:, 1].max():.4f}")
        print(f"  应力范围: {data[:, 2].min():.2f} 到 {data[:, 2].max():.2f} MPa")
        
    def generate_verification_plots(self, data):
        """生成验证图表"""
        if not self.config['generate_plots']:
            return
            
        print("\n生成验证图表...")
        
        # 创建图表目录
        plot_dir = Path('lookup_table_verification')
        plot_dir.mkdir(exist_ok=True)
        
        # 1. 绘制不同r_max等级的滞回曲线
        plt.figure(figsize=(12, 8))
        
        # 选择几个代表性的r_max等级进行绘制
        selected_indices = np.linspace(0, len(self.r_max_levels)-1, 5, dtype=int)
        colors = plt.cm.viridis(np.linspace(0, 1, len(selected_indices)))
        
        for i, (idx, color) in enumerate(zip(selected_indices, colors)):
            r_max = self.r_max_levels[idx]
            
            # 筛选当前r_max等级的数据
            mask = np.abs(data[:, 0] - r_max) < 1e-8
            strain_data = data[mask, 1]
            stress_data = data[mask, 2]
            
            plt.plot(strain_data, stress_data, 
                    color=color, linewidth=2, 
                    label=f'r_max = {r_max:.4f}')
        
        plt.xlabel('应变')
        plt.ylabel('应力 (MPa)')
        plt.title('查找表验证 - 不同损伤等级的滞回曲线')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(plot_dir / 'hysteresis_curves_verification.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. 绘制查找表数据的分布
        plt.figure(figsize=(12, 5))
        
        plt.subplot(1, 2, 1)
        plt.scatter(data[:, 0], data[:, 1], c=data[:, 2], cmap='RdBu_r', s=1, alpha=0.6)
        plt.colorbar(label='应力 (MPa)')
        plt.xlabel('r_max (应变幅值包络)')
        plt.ylabel('应变')
        plt.title('查找表数据分布')
        
        plt.subplot(1, 2, 2)
        plt.hist2d(data[:, 1], data[:, 2], bins=50, cmap='Blues')
        plt.colorbar(label='数据点密度')
        plt.xlabel('应变')
        plt.ylabel('应力 (MPa)')
        plt.title('应力-应变数据密度')
        
        plt.tight_layout()
        plt.savefig(plot_dir / 'data_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"验证图表保存在: {plot_dir}")
    
    def run(self):
        """运行完整的查找表生成流程"""
        print("=" * 60)
        print("混凝土本构查找表生成器")
        print("=" * 60)
        
        try:
            # 1. 生成查找表数据
            data = self.generate_lookup_data()
            
            # 2. 写入文件
            self.write_lookup_table(data)
            
            # 3. 生成验证图表
            self.generate_verification_plots(data)
            
            print("\n" + "=" * 60)
            print("查找表生成完成！")
            print("=" * 60)
            print(f"输出文件: {self.config['output_file']}")
            print("现在可以在Fortran VUMAT中使用此查找表")
            
        except Exception as e:
            print(f"生成过程中出现错误: {e}")
            raise


def main():
    """主函数"""
    # 可以通过命令行参数自定义配置
    config = {}
    
    if len(sys.argv) > 1:
        # 简单的命令行参数解析
        for arg in sys.argv[1:]:
            if arg.startswith('--r-max-levels='):
                config['num_r_max_levels'] = int(arg.split('=')[1])
            elif arg.startswith('--points='):
                config['points_per_segment'] = int(arg.split('=')[1])
            elif arg.startswith('--output='):
                config['output_file'] = arg.split('=')[1]
            elif arg == '--no-plots':
                config['generate_plots'] = False
    
    # 创建生成器并运行
    generator = LookupTableGenerator(config)
    generator.run()


if __name__ == "__main__":
    main() 