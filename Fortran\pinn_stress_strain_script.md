# 混凝土单轴本构PINN建模脚本

## 1. 项目目标

混凝土柱几何参数，标距长度为800mm,截面为圆形，直径为400mm

基于实验加载下获得的力-位移数据，利用物理信息神经网络（PINN）拟合混凝土单轴加载下的非线性应力-应变及损伤演化关系，实现如下功能：
- 输入：总应变（\(\epsilon\)）
- 输出：应力（\(\sigma\)）与损伤变量（\(d\)）
- 物理约束：结合本构关系与损伤演化规律
- 训练数据：实验数据与合成数据

---

## 2. 数据预处理
### 2.1 原始数据结构

- 本构关系高度非线性，建议直接用力-位移数据训练网络，输出应力和损伤

### 2.2 数据归一化
- 对输入的应变、输出的应力和损伤变量进行归一化处理，提升训练稳定性

---

## 3. PINN网络结构
- 输入层：1个神经元（应变\(\epsilon\)）
- 隐藏层：1层，10个神经元，ReLU激活
- 输出层：2个神经元（应力\(\sigma\)，损伤变量\(d\)），线性激活

```python
import torch
import torch.nn as nn

class PINN(nn.Module):
    def __init__(self):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(1, 10),
            nn.ReLU(),
            nn.Linear(10, 2)
        )
    def forward(self, x):
        return self.net(x)
```

---

## 4. 物理约束与损失函数
### 4.1 本构关系
\[
\sigma = (1 - d) E \epsilon
\]
其中\(E\)为弹性模量。

### 4.2 损伤演化规律
指数型损伤演化：
##\[d = 1 - e^{-k (\epsilon - \epsilon_0)^+}\]
其中\(\epsilon_0\)为应变阈值，\(k\)为材料参数。

### 4.3 总损失函数
- 数据损失：网络输出应力与实验应力的均方误差
- 物理损失：网络输出应力与本构公式计算应力的均方误差
- 损伤演化损失：网络输出损伤与演化公式损伤的均方误差
- 总损失 = 数据损失 + \(\lambda_1\)物理损失 + \(\lambda_2\)损伤损失

---

## 5. 训练流程
1. 加载并归一化实验数据
2. 构建PINN网络
3. 定义损失函数（数据损失+物理损失+损伤损失）
4. 选择优化器（如Adam）
5. 训练网络，动态调整损失权重
6. 验证模型泛化能力

---

## 6. 结果可视化与模型验证
- 绘制预测应力-应变曲线与实验曲线对比
- 绘制损伤变量随应变演化曲线
- 评估模型在未见样本上的预测能力

---

## 7. 附录：示例伪代码
```python
# 假设已获得归一化后的应变-应力数据
strain = ... # shape: [N, 1]
stress = ... # shape: [N, 1]
# PINN网络、损失函数、优化器定义略
for epoch in range(num_epochs):
    pred = model(strain)
    pred_stress, pred_damage = pred[:,0:1], pred[:,1:2]
    # 本构应力
    constitutive_stress = (1 - pred_damage) * E * strain
    # 损伤演化
    damage_evolution = 1 - torch.exp(-k * torch.relu(strain - eps0))
    # 损失项
    loss_data = mse(pred_stress, stress)
    loss_phys = mse(pred_stress, constitutive_stress)
    loss_damage = mse(pred_damage, damage_evolution)
    loss = loss_data + lambda1 * loss_phys + lambda2 * loss_damage
    # 反向传播与优化
    ...
```

---

## 8. 参考说明
- 可根据实际实验数据结构调整输入输出定义
- 损伤演化参数\(k, \epsilon_0\)可通过拟合或先验给定
- 建议与柱滞回曲线.md脚本风格保持一致，便于团队协作


## 1. **数据未正确转换为应力-应变形式**
### 问题根源
- 直接使用位移和力 (`F-ΔL`) 而非应力-应变 (`σ-ε`) 关系。
- 未考虑大变形下的真应力/真应变计算。

### 解决方案
1. **数据转换**：
   - 工程应力：$$\sigma = \frac{F}{A_0} \quad (A_0: \text{初始截面积})$$
   - 工程应变：$$\varepsilon = \frac{\Delta L}{L_0} \quad (L_0: \text{标距长度})$$
   - 大变形时用真应力/应变：  
     $$\sigma_{\text{true}} = \frac{F}{A}, \quad \varepsilon_{\text{true}} = \ln\left(\frac{L}{L_0}\right)$$

---