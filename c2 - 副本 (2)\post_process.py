"""
Abaqus后处理脚本
从ODB文件提取结果并与PINN预测进行对比
注意：此脚本需要在Abaqus Python环境中运行
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import json
import sys

# 尝试导入Abaqus模块
try:
    from odbAccess import openOdb
    ABAQUS_AVAILABLE = True
except ImportError:
    print("警告: 无法导入Abaqus模块，将使用模拟数据")
    ABAQUS_AVAILABLE = False

# 单元尺寸参数
LENGTH = 100.0  # mm
AREA = 100.0    # mm^2

def find_latest_session_dir():
    """查找最新的session目录"""
    results_dir = Path("results")
    if not results_dir.exists():
        return None
    
    sessions = sorted([d for d in results_dir.iterdir() 
                      if d.is_dir() and d.name.startswith("session_")])
    return sessions[-1] if sessions else None

def get_test_info(vumat_dir):
    """获取测试信息"""
    config_file = vumat_dir / "test_config.txt"
    test_type = "unknown"
    job_name = "tensile_test"  # 默认值
    
    if config_file.exists():
        with open(config_file, 'r') as f:
            lines = f.readlines()
            for line in lines:
                if "Test Type:" in line:
                    test_type = line.split(":")[1].strip()
                elif "INP File:" in line:
                    inp_file = line.split(":")[1].strip()
                    job_name = inp_file.replace(".inp", "")
    
    return test_type, job_name

def extract_odb_data(odb_path):
    """从ODB文件提取数据"""
    if not ABAQUS_AVAILABLE:
        print("使用模拟数据（Abaqus模块不可用）")
        # 返回模拟数据用于测试
        time = np.linspace(0, 1, 100)
        strain = np.linspace(0, 0.002, 100)
        stress = 30000 * strain * (1 - 0.5 * strain / 0.002)  # 简单的非线性
        return time, strain, stress
    
    print(f"正在打开ODB文件: {odb_path}")
    
    try:
        odb = openOdb(path=str(odb_path), readOnly=True)
        
        # 获取第一个分析步
        step = odb.steps[list(odb.steps.keys())[0]]
        
        # 获取节点集合
        assembly = odb.rootAssembly
        
        # 获取顶部节点的位移历史
        top_nset = assembly.nodeSets['SET_TOP']
        top_node = top_nset.nodes[0]
        u3_history = step.historyRegions[f'Node {top_node.label}'].historyOutputs['U3']
        
        # 获取底部节点的反力历史
        bottom_nset = assembly.nodeSets['SET_BOTTOM']
        rf3_history = step.historyRegions[f'NodeSet {bottom_nset.name}'].historyOutputs['RF3']
        
        # 提取数据
        disp_data = np.array(u3_history.data)
        force_data = np.array(rf3_history.data)
        
        # 计算应变和应力
        time = disp_data[:, 0]
        strain = disp_data[:, 1] / LENGTH
        stress = np.abs(force_data[:, 1]) / AREA
        
        odb.close()
        
        return time, strain, stress
        
    except Exception as e:
        print(f"错误: 读取ODB文件失败 - {e}")
        return None, None, None

def load_pinn_prediction(session_dir, test_type):
    """加载PINN预测结果"""
    prediction_dir = session_dir / "prediction"
    
    # 根据测试类型查找对应的预测文件
    prediction_files = list(prediction_dir.glob("prediction_results_*.xlsx"))
    
    if not prediction_files:
        print("警告: 未找到PINN预测结果")
        return None, None
    
    # 使用最新的预测文件
    latest_prediction = sorted(prediction_files)[-1]
    
    try:
        import pandas as pd
        df = pd.read_excel(latest_prediction)
        
        # 根据测试类型筛选数据
        if test_type == "tensile":
            # 只取拉伸部分
            mask = df['strain'] >= 0
        elif test_type == "compression":
            # 只取压缩部分
            mask = df['strain'] <= 0
        else:
            # 循环加载，取全部
            mask = np.ones(len(df), dtype=bool)
        
        strain_pinn = df.loc[mask, 'strain'].values
        stress_pinn = df.loc[mask, 'stress'].values
        
        return strain_pinn, stress_pinn
        
    except Exception as e:
        print(f"警告: 读取PINN预测结果失败 - {e}")
        return None, None

def plot_comparison(time, strain_vumat, stress_vumat, strain_pinn, stress_pinn, 
                   test_type, output_dir):
    """绘制对比图"""
    plt.figure(figsize=(12, 8))
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 绘制Vumat结果
    plt.plot(strain_vumat, stress_vumat, 'b-', linewidth=2, label='Abaqus VUMAT')
    
    # 绘制PINN预测（如果有）
    if strain_pinn is not None and stress_pinn is not None:
        plt.plot(strain_pinn, stress_pinn, 'r--', linewidth=2, label='PINN Prediction')
    
    # 设置图表属性
    if test_type == "tensile":
        plt.title('单轴拉伸应力-应变曲线对比', fontsize=16)
    elif test_type == "compression":
        plt.title('单轴压缩应力-应变曲线对比', fontsize=16)
    else:
        plt.title('循环加载应力-应变曲线对比', fontsize=16)
    
    plt.xlabel('应变', fontsize=14)
    plt.ylabel('应力 (MPa)', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=12)
    
    # 添加零线
    plt.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    plt.axvline(x=0, color='k', linestyle='--', alpha=0.5)
    
    # 保存图片
    output_path = output_dir / f'{test_type}_stress_strain_comparison.png'
    plt.savefig(str(output_path), dpi=300, bbox_inches='tight')
    print(f"✓ 对比图已保存: {output_path}")
    
    # 计算误差指标
    if strain_pinn is not None and stress_pinn is not None:
        try:
            # 尝试导入scipy进行插值
            from scipy.interpolate import interp1d
            
            # 确定共同的应变范围
            strain_min = max(strain_vumat.min(), strain_pinn.min())
            strain_max = min(strain_vumat.max(), strain_pinn.max())
            
            # 创建共同的应变点
            strain_common = np.linspace(strain_min, strain_max, 100)
            
            # 插值
            f_vumat = interp1d(strain_vumat, stress_vumat, kind='linear')
            f_pinn = interp1d(strain_pinn, stress_pinn, kind='linear')
            
            stress_vumat_interp = f_vumat(strain_common)
            stress_pinn_interp = f_pinn(strain_common)
            
            # 计算误差
            mse = np.mean((stress_vumat_interp - stress_pinn_interp)**2)
            rmse = np.sqrt(mse)
            mae = np.mean(np.abs(stress_vumat_interp - stress_pinn_interp))
            
            print(f"\n误差分析:")
            print(f"  均方误差 (MSE): {mse:.4f}")
            print(f"  均方根误差 (RMSE): {rmse:.4f}")
            print(f"  平均绝对误差 (MAE): {mae:.4f}")
            
        except ImportError:
            print(f"\n注意: scipy不可用，跳过误差分析")
            print(f"要进行误差分析，请安装: pip install scipy")

def save_results(time, strain, stress, output_dir, test_type):
    """保存结果数据"""
    # 组合数据
    results = np.column_stack((time, strain, stress))
    
    # 保存为CSV
    output_path = output_dir / f'{test_type}_vumat_results.csv'
    header = 'Time,Strain,Stress(MPa)'
    np.savetxt(str(output_path), results, delimiter=',', header=header, comments='')
    print(f"✓ 数据已保存: {output_path}")

def extract_and_plot():
    """主处理函数"""
    print("\n=== Abaqus后处理 V2.0 ===")
    
    # 查找最新session
    latest_session = find_latest_session_dir()
    if not latest_session:
        print("错误: 未找到任何session目录。")
        return
    
    print(f"使用session: {latest_session}")
    
    # 定义路径
    vumat_dir = latest_session / "vumat_verification"
    if not vumat_dir.exists():
        print(f"错误: Vumat验证目录不存在: {vumat_dir}")
        return
    
    # 获取测试信息
    test_type, job_name = get_test_info(vumat_dir)
    print(f"测试类型: {test_type}")
    print(f"作业名称: {job_name}")
    
    # 检查ODB文件
    odb_path = vumat_dir / f"{job_name}.odb"
    
    if not odb_path.exists() and ABAQUS_AVAILABLE:
        print(f"错误: ODB文件不存在: {odb_path}")
        print("请先运行Abaqus分析")
        return
    
    # 创建输出目录
    output_dir = vumat_dir / "verification_outputs"
    output_dir.mkdir(exist_ok=True)
    
    # 提取数据
    time, strain, stress = extract_odb_data(odb_path)
    
    if time is None:
        print("错误: 无法提取数据")
        return
    
    print(f"\n提取的数据点数: {len(time)}")
    print(f"应变范围: [{strain.min():.6f}, {strain.max():.6f}]")
    print(f"应力范围: [{stress.min():.2f}, {stress.max():.2f}] MPa")
    
    # 加载PINN预测结果
    strain_pinn, stress_pinn = load_pinn_prediction(latest_session, test_type)
    
    # 绘制对比图
    plot_comparison(time, strain, stress, strain_pinn, stress_pinn, 
                   test_type, output_dir)
    
    # 保存数据
    save_results(time, strain, stress, output_dir, test_type)
    
    print(f"\n✓ 后处理完成!")
    print(f"结果保存在: {output_dir}")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("用法: abaqus python post_process.py")
        print("或者: python post_process.py (使用模拟数据)")
        return
    
    extract_and_plot()

if __name__ == '__main__':
    main() 