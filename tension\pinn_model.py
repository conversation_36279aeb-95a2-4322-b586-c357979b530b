#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PINN模型核心模块
实现混凝土拉伸损伤演化的物理信息神经网络
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, Dict

class ConcreteDAMAGE_PINN(nn.Module):
    """
    混凝土拉伸损伤演化PINN模型
    
    基于思路.md文档中的物理约束:
    1. 应力本构关系: σ = (1-D) * E0 * (ε - εp)
    2. 损伤演化: D = 1 - (ε_d0/ε_eq) * exp(-β*(ε_eq - ε_d0))
    3. 塑性应变累积: dεp/dε ≥ 0
    4. 边界条件: ε=0时, σ=0, D=0, εp=0
    """
    
    def __init__(self, 
                 input_dim: int = 1, 
                 hidden_dims: list = [64, 64, 64], 
                 output_dim: int = 3,
                 activation: str = 'tanh'):
        super(ConcreteDAMAGE_PINN, self).__init__()
        
        # 网络结构参数
        self.input_dim = input_dim
        self.hidden_dims = hidden_dims
        self.output_dim = output_dim
        
        # 构建网络层
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, output_dim))
        self.layers = nn.ModuleList(layers)
        
        # 激活函数
        if activation == 'tanh':
            self.activation = nn.Tanh()
        elif activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'swish':
            self.activation = nn.SiLU()
        else:
            self.activation = nn.Tanh()
        
        # 可训练材料参数（基于文献中的典型值）
        self.E0 = nn.Parameter(torch.tensor(30000.0))    # 初始弹性模量 (MPa)
        self.ft = nn.Parameter(torch.tensor(3.0))        # 抗拉强度 (MPa)
        self.Gf = nn.Parameter(torch.tensor(0.1))        # 断裂能 (N/mm)
        self.alpha = nn.Parameter(torch.tensor(0.99))    # 最大损伤值
        self.beta = nn.Parameter(torch.tensor(1000.0))   # 损伤演化参数
        self.lch = nn.Parameter(torch.tensor(100.0))     # 特征长度 (mm)
        
        # 初始化网络权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """
        Xavier初始化网络权重
        """
        for layer in self.layers:
            if isinstance(layer, nn.Linear):
                nn.init.xavier_normal_(layer.weight)
                nn.init.zeros_(layer.bias)
    
    def forward(self, strain: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            strain: 输入应变张量 [batch_size, 1]
            
        Returns:
            stress: 预测应力 [batch_size, 1]
            damage: 预测损伤变量 [batch_size, 1]
            plastic_strain: 预测塑性应变 [batch_size, 1]
        """
        x = strain
        
        # 通过隐藏层
        for i, layer in enumerate(self.layers[:-1]):
            x = layer(x)
            x = self.activation(x)
        
        # 输出层
        output = self.layers[-1](x)
        
        # 输出分解和约束
        stress = output[:, 0:1]  # 应力（可以为负）
        damage = torch.sigmoid(output[:, 1:2])  # 损伤变量 [0,1]
        plastic_strain = torch.relu(output[:, 2:3])  # 塑性应变 ≥0
        
        return stress, damage, plastic_strain
    
    def compute_damage_evolution(self, strain: torch.Tensor, plastic_strain: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        基于补充.md中的损伤变量更新算法计算损伤演化
        
        实现步骤:
        1. 计算弹性试算应力
        2. 检查屈服条件
        3. 计算损伤能释放率
        4. 更新损伤变量和损伤阈值
        
        Args:
            strain: 总应变
            plastic_strain: 塑性应变
            
        Returns:
            damage: 损伤变量
            damage_threshold: 损伤阈值
        """
        # 1. 计算弹性应变
        elastic_strain = strain - plastic_strain
        
        # 2. 弹性试算应力
        sigma_trial = self.E0 * elastic_strain
        
        # 3. 屈服条件检查
        f0_plus = self.ft  # 抗拉强度
        yield_condition = sigma_trial >= f0_plus
        
        # 4. 计算损伤能释放率 Y^+
        # Y^+ = E0 * (σ^+ : Λ0 : σ)
        # 简化为单轴情况: Y^+ = σ^2 / (2 * E0)
        Y_plus = torch.clamp(sigma_trial, min=0.0)**2 / (2 * self.E0)
        
        # 5. 初始损伤阈值
        r0_plus = f0_plus**2 / (2 * self.E0)
        
        # 6. 更新损伤阈值 r^+_{n+1} = max(r^+_0, max Y^+)
        # 在训练过程中，我们使用当前批次的最大值作为近似
        r_max = torch.maximum(r0_plus, torch.max(Y_plus))
        
        # 7. 更新损伤变量
        # d^+_{n+1} = d_n^+ if Y^+_{n+1} <= r_n^+, else Y^+_{n+1}/r_n^+
        damage = torch.where(
            Y_plus <= r_max,
            torch.zeros_like(Y_plus),  # 如果未超过阈值，损伤保持为0
            Y_plus / r_max  # 否则按比例计算损伤
        )
        
        # 限制损伤范围 [0, 1]
        damage = torch.clamp(damage, 0.0, 1.0)
        
        return damage, r_max
    
    def physics_loss(self, 
                    strain: torch.Tensor, 
                    stress: torch.Tensor, 
                    damage: torch.Tensor, 
                    plastic_strain: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        计算物理约束损失函数
        
        Args:
            strain: 输入应变
            stress: 预测应力
            damage: 预测损伤
            plastic_strain: 预测塑性应变
            
        Returns:
            losses: 各项损失的字典
        """
        losses = {}
        
        # 1. 弹性应变计算 - 基于补充.md步骤2
        elastic_strain = strain - plastic_strain
        
        # 2. 弹性试算应力 - 基于补充.md步骤3
        sigma_trial = self.E0 * elastic_strain
        
        # 3. 应力本构关系损失
        effective_modulus = self.E0 * (1 - damage)
        stress_constitutive = effective_modulus * elastic_strain
        losses['constitutive'] = torch.mean((stress - stress_constitutive)**2)
        
        # 4. 屈服条件检查损失 - 基于补充.md步骤4
        f0_plus = self.ft
        yield_violation = torch.relu(sigma_trial - f0_plus)
        losses['yield_condition'] = torch.mean(yield_violation**2)
        
        # 5. 损伤变量更新损失 - 基于补充.md步骤6
        damage_theoretical, r_max = self.compute_damage_evolution(strain, plastic_strain)
        losses['damage_evolution'] = torch.mean((damage - damage_theoretical)**2)
        
        # 6. 有效应力张量更新损失 - 基于补充.md步骤7
        # σ_{n+1} = (1 - d^+_{n+1}) * σ^+_{n+1}
        sigma_effective = (1 - damage) * torch.clamp(sigma_trial, min=0.0)
        losses['effective_stress'] = torch.mean((stress - sigma_effective)**2)
        
        # 7. 塑性修正约束 - 基于补充.md步骤5
        # 塑性应变应该在屈服后才产生
        plastic_condition = plastic_strain * (sigma_trial < f0_plus).float()
        losses['plastic_condition'] = torch.mean(plastic_condition**2)
        
        # 8. 塑性应变单调性约束
        if strain.shape[0] > 1:
            sorted_indices = torch.argsort(strain.squeeze())
            plastic_sorted = plastic_strain[sorted_indices].squeeze()
            plastic_diff = torch.diff(plastic_sorted)
            losses['plastic_monotonic'] = torch.mean(torch.relu(-plastic_diff)**2)
        else:
            losses['plastic_monotonic'] = torch.tensor(0.0, device=strain.device)
        
        # 9. 损伤不可逆性约束
        if strain.shape[0] > 1:
            sorted_indices = torch.argsort(strain.squeeze())
            damage_sorted = damage[sorted_indices].squeeze()
            damage_diff = torch.diff(damage_sorted)
            losses['damage_monotonic'] = torch.mean(torch.relu(-damage_diff)**2)
        else:
            losses['damage_monotonic'] = torch.tensor(0.0, device=strain.device)
        
        # 10. 边界条件损失（初始条件）- 基于补充.md参数初始化
        zero_strain = torch.zeros_like(strain[:1])
        zero_stress, zero_damage, zero_plastic = self.forward(zero_strain)
        losses['initial_condition'] = (
            torch.mean(zero_stress**2) + 
            torch.mean(zero_damage**2) + 
            torch.mean(zero_plastic**2)
        )
        
        # 11. 能量耗散约束
        # 损伤应该与能量耗散相关
        if strain.shape[0] > 1:
            energy_dissipation = torch.cumsum(stress * torch.diff(strain, dim=0, prepend=strain[:1]), dim=0)
            damage_energy_relation = damage - energy_dissipation / (self.Gf * 1000)  # Gf转换为MPa·mm
            losses['energy_dissipation'] = torch.mean(damage_energy_relation**2)
        else:
            losses['energy_dissipation'] = torch.tensor(0.0, device=strain.device)
        
        
        return losses
    
    def get_material_parameters(self) -> Dict[str, float]:
        """
        获取当前材料参数
        """
        return {
            'E0': self.E0.item(),
            'ft': self.ft.item(),
            'Gf': self.Gf.item(),
            'alpha': self.alpha.item(),
            'beta': self.beta.item(),
            'lch': self.lch.item(),
            'epsilon_d0': (self.ft / self.E0).item()
        }
    
    def predict_full_response(self, strain_range: np.ndarray) -> Dict[str, np.ndarray]:
        """
        预测完整的材料响应
        
        Args:
            strain_range: 应变范围
            
        Returns:
            response: 包含所有预测结果的字典
        """
        self.eval()
        
        strain_tensor = torch.FloatTensor(strain_range.reshape(-1, 1))
        
        with torch.no_grad():
            stress, damage, plastic_strain = self.forward(strain_tensor)
            
            # 计算其他相关量
            elastic_strain = strain_tensor - plastic_strain
            effective_modulus = self.E0 * (1 - damage)
            
            # 计算理论损伤
            damage_theoretical, r_max = self.compute_damage_evolution(strain_tensor, plastic_strain)
        
        response = {
            'strain': strain_range,
            'stress': stress.numpy().flatten(),
            'damage': damage.numpy().flatten(),
            'plastic_strain': plastic_strain.numpy().flatten(),
            'elastic_strain': elastic_strain.numpy().flatten(),
            'effective_modulus': effective_modulus.numpy().flatten(),
            'damage_theoretical': damage_theoretical.numpy().flatten()
        }
        
        return response

class PINNTrainer:
    """
    PINN模型训练器
    """
    
    def __init__(self, model: ConcreteDAMAGE_PINN, device: str = 'cpu'):
        self.model = model.to(device)
        self.device = device
        self.training_history = {
            'total_loss': [],
            'data_loss': [],
            'physics_losses': {}
        }
    
    def train_step(self, 
                  strain_batch: torch.Tensor, 
                  stress_batch: torch.Tensor,
                  optimizer: torch.optim.Optimizer,
                  loss_weights: Dict[str, float]) -> Dict[str, float]:
        """
        单步训练
        """
        self.model.train()
        optimizer.zero_grad()
        
        # 前向传播
        pred_stress, pred_damage, pred_plastic = self.model(strain_batch)
        
        # 数据拟合损失
        data_loss = torch.mean((pred_stress - stress_batch)**2)
        
        # 物理约束损失
        physics_losses = self.model.physics_loss(
            strain_batch, pred_stress, pred_damage, pred_plastic
        )
        
        # 总损失
        total_loss = data_loss
        for loss_name, loss_value in physics_losses.items():
            weight = loss_weights.get(loss_name, 1.0)
            total_loss += weight * loss_value
        
        # 反向传播
        total_loss.backward()
        optimizer.step()
        
        # 记录损失
        losses_dict = {
            'total_loss': total_loss.item(),
            'data_loss': data_loss.item()
        }
        losses_dict.update({k: v.item() for k, v in physics_losses.items()})
        
        return losses_dict
    
    def train(self, 
             train_loader: torch.utils.data.DataLoader,
             num_epochs: int = 1000,
             lr: float = 0.001,
             loss_weights: Dict[str, float] = None,
             scheduler_params: Dict = None) -> Dict:
        """
        训练模型
        """
        if loss_weights is None:
            loss_weights = {
                'constitutive': 1.0,
                'yield_condition': 0.8,
                'damage_evolution': 0.5,
                'effective_stress': 1.0,
                'plastic_condition': 0.3,
                'plastic_monotonic': 0.1,
                'damage_monotonic': 0.1,
                'initial_condition': 1.0,
                'energy_dissipation': 0.1
            }
        
        # 优化器
        optimizer = torch.optim.Adam(self.model.parameters(), lr=lr)
        
        # 学习率调度器
        if scheduler_params:
            scheduler = torch.optim.lr_scheduler.StepLR(
                optimizer, 
                step_size=scheduler_params.get('step_size', 200),
                gamma=scheduler_params.get('gamma', 0.8)
            )
        else:
            scheduler = None
        
        print(f"开始训练，共 {num_epochs} 个epoch")
        print(f"损失权重: {loss_weights}")
        
        for epoch in range(num_epochs):
            epoch_losses = {}
            
            for batch_strain, batch_stress in train_loader:
                batch_strain = batch_strain.to(self.device)
                batch_stress = batch_stress.to(self.device)
                
                losses = self.train_step(batch_strain, batch_stress, optimizer, loss_weights)
                
                # 累积损失
                for key, value in losses.items():
                    if key not in epoch_losses:
                        epoch_losses[key] = []
                    epoch_losses[key].append(value)
            
            # 计算平均损失
            avg_losses = {key: np.mean(values) for key, values in epoch_losses.items()}
            
            # 记录训练历史
            self.training_history['total_loss'].append(avg_losses['total_loss'])
            self.training_history['data_loss'].append(avg_losses['data_loss'])
            
            for key, value in avg_losses.items():
                if key not in ['total_loss', 'data_loss']:
                    if key not in self.training_history['physics_losses']:
                        self.training_history['physics_losses'][key] = []
                    self.training_history['physics_losses'][key].append(value)
            
            # 更新学习率
            if scheduler:
                scheduler.step()
            
            # 打印进度
            if epoch % 100 == 0 or epoch == num_epochs - 1:
                print(f'Epoch {epoch:4d}: Total Loss = {avg_losses["total_loss"]:.6f}, '
                      f'Data Loss = {avg_losses["data_loss"]:.6f}')
                
                # 打印材料参数
                params = self.model.get_material_parameters()
                print(f'  材料参数: E0={params["E0"]:.1f}, ft={params["ft"]:.3f}, '
                      f'Gf={params["Gf"]:.4f}, α={params["alpha"]:.3f}')
        
        print("训练完成!")
        return self.training_history
    
    def plot_training_history(self, save_path: str = None):
        """
        绘制训练历史
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        
        # 总损失和数据损失
        axes[0,0].plot(self.training_history['total_loss'], label='总损失')
        axes[0,0].plot(self.training_history['data_loss'], label='数据损失')
        axes[0,0].set_xlabel('Epoch')
        axes[0,0].set_ylabel('Loss')
        axes[0,0].set_title('训练损失')
        axes[0,0].legend()
        axes[0,0].grid(True)
        axes[0,0].set_yscale('log')
        
        # 物理损失
        for loss_name, loss_history in self.training_history['physics_losses'].items():
            axes[0,1].plot(loss_history, label=loss_name)
        axes[0,1].set_xlabel('Epoch')
        axes[0,1].set_ylabel('Physics Loss')
        axes[0,1].set_title('物理约束损失')
        axes[0,1].legend()
        axes[0,1].grid(True)
        axes[0,1].set_yscale('log')
        
        # 材料参数演化（如果有记录的话）
        axes[1,0].text(0.1, 0.5, '材料参数最终值:\n' + 
                      '\n'.join([f'{k}: {v:.4f}' for k, v in self.model.get_material_parameters().items()]),
                      transform=axes[1,0].transAxes, fontsize=10)
        axes[1,0].set_title('最终材料参数')
        axes[1,0].axis('off')
        
        # 损失分布
        final_losses = [losses[-1] for losses in self.training_history['physics_losses'].values()]
        loss_names = list(self.training_history['physics_losses'].keys())
        axes[1,1].bar(range(len(final_losses)), final_losses)
        axes[1,1].set_xticks(range(len(loss_names)))
        axes[1,1].set_xticklabels(loss_names, rotation=45)
        axes[1,1].set_ylabel('Final Loss Value')
        axes[1,1].set_title('最终物理损失分布')
        axes[1,1].set_yscale('log')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()