#!/usr/bin/env python3
"""
混凝土拉伸损伤曲线PINN模型主训练脚本

使用方法:
1. 准备数据（CSV/Excel格式或使用合成数据）
2. 设置训练参数
3. 运行训练
4. 分析结果

作者: PINN团队
日期: 2024
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
import logging
from datetime import datetime
from typing import Optional, Dict, Any

# 导入自定义模块
from pinn_model import ConcreteTensionPINN
from trainer import PINNTrainer
from data_utils import ConcreteDataProcessor, generate_synthetic_tension_data

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training.log'),
        logging.StreamHandler()
    ]
)

def setup_device() -> str:
    """设置计算设备"""
    if torch.cuda.is_available():
        device = 'cuda'
        print(f"使用GPU: {torch.cuda.get_device_name()}")
    else:
        device = 'cpu'
        print("使用CPU")
    
    return device

def create_model_config() -> Dict[str, Any]:
    """创建模型配置"""
    config = {
        # 物理参数
        'E0': 30000.0,      # 初始弹性模量 (MPa)
        'ft': 3.0,          # 抗拉强度 (MPa)
        
        # 网络参数
        'hidden_size': 64,
        'num_layers': 2,
        
        # 训练参数
        'learning_rate': 0.001,
        'weight_decay': 1e-5,
        'num_epochs': 1000,
        'batch_size': 1,
        
        # 损失权重
        'loss_weights': {
            'data': 1.0,
            'physics': 0.8,
            'damage': 0.6,
            'plastic': 0.4
        },
        
        # 数据处理参数
        'test_split': 0.2,
        'normalize': True,
        'remove_outliers': True,
        'smooth_data': True,
        'smooth_window': 5,
        
        # 输出设置
        'save_model': True,
        'save_plots': True,
        'log_interval': 50
    }
    
    return config

def load_experimental_data(file_path: str, 
                           config: Dict[str, Any]) -> tuple:
    """
    加载实验数据
    
    Args:
        file_path: 数据文件路径
        config: 配置字典
        
    Returns:
        处理后的训练和测试数据
    """
    print(f"正在加载实验数据: {file_path}")
    
    # 创建数据处理器
    processor = ConcreteDataProcessor(
        normalize=config['normalize'],
        scaler_type='minmax'
    )
    
    # 根据文件类型加载数据
    if file_path.endswith('.csv'):
        strain_data, stress_data = processor.load_data_from_csv(file_path)
    elif file_path.endswith(('.xlsx', '.xls')):
        strain_data, stress_data = processor.load_data_from_excel(file_path)
    else:
        raise ValueError(f"不支持的文件格式: {file_path}")
    
    # 数据清洗和平滑
    strain_clean, stress_clean = processor.clean_and_smooth_data(
        strain_data, stress_data,
        remove_outliers=config['remove_outliers'],
        smooth=config['smooth_data'],
        smooth_window=config['smooth_window']
    )
    
    # 绘制原始数据
    if config['save_plots']:
        processor.plot_raw_data(
            strain_clean, stress_clean, 
            title="实验应力-应变曲线",
            save_path="experimental_data.png"
        )
    
    # 准备训练数据
    train_strain, train_stress, test_strain, test_stress = processor.prepare_training_data(
        strain_clean, stress_clean,
        test_split=config['test_split']
    )
    
    # 绘制数据分割
    if config['save_plots']:
        processor.plot_data_split(save_path="data_split.png")
    
    # 创建数据加载器
    train_loader, test_loader = processor.create_dataloaders(
        train_strain, train_stress, test_strain, test_stress,
        batch_size=config['batch_size']
    )
    
    return train_loader, test_loader, processor

def use_synthetic_data(config: Dict[str, Any]) -> tuple:
    """
    使用合成数据进行训练
    
    Args:
        config: 配置字典
        
    Returns:
        处理后的训练和测试数据
    """
    print("生成合成混凝土拉伸数据...")
    
    # 生成合成数据
    strain_data, stress_data = generate_synthetic_tension_data(
        E0=config['E0'],
        ft=config['ft'],
        A_plus=0.7,     # 真实参数，模型需要学习
        B_plus=2.0,     # 真实参数，模型需要学习
        xi=0.1,         # 真实参数，模型需要学习
        max_strain=0.0008,
        n_points=120,
        noise_level=0.03
    )
    
    # 创建数据处理器
    processor = ConcreteDataProcessor(
        normalize=config['normalize'],
        scaler_type='minmax'
    )
    
    # 处理数据
    strain_clean, stress_clean = processor.clean_and_smooth_data(
        strain_data, stress_data,
        remove_outliers=config['remove_outliers'],
        smooth=config['smooth_data'],
        smooth_window=config['smooth_window']
    )
    
    # 绘制原始数据
    if config['save_plots']:
        processor.plot_raw_data(
            strain_clean, stress_clean, 
            title="合成混凝土拉伸数据",
            save_path="synthetic_data.png"
        )
    
    # 准备训练数据
    train_strain, train_stress, test_strain, test_stress = processor.prepare_training_data(
        strain_clean, stress_clean,
        test_split=config['test_split']
    )
    
    # 创建数据加载器
    train_loader, test_loader = processor.create_dataloaders(
        train_strain, train_stress, test_strain, test_stress,
        batch_size=config['batch_size']
    )
    
    return train_loader, test_loader, processor

def train_model(config: Dict[str, Any], 
                data_file: Optional[str] = None) -> tuple:
    """
    训练PINN模型
    
    Args:
        config: 配置字典
        data_file: 数据文件路径（可选）
        
    Returns:
        训练好的模型和数据处理器
    """
    print("="*50)
    print("开始训练混凝土拉伸损伤PINN模型")
    print("="*50)
    
    # 设置设备
    device = setup_device()
    
    # 加载数据
    if data_file and os.path.exists(data_file):
        train_loader, test_loader, processor = load_experimental_data(data_file, config)
    else:
        print("未提供实验数据文件，使用合成数据进行训练")
        train_loader, test_loader, processor = use_synthetic_data(config)
    
    # 创建模型
    model = ConcreteTensionPINN(
        E0=config['E0'],
        ft=config['ft'],
        hidden_size=config['hidden_size'],
        num_layers=config['num_layers'],
        device=device
    )
    
    print(f"模型参数总数: {sum(p.numel() for p in model.parameters())}")
    
    # 创建训练器
    trainer = PINNTrainer(
        model=model,
        device=device,
        learning_rate=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    # 设置保存路径
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_path = f"concrete_pinn_model_{timestamp}.pth" if config['save_model'] else None
    
    # 开始训练
    trainer.train(
        train_dataloader=train_loader,
        val_dataloader=test_loader,
        num_epochs=config['num_epochs'],
        loss_weights=config['loss_weights'],
        save_path=save_path,
        log_interval=config['log_interval']
    )
    
    return trainer, processor, save_path

def evaluate_model(trainer: PINNTrainer, 
                   processor: ConcreteDataProcessor,
                   config: Dict[str, Any]) -> None:
    """
    评估模型性能
    
    Args:
        trainer: 训练器
        processor: 数据处理器
        config: 配置字典
    """
    print("\n" + "="*50)
    print("模型评估")
    print("="*50)
    
    # 绘制训练历史
    if config['save_plots']:
        trainer.plot_training_history(save_path="training_history.png")
    
    # 获取完整数据进行预测对比
    full_strain = processor.original_strain
    full_stress = processor.original_stress
    
    # 绘制预测对比
    if config['save_plots']:
        trainer.plot_prediction_comparison(
            full_strain, full_stress,
            save_path="prediction_comparison.png"
        )
    
    # 打印最终识别的材料参数
    final_params = trainer.model.get_material_parameters()
    print("\n最终识别的材料参数:")
    print("-" * 30)
    print(f"A+ = {final_params['A_plus']:.6f}")
    print(f"B+ = {final_params['B_plus']:.6f}")
    print(f"ξ  = {final_params['xi']:.6f}")
    print(f"E0 = {final_params['E0']:.1f} MPa (固定)")
    print(f"ft = {final_params['ft']:.2f} MPa (固定)")

def test_single_point_prediction(trainer: PINNTrainer, 
                                 processor: ConcreteDataProcessor) -> None:
    """
    测试单点预测功能
    
    Args:
        trainer: 训练器
        processor: 数据处理器
    """
    print("\n" + "="*50)
    print("单点预测测试")
    print("="*50)
    
    # 测试几个应变点
    test_strains = [0.00001, 0.0001, 0.0003, 0.0005, 0.0007]
    
    print("应变值\t\t应力(MPa)\t损伤\t\t塑性应变\t损伤更新")
    print("-" * 80)
    
    state_history = []
    for strain in test_strains:
        result = trainer.model.predict_single_point(strain, state_history)
        
        print(f"{strain:.6f}\t{result['stress']:.3f}\t\t{result['damage']:.4f}\t\t"
              f"{result['plastic_strain']:.6f}\t{'是' if result['is_damage_updated'] else '否'}")
        
        # 更新状态历史（简化版）
        state_history = [[{
            'r_max': torch.tensor(result['r_max']),
            'plastic_strain': torch.tensor(result['plastic_strain']),
            'strain': torch.tensor(result['strain']),
            'damage': torch.tensor(result['damage']),
            'lstm_state': None
        }]]

def main():
    """主函数"""
    # 创建配置
    config = create_model_config()
    
    # 打印配置信息
    print("训练配置:")
    print("-" * 20)
    for key, value in config.items():
        if key != 'loss_weights':
            print(f"{key}: {value}")
    print(f"损失权重: {config['loss_weights']}")
    
    # 指定实验数据文件路径
    data_file = r"D:\column\ten2\tension.xlsx"  # 用户的实验数据文件
    print(f"使用实验数据文件: {data_file}")
    
    try:
        # 训练模型
        trainer, processor, model_path = train_model(config, data_file)
        
        # 评估模型
        evaluate_model(trainer, processor, config)
        
        # 测试单点预测
        test_single_point_prediction(trainer, processor)
        
        if model_path:
            print(f"\n模型已保存到: {model_path}")
        
        print("\n训练完成！")
        
    except Exception as e:
        logging.error(f"训练过程中发生错误: {e}")
        print(f"\n❌ 训练过程中发生错误: {e}")
        print(f"\n🔍 错误调试建议:")
        print(f"1. 检查Excel文件路径: {data_file}")
        print(f"2. 确保Excel文件包含'stress'和'strain'列")
        print(f"3. 检查数据格式是否正确（数值型数据）")
        print(f"4. 确保安装了所有依赖: pip install -r requirements.txt")
        print(f"5. 如果是文件权限问题，请确保文件未被其他程序占用")
        
        # 尝试提供更多调试信息
        if os.path.exists(data_file):
            print(f"✅ 文件存在")
            try:
                import pandas as pd
                df = pd.read_excel(data_file)
                print(f"✅ 文件可读，列名: {list(df.columns)}")
                print(f"✅ 数据形状: {df.shape}")
            except Exception as read_error:
                print(f"❌ 文件读取失败: {read_error}")
        else:
            print(f"❌ 文件不存在")
        
        raise

def demo_with_custom_data():
    """使用自定义数据的演示"""
    print("="*50)
    print("自定义数据演示")
    print("="*50)
    
    # 创建自定义应变和应力数据
    custom_strain = np.array([0, 0.00005, 0.0001, 0.00015, 0.0002, 0.0003, 0.0004, 0.0005, 0.0006, 0.0007])
    custom_stress = np.array([0, 1.5, 2.8, 3.0, 2.9, 2.5, 2.0, 1.5, 1.0, 0.5])
    
    # 创建数据处理器
    processor = ConcreteDataProcessor(normalize=True)
    
    # 处理数据
    strain_clean, stress_clean = processor.load_data_from_arrays(custom_strain, custom_stress)
    
    # 其余步骤与main()函数类似...
    print("自定义数据演示完成")

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--demo":
            demo_with_custom_data()
        elif sys.argv[1] == "--help":
            print("使用方法:")
            print("python main_train.py          # 使用默认配置训练")
            print("python main_train.py --demo   # 运行自定义数据演示")
            print("python main_train.py --help   # 显示帮助信息")
        else:
            print(f"未知参数: {sys.argv[1]}")
            print("使用 --help 查看帮助信息")
    else:
        main() 