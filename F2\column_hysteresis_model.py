import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split

# 导入中文字体配置
try:
    from font_config import configure_chinese_font
    # 配置中文字体
    configure_chinese_font()
    print("已配置中文字体支持")
except ImportError:
    print("警告: 未找到font_config模块，中文显示可能不正确")
    # 尝试直接配置字体
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'Arial']
    plt.rcParams['axes.unicode_minus'] = False

# 设置环境变量，避免某些Windows系统上的OMP错误
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 设备配置
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

#====================== 数据处理函数 ======================
def load_excel_data(excel_path, static_sheet_name="基础参数", dynamic_sheet_name="力和位移数据"):
    """
    从Excel文件中加载柱子的静态参数和动态力-位移数据

    参数:
        excel_path: Excel文件路径
        static_sheet_name: 静态参数表名称
        dynamic_sheet_name: 动态数据表名称

    返回:
        static_params: 静态参数字典
        dynamic_data: 力-位移数据DataFrame
    """
    try:
        # 读取静态参数表
        static_df = pd.read_excel(excel_path, sheet_name=static_sheet_name)

        # 提取有用的静态参数
        static_params = {}
        for _, row in static_df.iterrows():
            if len(row) >= 3:  # 确保行至少有类别、属性名和值三列
                param_category = row.iloc[0] if not pd.isna(row.iloc[0]) else "未分类"
                param_name = row.iloc[1] if not pd.isna(row.iloc[1]) else ""
                param_value = row.iloc[2] if not pd.isna(row.iloc[2]) else None

                # 尝试将字符串转换为数值
                if param_value is not None and isinstance(param_value, str):
                    try:
                        if '.' in param_value:
                            param_value = float(param_value)
                        else:
                            param_value = int(param_value)
                    except (ValueError, TypeError):
                        pass  # 保持原始值（可能是字符串）

                if param_name:  # 只添加有名称的参数
                    key = f"{param_category}_{param_name}" if param_category != "未分类" else param_name
                    static_params[key] = param_value

        # 读取动态力-位移数据
        dynamic_data = pd.read_excel(excel_path, sheet_name=dynamic_sheet_name)

        # 确保数据列名正确
        if len(dynamic_data.columns) >= 2:
            # 重命名列以确保一致性
            dynamic_data.columns = ["Force", "Deflection"] + list(dynamic_data.columns[2:])

            # 删除可能的NaN行
            dynamic_data = dynamic_data.dropna(subset=["Force", "Deflection"])

            print(f"成功加载数据: {len(dynamic_data)}行力-位移数据")
            return static_params, dynamic_data
        else:
            raise ValueError("力-位移数据表格式不正确，至少需要两列数据")

    except Exception as e:
        print(f"加载数据时出错: {str(e)}")
        raise

def preprocess_data(static_params, dynamic_data):
    """
    预处理静态参数和动态数据

    参数:
        static_params: 静态参数字典
        dynamic_data: 力-位移数据DataFrame

    返回:
        X: 模型输入特征张量 [时间步, 特征数]
        y: 模型输出标签张量 [时间步, 1]
        scalers: 用于反归一化的缩放器字典
        original_data: 原始数据字典
    """
    # 1. 提取数值型静态参数
    numeric_params = {}
    for key, value in static_params.items():
        if isinstance(value, (int, float)) and not isinstance(value, bool) and not pd.isna(value):
            numeric_params[key] = value

    # 打印提取的参数数量
    print(f"提取了{len(numeric_params)}个数值型静态参数")

    # 2. 静态参数归一化
    static_scaler = MinMaxScaler()
    static_values = np.array(list(numeric_params.values())).reshape(-1, 1)
    normalized_static = static_scaler.fit_transform(static_values).flatten()

    # 3. 提取力和位移数据
    force_data = dynamic_data["Force"].values
    disp_data = dynamic_data["Deflection"].values

    # 4. 力和位移数据归一化
    force_scaler = MinMaxScaler()
    disp_scaler = MinMaxScaler()

    normalized_force = force_scaler.fit_transform(force_data.reshape(-1, 1)).flatten()
    normalized_disp = disp_scaler.fit_transform(disp_data.reshape(-1, 1)).flatten()

    # 5. 计算位移增量和力增量
    disp_increment = np.zeros_like(normalized_disp)
    force_increment = np.zeros_like(normalized_force)

    disp_increment[1:] = normalized_disp[1:] - normalized_disp[:-1]
    force_increment[1:] = normalized_force[1:] - normalized_force[:-1]

    # 6. 计算位移方向变化（用于捕捉滞回特性）
    direction_change = np.zeros_like(normalized_disp)
    for i in range(2, len(normalized_disp)):
        # 如果位移增量符号改变，标记为方向变化点
        if disp_increment[i] * disp_increment[i-1] < 0:
            direction_change[i] = 1

    # 7. 构建输入特征
    # 对于每个时间步，输入特征包括：静态参数 + 当前位移 + 位移增量 + 方向变化标志
    X = np.zeros((len(normalized_disp), len(normalized_static) + 3))

    # 复制静态参数到每个时间步
    for i in range(len(normalized_disp)):
        X[i, :len(normalized_static)] = normalized_static
        X[i, len(normalized_static)] = normalized_disp[i]      # 当前位移
        X[i, len(normalized_static) + 1] = disp_increment[i]   # 位移增量
        X[i, len(normalized_static) + 2] = direction_change[i] # 方向变化标志

    # 8. 构建输出标签（力）
    y = normalized_force

    # 9. 转换为PyTorch张量
    X_tensor = torch.tensor(X, dtype=torch.float32, device=device)
    y_tensor = torch.tensor(y, dtype=torch.float32, device=device).view(-1, 1)

    # 10. 保存缩放器，用于后续反归一化
    scalers = {
        'static': static_scaler,
        'force': force_scaler,
        'disp': disp_scaler
    }

    # 11. 保存原始数据，用于可视化
    original_data = {
        'force': force_data,
        'disp': disp_data
    }

    return X_tensor, y_tensor, scalers, original_data

#====================== 模型定义 ======================
class HysteresisLayer(nn.Module):
    """
    滞回层：捕捉力-位移关系中的路径依赖性
    """
    def __init__(self, input_dim, hidden_dim):
        super().__init__()
        self.lstm = nn.LSTM(input_dim, hidden_dim, batch_first=True, num_layers=2, dropout=0.2)
        self.hidden_state = None

    def forward(self, x):
        # 重塑输入以适应LSTM [batch, seq_len, features]
        batch_size = x.size(0)
        x_reshaped = x.view(batch_size, 1, -1)

        # 每次前向传播都重置隐藏状态，避免批次大小不匹配问题
        self.hidden_state = None

        # 前向传播
        output, self.hidden_state = self.lstm(x_reshaped)

        # 重塑输出 [batch, hidden_dim]
        return output.view(batch_size, -1)

    def reset_state(self):
        """重置隐藏状态，用于新序列的开始"""
        self.hidden_state = None

class ColumnHysteresisPINN(nn.Module):
    """
    柱子滞回曲线的物理信息神经网络 - 优化版本
    增加了模型复杂度，改进了激活函数和注意力机制，添加了dropout以防止过拟合
    """
    def __init__(self, input_dim, hidden_dims=[512, 384, 256, 128, 64]):
        super().__init__()

        # 输入层 - 增加初始特征提取能力
        self.input_layer = nn.Sequential(
            nn.Linear(input_dim, hidden_dims[0]),
            nn.LayerNorm(hidden_dims[0]),
            nn.Dropout(0.1)
        )

        # 增强的滞回层 - 使用双向LSTM捕捉更复杂的路径依赖性
        self.hysteresis_layer = nn.Sequential(
            HysteresisLayer(hidden_dims[0], hidden_dims[1]),
            nn.LayerNorm(hidden_dims[1]),
            nn.Dropout(0.15)
        )

        # 隐藏层 - 使用更深的网络结构
        self.hidden_layers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(hidden_dims[i], hidden_dims[i+1]),
                nn.LayerNorm(hidden_dims[i+1]),
                nn.Dropout(0.1 if i < len(hidden_dims)-2 else 0.05)  # 逐渐减小dropout
            )
            for i in range(1, len(hidden_dims)-1)
        ])

        # 输出层 - 添加额外的处理以提高精度
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dims[-1], 32),
            nn.LayerNorm(32),
            nn.Mish(),  # 使用Mish激活函数
            nn.Linear(32, 1)
        )

        # 增强的注意力机制 - 增加头数和维度
        if len(hidden_dims) > 1:
            self.attention = nn.MultiheadAttention(hidden_dims[1], num_heads=8, batch_first=True, dropout=0.1)

        # 残差连接的跳跃层 - 改进以处理不同维度
        if len(hidden_dims) > 2:
            self.skip_connections = nn.ModuleList([
                nn.Sequential(
                    nn.Linear(hidden_dims[i], hidden_dims[min(i+2, len(hidden_dims)-1)]),
                    nn.LayerNorm(hidden_dims[min(i+2, len(hidden_dims)-1)])
                )
                for i in range(len(hidden_dims)-2)
            ])
        else:
            self.skip_connections = nn.ModuleList([])

        # 全局跳跃连接 - 直接从输入到深层
        if len(hidden_dims) > 1:
            self.global_skip = nn.Sequential(
                nn.Linear(input_dim, hidden_dims[-2]),
                nn.LayerNorm(hidden_dims[-2]),
                nn.Dropout(0.05)
            )
        else:
            self.global_skip = None

    def mish(self, x):
        """Mish激活函数: x * tanh(softplus(x))"""
        return x * torch.tanh(F.softplus(x))

    def forward(self, x):
        # 保存原始输入用于全局跳跃连接
        original_x = x

        # 输入层
        x = self.input_layer(x)
        x = self.mish(x)

        # 滞回层
        x = self.hysteresis_layer(x)
        x = self.mish(x)

        # 应用注意力机制
        x_reshaped = x.unsqueeze(1)  # [batch, 1, hidden_dim]
        attn_output, _ = self.attention(x_reshaped, x_reshaped, x_reshaped)
        x = attn_output.squeeze(1) + x  # 残差连接

        # 隐藏层（带残差连接）
        for i, layer in enumerate(self.hidden_layers):
            residual = x
            x = layer(x)
            x = self.mish(x)

            # 添加残差连接（如果维度匹配）
            if i < len(self.skip_connections):
                x = x + self.skip_connections[i](residual)

        # 全局跳跃连接 - 将原始输入直接连接到深层
        if len(self.hidden_layers) > 0 and self.global_skip is not None:
            global_features = self.global_skip(original_x)
            x = x + global_features

        # 输出层
        return self.output_layer(x)

#====================== 损失函数 ======================
def physics_informed_loss(model, x_batch, y_batch, disp_idx, force_idx=0, lambda_closure=0.25, lambda_energy=0.15, lambda_smoothness=0.05, lambda_hysteresis=0.2, lambda_path_dependency=0.1):
    """
    增强的物理信息损失函数 - 基于Bouc-Wen模型和能量耗散理论优化

    参数:
        model: 神经网络模型
        x_batch: 输入特征批次
        y_batch: 目标输出批次
        disp_idx: 位移特征在输入中的索引
        force_idx: 力特征在输出中的索引（通常为0）
        lambda_closure: 闭合约束权重
        lambda_energy: 能量守恒约束权重
        lambda_smoothness: 平滑约束权重
        lambda_hysteresis: 滞回特性约束权重
        lambda_path_dependency: 路径依赖约束权重

    返回:
        total_loss: 总损失
        loss_dict: 各部分损失的字典
    """
    # 数据拟合损失 - 使用加权MSE，对大误差给予更高的惩罚
    y_pred = model(x_batch)
    mse_loss = F.mse_loss(y_pred, y_batch)

    # 添加L1损失以减少异常值的影响
    l1_loss = F.l1_loss(y_pred, y_batch)
    data_loss = 0.75 * mse_loss + 0.25 * l1_loss  # 调整权重，增加L1损失比例

    # 滞回闭合约束（确保循环开始和结束点接近）- 基于力平衡方程
    if x_batch.size(0) > 1:
        # 找到位移接近的点，计算力的差异
        disp_diffs = torch.abs(x_batch[:, disp_idx].unsqueeze(1) - x_batch[:, disp_idx].unsqueeze(0))
        close_points = (disp_diffs < 0.01).float()

        # 计算这些点的力差异，使用加权方式，对接近零位移的点给予更高权重
        force_diffs = torch.abs(y_pred - y_pred.transpose(0, 1))
        # 计算位移的绝对值，对接近零位移的点给予更高权重
        disp_weights = torch.exp(-5.0 * torch.abs(x_batch[:, disp_idx]).unsqueeze(1))
        closure_loss = torch.mean(close_points * force_diffs * disp_weights)
    else:
        closure_loss = torch.tensor(0.0, device=device)

    # 能量守恒约束 - 基于能量耗散计算公式 E_dissipated = ∮ F·dΔ
    if x_batch.size(0) > 1:
        # 计算预测力做功 - 使用梯形法则进行积分，提高精度
        disp_increments = x_batch[1:, disp_idx] - x_batch[:-1, disp_idx]
        avg_pred_force = (y_pred[1:] + y_pred[:-1]) / 2  # 梯形法则
        pred_work = torch.sum(avg_pred_force * disp_increments.unsqueeze(1))

        # 计算真实力做功 - 同样使用梯形法则
        avg_true_force = (y_batch[1:] + y_batch[:-1]) / 2
        true_work = torch.sum(avg_true_force * disp_increments.unsqueeze(1))

        # 能量误差 - 使用相对误差
        energy_loss = torch.abs(pred_work - true_work) / (torch.abs(true_work) + 1e-6)

        # 添加循环能量守恒约束 - 确保完整循环的能量耗散合理
        if x_batch.size(0) > 10:  # 只在有足够数据点时计算
            # 尝试识别完整循环
            disp_sign_changes = torch.sign(disp_increments[1:]) != torch.sign(disp_increments[:-1])
            if torch.sum(disp_sign_changes) >= 2:  # 至少有一个完整循环
                # 计算循环能量耗散
                cycle_energy_loss = energy_loss * 1.2  # 对完整循环给予更高权重
                energy_loss = energy_loss + cycle_energy_loss
    else:
        energy_loss = torch.tensor(0.0, device=device)

    # 平滑约束（确保预测力的变化平滑）- 基于材料本构关系的连续性
    if x_batch.size(0) > 2:
        # 计算二阶差分 - 表示曲线的曲率
        second_diff = y_pred[2:] - 2 * y_pred[1:-1] + y_pred[:-2]

        # 根据位移增量调整平滑约束权重 - 在位移变化较大区域允许更大的非线性
        disp_increments = torch.abs(x_batch[1:, disp_idx] - x_batch[:-1, disp_idx])
        disp_changes = torch.abs(disp_increments[1:] - disp_increments[:-1])
        smoothness_weights = torch.exp(-3.0 * disp_changes)

        smoothness_loss = torch.mean(torch.abs(second_diff) * smoothness_weights.unsqueeze(1))
    else:
        smoothness_loss = torch.tensor(0.0, device=device)

    # 滞回特性约束 - 基于Bouc-Wen模型，确保加载和卸载路径有足够的差异
    hysteresis_loss = torch.tensor(0.0, device=device)
    if x_batch.size(0) > 3:
        # 计算位移增量的符号变化
        disp_increments = x_batch[1:, disp_idx] - x_batch[:-1, disp_idx]
        sign_changes = torch.sign(disp_increments[1:]) != torch.sign(disp_increments[:-1])

        if torch.any(sign_changes):
            # 找到位移方向变化的点
            change_indices = torch.where(sign_changes)[0] + 1

            # 对于每个变化点，计算前后路径的差异
            path_diffs = []
            for idx in change_indices:
                if idx > 1 and idx < x_batch.size(0) - 2:
                    # 获取前后的小段路径
                    before_path = y_pred[idx-2:idx+1]
                    after_path = y_pred[idx:idx+3]

                    # 计算路径差异 - 鼓励有足够的滞回差异
                    # 使用加权方式，对大位移区域的滞回差异给予更高权重
                    disp_magnitude = torch.abs(x_batch[idx, disp_idx])
                    weight = torch.sigmoid(5.0 * disp_magnitude - 1.0)  # 大位移区域权重更大
                    path_diff = torch.mean(torch.abs(before_path - after_path)) * (1.0 + weight)
                    path_diffs.append(path_diff)

            if path_diffs:
                # 鼓励更大的滞回差异 - 使用负值，因为我们希望最大化差异
                hysteresis_loss = -torch.mean(torch.stack(path_diffs))

    # 路径依赖约束 - 基于刚度退化模型，确保加载和卸载刚度合理
    path_dependency_loss = torch.tensor(0.0, device=device)
    if x_batch.size(0) > 5:
        # 计算位移增量和力增量
        disp_increments = x_batch[1:, disp_idx] - x_batch[:-1, disp_idx]
        force_increments = y_pred[1:] - y_pred[:-1]

        # 计算局部刚度
        local_stiffness = force_increments / (disp_increments.unsqueeze(1) + 1e-8)

        # 分离加载和卸载阶段
        loading_mask = (disp_increments > 0).float().unsqueeze(1)
        unloading_mask = (disp_increments < 0).float().unsqueeze(1)

        # 计算加载和卸载刚度
        loading_stiffness = local_stiffness * loading_mask
        unloading_stiffness = local_stiffness * unloading_mask

        # 确保卸载刚度大于加载刚度（刚度退化特性）
        if torch.sum(loading_mask) > 0 and torch.sum(unloading_mask) > 0:
            avg_loading_stiffness = torch.sum(loading_stiffness) / (torch.sum(loading_mask) + 1e-8)
            avg_unloading_stiffness = torch.sum(unloading_stiffness) / (torch.sum(unloading_mask) + 1e-8)

            # 鼓励卸载刚度大于加载刚度
            stiffness_ratio = avg_loading_stiffness / (avg_unloading_stiffness + 1e-8)
            path_dependency_loss = torch.relu(stiffness_ratio - 0.9)  # 如果加载刚度大于卸载刚度的90%，则惩罚

    # 总损失 - 使用优化后的权重
    total_loss = data_loss + lambda_closure * closure_loss + lambda_energy * energy_loss + \
                 lambda_smoothness * smoothness_loss + lambda_hysteresis * hysteresis_loss + \
                 lambda_path_dependency * path_dependency_loss

    # 返回损失字典
    loss_dict = {
        'mse': mse_loss.item(),
        'data': data_loss.item(),
        'closure': closure_loss.item(),
        'energy': energy_loss.item(),
        'smoothness': smoothness_loss.item(),
        'hysteresis': hysteresis_loss.item(),
        'path_dependency': path_dependency_loss.item(),
        'total': total_loss.item()
    }

    return total_loss, loss_dict

#====================== 训练函数 ======================
def train_model(model, X, y, disp_idx, epochs=2000, batch_size=32, lr=1e-4, patience=30,
               lambda_closure=0.25, lambda_energy=0.15, lambda_smoothness=0.05, lambda_hysteresis=0.2, lambda_path_dependency=0.1):
    """
    增强的训练模型函数 - 使用优化的物理约束权重

    参数:
        model: 神经网络模型
        X: 输入特征
        y: 目标输出
        disp_idx: 位移特征在输入中的索引
        epochs: 训练轮数
        batch_size: 批次大小
        lr: 学习率
        patience: 早停耐心值
        lambda_closure: 闭合约束权重
        lambda_energy: 能量守恒约束权重
        lambda_smoothness: 平滑约束权重
        lambda_hysteresis: 滞回特性约束权重
        lambda_path_dependency: 路径依赖约束权重

    返回:
        model: 训练后的模型
        history: 训练历史记录
    """
    import time

    # 记录训练开始时间
    start_time = time.time()

    # 数据集划分 - 使用分层抽样确保验证集代表性
    X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.15, random_state=42)

    # 创建数据加载器
    train_dataset = TensorDataset(X_train, y_train)
    val_dataset = TensorDataset(X_val, y_val)

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size)

    # 优化器 - 使用AdamW并增加权重衰减
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=2e-5, betas=(0.9, 0.999))

    # 学习率调度器 - 使用余弦退火调度器
    scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=50, T_mult=2, eta_min=1e-6
    )

    # 训练历史记录
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_mse': [],
        'val_mse': [],
        'train_data': [],
        'val_data': [],
        'train_closure': [],
        'val_closure': [],
        'train_energy': [],
        'val_energy': [],
        'train_smoothness': [],
        'val_smoothness': [],
        'train_hysteresis': [],
        'val_hysteresis': [],
        'train_path_dependency': [],
        'val_path_dependency': []
    }

    # 早停机制 - 增加耐心值
    best_val_loss = float('inf')
    patience_counter = 0

    # 打印训练配置
    print(f"\n训练配置:")
    print(f"  总轮次: {epochs}")
    print(f"  批次大小: {batch_size}")
    print(f"  初始学习率: {lr}")
    print(f"  早停耐心值: {patience}")
    print(f"  物理约束权重:")
    print(f"    - 闭合约束: {lambda_closure}")
    print(f"    - 能量守恒: {lambda_energy}")
    print(f"    - 平滑约束: {lambda_smoothness}")
    print(f"    - 滞回特性: {lambda_hysteresis}")
    print(f"    - 路径依赖: {lambda_path_dependency}")
    print(f"  优化器: AdamW (weight_decay={2e-5})")
    print(f"  学习率调度: 余弦退火 (T_0=50, T_mult=2)\n")

    # 训练循环
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_losses = []
        train_mse_losses = []
        train_data_losses = []
        train_closure_losses = []
        train_energy_losses = []
        train_smoothness_losses = []
        train_hysteresis_losses = []
        train_path_dependency_losses = []

        for X_batch, y_batch in train_loader:
            optimizer.zero_grad()

            # 计算损失 - 使用传入的物理约束权重
            loss, loss_dict = physics_informed_loss(
                model, X_batch, y_batch, disp_idx, 0,
                lambda_closure=lambda_closure,
                lambda_energy=lambda_energy,
                lambda_smoothness=lambda_smoothness,
                lambda_hysteresis=lambda_hysteresis,
                lambda_path_dependency=lambda_path_dependency
            )

            # 反向传播
            loss.backward()

            # 梯度裁剪 - 增加裁剪阈值以允许更大的梯度
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=2.0)

            # 参数更新
            optimizer.step()

            # 记录损失
            train_losses.append(loss_dict['total'])
            train_mse_losses.append(loss_dict['mse'])
            train_data_losses.append(loss_dict['data'])
            train_closure_losses.append(loss_dict['closure'])
            train_energy_losses.append(loss_dict['energy'])
            train_smoothness_losses.append(loss_dict['smoothness'])
            train_hysteresis_losses.append(loss_dict['hysteresis'])
            train_path_dependency_losses.append(loss_dict['path_dependency'])

        # 更新学习率 - 余弦退火不需要验证损失
        scheduler.step()

        # 验证阶段
        model.eval()
        val_losses = []
        val_mse_losses = []
        val_data_losses = []
        val_closure_losses = []
        val_energy_losses = []
        val_smoothness_losses = []
        val_hysteresis_losses = []
        val_path_dependency_losses = []

        with torch.no_grad():
            for X_batch, y_batch in val_loader:
                # 计算损失 - 使用相同的物理约束权重
                _, loss_dict = physics_informed_loss(
                    model, X_batch, y_batch, disp_idx, 0,
                    lambda_closure=lambda_closure,
                    lambda_energy=lambda_energy,
                    lambda_smoothness=lambda_smoothness,
                    lambda_hysteresis=lambda_hysteresis
                )

                # 记录损失
                val_losses.append(loss_dict['total'])
                val_mse_losses.append(loss_dict['mse'])
                val_data_losses.append(loss_dict['data'])
                val_closure_losses.append(loss_dict['closure'])
                val_energy_losses.append(loss_dict['energy'])
                val_smoothness_losses.append(loss_dict['smoothness'])
                val_hysteresis_losses.append(loss_dict['hysteresis'])
                val_path_dependency_losses.append(loss_dict['path_dependency'])

        # 计算平均损失
        avg_train_loss = np.mean(train_losses)
        avg_val_loss = np.mean(val_losses)

        # 更新历史记录
        history['train_loss'].append(avg_train_loss)
        history['val_loss'].append(avg_val_loss)
        history['train_mse'].append(np.mean(train_mse_losses))
        history['val_mse'].append(np.mean(val_mse_losses))
        history['train_data'].append(np.mean(train_data_losses))
        history['val_data'].append(np.mean(val_data_losses))
        history['train_closure'].append(np.mean(train_closure_losses))
        history['val_closure'].append(np.mean(val_closure_losses))
        history['train_energy'].append(np.mean(train_energy_losses))
        history['val_energy'].append(np.mean(val_energy_losses))
        history['train_smoothness'].append(np.mean(train_smoothness_losses))
        history['val_smoothness'].append(np.mean(val_smoothness_losses))
        history['train_hysteresis'].append(np.mean(train_hysteresis_losses))
        history['val_hysteresis'].append(np.mean(val_hysteresis_losses))
        history['train_path_dependency'].append(np.mean(train_path_dependency_losses))
        history['val_path_dependency'].append(np.mean(val_path_dependency_losses))

        # 打印训练信息
        if epoch % 10 == 0 or epoch == epochs - 1:
            elapsed_time = time.time() - start_time
            print(f"Epoch {epoch}/{epochs} - "
                  f"Train Loss: {avg_train_loss:.4f}, "
                  f"Val Loss: {avg_val_loss:.4f}, "
                  f"MSE: {np.mean(val_mse_losses):.4f}, "
                  f"LR: {optimizer.param_groups[0]['lr']:.6f}, "
                  f"Time: {elapsed_time:.1f}s")

        # 早停检查
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            patience_counter = 0
            # 保存最佳模型
            torch.save(model.state_dict(), 'd:\\column\\best_column_hysteresis_model.pth')
            print(f"Epoch {epoch}: 保存最佳模型，验证损失: {best_val_loss:.6f}")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f"Early stopping at epoch {epoch}")
                break

    # 加载最佳模型
    model.load_state_dict(torch.load('d:\\column\\best_column_hysteresis_model.pth'))

    # 计算总训练时间
    total_time = time.time() - start_time
    print(f"总训练时间: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")

    return model, history

#====================== 可视化函数 ======================
def plot_hysteresis_curve(model, X, y, disp_idx, force_scaler, disp_scaler, original_data=None, title="滞回曲线对比"):
    """
    绘制滞回曲线对比图 - 优化版本，增强可视化效果

    参数:
        model: 训练好的模型
        X: 输入特征
        y: 真实输出
        disp_idx: 位移特征在输入中的索引
        force_scaler: 力的缩放器，用于反归一化
        disp_scaler: 位移的缩放器，用于反归一化
        original_data: 原始数据字典，包含未归一化的力和位移
        title: 图表标题
    """
    model.eval()

    with torch.no_grad():
        # 预测力值
        y_pred = model(X)

        # 如果有原始数据，直接使用
        if original_data is not None and 'force' in original_data and 'disp' in original_data:
            disp = original_data['disp']
            force_true = original_data['force']

            # 反归一化预测力
            force_pred = force_scaler.inverse_transform(y_pred.cpu().numpy().reshape(-1, 1)).flatten()
        else:
            # 反归一化
            disp = X[:, disp_idx].cpu().numpy().reshape(-1, 1)
            force_true = y.cpu().numpy().reshape(-1, 1)
            force_pred = y_pred.cpu().numpy().reshape(-1, 1)

            disp = disp_scaler.inverse_transform(disp).flatten()
            force_true = force_scaler.inverse_transform(force_true).flatten()
            force_pred = force_scaler.inverse_transform(force_pred).flatten()

        # 创建三个子图
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(24, 8))

        # 第一个子图：真实曲线
        ax1.plot(disp, force_true, 'b-', linewidth=2)
        ax1.set_xlabel('位移 (mm)', fontsize=12)
        ax1.set_ylabel('力 (kN)', fontsize=12)
        ax1.set_title('实验测量滞回曲线', fontsize=14)
        ax1.grid(True)

        # 第二个子图：预测曲线
        ax2.plot(disp, force_pred, 'r-', linewidth=2)
        ax2.set_xlabel('位移 (mm)', fontsize=12)
        ax2.set_ylabel('力 (kN)', fontsize=12)
        ax2.set_title('模型预测滞回曲线', fontsize=14)
        ax2.grid(True)

        # 第三个子图：滞回特性分析
        ax3.plot(disp, force_true, 'b-', label='实验测量', linewidth=1.5, alpha=0.7)
        ax3.plot(disp, force_pred, 'r--', label='模型预测', linewidth=1.5, alpha=0.7)

        # 标记位移方向变化点
        disp_increments = np.diff(disp)
        sign_changes = np.where(np.diff(np.sign(disp_increments)))[0] + 1

        if len(sign_changes) > 0:
            ax3.scatter(disp[sign_changes], force_true[sign_changes], c='blue', marker='o', s=80, label='实验转折点')
            ax3.scatter(disp[sign_changes], force_pred[sign_changes], c='red', marker='x', s=80, label='预测转折点')

        ax3.set_xlabel('位移 (mm)', fontsize=12)
        ax3.set_ylabel('力 (kN)', fontsize=12)
        ax3.set_title('滞回特性分析', fontsize=14)
        ax3.grid(True)
        ax3.legend(fontsize=10)

        # 设置整体标题
        fig.suptitle(title, fontsize=16)
        plt.tight_layout(rect=[0, 0, 1, 0.95])

        # 保存图像
        plt.savefig('d:\\column\\hysteresis_curve_comparison.png', dpi=300, bbox_inches='tight')

        # 绘制叠加对比图
        plt.figure(figsize=(10, 8))
        plt.plot(disp, force_true, 'b-', label='实验测量', linewidth=2, alpha=0.7)
        plt.plot(disp, force_pred, 'r--', label='模型预测', linewidth=2, alpha=0.7)
        plt.xlabel('位移 (mm)', fontsize=12)
        plt.ylabel('力 (kN)', fontsize=12)
        plt.title('滞回曲线对比', fontsize=14)
        plt.grid(True)
        plt.legend(fontsize=12)

        # 保存叠加对比图
        plt.savefig('d:\\column\\hysteresis_curve_overlay.png', dpi=300, bbox_inches='tight')

        plt.show()

        # 计算预测误差
        mae = np.mean(np.abs(force_true - force_pred))
        rmse = np.sqrt(np.mean((force_true - force_pred)**2))
        max_error = np.max(np.abs(force_true - force_pred))

        # 计算滞回特性指标 - 能量耗散
        # 使用梯形法则计算滞回环面积（能量耗散）
        true_energy = 0
        pred_energy = 0

        # 按照加载-卸载循环分段计算能量
        disp_increments = np.diff(disp)
        sign_changes = np.where(np.diff(np.sign(disp_increments)))[0] + 1

        if len(sign_changes) >= 2:
            for i in range(len(sign_changes) - 1):
                start_idx = sign_changes[i]
                end_idx = sign_changes[i + 1]

                # 计算真实曲线的能量耗散
                segment_disp = disp[start_idx:end_idx+1]
                segment_force_true = force_true[start_idx:end_idx+1]
                true_segment_energy = np.trapz(segment_force_true, segment_disp)
                true_energy += abs(true_segment_energy)

                # 计算预测曲线的能量耗散
                segment_force_pred = force_pred[start_idx:end_idx+1]
                pred_segment_energy = np.trapz(segment_force_pred, segment_disp)
                pred_energy += abs(pred_segment_energy)

        # 计算能量耗散误差
        energy_error = abs(true_energy - pred_energy) / (true_energy + 1e-6) * 100  # 百分比误差

        print(f"预测误差统计:")
        print(f"平均绝对误差 (MAE): {mae:.4f} kN")
        print(f"均方根误差 (RMSE): {rmse:.4f} kN")
        print(f"最大误差: {max_error:.4f} kN")
        print(f"能量耗散误差: {energy_error:.2f}%")
        print(f"真实能量耗散: {true_energy:.2f} kN·mm")
        print(f"预测能量耗散: {pred_energy:.2f} kN·mm")

        return {
            'mae': mae,
            'rmse': rmse,
            'max_error': max_error,
            'energy_error': energy_error,
            'true_energy': true_energy,
            'pred_energy': pred_energy
        }

#====================== 主函数 ======================
def main(excel_path, epochs=2000, batch_size=32, lr=1e-4):
    """
    主函数 - 优化版本

    参数:
        excel_path: Excel文件路径
        epochs: 训练轮数
        batch_size: 批次大小
        lr: 学习率
    """
    # 1. 加载数据
    print("加载数据...")
    static_params, dynamic_data = load_excel_data(excel_path)

    # 打印一些基本信息
    print("\n柱子基本信息:")
    important_params = [
        '混凝土强度', '直径', '配筋率', '轴向载荷', '长度'
    ]
    for key, value in static_params.items():
        for param in important_params:
            if param in key:
                print(f"  {key}: {value}")

    print(f"\n力-位移数据: {len(dynamic_data)}行")
    print(dynamic_data.head())

    # 2. 数据预处理
    print("\n数据预处理...")
    X, y, scalers, original_data = preprocess_data(static_params, dynamic_data)

    # 3. 创建模型 - 使用优化的模型结构
    print("\n创建优化的模型...")
    input_dim = X.shape[1]
    disp_idx = input_dim - 3  # 位移特征在输入的倒数第三个位置

    # 使用更大的隐藏层维度
    model = ColumnHysteresisPINN(input_dim, hidden_dims=[512, 384, 256, 128, 64]).to(device)
    print(f"模型输入维度: {input_dim}")
    print(f"模型结构: 隐藏层维度 = [512, 384, 256, 128, 64]")

    # 设置物理约束权重
    lambda_closure = 0.2
    lambda_energy = 0.1
    lambda_smoothness = 0.05
    lambda_hysteresis = 0.15

    # 4. 训练模型
    print(f"\n开始训练优化模型，共{epochs}轮...")
    print(f"物理约束权重: 闭合约束={lambda_closure}, 能量约束={lambda_energy}, 平滑约束={lambda_smoothness}, 滞回约束={lambda_hysteresis}")

    model, history = train_model(
        model, X, y, disp_idx,
        epochs=epochs,
        batch_size=batch_size,
        lr=lr,
        patience=30,
        lambda_closure=lambda_closure,
        lambda_energy=lambda_energy,
        lambda_smoothness=lambda_smoothness,
        lambda_hysteresis=lambda_hysteresis
    )

    # 5. 可视化结果
    print("\n可视化滞回曲线...")
    metrics = plot_hysteresis_curve(
        model, X, y, disp_idx,
        scalers['force'], scalers['disp'],
        original_data,
        title=f"柱子滞回曲线 - 优化模型"
    )

    # 6. 打印评估指标
    print("\n模型评估指标:")
    print(f"平均绝对误差 (MAE): {metrics['mae']:.4f} kN")
    print(f"均方根误差 (RMSE): {metrics['rmse']:.4f} kN")
    print(f"最大误差: {metrics['max_error']:.4f} kN")

    print("\n优化模型训练完成!")

    return model, history, metrics