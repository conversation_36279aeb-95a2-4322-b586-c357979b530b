#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试ODB文件内容的脚本
"""

import os
import sys

# 导入Abaqus Python模块
try:
    from odbAccess import openOdb
    from abaqusConstants import *
    IN_ABAQUS = True
    print("成功导入Abaqus模块")
except ImportError:
    print("错误: 未在Abaqus Python环境中运行")
    sys.exit(1)


def debug_odb(odb_path):
    """调试ODB文件内容"""
    print(f"调试ODB文件: {odb_path}")
    
    try:
        odb = openOdb(path=odb_path)
        print("ODB文件打开成功")
    except Exception as e:
        print(f"打开ODB文件失败: {e}")
        return
    
    try:
        # 打印基本信息
        print(f"\n=== ODB基本信息 ===")
        print(f"作业名称: {odb.name}")
        print(f"分析步数量: {len(odb.steps)}")
        
        # 打印分析步信息
        print(f"\n=== 分析步信息 ===")
        for step_name, step in odb.steps.items():
            print(f"分析步: {step_name}")
            print(f"  时间步数量: {len(step.frames)}")
            print(f"  描述: {step.description}")
            
            # 检查第一个和最后一个时间步
            if len(step.frames) > 0:
                first_frame = step.frames[0]
                last_frame = step.frames[-1]
                print(f"  第一个时间步: {first_frame.frameValue}")
                print(f"  最后一个时间步: {last_frame.frameValue}")
                
                # 打印可用的字段输出
                print(f"  可用的字段输出: {list(last_frame.fieldOutputs.keys())}")
        
        # 打印装配体信息
        print(f"\n=== 装配体信息 ===")
        print(f"实例数量: {len(odb.rootAssembly.instances)}")
        
        for instance_name, instance in odb.rootAssembly.instances.items():
            print(f"实例: {instance_name}")
            print(f"  节点数量: {len(instance.nodes)}")
            print(f"  单元数量: {len(instance.elements)}")
            
            # 打印前几个节点的信息
            print(f"  前5个节点:")
            for i, node in enumerate(instance.nodes[:5]):
                print(f"    节点{node.label}: {node.coordinates}")
            
            # 打印前几个单元的信息
            print(f"  前5个单元:")
            for i, element in enumerate(instance.elements[:5]):
                print(f"    单元{element.label}: 类型={element.type}, 节点={[n for n in element.connectivity]}")
        
        # 打印节点集和单元集
        print(f"\n=== 节点集 ===")
        print(f"节点集: {list(odb.rootAssembly.nodeSets.keys())}")
        
        print(f"\n=== 单元集 ===")
        print(f"单元集: {list(odb.rootAssembly.elementSets.keys())}")
        
        # 检查最后一个时间步的具体数据
        step = list(odb.steps.values())[-1]
        last_frame = step.frames[-1]
        
        print(f"\n=== 最后时间步的数据检查 ===")
        print(f"时间: {last_frame.frameValue}")
        
        # 检查位移数据
        if 'U' in last_frame.fieldOutputs:
            u_field = last_frame.fieldOutputs['U']
            print(f"位移字段: 找到 {len(u_field.values)} 个值")
            if len(u_field.values) > 0:
                for i, value in enumerate(u_field.values[:5]):
                    print(f"  节点{value.nodeLabel}: U = {value.data}")
        
        # 检查反力数据
        if 'RF' in last_frame.fieldOutputs:
            rf_field = last_frame.fieldOutputs['RF']
            print(f"反力字段: 找到 {len(rf_field.values)} 个值")
            if len(rf_field.values) > 0:
                for i, value in enumerate(rf_field.values[:5]):
                    print(f"  节点{value.nodeLabel}: RF = {value.data}")
        
        # 检查状态变量
        for sdv_name in ['SDV1', 'SDV2', 'SDV3', 'SDV4', 'SDV5']:
            if sdv_name in last_frame.fieldOutputs:
                sdv_field = last_frame.fieldOutputs[sdv_name]
                print(f"{sdv_name}字段: 找到 {len(sdv_field.values)} 个值")
                if len(sdv_field.values) > 0:
                    for i, value in enumerate(sdv_field.values[:3]):
                        print(f"  单元{value.elementLabel}: {sdv_name} = {value.data}")
        
        odb.close()
        print("\n调试完成")
        
    except Exception as e:
        print(f"调试过程中发生错误: {e}")
        odb.close()


def main():
    """主函数"""
    print("ODB文件调试工具")
    print("=" * 30)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        odb_path = sys.argv[1]
    else:
        odb_path = "tensile_test.odb"
    
    # 检查ODB文件是否存在
    if not os.path.exists(odb_path):
        print(f"错误: ODB文件 {odb_path} 不存在")
        return 1
    
    # 调试ODB文件
    debug_odb(odb_path)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())