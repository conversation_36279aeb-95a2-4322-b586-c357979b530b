# 混凝土受拉受压全滞回曲线PINN模型

## 项目简介

本项目实现了一个基于物理信息神经网络（PINN）的混凝土双向损伤本构模型，能够准确预测混凝土在循环拉压荷载下的滞回行为。模型基于吴建营等人提出的弹塑性损伤本构理论，通过神经网络学习材料参数，实现了对混凝土复杂力学行为的精确建模。

### 主要特点

- **双向损伤演化**：分别考虑受拉损伤(d+)和受压损伤(d-)
- **塑性应变累积**：独立的拉压塑性参数(ξ+, ξ-)
- **物理约束**：严格遵循损伤不可逆、能量耗散等物理规律
- **参数自动识别**：通过实验数据自动识别6个关键物理参数

## 目录结构

```
c2/
├── 核心PINN模块/
│   ├── pinn_model_v2.py        # PINN模型核心
│   ├── train.py                # 训练逻辑
│   ├── predict_hysteresis.py   # 滞回曲线预测
│   ├── data_processor.py       # 数据处理模块
│   ├── model_utils.py          # 模型工具函数
│   ├── font_config.py          # 字体配置
│   └── main.py                 # 主程序入口
│
├── Vumat验证模块/
│   ├── setup_vumat_test.py     # 测试环境搭建
│   ├── generate_material_card.py # 参数转换桥梁
│   ├── post_process.py         # 后处理脚本
│   └── run_full_workflow.py    # 一键执行脚本
│
├── vumat_templates/            # Vumat模板文件
│   ├── full_cyclic_vumat.for   # Fortran子程序
│   ├── tensile_test.inp        # 拉伸测试
│   ├── compression_test.inp    # 压缩测试
│   └── cyclic_test.inp         # 循环加载测试
│
├── cyclic_data.xlsx            # 示例数据
├── requirements.txt            # 依赖包列表
├── README.md                   # 项目说明
├── todo.md                     # 开发记录
│
└── results/                    # 结果保存目录
    └── session_YYYYMMDD_HHMMSS/
        ├── training/           # 训练结果
        ├── prediction/         # 预测结果
        ├── vumat_verification/ # Vumat验证结果
        │   ├── *.inp/.for      # 测试文件
        │   ├── *.odb           # Abaqus结果
        │   └── verification_outputs/
        │       ├── *_comparison.png
        │       └── *_results.csv
        └── session_summary.txt
```

## 安装指南

### 1. 环境要求

- Python 3.8+
- CUDA 11.0+ (可选，用于GPU加速)

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 准备数据

数据文件应为Excel格式，包含两列：
- `strain`: 应变数据
- `stress`: 应力数据（单位：MPa）

### 2. 训练模型

```bash
# 基础训练
python main.py train

# 自定义参数训练
python main.py train --epochs 5000 --lr 0.001 --data-file my_data.xlsx
```

训练参数说明：
- `--epochs`: 训练轮数（默认3000）
- `--lr`: 学习率（默认0.001）
- `--batch-size`: 批次大小（默认32）
- `--hidden-size`: 隐藏层大小（默认64）
- `--num-layers`: 网络层数（默认6）

### 3. 预测滞回曲线

```bash
# 使用最新模型预测
python main.py predict

# 使用指定模型预测
python main.py predict --model-path path/to/model.pth

# 自定义加载路径
python main.py predict --max-tensile-strain 0.003 --max-compressive-strain -0.004 --num-cycles 5
```

预测参数说明：
- `--max-tensile-strain`: 最大拉应变（默认0.002）
- `--max-compressive-strain`: 最大压应变（默认-0.003）
- `--num-cycles`: 循环次数（默认3）
- `--points-per-segment`: 每段点数（默认50）

### 4. 训练并预测

```bash
# 一步完成训练和预测
python main.py train-predict
```

## PINN-VUMAT验证工作流

本项目集成了完整的PINN-VUMAT验证工作流，可以自动将PINN训练得到的参数转换为Abaqus VUMAT子程序的输入，并进行有限元验证。

### 1. 快速开始

```bash
# 运行完整工作流（包括训练和验证）
python run_full_workflow.py
```

### 2. 分步执行

```bash
# 步骤1: 训练PINN模型（如已有模型可跳过）
python main.py train

# 步骤2: 搭建Vumat测试环境
python setup_vumat_test.py [tensile|compression|cyclic]

# 步骤3: 生成材料参数卡片
python generate_material_card.py

# 步骤4: 运行Abaqus分析
cd results/session_*/vumat_verification/
abaqus job=test_name user=full_cyclic_vumat.for interactive

# 步骤5: 后处理和对比分析
abaqus python post_process.py
```

### 3. Vumat验证特性

- **多工况支持**：单轴拉伸、单轴压缩、循环加载
- **自动参数转换**：PINN参数 → Abaqus PROPS格式
- **结果对比分析**：PINN预测 vs Vumat仿真
- **误差评估**：MSE、RMSE、MAE等指标

## 模型原理

### 损伤演化方程

受拉损伤演化：
```
d+ = 1 - (f_t/r+) * ((1-A+) + A+ * exp[B+(1-r+/f_t)])
```

受压损伤演化：
```
d- = 1 - (f_c/r-) * ((1-A-) + A- * exp[B-(1-r-/f_c)])
```

### 本构关系

```
σ = (1-d)E₀(ε-εᵖ)
```

其中d根据应变状态选择d+或d-。

### 识别参数

- **A+, B+**: 受拉损伤参数
- **A-, B-**: 受压损伤参数
- **ξ+**: 受拉塑性系数
- **ξ-**: 受压塑性系数

## 输出结果

### 训练输出

1. **模型文件**
   - `best_model.pth`: 最佳模型
   - `final_model.pth`: 最终模型
   - `checkpoint_epoch_*.pth`: 检查点文件

2. **参数文件**
   - `identified_parameters.json`: 识别的物理参数

3. **图表**
   - `experimental_data.png`: 实验数据可视化
   - `training_history.png`: 训练历史曲线

### 预测输出

1. **数据文件**
   - `prediction_results_*.xlsx`: 预测数据（应变、应力、损伤等）
   - `analysis_results_*.json`: 分析结果（能量耗散、峰值等）

2. **图表**
   - `hysteresis_prediction_*.png`: 滞回曲线及相关分析图

## 注意事项

1. **数据质量**：确保实验数据包含完整的加卸载循环
2. **初始参数**：可根据材料特性调整参数初始值
3. **收敛性**：如果训练不收敛，可尝试调整学习率或损失权重
4. **GPU加速**：大规模数据建议使用GPU加速训练

## 参考文献

1. 吴建营, 李杰. 混凝土弹塑性损伤本构模型研究 I: 基本公式
2. 吴建营, 李杰. 混凝土弹塑性损伤本构模型研究 II: 数值计算和试验验证
3. 吴建营, 李杰. 混凝土弹塑性损伤本构关系统一模型

## 许可证

本项目仅供学术研究使用。

## 联系方式

如有问题，请提交Issue或联系项目维护者。 