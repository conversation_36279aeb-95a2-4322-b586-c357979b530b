# 三维弹性板结构损伤识别与分析的PINN模型项目脚本

## 1. 项目背景与目标
- **背景**：结构损伤识别是工程力学的重要课题，传统方法依赖传感器数据和数值模拟，计算成本高
- **目标**：构建基于物理信息神经网络（PINN）的模型实现：
  - 三维弹性板的位移场和应力场预测
  - 隐含损伤区域识别
  - 损伤程度量化分析

## 2. 物理建模
- **控制方程**：
  1. 平衡方程：∇·σ + F = 0
     $$\nabla \cdot \sigma + F = 0$$
  2. 本构关系：σ = C(θ):ε
     $$\sigma = C(\theta) : \epsilon$$
  3. 应变-位移关系：ε = 0.5(∇u + (∇u)^T)
     $$\epsilon = 0.5 (\nabla u + (\nabla u)^T)$$
- **损伤表征**：
  - 引入损伤变量θ(x,y,z) ∈ [0,1]
  - 模量退化模型：E(x,y,z) = E_0(1 - θ)
     $$E(x,y,z) = E_0 (1 - \theta)$$

## 3. 数据准备
- **数值模拟数据**：
  - 生成基准解（无损伤状态）
  - 生成带损伤场景的有限元仿真数据
- **参数化损伤场景**：
  - 损伤位置：(x_c, y_c, z_c)
  - 损伤范围：(r_x, r_y, r_z)
  - 损伤程度：θ_max
- **数据增强**：
  - 高斯噪声注入
  - 多分辨率采样

## 4. PINN架构设计
- **模型结构**：
  - 空间编码层：将输入的空间坐标 (x, y, z) 映射到高维特征空间
  - 多尺度特征提取模块：提取不同尺度下的特征
  - 物理约束分支：预测位移场 (u, v, w)
  - 损伤识别分支：预测损伤变量 θ
  - 应力计算层：通过自动微分计算应力场

```python
# 伪代码结构示意
class PINN(nn.Module):
    def __init__(self):
        # 空间编码层 (x,y,z) → 256D
        # 多尺度特征提取模块
        # 物理约束分支：位移场预测 (u,v,w)
        # 损伤识别分支：θ预测
        # 应力计算层：通过自动微分实现
        pass
        
    def forward(self, x):
        # 耦合物理约束的联合预测
        return displacement, stress, damage
        pass
```

## 5. 物理约束实现
### 5.1 控制方程残差
- 平衡方程残差：$$\| \nabla \cdot \sigma + F \|^2_2$$
- 本构关系残差：$$\| \sigma - C(\theta) : \epsilon \|^2_2$$
- 应变协调残差：$$\| \epsilon - 0.5 (\nabla u + (\nabla u)^T) \|^2_2$$

### 5.2 边界条件约束
- Dirichlet边界：$$\| u_{pred} - u_{true} \|^2_{\Gamma_D}$$
- Neumann边界：$$\| \sigma \cdot n - \bar{t} \|^2_{\Gamma_N}$$

### 5.3 损伤场约束
- 光滑性约束：$$\lambda_1 \| \nabla \theta \|_1$$
- 区间约束：$$\lambda_2 [\| \max(-\theta, 0) \|^2 + \| \max(\theta - 1, 0) \|^2]$$

## 6. 多任务损失函数
- 复合损失构成：
  - 总损失 = $$\alpha \| u_{pred} - u_{FEM} \|^2$$（数据项） + $$\beta$$ (平衡方程 + 本构关系残差)（物理项） + $$\gamma$$ (损伤正则项)

- 动态权重方案：
  - $$\alpha$$（数据项权重）：基于测量数据密度调整，每epoch更新
  - $$\beta$$（物理项权重）：按相对残差量级反比调整，每迭代步更新
  - $$\gamma$$（损伤项权重）：采用梯度衰减机制，分段常数更新

## 7. 训练策略
### 7.1 两阶段优化流程
- 预训练阶段（0-200 epoch）：
  - 仅使用数据驱动损失（$$\beta=0$$）
  - 学习率从1e-3余弦衰减到1e-4
  - 批量固定256个采样点
- 物理增强阶段（201-1000 epoch）：
  - 动态聚焦损伤区域采样
  - 选择性残差反向传播
  - 自适应调整$$\beta/\gamma$$权重

### 7.2 课程学习设置
| 训练阶段 | 损伤模式       | 物理权重$$\beta$$ | 噪声水平 |
| -------- | -------------- | ----------------- | -------- |
| 1-200    | 单点局部损伤   | 0.1               | 30dB     |
| 201-500  | 多区域复杂损伤 | 0.5               | 20dB     |
| 501-1000 | 全域随机损伤   | 1.0               | 10dB     |

## 8. 结果评估体系
### 8.1 定量指标
- 位移平均绝对误差：$$\Sigma |u_{pred} - u_{true}| / N$$
- 损伤交并比（IoU）：$$(预测 ∩ 真实) / (预测 ∪ 真实)$$
- 应力重构信噪比：$$20 \log (\| \sigma_{true} \| / \| \sigma_{err} \|)$$

### 8.2 可视化方案
- 三维场对比：通过XY/XZ/YZ截面显示预测/真实位移场对比
- 损伤态势图：
  - 设置损伤阈值$$\theta=0.2$$
  - 进行空间聚类分析
  - 生成三维损伤概率云图

### 8.3 诊断能力测试
- 定位精度：$$TP / (TP + FP + FN)$$（微小损伤检测能力）
- 跨工况验证：不同边界条件/载荷组合的泛化性测试
- 噪声鲁棒性：5%-20%高斯噪声下的性能衰减率

