@echo off
echo ===================================
echo 混凝土拉伸损伤PINN模型运行脚本
echo ===================================
echo.

:: 检查Python环境
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [错误] 未检测到Python环境，请安装Python 3.7+
    goto :end
)

:: 检查依赖包
echo 检查依赖包...
pip install -r requirements.txt
echo.

:: 设置数据路径
set DATA_PATH=..\tension.xlsx

:: 菜单选项
:menu
echo 请选择操作:
echo 1. 测试组件
echo 2. 运行示例
echo 3. 训练模型
echo 4. 预测和评估
echo 5. 训练和预测一体化
echo 6. 退出
echo.

set /p choice=请输入选项 (1-6): 

if "%choice%"=="1" goto test_components
if "%choice%"=="2" goto run_example
if "%choice%"=="3" goto train_model
if "%choice%"=="4" goto predict_model
if "%choice%"=="5" goto train_and_predict
if "%choice%"=="6" goto end

echo [错误] 无效选项，请重新选择
goto menu

:test_components
echo.
echo 测试模型组件...
python test_components.py
echo.
echo 测试完成！
pause
goto menu

:run_example
echo.
echo 运行示例...
python example.py
echo.
echo 示例运行完成！
pause
goto menu

:train_model
echo.
echo 训练模型...
set /p epochs=请输入训练轮数 (默认1000): 
if "%epochs%"=="" set epochs=1000

set /p use_dynamic=是否使用动态加权损失? (y/n, 默认y): 
if /i "%use_dynamic%"=="y" (
    python main.py --mode train --data_path %DATA_PATH% --epochs %epochs% --use_dynamic_loss
) else (
    python main.py --mode train --data_path %DATA_PATH% --epochs %epochs%
)

echo.
echo 训练完成！
pause
goto menu

:predict_model
echo.
echo 预测和评估...

set /p sensitivity=是否进行敏感性分析? (y/n, 默认n): 
if /i "%sensitivity%"=="y" (
    python main.py --mode predict --data_path %DATA_PATH% --perform_sensitivity
) else (
    python main.py --mode predict --data_path %DATA_PATH%
)

echo.
echo 预测和评估完成！
pause
goto menu

:train_and_predict
echo.
echo 训练和预测一体化...
set /p epochs=请输入训练轮数 (默认1000): 
if "%epochs%"=="" set epochs=1000

set /p use_dynamic=是否使用动态加权损失? (y/n, 默认y): 
set /p sensitivity=是否进行敏感性分析? (y/n, 默认n): 

set cmd=python main.py --mode both --data_path %DATA_PATH% --epochs %epochs%

if /i "%use_dynamic%"=="y" set cmd=%cmd% --use_dynamic_loss
if /i "%sensitivity%"=="y" set cmd=%cmd% --perform_sensitivity

%cmd%

echo.
echo 训练和预测完成！
pause
goto menu

:end
echo.
echo 感谢使用！
pause