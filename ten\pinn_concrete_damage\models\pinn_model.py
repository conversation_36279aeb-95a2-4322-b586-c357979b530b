import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class ConcreteDamagePINN(nn.Module):
    """
    混凝土拉伸损伤的物理信息神经网络(PINN)模型
    """
    def __init__(self, input_dim=1, hidden_dim=64, lstm_layers=2, fc_layers=2, 
                 dropout=0.1, material_params=None):
        """
        初始化PINN模型
        
        参数:
            input_dim: 输入维度，默认为1（应变）
            hidden_dim: LSTM隐藏层维度
            lstm_layers: LSTM层数
            fc_layers: 全连接层数
            dropout: Dropout比例
            material_params: 材料参数字典，包含:
                - E_0: 初始弹性模量
                - r_0: 初始损伤阈值
                - A_plus: 损伤参数A^+
                - B_plus: 损伤参数B^+
                - xi_plus: 塑性参数
        """
        super(ConcreteDamagePINN, self).__init__()
        
        # 设置默认材料参数 - 修正为符合混凝土损伤模型的物理意义
        default_params = {
            'E_0': 30000.0,  # MPa - 混凝土初始弹性模量
            'r_0': 1e-4,     # 初始损伤阈值 - 较小值确保损伤从低应变开始
            'A_plus': 0.9,   # 损伤参数A^+ - 控制最大损伤程度
            'B_plus': 1.0,   # 损伤参数B^+ - 控制损伤演化速率
            'xi_plus': 0.01  # 塑性参数 - 控制塑性应变发展
        }
        
        # 使用提供的参数或默认参数
        self.material_params = default_params if material_params is None else material_params
        
        # 初始化可训练的材料参数
        for param_name, param_value in self.material_params.items():
            # 创建可训练的参数，使用对数空间避免负值
            if param_name in ['E_0', 'r_0', 'B_plus']:
                # 对于必须为正的参数，使用对数初始化
                param_tensor = torch.log(torch.tensor(param_value, dtype=torch.float32))
            else:
                param_tensor = torch.tensor(param_value, dtype=torch.float32)
            self.register_parameter(param_name + '_raw', nn.Parameter(param_tensor))
        
        # 添加参数访问属性
        self._setup_param_properties()
        
        # LSTM层
        self.lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=lstm_layers,
            dropout=dropout if lstm_layers > 1 else 0,
            batch_first=True
        )
        
        # 全连接层
        fc_layers_list = []
        in_features = hidden_dim
        
        for i in range(fc_layers - 1):
            fc_layers_list.append(nn.Linear(in_features, hidden_dim))
            fc_layers_list.append(nn.ReLU())
            fc_layers_list.append(nn.Dropout(dropout))
            in_features = hidden_dim
        
        # 输出层 - 预测损伤变量和塑性应变（应力通过本构方程计算）
        fc_layers_list.append(nn.Linear(in_features, 2))  # 输出: [损伤变量, 塑性应变]
        
        self.fc_layers = nn.Sequential(*fc_layers_list)
        
        # 状态变量
        self.reset_state()
    
    def _setup_param_properties(self):
        """设置参数访问属性"""
        @property
        def E_0(self):
            return torch.exp(self.E_0_raw).clamp(min=1000, max=100000)
        
        @property
        def r_0(self):
            return torch.exp(self.r_0_raw).clamp(min=1e-6, max=1e-1)
        
        @property
        def A_plus(self):
            return torch.sigmoid(self.A_plus_raw) * 0.9 + 0.05  # 限制在[0.05, 0.95]
        
        @property
        def B_plus(self):
            return torch.exp(self.B_plus_raw).clamp(min=0.1, max=2.0)  # 调整为更合理的范围
        
        @property
        def xi_plus(self):
            return torch.sigmoid(self.xi_plus_raw) * 0.2 + 0.01  # 限制在[0.01, 0.21]
        
        # 绑定属性到类
        type(self).E_0 = E_0
        type(self).r_0 = r_0
        type(self).A_plus = A_plus
        type(self).B_plus = B_plus
        type(self).xi_plus = xi_plus
    
    def reset_state(self, batch_size=1):
        """
        重置内部状态变量
        """
        # 获取设备
        device = next(self.parameters()).device
        
        # 初始化损伤变量、塑性应变和损伤阈值的历史记录
        self.damage_history = torch.zeros(batch_size, 1, device=device)
        self.plastic_strain_history = torch.zeros(batch_size, 1, device=device)
        self.damage_threshold_history = torch.ones(batch_size, 1, device=device) * self.r_0
    
    def forward(self, strain_seq, return_components=False):
        """
        前向传播
        
        参数:
            strain_seq: 应变序列，形状为 [batch_size, seq_len, 1]
            return_components: 是否返回各组成部分（应力、损伤变量、塑性应变）
            
        返回:
            如果return_components=False，返回预测的应力
            如果return_components=True，返回(应力, 损伤变量, 塑性应变)
        """
        batch_size, seq_len, _ = strain_seq.shape
        
        # 确保状态变量与批次大小匹配
        if self.damage_history.shape[0] != batch_size:
            self.reset_state(batch_size)
        
        # 通过LSTM处理序列
        lstm_out, _ = self.lstm(strain_seq)
        
        # 只使用最后一个时间步的输出
        lstm_last = lstm_out[:, -1, :]
        
        # 通过全连接层
        outputs = self.fc_layers(lstm_last)
        
        # 分离输出 - 只预测损伤变量和塑性应变
        damage_pred = torch.sigmoid(outputs[:, 0:1])  # 损伤变量 (0-1)
        plastic_strain_pred = F.softplus(outputs[:, 1:2])  # 塑性应变 (>0)
        
        # 获取当前应变
        current_strain = strain_seq[:, -1, :]
        
        # 确保应变为正值（拉伸损伤模型）
        current_strain = torch.clamp(current_strain, min=0.0)
        
        # 通过物理本构方程计算应力，确保应力为正值
        stress_pred = self.compute_stress_constitutive(current_strain, damage_pred, plastic_strain_pred)
        
        # 确保应力为正值
        stress_pred = torch.clamp(stress_pred, min=0.0)
        
        # 更新历史状态
        self.damage_history = damage_pred
        self.plastic_strain_history = plastic_strain_pred
        
        # 计算损伤阈值
        Y_plus = 0.5 * self.E_0 * (current_strain - self.plastic_strain_history)**2
        self.damage_threshold_history = torch.maximum(self.damage_threshold_history, Y_plus)
        
        if return_components:
            return stress_pred, damage_pred, plastic_strain_pred
        
        return stress_pred
    
    def compute_stress_constitutive(self, strain, damage, plastic_strain):
        """
        根据本构方程计算应力
        
        参数:
            strain: 应变
            damage: 损伤变量
            plastic_strain: 塑性应变
            
        返回:
            计算的应力
        """
        # 有效应变 - 确保为正值（拉伸损伤模型）
        effective_strain = torch.clamp(strain - plastic_strain, min=0.0)
        
        # 确保损伤变量在合理范围内
        damage = torch.clamp(damage, min=0.0, max=0.99)
        
        # 根据损伤力学本构方程计算应力
        stress = (1 - damage) * self.E_0 * effective_strain
        
        # 确保应力为正值
        stress = torch.clamp(stress, min=0.0)
        
        return stress
    
    def compute_damage_evolution(self, strain, plastic_strain):
        """
        计算损伤演化 - 修正版本确保损伤变量单调递增且为正值
        
        参数:
            strain: 应变
            plastic_strain: 塑性应变
            
        返回:
            损伤变量
        """
        # 计算有效应变（只考虑拉伸部分）
        effective_strain = torch.clamp(strain - plastic_strain, min=0.0)  # 只考虑正应变
        
        # 计算损伤能释放率 Y^+
        Y_plus = 0.5 * self.E_0 * effective_strain**2
        
        # 更新损伤阈值（单调递增）
        r_plus = torch.maximum(self.damage_threshold_history, Y_plus)
        r_plus = torch.clamp(r_plus, min=self.r_0, max=self.r_0 * 100)  # 减小上限避免数值问题
        self.damage_threshold_history = r_plus
        
        # 修正的损伤演化公式 - 确保单调递增且为正值
        # 当 r_plus <= r_0 时，损伤为0
        # 当 r_plus > r_0 时，损伤单调递增
        
        # 归一化的损伤驱动力
        normalized_drive = (r_plus - self.r_0) / (self.r_0 + 1e-8)
        
        # 使用修正的指数损伤演化公式
        # d = 1 - exp(-A_plus * normalized_drive^B_plus)
        # 这确保了损伤从0开始单调递增到接近1
        
        # 限制指数参数避免数值溢出
        exp_arg = -self.A_plus * torch.pow(normalized_drive + 1e-8, self.B_plus / 1000.0)  # 缩放B_plus避免过大
        exp_arg = torch.clamp(exp_arg, min=-50, max=0)  # 确保指数参数为负
        
        damage = 1 - torch.exp(exp_arg)
        
        # 确保损伤变量在[0,1)范围内且单调递增
        damage = torch.clamp(damage, 0, 0.99)
        
        # 确保损伤不会减少（单调性）
        damage = torch.maximum(damage, self.damage_history)
        
        return damage
    
    def compute_plastic_strain_increment(self, strain, strain_rate, stress, damage):
        """
        计算塑性应变增量
        
        参数:
            strain: 应变
            strain_rate: 应变率
            stress: 应力
            damage: 损伤变量
            
        返回:
            塑性应变增量
        """
        # 简化的H(d^+)函数
        H_d = torch.ones_like(damage)
        
        # 计算塑性应变增量，添加更多数值稳定性
        stress_abs = torch.abs(stress) + 1e-6  # 避免除零
        strain_clamped = torch.clamp(strain, min=-0.1, max=0.1)  # 限制应变范围
        strain_rate_clamped = torch.clamp(strain_rate, min=1e-6, max=1e-2)  # 限制应变率范围
        
        delta_epsilon_p = self.xi_plus * H_d * (strain_clamped * strain_rate_clamped) / stress_abs
        delta_epsilon_p = torch.clamp(delta_epsilon_p, min=0, max=0.01)  # 限制塑性应变增量
        
        return delta_epsilon_p
    
    def compute_loss(self, strain_seq, stress_true, loss_weights=None):
        """
        计算总损失函数
        
        参数:
            strain_seq: 应变序列
            stress_true: 真实应力
            loss_weights: 损失权重字典，包含:
                - data_fit: 数据拟合损失权重
                - positive_stress: 应力正值约束权重
                - damage_monotonic: 损伤单调性约束权重
                - plastic_constraint: 塑性应变约束权重
                
        返回:
            总损失和各部分损失的字典
        """
        # 默认损失权重
        default_weights = {
            'data_fit': 10.0,
            'positive_stress': 5.0,
            'damage_monotonic': 1.0,
            'plastic_constraint': 1.0
        }
        
        # 使用提供的权重或默认权重
        weights = default_weights if loss_weights is None else loss_weights
        
        # 前向传播获取预测值
        stress_pred, damage_pred, plastic_strain_pred = self.forward(strain_seq, return_components=True)
        
        # 1. 数据拟合损失 - MSE
        data_loss = F.mse_loss(stress_pred, stress_true)
        
        # 获取当前应变（序列的最后一个值）
        current_strain = strain_seq[:, -1, :]
        
        # 2. 应力正值约束损失
        positive_stress_loss = torch.mean(F.relu(-stress_pred))  # 惩罚负应力
        
        # 3. 损伤演化物理约束损失
        # 损伤应该随应变增加而增加
        damage_monotonic_loss = torch.tensor(0.0, device=damage_pred.device)
        if strain_seq.shape[1] > 1:
            strain_diff = current_strain - strain_seq[:, -2, :]
            damage_diff = damage_pred - self.damage_history
            # 当应变增加时，损伤不应该减少
            damage_monotonic_loss = torch.mean(F.relu(-damage_diff * F.relu(strain_diff)))
        
        # 4. 塑性应变物理约束损失
        # 塑性应变应该为正值且合理范围内
        plastic_constraint_loss = torch.mean(F.relu(-plastic_strain_pred)) + \
                                torch.mean(F.relu(plastic_strain_pred - current_strain))
        
        # 5. 应力范围约束损失
        stress_range_loss = torch.tensor(0.0, device=stress_pred.device)
        if stress_pred.max() - stress_pred.min() < 0.5:  # 如果预测应力范围太小
            stress_range_loss = 1.0 / (stress_pred.max() - stress_pred.min() + 1e-6)
        
        # 计算总损失
        total_loss = (
            weights['data_fit'] * data_loss +
            weights['positive_stress'] * positive_stress_loss +
            weights['damage_monotonic'] * damage_monotonic_loss +
            weights['plastic_constraint'] * plastic_constraint_loss +
            0.1 * stress_range_loss  # 添加应力范围约束
        )
        
        # 返回总损失和各部分损失
        loss_dict = {
            'total': total_loss,
            'data_fit': data_loss,
            'positive_stress': positive_stress_loss,
            'damage_monotonic': damage_monotonic_loss,
            'plastic_constraint': plastic_constraint_loss,
            'stress_range': stress_range_loss
        }
        
        return loss_dict


class DynamicWeightedLoss(nn.Module):
    """
    动态加权损失函数
    """
    def __init__(self, loss_terms=['data_fit', 'constitutive', 'damage_evolution', 'plastic_accumulation']):
        """
        初始化动态加权损失
        
        参数:
            loss_terms: 损失项列表
        """
        super(DynamicWeightedLoss, self).__init__()
        self.loss_terms = loss_terms
        self.n_terms = len(loss_terms)
        
        # 初始化对数权重参数
        self.log_weights = nn.Parameter(torch.zeros(self.n_terms))
        
        # 初始化损失历史
        self.loss_history = {term: [] for term in loss_terms}
    
    def forward(self, loss_dict):
        """
        计算动态加权损失
        
        参数:
            loss_dict: 损失字典，包含各损失项
            
        返回:
            加权总损失
        """
        # 计算权重 (softmax of log_weights)
        weights = F.softmax(self.log_weights, dim=0)
        
        # 计算加权损失
        weighted_loss = 0
        for i, term in enumerate(self.loss_terms):
            if term in loss_dict:
                weighted_loss += weights[i] * loss_dict[term]
                self.loss_history[term].append(loss_dict[term].item())
        
        return weighted_loss
    
    def get_weights(self):
        """
        获取当前权重
        """
        return F.softmax(self.log_weights, dim=0).detach().cpu().numpy()
    
    def get_loss_history(self):
        """
        获取损失历史
        """
        return self.loss_history