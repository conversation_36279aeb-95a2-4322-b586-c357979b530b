import numpy as np
import pandas as pd

def gen(E0, f0, A, B, name):
    eps = np.linspace(0, 0.0008, 500)
    sigma_eff = E0 * eps
    r = f0 * np.ones_like(eps)
    d = np.zeros_like(eps)
    for i in range(1, len(eps)):
        r[i] = max(r[i-1], sigma_eff[i])
        d[i] = 1 - ((f0 / r[i]) * (1 - A) + A) * np.exp(B * (1 - r[i] / f0))
    sigma = (1 - d) * sigma_eff
    df = pd.DataFrame({'strain': eps, 'stress': sigma, 'damage': d, 'r': r})
    df.to_excel(f'd:/column/single/{name}.xlsx', index=False)

# 高性能混凝土
E0_hp = 38000
f0_hp = 3.68
A_hp = 0.0
B_hp = 0.683
# 普通混凝土
E0_nc = 31000
f0_nc = 3.57
A_nc = 0.0
B_nc = 0.518

gen(E0_hp, f0_hp, A_hp, B_hp, 'uniaxial_tension_high_performance')
gen(E0_nc, f0_nc, A_nc, B_nc, 'uniaxial_tension_normal_concrete')