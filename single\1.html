<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>混凝土拉伸损伤模拟实验数据</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #1a2a6c, #2a4d69);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
        }

        .header p {
            margin: 10px 0 0;
            opacity: 0.9;
        }

        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .card h2 {
            color: #2a4d69;
            border-bottom: 2px solid #e0e6ed;
            padding-bottom: 10px;
            margin-top: 0;
        }

        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #2a4d69;
        }

        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #d1d8e0;
            border-radius: 5px;
            font-size: 14px;
        }

        button {
            background: #2a4d69;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: background 0.3s;
            grid-column: span 2;
        }

        button:hover {
            background: #1a2a6c;
        }

        .data-table {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th,
        td {
            padding: 10px;
            text-align: center;
            border-bottom: 1px solid #e0e6ed;
        }

        th {
            background-color: #f0f4f8;
            font-weight: 600;
        }

        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }

        .actions button {
            flex: 1;
            max-width: 200px;
        }

        .chart-container {
            height: 400px;
            position: relative;
        }

        .parameters {
            background-color: #f0f8ff;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .parameter-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .param-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .param-name {
            font-weight: 600;
            color: #2a4d69;
        }

        .param-value {
            font-size: 18px;
            color: #1a2a6c;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            color: #6b7c93;
            font-size: 14px;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>混凝土拉伸损伤模拟实验数据</h1>
        <p>基于吴建营弹塑性损伤本构模型（2005）生成</p>
    </div>

    <div class="container">
        <div class="card">
            <h2>模型参数控制</h2>
            <div class="controls">
                <div class="control-group">
                    <label for="E0">弹性模量 E₀ (MPa)</label>
                    <input type="number" id="E0" value="31027" min="10000" max="50000" step="1000">
                </div>
                <div class="control-group">
                    <label for="ft">抗拉强度 fₜ (MPa)</label>
                    <input type="number" id="ft" value="2.41" min="1" max="5" step="0.1">
                </div>
                <div class="control-group">
                    <label for="A">损伤参数 A⁺</label>
                    <input type="number" id="A" value="1.0" min="0.5" max="1.5" step="0.1">
                </div>
                <div class="control-group">
                    <label for="B">损伤参数 B⁺</label>
                    <input type="number" id="B" value="0.13" min="0.05" max="0.3" step="0.01">
                </div>
                <div class="control-group">
                    <label for="mu">粘性系数 μ⁺ (×10³)</label>
                    <input type="number" id="mu" value="2.1" min="0.5" max="5" step="0.1">
                </div>
                <div class="control-group">
                    <label for="a">非线性指数 a⁺</label>
                    <input type="number" id="a" value="5.5" min="1" max="10" step="0.5">
                </div>
                <div class="control-group">
                    <label for="cycles">循环次数</label>
                    <input type="number" id="cycles" value="5" min="1" max="10" step="1">
                </div>
                <div class="control-group">
                    <label for="strainRate">应变率 (×10⁻⁶/s)</label>
                    <input type="number" id="strainRate" value="5.0" min="0.1" max="50" step="0.1">
                </div>
            </div>
            <button id="generateBtn">生成实验数据</button>
        </div>

        <div class="card">
            <h2>模拟数据预览</h2>
            <div class="data-table">
                <table id="dataPreview">
                    <thead>
                        <tr>
                            <th>应变 (×10⁻⁶)</th>
                            <th>应力 (MPa)</th>
                            <th>损伤变量 d⁺</th>
                            <th>阈值 r⁺</th>
                        </tr>
                    </thead>
                    <tbody id="dataBody">
                        <!-- Data will be populated here -->
                    </tbody>
                </table>
            </div>
            <div class="actions">
                <button id="downloadCSV">下载CSV数据</button>
                <button id="downloadExcel">下载Excel</button>
            </div>
            <div class="parameters">
                <h3>当前参数设置</h3>
                <div class="parameter-list">
                    <div class="param-item">
                        <div class="param-name">弹性模量</div>
                        <div class="param-value" id="param-E0">31027 MPa</div>
                    </div>
                    <div class="param-item">
                        <div class="param-name">抗拉强度</div>
                        <div class="param-value" id="param-ft">2.41 MPa</div>
                    </div>
                    <div class="param-item">
                        <div class="param-name">损伤参数 A⁺</div>
                        <div class="param-value" id="param-A">1.0</div>
                    </div>
                    <div class="param-item">
                        <div class="param-name">损伤参数 B⁺</div>
                        <div class="param-value" id="param-B">0.13</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <h2>应力-应变曲线</h2>
        <div class="chart-container">
            <canvas id="stressStrainChart"></canvas>
        </div>
    </div>

    <div class="card">
        <h2>损伤演化曲线</h2>
        <div class="chart-container">
            <canvas id="damageChart"></canvas>
        </div>
    </div>

    <div class="footer">
        <p>混凝土弹塑性损伤本构关系统一模型 - 吴建营, 李杰 (2005) | 数据生成时间: <span id="timestamp"></span></p>
    </div>

    <script>
        // 获取当前时间
        document.getElementById('timestamp').textContent = new Date().toLocaleString();

        // 获取DOM元素
        const generateBtn = document.getElementById('generateBtn');
        const downloadCSV = document.getElementById('downloadCSV');
        const downloadExcel = document.getElementById('downloadExcel');

        // 图表变量
        let stressStrainChart = null;
        let damageChart = null;
        let generatedData = [];

        // 更新参数显示
        function updateParamDisplay() {
            document.getElementById('param-E0').textContent = document.getElementById('E0').value + ' MPa';
            document.getElementById('param-ft').textContent = document.getElementById('ft').value + ' MPa';
            document.getElementById('param-A').textContent = document.getElementById('A').value;
            document.getElementById('param-B').textContent = document.getElementById('B').value;
        }

        // 初始化参数显示
        updateParamDisplay();

        // 根据参数输入更新显示
        document.querySelectorAll('.controls input').forEach(input => {
            input.addEventListener('input', updateParamDisplay);
        });

        // 生成实验数据函数
        function generateData() {
            // 获取输入参数
            const E0 = parseFloat(document.getElementById('E0').value);
            const ft = parseFloat(document.getElementById('ft').value);
            const A = parseFloat(document.getElementById('A').value);
            const B = parseFloat(document.getElementById('B').value);
            const mu = parseFloat(document.getElementById('mu').value) * 1000; // 转换为N/(s·m)
            const a = parseFloat(document.getElementById('a').value);
            const cycles = parseInt(document.getElementById('cycles').value);
            const strainRate = parseFloat(document.getElementById('strainRate').value) * 1e-6; // 转换为/s

            // 初始值
            let strain = 0;
            let stress = 0;
            let r = ft; // 初始损伤阈值 = 抗拉强度
            let d = 0;
            let data = [];
            let time = 0;

            // 应变范围 (0到0.0003)
            const maxStrain = 0.0003;
            const pointsPerCycle = 100;

            for (let cycle = 0; cycle < cycles; cycle++) {
                // 加载阶段 (应变增加)
                for (let i = 0; i <= pointsPerCycle; i++) {
                    strain = (maxStrain * i) / pointsPerCycle;
                    time += 1 / strainRate;

                    // 计算有效应力
                    const effectiveStress = E0 * strain;

                    // 计算损伤能释放率 Y⁺
                    const Y = effectiveStress;

                    // 更新损伤阈值 (应变率相关)
                    const dt = 1 / strainRate;
                    const phi = Math.max(Y / r - 1, 0);
                    r += dt * mu * Math.pow(phi, a);

                    // 计算损伤变量 d⁺
                    const term1 = (ft / r) * (1 - A) + A;
                    const term2 = Math.exp(B * (1 - r / ft));
                    d = 1 - term1 * term2;

                    // 计算实际应力
                    stress = (1 - d) * effectiveStress;

                    // 保存数据点
                    data.push({
                        strain: strain * 1e6,
                        stress: stress,
                        damage: d,
                        threshold: r,
                        time: time
                    });
                }

                // 卸载阶段 (应变减少)
                for (let i = pointsPerCycle; i >= 0; i--) {
                    strain = (maxStrain * i) / pointsPerCycle;
                    time += 1 / strainRate;

                    // 卸载阶段损伤不更新，但应力随应变减少
                    stress = (1 - d) * E0 * strain;

                    // 保存数据点
                    data.push({
                        strain: strain * 1e6,
                        stress: stress,
                        damage: d,
                        threshold: r,
                        time: time
                    });
                }
            }

            return data;
        }

        // 更新数据表格预览
        function updateDataTable(data) {
            const tableBody = document.getElementById('dataBody');
            tableBody.innerHTML = '';

            // 只显示部分数据点
            const step = Math.floor(data.length / 10);
            for (let i = 0; i < data.length; i += step) {
                if (i >= 30) break; // 最多显示30行

                const point = data[i];
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${point.strain.toFixed(2)}</td>
                    <td>${point.stress.toFixed(4)}</td>
                    <td>${point.damage.toFixed(4)}</td>
                    <td>${point.threshold.toFixed(4)}</td>
                `;
                tableBody.appendChild(row);
            }
        }

        // 绘制应力-应变曲线
        function plotStressStrain(data) {
            const ctx = document.getElementById('stressStrainChart').getContext('2d');

            if (stressStrainChart) {
                stressStrainChart.destroy();
            }

            stressStrainChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.map(p => p.strain),
                    datasets: [{
                        label: '应力 (MPa)',
                        data: data.map(p => p.stress),
                        borderColor: '#1a2a6c',
                        backgroundColor: 'rgba(26, 42, 108, 0.1)',
                        borderWidth: 2,
                        tension: 0.1,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '应变 (×10⁻⁶)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '应力 (MPa)'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '混凝土拉伸应力-应变曲线',
                            font: {
                                size: 16
                            }
                        }
                    }
                }
            });
        }

        // 绘制损伤演化曲线
        function plotDamageEvolution(data) {
            const ctx = document.getElementById('damageChart').getContext('2d');

            if (damageChart) {
                damageChart.destroy();
            }

            damageChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.map(p => p.time),
                    datasets: [{
                        label: '损伤变量 d⁺',
                        data: data.map(p => p.damage),
                        borderColor: '#d9534f',
                        backgroundColor: 'rgba(217, 83, 79, 0.1)',
                        borderWidth: 2,
                        tension: 0.1,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '时间 (s)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '损伤变量 d⁺'
                            },
                            min: 0,
                            max: 1
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '混凝土拉伸损伤演化',
                            font: {
                                size: 16
                            }
                        }
                    }
                }
            });
        }

        // 生成CSV文件
        function downloadCSVFile(data) {
            const csv = Papa.unparse({
                fields: ['应变(×10⁻⁶)', '应力(MPa)', '损伤变量d⁺', '阈值r⁺', '时间(s)'],
                data: data.map(p => [p.strain, p.stress, p.damage, p.threshold, p.time])
            });

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', `concrete_tensile_data_${new Date().toISOString().slice(0, 10)}.csv`);
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 生成Excel文件
        function downloadExcelFile(data) {
            // 由于浏览器限制，实际下载Excel需要服务器支持
            // 这里我们提供一个替代方案：下载CSV文件，用户可以在Excel中打开
            alert("由于浏览器限制，将下载CSV文件。您可以在Excel中打开此文件。");
            downloadCSVFile(data);
        }

        // 事件监听
        generateBtn.addEventListener('click', () => {
            generatedData = generateData();
            updateDataTable(generatedData);
            plotStressStrain(generatedData);
            plotDamageEvolution(generatedData);
        });

        downloadCSV.addEventListener('click', () => {
            if (generatedData.length > 0) {
                downloadCSVFile(generatedData);
            } else {
                alert('请先生成数据！');
            }
        });

        downloadExcel.addEventListener('click', () => {
            if (generatedData.length > 0) {
                downloadExcelFile(generatedData);
            } else {
                alert('请先生成数据！');
            }
        });

        // 初始生成一次数据
        window.addEventListener('load', () => {
            generatedData = generateData();
            updateDataTable(generatedData);
            plotStressStrain(generatedData);
            plotDamageEvolution(generatedData);
        });
    </script>
</body>

</html>