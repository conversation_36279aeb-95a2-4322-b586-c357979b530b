import torch
import numpy as np
print("开始简单测试...")

# 测试PyTorch基本功能
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")

# 测试基本张量操作
x = torch.randn(3, 4)
print(f"张量创建成功: {x.shape}")

# 测试简单神经网络
import torch.nn as nn

class SimpleNet(nn.Module):
    def __init__(self):
        super().__init__()
        self.lstm = nn.LSTM(1, 10, 1, batch_first=True)
        self.fc = nn.Linear(10, 3)
    
    def forward(self, x):
        out, _ = self.lstm(x)
        return self.fc(out)

net = SimpleNet()
test_input = torch.randn(1, 5, 1)
output = net(test_input)
print(f"神经网络测试成功: 输入{test_input.shape} -> 输出{output.shape}")

# 测试损失计算
loss_fn = nn.MSELoss()
target = torch.randn_like(output)
loss = loss_fn(output, target)
print(f"损失计算成功: {loss.item():.6f}")

# 测试反向传播
loss.backward()
print("反向传播成功")

# 测试优化器
optimizer = torch.optim.Adam(net.parameters(), lr=0.001)
optimizer.step()
print("优化器更新成功")

print("\n✅ 所有基本功能测试通过！")
print("PyTorch环境配置正确，PINN模型可以正常运行。")