# PINN模型与VUMAT接口使用指南

本文档详细介绍了如何将训练好的物理信息神经网络（PINN）模型与Abaqus/Explicit中的VUMAT材料模型进行对接，实现高效的混凝土弹塑性损伤行为预测。

## 1. 概述

PINN-VUMAT接口系统由以下几个部分组成：

1. **导出工具** (`export_pinn_for_vumat.py`): 将训练好的PINN模型权重和参数导出为VUMAT可用的格式
2. **接口函数** (`vumat_pinn_interface.f`): 在Fortran中实现PINN模型的前向传播和物理计算
3. **VUMAT实现** (`vumat_concrete_pinn.f`): 使用PINN模型或PINN识别的参数进行材料行为预测
4. **参数文件** (`material_parameters.inc`): 存储PINN识别的材料参数
5. **权重文件** (`network_weights.inc`): 存储PINN模型的权重和偏置

## 2. 工作流程

### 2.1 导出PINN模型

首先，使用导出工具将训练好的PINN模型导出为VUMAT可用的格式：

```bash
python export_pinn_for_vumat.py --model path/to/best_model.pth --output-dir fortran_vumat
```

参数说明：
- `--model`: 训练好的PINN模型路径
- `--output-dir`: 输出目录，默认为'fortran_vumat'
- `--quiet`: 静默模式，不打印详细信息

### 2.2 准备VUMAT文件

导出工具会生成以下文件：

1. `material_parameters.inc`: 包含PINN识别的材料参数
2. `network_weights.inc`: 包含PINN模型的权重和偏置
3. `pinn_weights.npz`: NumPy格式的权重文件（用于Python中的验证）
4. `pinn_model_summary.txt`: 模型摘要信息

将这些文件放在与VUMAT源代码相同的目录中。

### 2.3 选择实现方式

VUMAT实现提供了两种方式使用PINN模型：

1. **直接PINN预测**：使用神经网络直接预测应力、损伤和塑性应变
2. **物理模型预测**：使用PINN识别的参数进行物理模型计算

在`vumat_concrete_pinn.f`文件中，通过修改`USE_DIRECT_PINN`参数来选择使用方式：

```fortran
C     Flag to use direct PINN prediction (1) or physics-based model (0)
      INTEGER USE_DIRECT_PINN
      PARAMETER (USE_DIRECT_PINN = 0)  ! 0: 物理模型, 1: 直接PINN
```

### 2.4 编译和运行

1. 将所有文件放在同一目录下
2. 使用Abaqus编译VUMAT：
   ```bash
   abaqus make job=vumat_concrete_pinn
   ```
3. 在Abaqus输入文件中引用VUMAT：
   ```
   *Material, name=CONCRETE_DAMAGE
   *User Material, constants=9
   10000.0, 3.67, 10.0, 0.845, 1.814, 0.5, 2.0, 1.329, 0.5
   ```

## 3. 接口详解

### 3.1 导出工具 (`export_pinn_for_vumat.py`)

该工具的主要功能：

1. 加载训练好的PINN模型
2. 提取模型权重和参数
3. 将权重和参数转换为Fortran可用的格式
4. 生成各种格式的输出文件

### 3.2 接口函数 (`vumat_pinn_interface.f`)

该文件提供了两个主要函数：

1. `PINN_FORWARD`: 实现神经网络前向传播，直接预测材料行为
   ```fortran
   SUBROUTINE PINN_FORWARD(T_INPUT, SIGMA, DAMAGE, XI)
   ```
   
2. `COMPUTE_DAMAGE_EVOLUTION`: 使用PINN识别的参数计算损伤演化
   ```fortran
   SUBROUTINE COMPUTE_DAMAGE_EVOLUTION(
     1  STRAIN_ELASTIC, STRAIN_INC, 
     2  D_PLUS_OLD, D_MINUS_OLD, R_MAX_PLUS_OLD, R_MAX_MINUS_OLD,
     3  D_PLUS_NEW, D_MINUS_NEW, DELTA_EP)
   ```

### 3.3 VUMAT实现 (`vumat_concrete_pinn.f`)

VUMAT实现了两种预测方式：

1. 直接PINN预测：使用时间步作为输入，直接预测材料响应
2. 物理模型预测：使用PINN识别的参数进行物理模型计算

状态变量说明：
- `STATEV(1)`: 累积塑性应变
- `STATEV(2)`: 受拉损伤变量 d+
- `STATEV(3)`: 受压损伤变量 d-
- `STATEV(4)`: 最大受拉损伤驱动力
- `STATEV(5)`: 最大受压损伤驱动力
- `STATEV(6)`: 时间步计数器（用于PINN输入）

## 4. 参数说明

### 4.1 材料参数

- `E0`: 初始弹性模量 (MPa)
- `f_t`: 抗拉强度 (MPa)
- `f_c`: 抗压强度 (MPa)
- `A_plus`: 受拉损伤参数A
- `B_plus`: 受拉损伤参数B
- `xi_plus`: 受拉塑性系数
- `A_minus`: 受压损伤参数A
- `B_minus`: 受压损伤参数B
- `xi_minus`: 受压塑性系数

### 4.2 PINN模型结构

默认PINN模型结构：
- 输入层: 1个神经元（时间步索引）
- 隐藏层: 6层，每层64个神经元，tanh激活函数
- 输出层: 3个神经元（应力、损伤、塑性系数）

## 5. 验证与调试

### 5.1 验证PINN模型导出

可以使用以下Python代码验证导出的PINN权重是否正确：

```python
import numpy as np
import torch
from pinn_model_v2 import DamagePINNV2

# 加载NumPy格式的权重
weights = np.load('fortran_vumat/pinn_weights.npz')

# 创建模型
model = DamagePINNV2(input_size=1, hidden_size=64, num_layers=6, output_size=3)

# 手动加载权重
state_dict = {}
for name in weights.files:
    state_dict[name] = torch.tensor(weights[name])

model.load_state_dict(state_dict)
model.eval()

# 测试模型
with torch.no_grad():
    t = torch.tensor([[0.5]], dtype=torch.float32)
    output = model(t)
    print("模型输出:", output)
```

### 5.2 调试VUMAT

1. 在Abaqus中使用较小的模型进行测试
2. 启用Abaqus的详细输出选项：
   ```
   *Diagnostic, level=3
   ```
3. 检查状态变量的演化，确保损伤和塑性应变的计算正确

## 6. 注意事项

1. PINN模型的输入和输出应与VUMAT接口保持一致
2. 确保PINN模型的权重和参数正确导出
3. 对于大型模型，可能需要调整VUMAT中的数组大小
4. 直接PINN预测方式可能计算量较大，影响计算效率
5. 物理模型预测方式更高效，但可能无法捕捉PINN模型的全部特性

## 7. 进阶应用

### 7.1 多轴应力状态扩展

当前实现主要针对单轴应力状态，若要扩展到多轴应力状态，需要：

1. 修改PINN模型以接受多维输入（应变张量）
2. 调整VUMAT接口以处理多维应力/应变
3. 实现适当的等效应变和应力计算

### 7.2 动态加载

对于动态加载问题，可以：

1. 在PINN模型中加入加载率相关的输入
2. 在VUMAT中计算应变率并传递给PINN接口
3. 调整损伤演化方程以考虑应变率效应

## 8. 故障排除

常见问题及解决方案：

1. **编译错误**：检查Fortran语法和数组维度
2. **运行时错误**：检查参数文件和权重文件是否正确加载
3. **收敛问题**：调整时间步长或使用物理模型预测方式
4. **不合理结果**：验证PINN模型预测和物理参数的合理性

## 9. 参考资料

1. Abaqus用户手册：VUMAT接口说明
2. 混凝土弹塑性损伤本构模型理论基础
3. PINN模型训练和验证文档

## 10. 联系方式

如有问题或建议，请联系项目维护者。 