# 混凝土本构PINN-VUMAT查表法实现

> 基于物理信息神经网络(PINN)训练结果的混凝土本构VUMAT子程序，采用高效查表+插值方法

## 🎯 项目概述

本项目实现了一个完整的"PINN训练 → 查表生成 → VUMAT验证"工作流，将复杂的混凝土双向损伤本构模型转化为高效的Abaqus VUMAT子程序。

### 核心特点

- ✅ **物理一致性**: 基于PINN训练的物理参数生成查找表
- ✅ **计算高效**: Fortran插值算法，避免复杂的损伤演化计算  
- ✅ **数值稳定**: 预计算的查找表确保数值稳定性
- ✅ **易于维护**: 模块化设计，修改物理模型只需重新生成查找表

## 📁 文件结构

```
c2/
├── 🐍 Python模块
│   ├── generate_lookup_table.py    # 查找表生成器
│   ├── test_lookup_table.py        # 查找表测试工具
│   ├── run_full_workflow.py        # 一键运行工作流
│   └── predict_hysteresis.py       # PINN预测器(已有)
│
├── 🔧 Fortran VUMAT
│   ├── vumat.for                   # VUMAT子程序
│   └── lookup_table_1d.dat         # 查找表数据(生成)
│
├── 📋 Abaqus验证
│   ├── verification_monotonic.inp  # 验证输入文件
│   └── verification_YYYYMMDD/      # 验证结果目录(生成)
│
└── 📚 文档
    ├── README_VUMAT.md             # 本文档
    └── todo.md                     # 原始任务清单
```

## 🚀 快速开始

### 1. 环境准备

```bash
# Python依赖
pip install numpy matplotlib torch

# Abaqus环境
# 确保abaqus命令可在命令行中使用
abaqus -information=environment
```

### 2. 一键运行完整工作流

```bash
# 进入项目目录
cd c2/

# 运行完整工作流
python run_full_workflow.py
```

这将自动执行：
1. 生成查找表数据
2. 准备Abaqus文件  
3. 运行VUMAT验证分析
4. 提取和对比结果
5. 生成验证报告

### 3. 分步执行（高级用户）

```bash
# 步骤1: 生成查找表
python generate_lookup_table.py

# 步骤2: 测试查找表质量
python test_lookup_table.py

# 步骤3: 运行Abaqus验证
abaqus job=verification_monotonic user=vumat.for interactive

# 步骤4: 查看结果
# 结果在 verification_YYYYMMDD/ 目录中
```

## 🔬 技术原理

### 查找表设计

查找表采用**二维参数化**设计：

```
r_max (应变幅值包络) × strain (当前应变) → stress (应力)
```

**关键洞察**: 用单一变量`r_max = max(r_max_old, |strain_current|)`统一表征混凝土的损伤历史，大大简化了状态管理。

### VUMAT计算逻辑

```fortran
! 主要计算步骤:
1. r_max_new = MAX(r_max_old, ABS(strain_new))  ! 更新损伤历史
2. stress_new = INTERPOLATE_2D(strain_new, r_max_new)  ! 二维插值
3. 更新状态变量: STATEV(1)=r_max_new, STATEV(2)=strain_new
```

### 插值算法

采用双线性插值：
1. **r_max方向**: 找到包围当前`r_max`的两个等级
2. **strain方向**: 在每个r_max等级上进行一维插值
3. **最终插值**: 在r_max方向上插值得到最终应力

## ⚙️ 配置选项

### 查找表生成配置

```python
config = {
    'r_max_min': 0.0002,           # 最小r_max等级
    'r_max_max': 0.004,            # 最大r_max等级
    'num_r_max_levels': 20,        # r_max等级数
    'points_per_segment': 200,     # 每段采样点数
    'output_file': 'lookup_table_1d.dat'
}
```

### Abaqus材料卡片

```abaqus
*MATERIAL, NAME=CONCRETE_VUMAT
*DENSITY
  2.5E-9,              ! 密度 (吨/mm³)
*USER MATERIAL, CONSTANTS=1
  30000.0              ! E0 (MPa)
*DEPVAR, N=2
  1, "R_MAX"           ! 应变幅值包络
  2, "EPS_TOTAL"       ! 总应变
```

## 📊 验证结果

典型验证包含：

### 测试工况
- **拉伸阶段**: 0 → +3mm (应变约+0.3%)
- **压缩阶段**: +3mm → -5mm (应变约-0.8%)
- **单元类型**: T3D2桁架单元

### 预期结果
- ✅ 滞回曲线: 展现拉压不对称损伤行为
- ✅ 损伤演化: r_max单调递增，正确反映损伤历史
- ✅ 数值稳定: 无收敛问题或异常振荡

## 🔧 故障排除

### 常见问题

**1. 查找表生成失败**
```bash
# 检查PINN模型文件
ls results/session_*/training/final_model.pth

# 重新运行PINN训练
python main.py train
```

**2. Abaqus分析失败**
```bash
# 检查VUMAT编译
abaqus verify -user vumat.for

# 检查查找表文件
python test_lookup_table.py
```

**3. 插值结果异常**
- 检查查找表数据范围是否覆盖分析工况
- 增加r_max等级数或采样点数
- 验证PINN物理参数的合理性

### 调试技巧

```bash
# 启用详细输出
python generate_lookup_table.py --verbose

# 生成诊断图表
python test_lookup_table.py --generate-plots

# 检查Abaqus消息文件
cat verification_monotonic.msg
```

## 📈 性能优化

### 查找表优化
- **平衡精度与效率**: 增加等级数提高精度，但增加内存消耗
- **建议设置**: 20个r_max等级，每级200个采样点
- **内存占用**: 约20×600×3×8字节 ≈ 288KB

### VUMAT优化
- **编译优化**: 使用Intel Fortran编译器的O2优化
- **并行化**: 支持Abaqus的自动并行化
- **内存管理**: 查找表在所有积分点间共享

## 🔮 扩展方向

### 近期改进
1. **多轴扩展**: 扩展到2D/3D应力状态
2. **温度效应**: 增加温度相关的材料参数
3. **疲劳损伤**: 集成循环疲劳损伤模型

### 长期发展
1. **机器学习**: 直接集成神经网络到VUMAT
2. **多尺度**: 连接分子动力学和宏观本构
3. **实时学习**: 根据实验数据在线更新模型

## 📚 参考文献

1. **PINN方法**: Raissi, M. et al. "Physics-informed neural networks"
2. **混凝土损伤**: 吴建营等人的弹塑性损伤本构理论
3. **VUMAT开发**: Abaqus用户手册-用户子程序

## 🤝 贡献指南

### 问题报告
请在GitHub Issues中报告问题，包含：
- 详细的错误信息
- 输入文件和配置
- 系统环境信息

### 功能请求
欢迎提出新功能需求，特别是：
- 新的材料模型
- 性能优化建议
- 工程应用案例

## 📄 许可证

本项目采用MIT许可证开源。

---

*最后更新: 2024年*
*如有问题，请联系项目维护者* 