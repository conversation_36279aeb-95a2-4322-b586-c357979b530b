1

   Abaqus 2024                                  Date 15-Jul-2025   Time 16:56:28
   For use by Supplied by Team-SolidSQUAD under license from Dassault Systemes or its subsidiary.



                         The Abaqus Software is a product of:

                           Dassault Systemes SIMULIA Corp.
                           1301 Atwood Avenue, Suite 101W
                              Johnston, RI 02919, USA
 


                   The Abaqus Software is available only under license
                   from Dassault Systemes or its subsidiary and may be
                   used or reproduced only in accordance with the terms
                   of such license.
 
                          On machine while 
                          you are authorized to run
                          Abaqus/Standard until 31-Dec-2055

                          Your site id is:  


 
                    For assistance or any other information you may
                    obtain contact information for your local office
                    from the world wide web at:

                      https://www.3ds.com/products-services/simulia/services-support/

 
                * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * 
                *                                                         * 
                *                   *****************                     * 
                *                   *  N O T I C E  *                     * 
                *                   *****************                     * 
                *                                                         * 
                *                                                         * 
                *                       Abaqus 2024                       * 
                *                                                         * 
                *       BUILD ID: 2023_09_21-20.55.25 RELr426 190762      * 
                *                                                         * 
                *                                                         * 
                *  Please make sure you are using                         * 
                *  release Abaqus 2024 manuals                            * 
                *  plus the notes accompanying this release.              * 
                *                                                         * 
                *                                                         * 
                *                                                         * 
                *                                                         * 
                * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * 


 



     PROCESSING PART, INSTANCE, AND ASSEMBLY INFORMATION
   *******************************************************


 ***ERROR: in keyword *BEAMSECTION, file "verification_monotonic.inp", line 17: 
           OdbError: The dimension b must be greater than zero.
 ***NOTE: DUE TO AN INPUT ERROR THE ANALYSIS PRE-PROCESSOR HAS BEEN UNABLE TO 
          INTERPRET SOME DATA.  SUBSEQUENT ERRORS MAY BE CAUSED BY THIS OMISSION

     END PROCESSING PART, INSTANCE, AND ASSEMBLY INFORMATION
   ***********************************************************




     OPTIONS BEING PROCESSED
   ***************************


  *Heading
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
  *Element, type=B31
  *Elset, elset=ASSEMBLY_C1_COLUMN_ELEMS
  *Elset, elset=ASSEMBLY_ALL_BEAMS
  *material, name=CONCRETE_VUMAT
  *density
  *depvar

 ***ERROR: INVALID INTEGER VALUE
 LINE IMAGE: 2**正确写法：下一行直接写“2”

 ***ERROR: THE NUMBER OF SOLUTION-DEPENDENT STATE VARIABLES MUST BE AN INTEGER
  *usermaterial, constants=1

 ***ERROR: THERE ARE INVALID DATA ASSOCIATED WITH THIS USER DEFINED MATERIAL 
           DEFINITION
  *beamsection, elset=ASSEMBLY_C1_COLUMN_ELEMS, material=CONCRETE_VUMAT, section=RECTANGULAR

 ***ERROR: THE RECTANGULAR BEAM SECTION HAS ZERO AREA

 ***ERROR: BENDING STIFFNESS I(11) OF BEAM SECTION IS ZERO; CHECK INPUT DATA.

 ***ERROR: BENDING STIFFNESS I(22) OF BEAM  SECTION IS ZERO; CHECK INPUT DATA.
  *boundary, op=NEW
  *boundary, amplitude=RAMP
  *boundary, op=NEW
  *boundary, amplitude=RAMP
  *output, history, timeinterval=0.1

 ***ERROR: ELEMENT 1 INSTANCE C1 IS CLOSE TO PARALLEL WITH ITS BEAM SECTION 
           AXIS. DIRECTION COSINES OF ELEMENT AXIS 0.0000 0.0000 1.0000. 
           DIRECTION COSINES OF FIRST SECTION AXIS 0.0000 0.0000 -1.0000

 ***ERROR: ELEMENT 2 INSTANCE C1 IS CLOSE TO PARALLEL WITH ITS BEAM SECTION 
           AXIS. DIRECTION COSINES OF ELEMENT AXIS 0.0000 0.0000 1.0000. 
           DIRECTION COSINES OF FIRST SECTION AXIS 0.0000 0.0000 -1.0000

 ***ERROR: ELEMENT 3 INSTANCE C1 IS CLOSE TO PARALLEL WITH ITS BEAM SECTION 
           AXIS. DIRECTION COSINES OF ELEMENT AXIS 0.0000 0.0000 1.0000. 
           DIRECTION COSINES OF FIRST SECTION AXIS 0.0000 0.0000 -1.0000
  *output, history, timeinterval=0.1
  *output, history, timeinterval=0.1

 ***ERROR: THE BEAM CROSS-SECTION DIRECTION VECTORS COINCIDE AT A NODE OF 
           ELEMENT 1 INSTANCE C1

 ***ERROR: THE BEAM CROSS-SECTION DIRECTION VECTORS COINCIDE AT A NODE OF 
           ELEMENT 1 INSTANCE C1

 ***ERROR: THE BEAM CROSS-SECTION DIRECTION VECTORS COINCIDE AT A NODE OF 
           ELEMENT 2 INSTANCE C1

 ***ERROR: THE BEAM CROSS-SECTION DIRECTION VECTORS COINCIDE AT A NODE OF 
           ELEMENT 2 INSTANCE C1

 ***ERROR: THE BEAM CROSS-SECTION DIRECTION VECTORS COINCIDE AT A NODE OF 
           ELEMENT 3 INSTANCE C1

 ***ERROR: THE BEAM CROSS-SECTION DIRECTION VECTORS COINCIDE AT A NODE OF 
           ELEMENT 3 INSTANCE C1
  
                               E L E M E N T   Q U A L I T Y  C H E C K S        
  
  
 ***NOTES: DISTORTED ISOPARAMETRIC ELEMENTS: ANGLE BETWEEN ISOPARAMETRIC LINES 
           IS LESS THAN 45 DEGREES OR GREATER THAN 135 DEGREES.
  
           TETRAHEDRAL QUALITY MEASURE: VOLUME OF TETRAHEDRON DIVIDED BY THE 
           VOLUME OF EQUILATERAL TETRAHEDRON WITH SAME CIRCUMSPHERE RADIUS; 
           0 FOR DEGENERATE TETRAHEDRON AND 1 FOR EQUILATERIAL TETRAHEDRON. IT 
           IS RECOMMENDED THAT THE TETRAHEDRAL QUALITY MEASURE BE GREATER THAN 
           0.02, THE MIN INTERIOR (DIHEDRAL) ANGLE BE GREATER THAN 10 DEGREES, 
           AND THE MAX INTERIOR (DIHEDRAL) ANGLE BE LESS THAN 160 DEGREES. 
  
           MODIFIED TETRAHEDRAL QUALITY MEASURE: ANGLES BETWEEN THE TWO LINE 
           SEGMENTS ON EACH EDGE; THE EDGES OF MODIFIED TETRAHEDRAL SHOULD BE 
           AS STRAIGHT AS POSSIBLE. IT IS RECOMMENDED THAT THE ANGLE BETWEEN 
           THE TWO LINE SEGMENTS ON EACH EDGE IS BETWEEN 160 AND 180 DEGREES. 
  
           TRIANGULAR QUALITY MEASURE: AREA OF TRIANGLE DIVIDED BY THE AREA OF 
           EQUILATERAL TRIANGLE WITH SAME CIRCUMCIRCLE RADIUS; 0 FOR DEGENERATE 
           TRIANGLE AND 1 FOR EQUILATERAL TRIANGLE. IT IS RECOMMENDED THAT THE
           TRIANGULAR QUALITY MEASURE BE GREATER THAN 0.01, THE MIN INTERIOR 
           ANGLE BE GREATER THAN 10 DEGREES, AND THE MAX INTERIOR ANGLE BE LESS 
           THAN 160 DEGREES. 
  
           NODAL ADJUSTMENTS ARISING FROM CONTACT INTERACTIONS AND/OR TIE 
           CONSTRAINTS CAN CAUSE SEVERE ELEMENT DISTORTION. IT MAY BE NECESSARY 
           TO REMESH IN ORDER TO REDUCE THE AMOUNT OF ADJUSTMENT. 

 ***ERROR: Error in defining or computing beam section normal in 3 elements. 
           The elements have been identified in element set 
           ErrElemBeamSecNormal.

 ***ERROR: The beam cross-section direction vectors coincide at a node in 3 
           elements. The elements have been identified in element set 
           ErrElemBeamSecDirVect.
  
  
 --------------------------------------------------------------------------------
 Element normals cannot be calculated
  
   Element    Adjusted nodes 
 ------------ -------------- 
         C1.1       NO       
         C1.2       NO       
         C1.3       NO       
  
  *Step, name=AXIAL_TENSION, nlgeom=NO
  *output, history, timeinterval=0.1
  *Step, name=AXIAL_TENSION, nlgeom=NO
  *Step, name=AXIAL_TENSION, nlgeom=NO
  *static
  *boundary, op=NEW
  *boundary, amplitude=RAMP
  *output, history, timeinterval=0.1
  *nodeoutput, nset=ASSEMBLY_N_TOP
  *endstep

 ***WARNING: OUTPUT AT EXACT, PREDEFINED TIME POINTS WAS REQUESTED IN THIS 
             STEP. IN ORDER TO WRITE OUTPUT AT EXACT TIME POINTS SPECIFIED, 
             Abaqus MIGHT USE TIME INCREMENTS SMALLER THAN THE MINIMUM TIME 
             INCREMENT ALLOWED IN THE STEP. IN ADDITION, THE NUMBER OF 
             INCREMENTS REQUIRED TO COMPLETE THE STEP WILL IN GENERAL INCREASE.
  *Step, name=AXIAL_TENSION, nlgeom=NO
  *static
  *boundary, op=NEW
  *boundary, amplitude=RAMP
  *endstep

 ***ERROR: YOU ARE MIXING OP=NEW AND OP=MOD FOR *BOUNDARY

 ***WARNING: USER SUBROUTINE UMAT WILL BE USED WITH THE STATEV ARRAY 
             DIMENSIONED TO ZERO SINCE THE *DEPVAR OPTION IS NOT USED WITH THIS 
             MATERIAL. CONSEQUENTLY, DEFINING STATEV ENTRIES IN SUBROUTINE UMAT 
             WILL CAUSE CODE EXECUTION ERRORS.



                            P R O B L E M   S I Z E


          NUMBER OF ELEMENTS IS                                     3
          NUMBER OF NODES IS                                       10
          NUMBER OF NODES DEFINED BY THE USER                       4
          NUMBER OF INTERNAL NODES GENERATED BY THE PROGRAM         6
          TOTAL NUMBER OF VARIABLES IN THE MODEL                   24
          (DEGREES OF FREEDOM PLUS MAX NO. OF ANY LAGRANGE MULTIPLIER
           VARIABLES. INCLUDE *PRINT,SOLVE=YES TO GET THE ACTUAL NUMBER.)





          THE PROGRAM HAS DISCOVERED    19 FATAL ERRORS

               ** EXECUTION IS TERMINATED **



                              END OF USER INPUT PROCESSING



     JOB TIME SUMMARY
       USER TIME (SEC)      =     0.10    
       SYSTEM TIME (SEC)    =     0.10    
       TOTAL CPU TIME (SEC) =     0.20    
       WALLCLOCK TIME (SEC) =            0
