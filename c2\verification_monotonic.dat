1

   Abaqus 2024                                  Date 15-Jul-2025   Time 17:10:27
   For use by Supplied by Team-SolidSQUAD under license from Dassault Systemes or its subsidiary.



                         The Abaqus Software is a product of:

                           Dassault Systemes SIMULIA Corp.
                           1301 Atwood Avenue, Suite 101W
                              Johnston, RI 02919, USA
 


                   The Abaqus Software is available only under license
                   from Dassault Systemes or its subsidiary and may be
                   used or reproduced only in accordance with the terms
                   of such license.
 
                          On machine while 
                          you are authorized to run
                          Abaqus/Standard until 31-Dec-2055

                          Your site id is:  


 
                    For assistance or any other information you may
                    obtain contact information for your local office
                    from the world wide web at:

                      https://www.3ds.com/products-services/simulia/services-support/

 
                * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * 
                *                                                         * 
                *                   *****************                     * 
                *                   *  N O T I C E  *                     * 
                *                   *****************                     * 
                *                                                         * 
                *                                                         * 
                *                       Abaqus 2024                       * 
                *                                                         * 
                *       BUILD ID: 2023_09_21-20.55.25 RELr426 190762      * 
                *                                                         * 
                *                                                         * 
                *  Please make sure you are using                         * 
                *  release Abaqus 2024 manuals                            * 
                *  plus the notes accompanying this release.              * 
                *                                                         * 
                *                                                         * 
                *                                                         * 
                *                                                         * 
                * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * 


 



     PROCESSING PART, INSTANCE, AND ASSEMBLY INFORMATION
   *******************************************************


     END PROCESSING PART, INSTANCE, AND ASSEMBLY INFORMATION
   ***********************************************************




     OPTIONS BEING PROCESSED
   ***************************


  *Heading
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
  *Element, type=B31
  *Elset, elset=ASSEMBLY_C1_COLUMN_ELEMS
  *Elset, elset=ASSEMBLY_ALL_BEAMS
  *material, name=CONCRETE_VUMAT
  *density
  *depvar
  *usermaterial, constants=1, type=MECHANICAL
  *transverseshear

 ***ERROR: THE INDEPENDENT VARIABLES MUST BE ARRANGED IN ASCENDING ORDER. THIS 
           ERROR MAY HAVE BEEN CAUSED BY A POSSIBLE EMPTY LINE ON THE DATACARDS 
           IN THE PROPERTY DEFINITION.
 LINE IMAGE:                                                                    
                                                                                
                                                                                
                                                                                
                                                                                
                                                                                
                                                                                
                                                        
 ***NOTE: DUE TO AN INPUT ERROR THE ANALYSIS PRE-PROCESSOR HAS BEEN UNABLE TO 
          INTERPRET SOME DATA.  SUBSEQUENT ERRORS MAY BE CAUSED BY THIS OMISSION
  *beamsection, elset=ASSEMBLY_C1_COLUMN_ELEMS, material=CONCRETE_VUMAT, section=RECTANGULAR
  *boundary
  *boundary, amplitude=RAMP
  *boundary
  *boundary, amplitude=RAMP
  *output, history, timeinterval=0.1
  *output, history, timeinterval=0.1
  *output, history, timeinterval=0.1
  *Step, name=AXIAL_TENSION, nlgeom=NO
  *output, history, timeinterval=0.1
  *Step, name=AXIAL_TENSION, nlgeom=NO
  *Step, name=AXIAL_TENSION, nlgeom=NO
  *static
  *boundary
  *boundary, amplitude=RAMP
  *output, history, timeinterval=0.1
  *nodeoutput, nset=ASSEMBLY_N_TOP
  *endstep

 ***WARNING: OUTPUT AT EXACT, PREDEFINED TIME POINTS WAS REQUESTED IN THIS 
             STEP. IN ORDER TO WRITE OUTPUT AT EXACT TIME POINTS SPECIFIED, 
             Abaqus MIGHT USE TIME INCREMENTS SMALLER THAN THE MINIMUM TIME 
             INCREMENT ALLOWED IN THE STEP. IN ADDITION, THE NUMBER OF 
             INCREMENTS REQUIRED TO COMPLETE THE STEP WILL IN GENERAL INCREASE.
  *Step, name=AXIAL_TENSION, nlgeom=NO
  *static
  *boundary
  *boundary, amplitude=RAMP
  *endstep

 ***WARNING: NON-POSITIVE VALUES OF THE TRANSVERSE  SHEAR MODULUS          
             SPECIFIED BY THE USER WILL BE IGNORED. THEY WILL BE CALCULATED 
             BASED ON THE MATERIAL DEFINITION.



                            P R O B L E M   S I Z E


          NUMBER OF ELEMENTS IS                                     3
          NUMBER OF NODES IS                                       10
          NUMBER OF NODES DEFINED BY THE USER                       4
          NUMBER OF INTERNAL NODES GENERATED BY THE PROGRAM         6
          TOTAL NUMBER OF VARIABLES IN THE MODEL                   24
          (DEGREES OF FREEDOM PLUS MAX NO. OF ANY LAGRANGE MULTIPLIER
           VARIABLES. INCLUDE *PRINT,SOLVE=YES TO GET THE ACTUAL NUMBER.)





          THE PROGRAM HAS DISCOVERED     1 FATAL ERRORS

               ** EXECUTION IS TERMINATED **



                              END OF USER INPUT PROCESSING



     JOB TIME SUMMARY
       USER TIME (SEC)      =     0.20    
       SYSTEM TIME (SEC)    =      0.0    
       TOTAL CPU TIME (SEC) =     0.20    
       WALLCLOCK TIME (SEC) =            0
