import os
import torch
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import argparse
import sys
from column_hysteresis_model import (
    load_excel_data, 
    preprocess_data, 
    ColumnHysteresisPINN, 
    train_model, 
    plot_hysteresis_curve
)
# 导入中文字体配置
from font_config import configure_chinese_font

# 配置中文字体
configure_chinese_font()

# 设置环境变量，避免某些Windows系统上的OMP错误
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 设备配置
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='柱子滞回曲线PINN模型训练与预测')
    parser.add_argument('--excel_path', type=str, default=os.path.join(os.path.dirname(__file__), "column", "滞回曲线", "column1.xlsx"),
                        help='Excel数据文件路径')
    parser.add_argument('--epochs', type=int, default=500,
                        help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=64,
                        help='批次大小')
    parser.add_argument('--lr', type=float, default=5e-4,
                        help='学习率')
    parser.add_argument('--save_dir', type=str, default=os.path.dirname(__file__),
                        help='保存模型和图表的目录')
    parser.add_argument('--mode', type=str, choices=['train', 'predict'], default='train',
                        help='模式：训练或预测')
    parser.add_argument('--model_path', type=str, default=None,
                        help='预测模式下加载的模型路径')
    
    args = parser.parse_args()
    
    # 确保保存目录存在
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 1. 加载数据
    print("加载数据...")
    try:
        static_params, dynamic_data = load_excel_data(args.excel_path)
        print(f"成功加载Excel数据: {args.excel_path}")
        
        # 打印一些基本信息
        print("\n柱子基本信息:")
        important_params = [
            '混凝土强度', '直径', '配筋率', '轴向载荷', '长度'
        ]
        for key, value in static_params.items():
            for param in important_params:
                if param in key:
                    print(f"  {key}: {value}")
        
        print(f"\n力-位移数据: {len(dynamic_data)}行")
        print(dynamic_data.head())
        
    except Exception as e:
        print(f"加载数据失败: {str(e)}")
        return
    
    # 2. 数据预处理
    print("\n数据预处理...")
    X, y, scalers, original_data = preprocess_data(static_params, dynamic_data)
    
    # 3. 创建或加载模型
    print("\n创建模型...")
    input_dim = X.shape[1]
    disp_idx = input_dim - 3  # 假设位移特征在输入的倒数第三个位置
    
    # 使用优化后的模型结构，更大的隐藏层维度
    model = ColumnHysteresisPINN(input_dim, hidden_dims=[512, 384, 256, 128, 64]).to(device)
    print(f"模型输入维度: {input_dim}")
    print(f"模型结构: 隐藏层维度 = [512, 384, 256, 128, 64]")
    
    if args.mode == 'predict' and args.model_path:
        try:
            model.load_state_dict(torch.load(args.model_path))
            print(f"成功加载模型: {args.model_path}")
        except Exception as e:
            print(f"加载模型失败: {str(e)}")
            return
    
    # 4. 训练或预测
    if args.mode == 'train':
        # 设置优化的物理约束权重
        lambda_closure = 0.2
        lambda_energy = 0.1
        lambda_smoothness = 0.05
        lambda_hysteresis = 0.15
        
        print(f"\n开始训练优化模型，共{args.epochs}轮...")
        print(f"物理约束权重: 闭合约束={lambda_closure}, 能量约束={lambda_energy}, 平滑约束={lambda_smoothness}, 滞回约束={lambda_hysteresis}")
        
        model, history = train_model(
            model, X, y, disp_idx, 
            epochs=args.epochs, 
            batch_size=args.batch_size, 
            lr=args.lr,
            patience=30,
            lambda_closure=lambda_closure,
            lambda_energy=lambda_energy,
            lambda_smoothness=lambda_smoothness,
            lambda_hysteresis=lambda_hysteresis
        )
        
        # 保存训练历史
        save_training_history(history, os.path.join(args.save_dir, 'training_history.png'))
        
        # 保存模型
        model_save_path = os.path.join(args.save_dir, 'column_hysteresis_model.pth')
        torch.save(model.state_dict(), model_save_path)
        print(f"模型已保存至: {model_save_path}")
    
    # 5. 可视化结果
    print("\n可视化滞回曲线...")
    metrics = plot_hysteresis_curve(
        model, X, y, disp_idx, 
        scalers['force'], scalers['disp'], 
        original_data,
        title=f"柱子滞回曲线 - {os.path.basename(args.excel_path)}"
    )
    
    # 6. 打印评估指标
    print("\n模型评估指标:")
    print(f"平均绝对误差 (MAE): {metrics['mae']:.4f} kN")
    print(f"均方根误差 (RMSE): {metrics['rmse']:.4f} kN")
    print(f"最大误差: {metrics['max_error']:.4f} kN")
    
    print("\n处理完成!")

def save_training_history(history, save_path):
    """保存训练历史曲线"""
    plt.figure(figsize=(12, 10))
    
    # 绘制总损失
    plt.subplot(2, 2, 1)
    plt.plot(history['train_loss'], label='训练损失')
    plt.plot(history['val_loss'], label='验证损失')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('总损失')
    plt.legend()
    plt.grid(True)
    
    # 绘制MSE损失
    plt.subplot(2, 2, 2)
    plt.plot(history['train_mse'], label='训练MSE')
    plt.plot(history['val_mse'], label='验证MSE')
    plt.xlabel('Epoch')
    plt.ylabel('MSE')
    plt.title('MSE损失')
    plt.legend()
    plt.grid(True)
    
    # 绘制闭合约束损失
    plt.subplot(2, 2, 3)
    plt.plot(history['train_closure'], label='训练闭合约束')
    plt.plot(history['val_closure'], label='验证闭合约束')
    plt.xlabel('Epoch')
    plt.ylabel('Closure Loss')
    plt.title('闭合约束损失')
    plt.legend()
    plt.grid(True)
    
    # 绘制能量约束损失
    plt.subplot(2, 2, 4)
    plt.plot(history['train_energy'], label='训练能量约束')
    plt.plot(history['val_energy'], label='验证能量约束')
    plt.xlabel('Epoch')
    plt.ylabel('Energy Loss')
    plt.title('能量约束损失')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"训练历史已保存至: {save_path}")

def predict_new_column(model, new_static_params, displacement_sequence, scalers):
    """使用训练好的模型预测新柱子的滞回曲线"""
    # 预处理新的静态参数
    numeric_params = {}
    for key, value in new_static_params.items():
        if isinstance(value, (int, float)) and not isinstance(value, bool) and not pd.isna(value):
            numeric_params[key] = value
    
    # 归一化静态参数
    static_values = np.array(list(numeric_params.values())).reshape(-1, 1)
    normalized_static = scalers['static'].transform(static_values).flatten()
    
    # 归一化位移序列
    normalized_disp = scalers['disp'].transform(np.array(displacement_sequence).reshape(-1, 1)).flatten()
    
    # 计算位移增量
    disp_increment = np.zeros_like(normalized_disp)
    disp_increment[1:] = normalized_disp[1:] - normalized_disp[:-1]
    
    # 计算方向变化
    direction_change = np.zeros_like(normalized_disp)
    for i in range(2, len(normalized_disp)):
        if disp_increment[i] * disp_increment[i-1] < 0:
            direction_change[i] = 1
    
    # 构建输入特征
    X = np.zeros((len(normalized_disp), len(normalized_static) + 3))
    for i in range(len(normalized_disp)):
        X[i, :len(normalized_static)] = normalized_static
        X[i, len(normalized_static)] = normalized_disp[i]
        X[i, len(normalized_static) + 1] = disp_increment[i]
        X[i, len(normalized_static) + 2] = direction_change[i]
    
    # 转换为PyTorch张量
    X_tensor = torch.tensor(X, dtype=torch.float32, device=device)
    
    # 预测
    model.eval()
    with torch.no_grad():
        y_pred = model(X_tensor)
        force_pred = scalers['force'].inverse_transform(y_pred.cpu().numpy().reshape(-1, 1)).flatten()
    
    return displacement_sequence, force_pred

if __name__ == "__main__":
    main()