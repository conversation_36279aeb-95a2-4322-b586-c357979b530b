import numpy as np
import pandas as pd

def gen_compression(E0, f0_minus, A_minus, B_minus, alpha, name):
    """
    生成单轴压缩应力应变数据
    参数:
    - E0: 初始弹性模量 (MPa)
    - f0_minus: 压缩强度 (MPa, 正值)
    - A_minus: 压缩损伤参数A-
    - B_minus: 压缩损伤参数B-
    - alpha: 压缩损伤能释放率系数
    - name: 输出文件名
    """
    # 应变范围：0到-0.006（压缩为负值）
    eps = np.linspace(0, -0.006, 500)
    
    # 有效应力 σ_eff = E0 * ε (负值)
    sigma_eff = E0 * eps
    
    # 计算损伤能释放率 Y- = (1-α) * |σ_eff|
    Y_minus = (1 - alpha) * np.abs(sigma_eff)
    
    # 初始化损伤阈值 r-
    r_minus = np.zeros_like(eps)
    r_minus[0] = f0_minus  # 初始阈值
    
    # 更新损伤阈值：r- = max(r-, Y-)
    for i in range(1, len(eps)):
        r_minus[i] = max(r_minus[i-1], Y_minus[i])
    
    # 计算压缩损伤变量 d- (equation 39b)
    d_minus = np.zeros_like(eps)
    for i in range(len(eps)):
        if r_minus[i] <= f0_minus:
            d_minus[i] = 0.0
        else:
            ratio = f0_minus / r_minus[i]
            term1 = ratio * (1 - A_minus) + A_minus
            term2 = np.exp(B_minus * (1 - r_minus[i] / f0_minus))
            d_minus[i] = 1 - term1 * term2
            d_minus[i] = max(0.0, min(0.99, d_minus[i]))  # 限制在[0, 0.99]
    
    # 实际应力 σ = (1 - d-) * σ_eff (负值)
    sigma = (1 - d_minus) * sigma_eff
    
    # 创建DataFrame，用绝对值表示（用于绘图）
    df = pd.DataFrame({
        'strain_abs': np.abs(eps),  # 应变绝对值
        'stress_abs': np.abs(sigma),  # 应力绝对值
        'strain': eps,  # 原始应变（负值）
        'stress': sigma,  # 原始应力（负值）
        'damage': d_minus,  # 损伤变量
        'r_minus': r_minus  # 损伤阈值
    })
    
    # 保存到Excel文件
    df.to_excel(f'd:/column/single/{name}.xlsx', index=False)
    print(f"已生成压缩数据文件: d:/column/single/{name}.xlsx")

# 材料参数设置
# 高性能混凝土压缩参数
E0_hp = 38000  # 弹性模量 (MPa)
f0_minus_hp = 45.0  # 压缩强度 (MPa)
A_minus_hp = 0.9  # 压缩损伤参数A-
B_minus_hp = 0.15  # 压缩损伤参数B-
alpha_hp = 0.12  # 损伤能释放率系数

# 普通混凝土压缩参数
E0_nc = 31000  # 弹性模量 (MPa)
f0_minus_nc = 35.0  # 压缩强度 (MPa)
A_minus_nc = 1.0  # 压缩损伤参数A-
B_minus_nc = 0.12  # 压缩损伤参数B-
alpha_nc = 0.12  # 损伤能释放率系数

# 生成高性能混凝土压缩数据
gen_compression(E0_hp, f0_minus_hp, A_minus_hp, B_minus_hp, alpha_hp, 'uniaxial_compression_high_performance')

# 生成普通混凝土压缩数据
gen_compression(E0_nc, f0_minus_nc, A_minus_nc, B_minus_nc, alpha_nc, 'uniaxial_compression_normal_concrete')

print("压缩数据生成完成！")
print("文件路径:")
print("- d:/column/single/uniaxial_compression_high_performance.xlsx")
print("- d:/column/single/uniaxial_compression_normal_concrete.xlsx")