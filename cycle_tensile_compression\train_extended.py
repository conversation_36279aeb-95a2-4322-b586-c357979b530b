"""
训练脚本 - 扩展版
支持拉压循环的训练，包含受压损伤参数
"""

import torch
import torch.nn as nn
from torch.optim import Adam
from torch.optim.lr_scheduler import ReduceLROnPlateau
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os
import json
from pathlib import Path

from pinn_model_v2_extended import DamagePINNV2Extended, PhysicsCalculatorV2Extended, LossCalculatorExtended
from data_processor_extended import DataProcessorExtended
# 需要创建font_config文件，这里暂时注释
import font_config  # 确保中文字体显示正常

class TrainerExtended:
    """
    PINN训练器 - 扩展版
    支持拉压循环的训练
    """
    
    def __init__(self, config=None):
        # 默认配置
        self.config = {
            'E0': 30000.0,           # 初始弹性模量
            'f_t': 3.0,              # 单轴抗拉强度
            'f_c': 30.0,             # 单轴抗压强度
            'num_epochs': 3000,      # 训练轮数（增加以适应更复杂的模型）
            'learning_rate': 0.001,  # 学习率
            'hidden_size': 64,       # 隐藏层大小
            'num_layers': 2,         # GRU层数
            'save_interval': 100,    # 保存间隔
            'print_interval': 50,    # 打印间隔
        }
        if config:
            self.config.update(config)
        
        # 创建带时间戳的结果文件夹
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.results_dir = f'results/training_{self.timestamp}'
        # 注意：results_dir可能会在main.py中被重写为session目录下的training子目录
        
        # 初始化组件
        self.model = None
        self.physics_calc = None
        self.loss_calc = None
        self.optimizer = None
        self.scheduler = None
        self.training_data = None
        
        # 可训练物理参数
        self.A_plus = None
        self.B_plus = None
        self.A_minus = None
        self.B_minus = None
        self.xi_plus = None
        self.xi_minus = None
        
        # 训练历史
        self.loss_history = []
        self.param_history = []
        
    def initialize_model(self):
        """
        初始化模型和组件
        """
        # 初始化PINN模型
        self.model = DamagePINNV2Extended(
            input_size=1,
            hidden_size=self.config['hidden_size'],
            num_layers=self.config['num_layers'],
            output_size=3
        )
        
        # 初始化物理计算器
        self.physics_calc = PhysicsCalculatorV2Extended(
            E0=self.config['E0'],
            f_t=self.config['f_t'],
            f_c=self.config['f_c']
        )
        
        # 初始化损失计算器（修正权重以平衡各项损失）
        self.loss_calc = LossCalculatorExtended(
            lambda_data=1.0,
            lambda_stress=0.1,  # 大幅降低应力损失权重
            lambda_damage_plus=0.5,
            lambda_damage_minus=0.5,
            lambda_plastic=1.0
        )
        
        # 初始化可训练物理参数
        # 受拉参数
        self.A_plus = torch.nn.Parameter(torch.tensor(0.5, requires_grad=True))
        self.B_plus = torch.nn.Parameter(torch.tensor(1.0, requires_grad=True))
        self.xi_plus = torch.nn.Parameter(torch.tensor(0.05, requires_grad=True))
        
        # 受压参数（根据文献给定初始值）
        self.A_minus = torch.nn.Parameter(torch.tensor(1.5, requires_grad=True))
        self.B_minus = torch.nn.Parameter(torch.tensor(0.5, requires_grad=True))
        self.xi_minus = torch.nn.Parameter(torch.tensor(0.02, requires_grad=True))
        
        # 初始化优化器
        self.optimizer = Adam(
            list(self.model.parameters()) + 
            [self.A_plus, self.B_plus, self.xi_plus, self.A_minus, self.B_minus, self.xi_minus],
            lr=self.config['learning_rate']
        )
        
        # 初始化学习率调度器
        self.scheduler = ReduceLROnPlateau(
            self.optimizer, 
            mode='min', 
            factor=0.5, 
            patience=200
        )
        
        print("模型初始化完成")
        print(f"  模型参数数量: {sum(p.numel() for p in self.model.parameters())}")
        print(f"  可训练物理参数:")
        print(f"    受拉: A+={self.A_plus.item():.3f}, B+={self.B_plus.item():.3f}, xi+={self.xi_plus.item():.3f}")
        print(f"    受压: A-={self.A_minus.item():.3f}, B-={self.B_minus.item():.3f}, xi-={self.xi_minus.item():.3f}")
    
    def load_data(self, excel_path="cyclic_data.xlsx"):
        """
        加载训练数据
        """
        processor = DataProcessorExtended(excel_path)
        
        if not processor.load_experimental_data():
            raise ValueError("数据加载失败")
        
        if not processor.validate_data_quality():
            raise ValueError("数据质量验证失败")
        
        # 准备训练数据
        self.training_data = processor.prepare_training_data()
        
        # 绘制实验数据到指定文件夹
        processor.plot_experimental_data(save_path=f"{self.results_dir}/experimental_data.png")
        
        # 获取材料属性并更新配置
        properties = processor.get_material_properties()
        self.config['E0'] = properties['E0_estimated']
        self.config['f_t'] = properties['f_t_estimated']
        self.config['f_c'] = properties['f_c_estimated']
        
        # 更新物理计算器的参数
        if self.physics_calc:
            self.physics_calc.E0 = self.config['E0']
            self.physics_calc.f_t = self.config['f_t']
            self.physics_calc.f_c = self.config['f_c']
        
        print(f"数据加载完成，序列长度: {self.training_data['sequence_length']}")
        
        # 分析加载循环
        cycles = processor.analyze_loading_cycles()
        
    def train_epoch(self):
        """
        训练一个epoch
        """
        self.model.train()
        
        # 获取训练数据
        strain_increment_input = self.training_data['strain_increment_input']
        strain_total_exp = self.training_data['strain_total_exp']
        stress_exp = self.training_data['stress_exp']
        strain_increment = self.training_data['strain_increment']
        
        # --- 步骤 A: 神经网络前向传播 ---
        sigma_hat_seq, d_hat_seq, xi_hat_seq = self.model(strain_increment_input)
        sigma_hat_seq = sigma_hat_seq.squeeze()
        d_hat_seq = d_hat_seq.squeeze()
        xi_hat_seq = xi_hat_seq.squeeze()
        
        # --- 步骤 B: 物理约束目标的增量式计算 ---
        d_plus_phy_seq, d_minus_phy_seq, ep_phy_seq, stress_phy_seq = \
            self.physics_calc.calculate_physics_constraints(
                strain_increment, 
                self.A_plus, self.B_plus, self.A_minus, self.B_minus,
                self.xi_plus, self.xi_minus
            )
        
        # --- 步骤 C: 计算总损失 ---
        total_loss, loss_dict = self.loss_calc.calculate_total_loss(
            sigma_hat_seq, d_hat_seq, xi_hat_seq,
            stress_exp, strain_total_exp, strain_increment,
            d_plus_phy_seq, d_minus_phy_seq, ep_phy_seq, 
            stress_phy_seq, self.config['E0']
        )
        
        # --- 步骤 D: 反向传播与优化 ---
        self.optimizer.zero_grad()
        total_loss.backward()
        
        # 梯度裁剪，防止梯度爆炸
        torch.nn.utils.clip_grad_norm_(
            list(self.model.parameters()) + 
            [self.A_plus, self.B_plus, self.xi_plus, self.A_minus, self.B_minus, self.xi_minus],
            max_norm=1.0
        )
        
        self.optimizer.step()
        
        # 约束物理参数在合理范围内
        with torch.no_grad():
            # 受拉参数约束
            self.A_plus.data.clamp_(0.01, 0.99)
            self.B_plus.data.clamp_(0.1, 10.0)
            self.xi_plus.data.clamp_(0.005, 0.2)
            
            # 受压参数约束
            self.A_minus.data.clamp_(0.1, 5.0)
            self.B_minus.data.clamp_(0.1, 10.0)
            self.xi_minus.data.clamp_(0.005, 0.2)
        
        return loss_dict
    
    def train(self):
        """
        完整训练过程
        """
        print(f"\n开始训练，共{self.config['num_epochs']}轮")
        print("=" * 60)
        
        for epoch in range(self.config['num_epochs']):
            # 训练一个epoch
            loss_dict = self.train_epoch()
            
            # 记录历史
            self.loss_history.append(loss_dict)
            self.param_history.append({
                'A_plus': self.A_plus.item(),
                'B_plus': self.B_plus.item(),
                'xi_plus': self.xi_plus.item(),
                'A_minus': self.A_minus.item(),
                'B_minus': self.B_minus.item(),
                'xi_minus': self.xi_minus.item()
            })
            
            # 更新学习率
            old_lr = self.optimizer.param_groups[0]['lr']
            self.scheduler.step(loss_dict['total_loss'])
            new_lr = self.optimizer.param_groups[0]['lr']
            
            # 手动输出学习率变化信息（替代verbose参数）
            if old_lr != new_lr:
                print(f"     学习率降低: {old_lr:.2e} -> {new_lr:.2e}")
            
            # 打印训练进度
            if (epoch + 1) % self.config['print_interval'] == 0:
                print(f"Epoch {epoch+1:4d}/{self.config['num_epochs']} | "
                      f"Total Loss: {loss_dict['total_loss']:.6f} | "
                      f"Data: {loss_dict['loss_data']:.6f} | "
                      f"Stress: {loss_dict['loss_stress']:.6f}")
                print(f"     Damage: Plus={loss_dict['loss_damage_plus']:.6f}, "
                      f"Minus={loss_dict['loss_damage_minus']:.6f} | "
                      f"Plastic: {loss_dict['loss_plastic']:.6f}")
                print(f"     受拉参数: A+={self.A_plus.item():.4f}, "
                      f"B+={self.B_plus.item():.4f}, xi+={self.xi_plus.item():.4f}")
                print(f"     受压参数: A-={self.A_minus.item():.4f}, "
                      f"B-={self.B_minus.item():.4f}, xi-={self.xi_minus.item():.4f}")
            
            # 保存检查点
            if (epoch + 1) % self.config['save_interval'] == 0:
                self.save_checkpoint(epoch + 1)
        
        print("=" * 60)
        print("训练完成!")
        print(f"最终识别的参数:")
        print(f"  受拉: A+ = {self.A_plus.item():.4f}, B+ = {self.B_plus.item():.4f}, xi+ = {self.xi_plus.item():.4f}")
        print(f"  受压: A- = {self.A_minus.item():.4f}, B- = {self.B_minus.item():.4f}, xi- = {self.xi_minus.item():.4f}")
        
        # 保存最终模型
        self.save_final_model()
        
        # 绘制训练历史
        self.plot_training_history()
        
    def save_checkpoint(self, epoch):
        """
        保存训练检查点
        """
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'physics_parameters': {
                'A_plus': self.A_plus.item(),
                'B_plus': self.B_plus.item(),
                'xi_plus': self.xi_plus.item(),
                'A_minus': self.A_minus.item(),
                'B_minus': self.B_minus.item(),
                'xi_minus': self.xi_minus.item()
            },
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'config': self.config,
            'loss_history': self.loss_history,
            'param_history': self.param_history
        }
        
        torch.save(checkpoint, f'{self.results_dir}/checkpoint_epoch_{epoch}.pth')
    
    def save_final_model(self):
        """
        保存最终模型
        """
        model_path = f'{self.results_dir}/pinn_model_{self.timestamp}.pth'
        
        # 保存完整模型信息
        model_info = {
            'model_state_dict': self.model.state_dict(),
            'physics_parameters': {
                'A_plus': self.A_plus.item(),
                'B_plus': self.B_plus.item(),
                'xi_plus': self.xi_plus.item(),
                'A_minus': self.A_minus.item(),
                'B_minus': self.B_minus.item(),
                'xi_minus': self.xi_minus.item()
            },
            'material_constants': {
                'E0': self.config['E0'],
                'f_t': self.config['f_t'],
                'f_c': self.config['f_c']
            },
            'config': self.config,
            'loss_history': self.loss_history,
            'param_history': self.param_history,
            'timestamp': self.timestamp,
            'results_dir': self.results_dir
        }
        
        torch.save(model_info, model_path)
        
        # 保存参数到JSON文件
        params_path = f'{self.results_dir}/identified_parameters_{self.timestamp}.json'
        with open(params_path, 'w', encoding='utf-8') as f:
            json.dump({
                'physics_parameters': model_info['physics_parameters'],
                'material_constants': model_info['material_constants'],
                'training_config': self.config,
                'final_loss': self.loss_history[-1] if self.loss_history else None,
                'timestamp': self.timestamp,
                'results_dir': self.results_dir
            }, f, indent=2, ensure_ascii=False)
        
        print(f"最终模型已保存至: {model_path}")
        print(f"识别参数已保存至: {params_path}")
    
    def plot_training_history(self):
        """
        绘制训练历史
        """
        if not self.loss_history:
            return
        
        epochs = range(1, len(self.loss_history) + 1)
        
        # 绘制损失历史
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 总损失
        total_losses = [h['total_loss'] for h in self.loss_history]
        ax1.plot(epochs, total_losses, 'b-', linewidth=2)
        ax1.set_title('总损失')
        ax1.set_xlabel('训练轮数')
        ax1.set_ylabel('损失值')
        ax1.grid(True, alpha=0.3)
        ax1.set_yscale('log')
        
        # 各项损失
        data_losses = [h['loss_data'] for h in self.loss_history]
        stress_losses = [h['loss_stress'] for h in self.loss_history]
        damage_plus_losses = [h['loss_damage_plus'] for h in self.loss_history]
        damage_minus_losses = [h['loss_damage_minus'] for h in self.loss_history]
        plastic_losses = [h['loss_plastic'] for h in self.loss_history]
        
        ax2.plot(epochs, data_losses, 'r-', label='数据拟合', linewidth=2)
        ax2.plot(epochs, stress_losses, 'g-', label='本构自洽', linewidth=2)
        ax2.plot(epochs, damage_plus_losses, 'b-', label='拉伸损伤', linewidth=2)
        ax2.plot(epochs, damage_minus_losses, 'c-', label='压缩损伤', linewidth=2)
        ax2.plot(epochs, plastic_losses, 'm-', label='塑性物理', linewidth=2)
        ax2.set_title('各项损失')
        ax2.set_xlabel('训练轮数')
        ax2.set_ylabel('损失值')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_yscale('log')
        
        # 损伤参数演化
        A_plus_history = [h['A_plus'] for h in self.param_history]
        B_plus_history = [h['B_plus'] for h in self.param_history]
        A_minus_history = [h['A_minus'] for h in self.param_history]
        B_minus_history = [h['B_minus'] for h in self.param_history]
        
        ax3.plot(epochs, A_plus_history, 'r-', label='A+', linewidth=2)
        ax3.plot(epochs, B_plus_history, 'g-', label='B+', linewidth=2)
        ax3.plot(epochs, A_minus_history, 'r--', label='A-', linewidth=1.5)
        ax3.plot(epochs, B_minus_history, 'g--', label='B-', linewidth=1.5)
        ax3.set_title('损伤参数演化')
        ax3.set_xlabel('训练轮数')
        ax3.set_ylabel('参数值')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 塑性参数演化
        xi_plus_history = [h['xi_plus'] for h in self.param_history]
        xi_minus_history = [h['xi_minus'] for h in self.param_history]
        
        ax4.plot(epochs, xi_plus_history, 'b-', linewidth=2, label='xi_plus')
        ax4.plot(epochs, xi_minus_history, 'm--', linewidth=1.5, label='xi_minus')
        ax4.set_title('塑性参数演化')
        ax4.set_xlabel('训练轮数')
        ax4.set_ylabel('xi值')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        save_path = f'{self.results_dir}/training_history.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"训练历史图像已保存至: {save_path}")


def main():
    """
    主训练函数
    """
    print("初始化混凝土拉压循环损伤参数识别PINN训练器")
    print("=" * 60)
    
    # 创建训练器
    trainer = TrainerExtended()
    
    # 加载数据
    # 需要准备包含拉压循环的数据文件
    trainer.load_data("cyclic_data.xlsx")
    
    # 初始化模型
    trainer.initialize_model()
    
    # 开始训练
    trainer.train()
    
    print("训练程序完成!")


if __name__ == "__main__":
    main() 