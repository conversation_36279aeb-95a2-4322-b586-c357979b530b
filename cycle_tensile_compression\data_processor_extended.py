"""
数据处理器 - 扩展版
支持拉压循环数据的处理，识别加卸载过程
"""

import pandas as pd
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import font_config  # 确保中文字体显示正常

class DataProcessorExtended:
    """
    数据处理器 - 扩展版
    从Excel文件读取strain和stress数据，转换为增量式输入格式
    支持识别加卸载过程和拉压状态
    """
    
    def __init__(self, excel_path="cyclic_data.xlsx"):
        self.excel_path = excel_path
        self.strain_exp = None
        self.stress_exp = None
        self.strain_increment = None
        self.loading_states = None  # 加载状态序列
        self.stress_states = None   # 拉压状态序列
        
    def load_experimental_data(self):
        """
        从Excel文件加载实验数据
        要求Excel文件包含strain和stress两列
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(self.excel_path)
            
            # 检查是否包含必要的列
            if 'strain' not in df.columns or 'stress' not in df.columns:
                raise ValueError("Excel文件必须包含'strain'和'stress'两列")
            
            # 提取数据并转换为numpy数组
            self.strain_exp = df['strain'].values.astype(np.float32)
            self.stress_exp = df['stress'].values.astype(np.float32)
            
            # 计算应变增量序列 - 核心数据转换
            self.strain_increment = np.diff(self.strain_exp, prepend=0.0)
            
            # 识别加卸载状态和拉压状态
            self._identify_loading_states()
            
            print(f"成功加载实验数据:")
            print(f"  数据点数: {len(self.strain_exp)}")
            print(f"  应变范围: {self.strain_exp.min():.6f} ~ {self.strain_exp.max():.6f}")
            print(f"  应力范围: {self.stress_exp.min():.6f} ~ {self.stress_exp.max():.6f}")
            print(f"  应变增量范围: {self.strain_increment.min():.6f} ~ {self.strain_increment.max():.6f}")
            print(f"  拉伸数据点数: {np.sum(self.stress_states > 0)}")
            print(f"  压缩数据点数: {np.sum(self.stress_states < 0)}")
            
            return True
            
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def _identify_loading_states(self):
        """
        识别加卸载状态和拉压状态
        """
        n_points = len(self.strain_exp)
        self.loading_states = np.zeros(n_points, dtype=np.int32)  # 1: 加载, -1: 卸载, 0: 初始
        self.stress_states = np.zeros(n_points, dtype=np.int32)   # 1: 拉伸, -1: 压缩, 0: 零应力
        
        # 第一个点设为初始状态
        self.loading_states[0] = 0
        self.stress_states[0] = np.sign(self.stress_exp[0])
        
        for i in range(1, n_points):
            # 判断拉压状态（基于应力符号）
            if self.stress_exp[i] > 1e-6:
                self.stress_states[i] = 1   # 拉伸
            elif self.stress_exp[i] < -1e-6:
                self.stress_states[i] = -1  # 压缩
            else:
                self.stress_states[i] = 0   # 零应力
            
            # 判断加卸载状态
            # 方法1：基于应力变化方向
            stress_change = self.stress_exp[i] - self.stress_exp[i-1]
            strain_change = self.strain_exp[i] - self.strain_exp[i-1]
            
            if abs(strain_change) < 1e-9:  # 应变没有变化
                self.loading_states[i] = self.loading_states[i-1]
            else:
                # 判断是否同向（应力和应变变化同号表示加载）
                if stress_change * strain_change > 0:
                    self.loading_states[i] = 1   # 加载
                else:
                    self.loading_states[i] = -1  # 卸载
            
            # 方法2：对于循环加载，也可以通过应变增量和当前应变的符号判断
            # 如果应变增量和当前有效应变同号，则为加载
            # 这需要估计塑性应变，在初步处理时可能不够准确
    
    def prepare_training_data(self):
        """
        准备训练数据
        按照框架要求，整条曲线都作为训练集，不进行拆分
        """
        if self.strain_exp is None:
            raise ValueError("请先调用load_experimental_data()加载数据")
        
        # 转换为PyTorch张量
        strain_total_exp = torch.tensor(self.strain_exp, dtype=torch.float32)
        stress_exp = torch.tensor(self.stress_exp, dtype=torch.float32)
        strain_increment = torch.tensor(self.strain_increment, dtype=torch.float32)
        loading_states = torch.tensor(self.loading_states, dtype=torch.float32)
        stress_states = torch.tensor(self.stress_states, dtype=torch.float32)
        
        # 为神经网络准备输入格式 [batch_size=1, seq_len, input_size=1]
        strain_increment_input = strain_increment.unsqueeze(0).unsqueeze(-1)
        
        training_data = {
            'strain_total_exp': strain_total_exp,
            'stress_exp': stress_exp,
            'strain_increment': strain_increment,
            'strain_increment_input': strain_increment_input,
            'loading_states': loading_states,
            'stress_states': stress_states,
            'sequence_length': len(strain_total_exp)
        }
        
        return training_data
    
    def plot_experimental_data(self, save_path="results/experimental_data.png"):
        """
        绘制实验数据，包括加卸载状态标识
        """
        if self.strain_exp is None:
            raise ValueError("请先加载实验数据")
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # 上图：应力-应变曲线
        ax1.plot(self.strain_exp * 1000, self.stress_exp, 'b-', linewidth=2, label='实验数据')
        ax1.axhline(y=0, color='k', linestyle='--', alpha=0.3)
        ax1.axvline(x=0, color='k', linestyle='--', alpha=0.3)
        ax1.set_xlabel('应变 (‰)')
        ax1.set_ylabel('应力 (MPa)')
        ax1.set_title('实验应力-应变曲线')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 标记加卸载段
        loading_indices = np.where(self.loading_states == 1)[0]
        unloading_indices = np.where(self.loading_states == -1)[0]
        
        if len(loading_indices) > 0:
            ax1.scatter(self.strain_exp[loading_indices] * 1000, 
                       self.stress_exp[loading_indices], 
                       c='red', s=20, alpha=0.5, label='加载段')
        if len(unloading_indices) > 0:
            ax1.scatter(self.strain_exp[unloading_indices] * 1000, 
                       self.stress_exp[unloading_indices], 
                       c='green', s=20, alpha=0.5, label='卸载段')
        
        # 下图：应力和应变时程
        time_points = np.arange(len(self.strain_exp))
        ax2.plot(time_points, self.strain_exp * 1000, 'b-', label='应变', linewidth=2)
        ax2.plot(time_points, self.stress_exp / 10, 'r-', label='应力/10', linewidth=2)
        ax2.axhline(y=0, color='k', linestyle='--', alpha=0.3)
        ax2.set_xlabel('数据点')
        ax2.set_ylabel('应变(‰) / 应力(MPa)/10')
        ax2.set_title('应变和应力时程曲线')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        plt.tight_layout()
        
        # 确保保存目录存在
        Path(save_path).parent.mkdir(parents=True, exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"实验数据图像已保存至: {save_path}")
    
    def validate_data_quality(self):
        """
        验证数据质量
        """
        if self.strain_exp is None:
            return False
        
        # 检查数据完整性
        if len(self.strain_exp) != len(self.stress_exp):
            print("错误: 应变和应力数据长度不匹配")
            return False
        
        # 检查是否有缺失值
        if np.any(np.isnan(self.strain_exp)) or np.any(np.isnan(self.stress_exp)):
            print("错误: 数据中包含缺失值")
            return False
        
        # 检查是否包含拉压数据
        has_tension = np.any(self.stress_exp > 1e-6)
        has_compression = np.any(self.stress_exp < -1e-6)
        
        if has_tension and has_compression:
            print("数据包含拉压循环")
        elif has_tension:
            print("警告: 数据仅包含拉伸部分")
        elif has_compression:
            print("警告: 数据仅包含压缩部分")
        else:
            print("错误: 数据既无拉伸也无压缩")
            return False
        
        print("数据质量验证通过")
        return True
    
    def get_material_properties(self):
        """
        从实验数据估算材料属性
        用于初始化物理参数
        """
        if self.strain_exp is None:
            raise ValueError("请先加载实验数据")
        
        # 修正：弹性模量估算应该基于最小应变范围的线性段
        # 找到接近原点的小应变数据点进行拟合
        
        # 定义小应变范围（通常在0.0001以内为弹性范围）
        small_strain_threshold = 0.0001  # 0.01%应变
        
        # 找到小应变范围内的数据点
        small_strain_mask = np.abs(self.strain_exp) <= small_strain_threshold
        
        E0_tension = 30000.0  # 默认值
        E0_compression = 30000.0  # 默认值
        
        if np.sum(small_strain_mask) > 5:  # 需要足够的数据点
            small_strain = self.strain_exp[small_strain_mask]
            small_stress = self.stress_exp[small_strain_mask]
            
            # 分别处理拉伸和压缩数据
            tension_mask = small_stress > 0
            compression_mask = small_stress < 0
            
            if np.sum(tension_mask) > 2:
                strain_tension = small_strain[tension_mask]
                stress_tension = small_stress[tension_mask]
                if len(strain_tension) > 1 and np.std(strain_tension) > 1e-8:
                    E0_tension = np.polyfit(strain_tension, stress_tension, 1)[0]
                    # 确保弹性模量在合理范围内
                    E0_tension = max(min(E0_tension, 50000.0), 20000.0)
            
            if np.sum(compression_mask) > 2:
                strain_compression = small_strain[compression_mask]
                stress_compression = small_stress[compression_mask]
                if len(strain_compression) > 1 and np.std(strain_compression) > 1e-8:
                    E0_compression = abs(np.polyfit(strain_compression, stress_compression, 1)[0])
                    # 确保弹性模量在合理范围内
                    E0_compression = max(min(E0_compression, 50000.0), 20000.0)
        
        # 如果小应变范围估算失败，使用理论值
        if E0_tension < 10000 or E0_tension > 60000:
            print(f"  警告: 拉伸弹性模量估算异常 ({E0_tension:.2f}), 使用默认值 30000 MPa")
            E0_tension = 30000.0
            
        if E0_compression < 10000 or E0_compression > 60000:
            print(f"  警告: 压缩弹性模量估算异常 ({E0_compression:.2f}), 使用默认值 30000 MPa")
            E0_compression = 30000.0
        
        # 取平均值作为统一的弹性模量
        E0_estimated = (E0_tension + E0_compression) / 2
        
        # 抗拉强度（最大拉应力）
        f_t_estimated = max(self.stress_exp.max(), 3.0)  # 至少为3.0 MPa
        
        # 抗压强度（最大压应力的绝对值）
        f_c_estimated = max(abs(self.stress_exp.min()), 30.0)  # 至少为30.0 MPa
        
        properties = {
            'E0_estimated': float(E0_estimated),
            'E0_tension': float(E0_tension),
            'E0_compression': float(E0_compression),
            'f_t_estimated': float(f_t_estimated),
            'f_c_estimated': float(f_c_estimated),
            'max_strain': float(self.strain_exp.max()),
            'min_strain': float(self.strain_exp.min()),
            'max_stress': float(self.stress_exp.max()),
            'min_stress': float(self.stress_exp.min())
        }
        
        print(f"估算的材料属性:")
        print(f"  初始弹性模量 E0: {E0_estimated:.2f} MPa (拉: {E0_tension:.2f}, 压: {E0_compression:.2f})")
        print(f"  抗拉强度 f_t: {f_t_estimated:.2f} MPa")
        print(f"  抗压强度 f_c: {f_c_estimated:.2f} MPa")
        
        return properties
    
    def analyze_loading_cycles(self):
        """
        分析加载循环
        """
        if self.loading_states is None:
            raise ValueError("请先加载实验数据")
        
        # 找出加载/卸载转换点
        transitions = np.where(np.diff(self.loading_states) != 0)[0] + 1
        
        cycles = []
        start_idx = 0
        
        for i, trans_idx in enumerate(transitions):
            segment_type = 'loading' if self.loading_states[start_idx] == 1 else 'unloading'
            cycles.append({
                'segment_number': i + 1,
                'type': segment_type,
                'start_idx': start_idx,
                'end_idx': trans_idx - 1,
                'start_strain': self.strain_exp[start_idx],
                'end_strain': self.strain_exp[trans_idx - 1],
                'start_stress': self.stress_exp[start_idx],
                'end_stress': self.stress_exp[trans_idx - 1]
            })
            start_idx = trans_idx
        
        # 最后一段
        segment_type = 'loading' if self.loading_states[start_idx] == 1 else 'unloading'
        cycles.append({
            'segment_number': len(cycles) + 1,
            'type': segment_type,
            'start_idx': start_idx,
            'end_idx': len(self.strain_exp) - 1,
            'start_strain': self.strain_exp[start_idx],
            'end_strain': self.strain_exp[-1],
            'start_stress': self.stress_exp[start_idx],
            'end_stress': self.stress_exp[-1]
        })
        
        print(f"识别出 {len(cycles)} 个加载/卸载段:")
        for cycle in cycles[:5]:  # 只打印前5个
            print(f"  段 {cycle['segment_number']}: {cycle['type']}, "
                  f"应变: {cycle['start_strain']:.6f} -> {cycle['end_strain']:.6f}, "
                  f"应力: {cycle['start_stress']:.2f} -> {cycle['end_stress']:.2f}")
        
        if len(cycles) > 5:
            print(f"  ... (共 {len(cycles)} 段)")
        
        return cycles


def test_data_processor():
    """
    测试数据处理器
    """
    print("测试扩展版数据处理器...")
    
    # 假设有一个包含循环加载数据的Excel文件
    processor = DataProcessorExtended("cyclic_data.xlsx")
    
    # 加载数据
    if processor.load_experimental_data():
        # 验证数据质量
        if processor.validate_data_quality():
            # 准备训练数据
            training_data = processor.prepare_training_data()
            print(f"训练数据准备完成，序列长度: {training_data['sequence_length']}")
            
            # 绘制实验数据
            processor.plot_experimental_data()
            
            # 获取材料属性
            properties = processor.get_material_properties()
            
            # 分析加载循环
            cycles = processor.analyze_loading_cycles()
            
            return True
    
    return False


if __name__ == "__main__":
    test_data_processor() 