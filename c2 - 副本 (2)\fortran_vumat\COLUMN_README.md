# 混凝土柱VUMAT验证项目

## 项目概述

本项目使用Abaqus VUMAT用户子程序验证基于PINN(Physics-Informed Neural Network)识别的混凝土弹塑性损伤本构模型。模型基于吴建营等人的双向损伤演化理论，在有限元环境中验证材料的滞回行为。

## 模型几何

### 结构尺寸
- **截面形状**: 正方形截面
- **截面尺寸**: 200mm × 200mm  
- **柱体长度**: 800mm
- **网格划分**: 3×3×4 = 16个C3D8R单元

### 坐标系统
- **X轴**: 横向 (-100mm 到 +100mm)
- **Y轴**: 横向 (-100mm 到 +100mm)  
- **Z轴**: 纵向 (0mm 到 800mm，加载方向)

### 网格结构
- **底面**: Z=0mm，9个节点 (3×3网格)
- **中间层1**: Z=200mm，9个节点
- **中间层2**: Z=400mm，9个节点  
- **中间层3**: Z=600mm，9个节点
- **顶面**: Z=800mm，9个节点 (3×3网格)
- **总节点数**: 45个
- **总单元数**: 16个C3D8R单元

## 本构模型

### 双向损伤演化
基于吴建营理论的弹塑性损伤模型：

**受拉损伤**:
```
d+ = 1 - (f_t/r+) * [(1-A+) + A+ * exp(B+(1-r+/f_t))]
```

**受压损伤**:
```
d- = 1 - (f_c/r-) * [(1-A-) + A- * exp(B-(1-r-/f_c))]
```

### 材料参数
- **弹性模量**: E0 = 10000 MPa
- **受拉强度**: f_t = 3.67 MPa
- **受压强度**: f_c = 10.0 MPa
- **拉损伤参数**: A+ = 0.845, B+ = 1.814
- **压损伤参数**: A- = 2.000, B- = 1.329
- **塑性参数**: ξ+ = 0.500, ξ- = 0.500

## 边界条件和加载

### 边界条件
- **底面固定**: 所有底面节点的位移约束 (U1=U2=U3=0)
- **顶面加载**: 仅在Z方向施加循环位移

### 循环加载制度
```
时间    位移倍数
0.0  →  0.0
0.1  →  0.16
0.2  →  0.0  
0.3  →  -0.24
0.4  →  0.0
0.5  →  0.32
0.6  →  0.0
0.7  →  -0.48
0.8  →  0.0
0.9  →  0.48
1.0  →  0.0
```

## 状态变量定义

VUMAT使用5个状态变量：
1. **SDV1**: 累积塑性应变
2. **SDV2**: 受拉损伤 d+
3. **SDV3**: 受压损伤 d-
4. **SDV4**: 最大拉伸损伤驱动力 r_max+
5. **SDV5**: 最大压缩损伤驱动力 r_max-

## 文件结构

### 核心文件
- `vumat_concrete.f` - VUMAT子程序实现
- `concrete_column.inp` - Abaqus输入文件
- `material_parameters.inc` - 材料参数定义

### 分析工具
- `cleanup_and_rerun.bat` - 清理重运行脚本
- `extract_basic_data.py` - 结果提取工具
- `simple_comparison.py` - 结果比较工具

### 输出结果
- `column_analysis_YYYYMMDD_HHMMSS/` - 分析结果文件夹
- `basic_results.txt` - 提取的应力应变数据

## 运行指南

### 快速运行
```bash
# 清理并重新运行完整分析
cleanup_and_rerun.bat
```

### 手动步骤
```bash
# 1. 运行Abaqus分析
abaqus job=column_analysis user=vumat_concrete.f input=concrete_column.inp

# 2. 提取结果 (在Abaqus Python环境)
abaqus python extract_basic_data.py column_analysis.odb

# 3. 比较结果 (在标准Python环境)
python simple_comparison.py
```

## 验证目标

1. **数值稳定性**: 确保VUMAT在循环加载下收敛
2. **物理合理性**: 验证损伤演化和滞回行为
3. **参数一致性**: 对比PINN预测与VUMAT结果
4. **本构关系**: 验证双向损伤演化模型

## 预期结果

- 应力应变滞回曲线
- 损伤变量演化过程
- 塑性应变累积
- 能量耗散特性

## 技术特点

- ✅ 显式动力学求解器 (Abaqus/Explicit)
- ✅ 用户自定义材料模型 (VUMAT)
- ✅ 双向损伤演化机制
- ✅ PINN参数集成
- ✅ 循环加载验证

---
**项目状态**: 已完成并验证  
**几何更新**: 正方形截面 200mm×200mm  
**最后更新**: 2025年7月3日 