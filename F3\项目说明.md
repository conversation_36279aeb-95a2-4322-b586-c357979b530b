# 混凝土损伤本构AI建模项目说明

---

## 一、项目背景与目标
本项目旨在基于循环加卸载试验数据，利用物理信息神经网络（PINN）方法，建立混凝土单轴加载下的弹塑性损伤本构模型，实现损伤变量与应力应变关系的高精度拟合与预测。

---

## 二、实验数据与预处理
- **原始数据**：循环加卸载试验的力-位移（或应力-应变）曲线。
- **数据分段**：每循环分为加载段（应变增加）和卸载段（应变减少），标记起始点和峰值点。
- **目标输出**：损伤变量 $d^\pm$ 随循环次数或应变累积的变化曲线。

---

## 三、损伤本构模型理论基础
### 1. 张量分解与投影算子
- $X = X^+ + X^-$
- $X^\pm = P_X^\pm : X$
- $P_X^\pm = \sum H(X^{(n)}) \mathbf{p}_X^{(n)} \otimes \mathbf{p}_X^{(n)}$

### 2. 本构关系与损伤演化
- 总应力：
  $$
  \sigma_{\text{tot}} = (1-d^+)\sigma^+ + (1-d^-)\sigma^- + \sigma_{\text{vis}}
  $$
- 有效应力：
  $$
  \sigma = C_0 : (\varepsilon - \varepsilon^p)
  $$
- 损伤能释放率：
  $$
  Y^+ = \sqrt{E_0 (\sigma^+ : A_0 : \sigma^+)}, \quad Y^- = \alpha I_1 + \sqrt{3 J_2}
  $$
- 损伤变量：
  $$
  d^\pm = 1 - \left( \frac{r_0^\pm}{r^\pm}(1-A^\pm) + A^\pm \right) \exp\left(B^\pm\left(1 - \frac{r^\pm}{r_0^\pm}\right)\right)
  $$

### 3. 损伤阈值更新规则
- **准静态加载**：
  $$
  r^\pm_{\text{new}} = \max\left(r^\pm_{\text{old}}, Y^\pm\right)
  $$
- **动力加载**：
  $$
  \dot{r}^\pm = \mu^\pm \left\langle \frac{Y^\pm}{r^\pm} - 1 \right\rangle^{a^\pm}
  $$

---

## 四、PINN网络结构与物理约束
- **输入**：应变 $\epsilon$ 变化量
- **输出**：应力 $\sigma$ 和损伤变量 $d$
- **网络结构**：
  - 输入层：1 个神经元（应变）
  - 隐藏层：10 个神经元，ReLU 激活
  - 输出层：2 个神经元（应力和损伤量）
- **损失函数**：
  - 本构物理约束：$\sigma = (1-d) E \epsilon$
  - 损伤演化规律：$d = 1 - e^{-k (\epsilon - \epsilon_0)^+}$
  - 数据拟合误差：最小化模型预测应力与试验应力的均方误差

---

## 五、参数标定与优化流程
- **参数定义**：$r_0^\pm, A^\pm, B^\pm, \mu^\pm, a^\pm, \xi^\pm$
- **目标函数**：
  $$
  \text{误差} = \sum_{k=1}^{N} \left( \sigma_k^{\text{model}} - \sigma_k^{\text{test}} \right)^2
  $$
- **优化算法**：遗传算法、粒子群优化（PSO）、Levenberg-Marquardt
- **迭代流程**：
  ```python
  for 参数组合 in 参数空间:
      计算所有循环的 d^± 和 σ_model
      计算误差
      保留误差最小的参数组合
  ```

---

## 六、数值实现与应用示例
1. 初始化 $r^\pm = r_0^\pm$，$d^\pm = 0$
2. 遍历应变增量，计算 $Y^\pm$
3. 判断 $Y^\pm \geq r^\pm$ 是否触发阈值更新
4. 更新 $r^\pm$，计算 $d^\pm$
5. 记录 $d^\pm, r^\pm, \varepsilon_k, \sigma_k$
6. 拟合并调整参数使模型与试验数据匹配

---

## 七、材料参数与工程验证
- 材料参数示例：$f_0^+ = 2.41$ MPa，$f_0^- = 10.0$ MPa，$\xi^\pm = 0.2$，$\beta = 0.03$
- KOYNA大坝动力分析：模型可直接捕捉应变率效应，无需人为调整抗拉强度

---

## 八、项目总结与AI创作建议
- 本项目流程涵盖了数据预处理、理论建模、PINN网络设计、参数优化与工程验证，适合AI驱动的本构建模与材料损伤分析。
- 可扩展为多轴加载、不同材料类型、更多物理约束的AI建模项目。
- 推荐将上述流程模块化，便于后续AI自动化建模与创新。