# 混凝土拉伸损伤曲线PINN模型

## 项目简介

本项目实现了一个基于物理信息神经网络（Physics-Informed Neural Networks, PINN）的混凝土拉伸损伤曲线识别模型。该模型能够：

1. **输入应变序列**，输出对应的应力、损伤变量和塑性应变
2. **内置物理约束**，确保预测结果符合材料力学原理
3. **自动识别材料参数**，包括损伤演化参数 A⁺、B⁺ 和塑性应变系数 ξ
4. **支持单点预测**，输入任意应变值即可获得对应的材料状态

## 核心特性

### 🔬 物理约束集成
- **应力-应变关系**: σ = E₀(1-d)(ε-εᵖ)
- **损伤驱动力**: Y = E₀(ε-εᵖ)
- **损伤阈值演化**: r(t) = max(r_old, Y(t))
- **损伤演化法则**: d = 1 - (r₀/r)[(1-A⁺) + A⁺exp[B⁺(1-r/r₀)]]
- **塑性应变演化**: εᵖ(t) = εᵖ_old + ξ·(ε(t) - ε_old)

### 🧠 神经网络架构
- **LSTM网络**: 捕捉材料状态的历史依赖性
- **状态更新机制**: 模拟真实的损伤演化过程
- **阈值判断逻辑**: 只有当损伤驱动力超过阈值时才更新损伤状态

### 📊 训练特点
- **多重损失函数**: 数据拟合 + 物理约束 + 损伤演化 + 塑性累积
- **参数联合优化**: 神经网络参数与物理参数同时训练
- **早停机制**: 防止过拟合，提高泛化能力

## 安装要求

```bash
pip install -r requirements.txt
```

主要依赖：
- PyTorch >= 1.12.0
- NumPy >= 1.21.0
- Matplotlib >= 3.5.0
- Pandas >= 1.4.0
- Scikit-learn >= 1.1.0
- SciPy >= 1.8.0

## 使用方法

### 1. 快速开始（使用合成数据）

```bash
python main_train.py
```

这将使用合成的混凝土拉伸数据进行训练演示。

### 2. 使用实验数据

准备您的实验数据（CSV或Excel格式），包含应变和应力两列：

```python
# 修改 main_train.py 中的数据文件路径
data_file = "your_experimental_data.csv"
```

然后运行：
```bash
python main_train.py
```

### 3. 自定义数据演示

```bash
python main_train.py --demo
```

### 4. 在代码中使用

```python
from pinn_model import ConcreteTensionPINN
from trainer import PINNTrainer
from data_utils import ConcreteDataProcessor

# 创建模型
model = ConcreteTensionPINN(E0=30000.0, ft=3.0)

# 创建训练器
trainer = PINNTrainer(model)

# 训练模型
trainer.train(train_dataloader, val_dataloader)

# 单点预测
result = model.predict_single_point(strain=0.0005)
print(f"应力: {result['stress']:.2f} MPa")
print(f"损伤: {result['damage']:.4f}")
```

## 文件结构

```
ten2/
├── pinn_model.py          # PINN模型核心实现
├── trainer.py             # 训练器和损失函数
├── data_utils.py          # 数据处理工具
├── main_train.py          # 主训练脚本
├── requirements.txt       # 依赖项
├── README.md             # 说明文档
└── 架构.md               # 详细架构说明
```

## 模型架构详解

### 核心逻辑流程

1. **输入应变增量** → 模型接收当前时刻的应变值
2. **状态更新** → 更新塑性应变、计算损伤驱动力
3. **阈值判断** → 检查是否需要更新损伤变量
4. **损伤更新** → 超过阈值时更新损伤状态
5. **应力计算** → 基于物理约束计算当前应力
6. **状态传递** → 为下一时刻保存状态信息

### 训练过程

```python
for epoch in range(num_epochs):
    for strain_sequence in dataloader:
        # 1. 前向传播
        stress_pred, damage_pred, plastic_pred, states = model(strain_sequence)
        
        # 2. 计算损失
        loss_data = MSE(stress_pred, stress_exp)
        loss_physics = physics_constraints(...)
        loss_damage = damage_evolution_loss(...)
        loss_plastic = plastic_strain_loss(...)
        
        total_loss = weighted_sum(losses)
        
        # 3. 反向传播
        total_loss.backward()
        optimizer.step()
```

## 输出结果

训练完成后，您将获得：

1. **训练历史图**: 损失函数收敛过程和参数演化
2. **预测对比图**: 模型预测 vs 实验数据对比
3. **材料参数**: 识别出的 A⁺, B⁺, ξ 值
4. **性能指标**: MSE, MAE, R² 等统计指标
5. **训练好的模型**: 可用于后续预测的模型文件

## 参数说明

### 物理参数
- `E0`: 初始弹性模量 (MPa) - 通常固定
- `ft`: 抗拉强度 (MPa) - 通常固定
- `A⁺`: 损伤演化形状参数 - 需要识别
- `B⁺`: 损伤演化速率参数 - 需要识别
- `ξ`: 塑性应变发展系数 - 需要识别

### 网络参数
- `hidden_size`: LSTM隐藏层大小 (默认: 64)
- `num_layers`: LSTM层数 (默认: 2)
- `learning_rate`: 学习率 (默认: 0.001)

### 损失权重
- `data`: 数据拟合损失权重 (默认: 1.0)
- `physics`: 物理约束损失权重 (默认: 0.8)
- `damage`: 损伤演化损失权重 (默认: 0.6)
- `plastic`: 塑性应变损失权重 (默认: 0.4)

## 应用场景

1. **材料参数识别**: 从实验数据中识别混凝土损伤模型参数
2. **损伤预测**: 预测给定应变历史下的损伤演化
3. **性能评估**: 评估材料在不同加载条件下的性能
4. **数字孪生**: 为结构健康监测提供材料层面的数字孪生

## 注意事项

1. **数据质量**: 确保实验数据的质量和完整性
2. **参数调整**: 根据具体材料调整物理参数的初始值
3. **损失权重**: 根据训练效果调整不同损失项的权重
4. **收敛性**: 关注训练过程中的收敛性和稳定性

## 示例结果

```
最终识别的材料参数:
------------------------------
A+ = 0.712456
B+ = 1.987321
ξ  = 0.098765
E0 = 30000.0 MPa (固定)
ft = 3.00 MPa (固定)

预测性能统计:
MSE: 0.001234
MAE: 0.023456
R²: 0.987654
```

## 技术支持

如有问题或建议，请查看 `架构.md` 文件了解更多技术细节，或联系开发团队。

## 许可证

本项目仅用于学术研究目的。 