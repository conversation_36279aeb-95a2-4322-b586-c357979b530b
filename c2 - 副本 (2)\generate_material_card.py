"""
材料参数转换脚本 V2.3
从PINN训练的参数文件生成Abaqus材料卡片
- 修复: 移除 *ELASTIC 卡片以避免与 *USER MATERIAL 冲突
- 新增: 添加 *DENSITY 定义以满足 Abaqus/Explicit 要求
"""

import json
from pathlib import Path

OUTPUT_INP_NAME = 'material_for_abaqus.inp'
NUM_DEPVAR = 5  # 状态变量数量: d+, d-, r_max+, r_max-, ep
CONCRETE_DENSITY = 2.4e-9  # 混凝土密度: 2400 kg/m³ = 2.4e-9 tonne/mm³
POISSON_RATIO = 0.2  # 混凝土泊松比

def find_latest_session_dir():
    """查找最新的session目录"""
    results_dir = Path("results")
    if not results_dir.exists():
        return None
    
    sessions = sorted([d for d in results_dir.iterdir() 
                      if d.is_dir() and d.name.startswith("session_")])
    return sessions[-1] if sessions else None

def generate_material_card():
    """生成Abaqus材料卡片"""
    print("\n=== 生成Abaqus材料卡片 V2.3 (添加*DENSITY定义) ===")
    
    # 查找最新session
    latest_session = find_latest_session_dir()
    if not latest_session:
        print("错误: 未找到任何session目录。")
        return False
    
    print(f"使用session: {latest_session}")
    
    # 定义Vumat验证工作目录
    vumat_dir = latest_session / "vumat_verification"
    if not vumat_dir.exists():
        print(f"警告: 目录 {vumat_dir} 不存在")
        print("提示: 请先运行 python setup_vumat_test.py")
        return False
    
    # 读取参数文件
    params_file = latest_session / "training" / "identified_parameters.json"
    if not params_file.exists():
        print(f"错误: 未找到参数文件 {params_file}")
        return False
    
    print(f"读取参数文件: {params_file}")
    
    try:
        with open(params_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"错误: 读取参数文件失败 - {e}")
        return False
    
    # 提取参数值（按照Vumat中PROPS的顺序）
    try:
        E0 = data['material_constants']['E0']
        props_values = [
            E0,                                    # PROPS(1): 弹性模量
            data['material_constants']['f_t'],     # PROPS(2): 抗拉强度
            data['material_constants']['f_c'],     # PROPS(3): 抗压强度
            data['physics_parameters']['A_plus'],  # PROPS(4): 受拉损伤参数A+
            data['physics_parameters']['B_plus'],  # PROPS(5): 受拉损伤参数B+
            data['physics_parameters']['xi_plus'], # PROPS(6): 受拉塑性参数ξ+
            data['physics_parameters']['A_minus'], # PROPS(7): 受压损伤参数A-
            data['physics_parameters']['B_minus'], # PROPS(8): 受压损伤参数B-
            data['physics_parameters']['xi_minus'], # PROPS(9): 受压塑性参数ξ-
            POISSON_RATIO                          # PROPS(10): 泊松比
        ]
    except KeyError as e:
        print(f"错误: 参数文件缺少必要字段 - {e}")
        return False
    
    # 打印参数信息
    print("\n识别的材料参数:")
    print(f"  密度 = {CONCRETE_DENSITY:.2e} tonne/mm³ ({CONCRETE_DENSITY*1e9:.0f} kg/m³)")
    print(f"  E0 = {props_values[0]:.0f} MPa")
    print(f"  f_t = {props_values[1]:.2f} MPa")
    print(f"  f_c = {props_values[2]:.2f} MPa")
    print(f"  受拉: A+ = {props_values[3]:.4f}, B+ = {props_values[4]:.4f}, ξ+ = {props_values[5]:.4f}")
    print(f"  受压: A- = {props_values[6]:.4f}, B- = {props_values[7]:.4f}, ξ- = {props_values[8]:.4f}")
    
    # --- 【核心修改】 ---
    # 生成仅包含 *USER MATERIAL 的材料卡片内容（移除*ELASTIC以避免冲突）
    inp_content = f"""** ============================================
** Auto-generated Material Card for Abaqus (V2.3)
** Generated by: generate_material_card.py
** Session: {latest_session.name}
** Fixed: Removed *ELASTIC to avoid conflict with *USER MATERIAL
** Added: *DENSITY definition for Abaqus/Explicit
** ============================================
**
*MATERIAL, NAME=CONCRETE_VUMAT
** Material density (required for Abaqus/Explicit)
*DENSITY
{CONCRETE_DENSITY:.2e}
** User-defined material properties for the VUMAT subroutine
** Note: Elastic behavior is fully defined within the VUMAT
*USER MATERIAL, CONSTANTS={len(props_values)}
** PROPS(1-10): E0, ft, fc, A+, B+, xi+, A-, B-, xi-, nu
{', '.join(f'{v:.6f}' if abs(v) < 10 else f'{v:.2f}' for v in props_values)}
*DEPVAR
{NUM_DEPVAR}
** State variables:
** SDV(1): Damage_plus (d+)
** SDV(2): Damage_minus (d-)
** SDV(3): R_max_plus
** SDV(4): R_max_minus
** SDV(5): Plastic_strain (ep)
"""
    
    # 写入文件
    output_path = vumat_dir / OUTPUT_INP_NAME
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(inp_content)
        print(f"\n✓ 成功! 完整的材料卡片已保存至:")
        print(f"  {output_path}")
        print("  (已移除*ELASTIC冲突，添加*DENSITY定义)")
    except Exception as e:
        print(f"错误: 写入文件失败 - {e}")
        return False
    
    # 检查是否有对应的INP文件
    inp_files = list(vumat_dir.glob("*_test.inp"))
    if inp_files:
        print(f"\n检测到测试文件: {[f.name for f in inp_files]}")
        print("\n下一步:")
        print(f"1. 进入目录: cd {vumat_dir}")
        print(f"2. 运行Abaqus: abaqus job={inp_files[0].stem} user=full_cyclic_vumat.for interactive")
    
    return True

def main():
    """主函数"""
    success = generate_material_card()
    if not success:
        print("\n材料卡片生成失败，请检查错误信息。")
        return 1
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())