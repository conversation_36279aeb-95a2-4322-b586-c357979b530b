#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的损伤变量计算逻辑
"""

import numpy as np
import matplotlib.pyplot as plt
import torch
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.pinn_model import ConcreteDamagePINN
from utils.data_processor import calculate_damage_variable
from utils.font_config import setup_chinese_font

def test_damage_calculation():
    """
    测试损伤变量计算的正确性
    """
    # 设置中文字体
    setup_chinese_font()
    
    # 创建模型实例
    model = ConcreteDamagePINN()
    model.eval()
    
    # 测试应变范围
    strain_values = np.linspace(0, 0.01, 100)  # 0到1%应变
    
    # 初始化损伤变量数组
    damage_values_model = []
    damage_values_formula = []
    
    # 材料参数
    E_0 = 30000.0
    r_0 = 1e-4
    A_plus = 0.9
    B_plus = 1.0
    
    print(f"材料参数:")
    print(f"E_0 = {E_0} MPa")
    print(f"r_0 = {r_0}")
    print(f"A_plus = {A_plus}")
    print(f"B_plus = {B_plus}")
    print("\n开始计算损伤变量...")
    
    # 重置模型状态
    model.reset_state(1)
    
    for i, strain in enumerate(strain_values):
        # 使用模型计算
        strain_tensor = torch.tensor([[strain]], dtype=torch.float32).unsqueeze(0)
        plastic_strain = torch.zeros_like(strain_tensor[:, -1, :])
        
        with torch.no_grad():
            damage_model = model.compute_damage_evolution(strain_tensor[:, -1, :], plastic_strain)
            damage_values_model.append(damage_model.item())
        
        # 使用公式计算
        effective_strain = max(0, strain)  # 只考虑正应变
        Y_plus = 0.5 * E_0 * effective_strain**2
        r_plus = max(r_0, Y_plus)
        
        damage_formula = calculate_damage_variable(r_plus, r_0, A_plus, B_plus)
        damage_values_formula.append(damage_formula)
        
        if i % 20 == 0:
            print(f"应变: {strain:.6f}, 模型损伤: {damage_model.item():.6f}, 公式损伤: {damage_formula:.6f}")
    
    # 转换为numpy数组
    damage_values_model = np.array(damage_values_model)
    damage_values_formula = np.array(damage_values_formula)
    
    # 检查损伤变量的特性
    print("\n损伤变量特性检查:")
    print(f"模型损伤变量范围: [{damage_values_model.min():.6f}, {damage_values_model.max():.6f}]")
    print(f"公式损伤变量范围: [{damage_values_formula.min():.6f}, {damage_values_formula.max():.6f}]")
    
    # 检查单调性
    model_monotonic = np.all(np.diff(damage_values_model) >= -1e-8)  # 允许小的数值误差
    formula_monotonic = np.all(np.diff(damage_values_formula) >= -1e-8)
    
    print(f"模型损伤变量单调性: {'✓' if model_monotonic else '✗'}")
    print(f"公式损伤变量单调性: {'✓' if formula_monotonic else '✗'}")
    
    # 检查是否有负值
    model_positive = np.all(damage_values_model >= 0)
    formula_positive = np.all(damage_values_formula >= 0)
    
    print(f"模型损伤变量非负性: {'✓' if model_positive else '✗'}")
    print(f"公式损伤变量非负性: {'✓' if formula_positive else '✗'}")
    
    # 绘制结果
    plt.figure(figsize=(12, 8))
    
    # 子图1: 损伤演化曲线
    plt.subplot(2, 2, 1)
    plt.plot(strain_values * 1000, damage_values_model, 'b-', linewidth=2, label='模型计算')
    plt.plot(strain_values * 1000, damage_values_formula, 'r--', linewidth=2, label='公式计算')
    plt.xlabel('应变 (‰)')
    plt.ylabel('损伤变量 d^+')
    plt.title('损伤变量演化曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图2: 损伤变量差异
    plt.subplot(2, 2, 2)
    diff = damage_values_model - damage_values_formula
    plt.plot(strain_values * 1000, diff, 'g-', linewidth=2)
    plt.xlabel('应变 (‰)')
    plt.ylabel('损伤变量差异')
    plt.title('模型与公式计算差异')
    plt.grid(True, alpha=0.3)
    
    # 子图3: 损伤变量增长率
    plt.subplot(2, 2, 3)
    damage_rate_model = np.gradient(damage_values_model, strain_values)
    damage_rate_formula = np.gradient(damage_values_formula, strain_values)
    plt.plot(strain_values * 1000, damage_rate_model, 'b-', linewidth=2, label='模型增长率')
    plt.plot(strain_values * 1000, damage_rate_formula, 'r--', linewidth=2, label='公式增长率')
    plt.xlabel('应变 (‰)')
    plt.ylabel('损伤增长率')
    plt.title('损伤变量增长率')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图4: 对数尺度损伤演化
    plt.subplot(2, 2, 4)
    # 避免log(0)，添加小的偏移
    damage_model_log = np.maximum(damage_values_model, 1e-10)
    damage_formula_log = np.maximum(damage_values_formula, 1e-10)
    plt.semilogy(strain_values * 1000, damage_model_log, 'b-', linewidth=2, label='模型计算')
    plt.semilogy(strain_values * 1000, damage_formula_log, 'r--', linewidth=2, label='公式计算')
    plt.xlabel('应变 (‰)')
    plt.ylabel('损伤变量 d^+ (对数尺度)')
    plt.title('损伤变量演化 (对数尺度)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('./results/damage_validation.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("\n测试完成！结果已保存到 ./results/damage_validation.png")
    
    return {
        'model_damage': damage_values_model,
        'formula_damage': damage_values_formula,
        'strain': strain_values,
        'monotonic': model_monotonic and formula_monotonic,
        'positive': model_positive and formula_positive
    }

if __name__ == "__main__":
    # 确保结果目录存在
    os.makedirs('./results', exist_ok=True)
    
    # 运行测试
    results = test_damage_calculation()
    
    if results['monotonic'] and results['positive']:
        print("\n✓ 损伤变量计算修正成功！")
        print("  - 损伤变量单调递增")
        print("  - 损伤变量始终为正值")
        print("  - 损伤变量在[0,1)范围内")
    else:
        print("\n✗ 损伤变量计算仍有问题，需要进一步调整")