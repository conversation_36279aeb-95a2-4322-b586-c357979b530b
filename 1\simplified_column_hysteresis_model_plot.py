import torch
import numpy as np
import matplotlib.pyplot as plt
from simplified_column_hysteresis_model import SimplifiedColumnHysteresisModel

def plot_hysteresis_curve(model, X, y, static_feature_count, force_scaler, disp_scaler, original_data=None, title="简化模型滞回曲线对比"):
    """绘制滞回曲线对比图"""
    model.eval()
    
    with torch.no_grad():
        # 预测力值
        y_pred = model(X, static_feature_count)
        
        # 如果有原始数据，直接使用
        if original_data is not None and 'force' in original_data and 'disp' in original_data:
            disp = original_data['disp']
            force_true = original_data['force']
            
            # 反归一化预测力
            force_pred = force_scaler.inverse_transform(y_pred.cpu().numpy().reshape(-1, 1)).flatten()
            
            # 提取方向变化点
            direction_changes = original_data.get('direction_changes', [])
        else:
            # 提取位移特征并反归一化
            disp_idx = static_feature_count
            disp_feature = X[:, disp_idx].cpu().numpy().reshape(-1, 1)
            disp = disp_scaler.inverse_transform(disp_feature).flatten()
            
            # 反归一化力值
            force_true = force_scaler.inverse_transform(y.cpu().numpy().reshape(-1, 1)).flatten()
            force_pred = force_scaler.inverse_transform(y_pred.cpu().numpy().reshape(-1, 1)).flatten()
            
            # 计算方向变化点
            direction_changes = []
            for i in range(1, len(disp)-1):
                if (disp[i-1] < disp[i] and disp[i] > disp[i+1]) or \
                   (disp[i-1] > disp[i] and disp[i] < disp[i+1]):
                    direction_changes.append(i)
        
        # 创建两个子图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
        
        # 第一个子图：真实曲线和预测曲线对比
        ax1.plot(disp, force_true, 'b-', label='实验测量', linewidth=2)
        ax1.plot(disp, force_pred, 'r--', label='模型预测', linewidth=2)
        
        # 标记方向变化点
        if len(direction_changes) > 0:
            ax1.scatter(disp[direction_changes], force_true[direction_changes], 
                       c='blue', marker='o', s=80, label='实验转折点')
            ax1.scatter(disp[direction_changes], force_pred[direction_changes], 
                       c='red', marker='x', s=80, label='预测转折点')
        
        ax1.set_xlabel('位移 (mm)', fontsize=12)
        ax1.set_ylabel('力 (kN)', fontsize=12)
        ax1.set_title('滞回曲线对比', fontsize=14)
        ax1.grid(True)
        ax1.legend(fontsize=12)
        
        # 第二个子图：误差分析
        error = force_pred - force_true
        ax2.plot(disp, error, 'g-', linewidth=1.5)
        ax2.axhline(y=0, color='k', linestyle='--', alpha=0.7)
        
        # 标记误差较大的区域
        error_threshold = np.std(error) * 2  # 使用2倍标准差作为阈值
        large_error_indices = np.where(np.abs(error) > error_threshold)[0]
        if len(large_error_indices) > 0:
            ax2.scatter(disp[large_error_indices], error[large_error_indices], 
                       c='red', marker='x', s=50, label='大误差点')
        
        ax2.set_xlabel('位移 (mm)', fontsize=12)
        ax2.set_ylabel('预测误差 (kN)', fontsize=12)
        ax2.set_title('预测误差分析', fontsize=14)
        ax2.grid(True)
        if len(large_error_indices) > 0:
            ax2.legend(fontsize=10)
        
        # 设置整体标题
        fig.suptitle(title, fontsize=16)
        plt.tight_layout(rect=[0, 0, 1, 0.95])
        
        # 保存图像
        plt.savefig('d:\\column\\simplified_hysteresis_curve_comparison.png', dpi=300, bbox_inches='tight')
        
        # 计算预测误差
        mae = np.mean(np.abs(force_true - force_pred))
        rmse = np.sqrt(np.mean((force_true - force_pred)**2))
        max_error = np.max(np.abs(force_true - force_pred))
        
        # 计算滞回特性指标 - 能量耗散
        # 使用梯形法则计算滞回环面积（能量耗散）
        true_energy = 0
        pred_energy = 0
        
        # 按照加载-卸载循环分段计算能量
        if len(direction_changes) >= 2:
            for i in range(len(direction_changes) - 1):
                start_idx = direction_changes[i]
                end_idx = direction_changes[i + 1]
                
                # 计算真实曲线的能量耗散
                segment_disp = disp[start_idx:end_idx+1]
                segment_force_true = force_true[start_idx:end_idx+1]
                true_segment_energy = np.trapz(segment_force_true, segment_disp)
                true_energy += abs(true_segment_energy)
                
                # 计算预测曲线的能量耗散
                segment_force_pred = force_pred[start_idx:end_idx+1]
                pred_segment_energy = np.trapz(segment_force_pred, segment_disp)
                pred_energy += abs(pred_segment_energy)
        
        # 计算能量耗散误差
        energy_error = abs(true_energy - pred_energy) / (true_energy + 1e-6) * 100  # 百分比误差
        
        print(f"预测误差统计:")
        print(f"平均绝对误差 (MAE): {mae:.4f} kN")
        print(f"均方根误差 (RMSE): {rmse:.4f} kN")
        print(f"最大误差: {max_error:.4f} kN")
        print(f"能量耗散误差: {energy_error:.2f}%")
        print(f"真实能量耗散: {true_energy:.2f} kN·mm")
        print(f"预测能量耗散: {pred_energy:.2f} kN·mm")
        
        plt.show()
        
        return {
            'mae': mae,
            'rmse': rmse,
            'max_error': max_error,
            'energy_error': energy_error,
            'true_energy': true_energy,
            'pred_energy': pred_energy
        }

def plot_combined_hysteresis_curve(model, X, y, static_feature_count, force_scaler, disp_scaler, original_data=None):
    """绘制叠加的滞回曲线对比图"""
    model.eval()
    
    with torch.no_grad():
        # 预测力值
        y_pred = model(X, static_feature_count)
        
        # 如果有原始数据，直接使用
        if original_data is not None and 'force' in original_data and 'disp' in original_data:
            disp = original_data['disp']
            force_true = original_data['force']
            
            # 反归一化预测力
            force_pred = force_scaler.inverse_transform(y_pred.cpu().numpy().reshape(-1, 1)).flatten()
            
            # 提取方向变化点
            direction_changes = original_data.get('direction_changes', [])
        else:
            # 提取位移特征并反归一化
            disp_idx = static_feature_count
            disp_feature = X[:, disp_idx].cpu().numpy().reshape(-1, 1)
            disp = disp_scaler.inverse_transform(disp_feature).flatten()
            
            # 反归一化力值
            force_true = force_scaler.inverse_transform(y.cpu().numpy().reshape(-1, 1)).flatten()
            force_pred = force_scaler.inverse_transform(y_pred.cpu().numpy().reshape(-1, 1)).flatten()
            
            # 计算方向变化点
            direction_changes = []
            for i in range(1, len(disp)-1):
                if (disp[i-1] < disp[i] and disp[i] > disp[i+1]) or \
                   (disp[i-1] > disp[i] and disp[i] < disp[i+1]):
                    direction_changes.append(i)
        
        # 创建图形
        plt.figure(figsize=(12, 10))
        
        # 绘制真实曲线和预测曲线
        plt.plot(disp, force_true, 'b-', label='实验测量', linewidth=2)
        plt.plot(disp, force_pred, 'r--', label='简化模型预测', linewidth=2)
        
        # 标记方向变化点
        if len(direction_changes) > 0:
            plt.scatter(disp[direction_changes], force_true[direction_changes], 
                       c='blue', marker='o', s=60, label='实验转折点')
            plt.scatter(disp[direction_changes], force_pred[direction_changes], 
                       c='red', marker='x', s=60, label='预测转折点')
        
        plt.xlabel('位移 (mm)', fontsize=14)
        plt.ylabel('力 (kN)', fontsize=14)
        plt.title('简化模型滞回曲线对比', fontsize=16)
        plt.grid(True)
        plt.legend(fontsize=12)
        
        # 保存图像
        plt.savefig('d:\\column\\simplified_hysteresis_curve_overlay.png', dpi=300, bbox_inches='tight')
        plt.show()