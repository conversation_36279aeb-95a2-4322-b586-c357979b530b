"""
PINN模型权重导出工具
将训练好的PINN模型权重和参数导出为VUMAT可用的格式
"""

import os
import sys
import torch
import numpy as np
import argparse
from pathlib import Path
from datetime import datetime

# 导入本地模块
from pinn_model_v2 import DamagePINNV2
from model_utils import safe_load_model


class PINNtoVUMATExporter:
    """
    PINN模型到VUMAT接口导出器
    将训练好的PINN模型权重和参数转换为VUMAT可用的格式
    """
    
    def __init__(self, model_path, output_dir=None, verbose=True):
        """
        初始化导出器
        
        Args:
            model_path: 训练好的PINN模型路径
            output_dir: 输出目录，默认为'fortran_vumat'
            verbose: 是否打印详细信息
        """
        self.model_path = model_path
        self.output_dir = output_dir or 'fortran_vumat'
        self.verbose = verbose
        self.device = torch.device('cpu')  # 使用CPU进行导出
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 模型信息
        self.model_info = None
        self.model = None
        self.physics_params = None
        self.material_constants = None
        
        # 模型权重
        self.weights = {}
        
        # 时间戳
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if self.verbose:
            print(f"PINN模型权重导出工具初始化完成")
            print(f"模型路径: {self.model_path}")
            print(f"输出目录: {self.output_dir}")
    
    def load_model(self):
        """加载PINN模型"""
        if self.verbose:
            print(f"\n正在加载模型: {self.model_path}")
        
        # 使用安全加载函数
        self.model_info = safe_load_model(self.model_path, self.device)
        
        # 提取物理参数
        self.physics_params = self.model_info.get('physics_parameters', {})
        self.material_constants = self.model_info.get('material_constants', {})
        
        # 检查必要参数是否存在
        if not self.physics_params or not self.material_constants:
            print("警告: 模型文件可能不完整，缺少物理参数或材料常数")
            print("如果这是一个旧版本的模型文件，请重新训练")
            # 设置默认值
            if not self.physics_params:
                self.physics_params = {
                    'A_plus': 0.5, 'B_plus': 1.0, 'xi_plus': 0.01,
                    'A_minus': 1.5, 'B_minus': 0.5, 'xi_minus': 0.02
                }
            if not self.material_constants:
                self.material_constants = {'E0': 30000.0, 'f_t': 3.0, 'f_c': 30.0}
        
        # 重建模型
        config = self.model_info.get('config', {})
        self.model = DamagePINNV2(
            input_size=1,
            hidden_size=config.get('hidden_size', 64),
            num_layers=config.get('num_layers', 6),
            output_size=3
        ).to(self.device)
        
        # 加载模型权重
        self.model.load_state_dict(self.model_info['model_state_dict'])
        self.model.eval()
        
        if self.verbose:
            print("模型加载成功!")
            print(f"材料常数:")
            print(f"  E0 = {self.material_constants['E0']:.0f} MPa")
            print(f"  f_t = {self.material_constants['f_t']:.2f} MPa")
            print(f"  f_c = {self.material_constants['f_c']:.2f} MPa")
            print(f"识别的物理参数:")
            print(f"  受拉: A+ = {self.physics_params['A_plus']:.4f}, "
                  f"B+ = {self.physics_params['B_plus']:.4f}, "
                  f"ξ+ = {self.physics_params['xi_plus']:.4f}")
            print(f"  受压: A- = {self.physics_params['A_minus']:.4f}, "
                  f"B- = {self.physics_params['B_minus']:.4f}, "
                  f"ξ- = {self.physics_params['xi_minus']:.4f}")
    
    def extract_weights(self):
        """提取模型权重"""
        if self.verbose:
            print("\n提取模型权重...")
        
        # 确保模型已加载
        if self.model is None:
            self.load_model()
        
        # 提取网络权重
        for name, param in self.model.named_parameters():
            # 转换为NumPy数组
            self.weights[name] = param.detach().cpu().numpy()
        
        if self.verbose:
            print(f"提取了 {len(self.weights)} 个权重参数")
            for name, weight in self.weights.items():
                print(f"  {name}: 形状{weight.shape}")
    
    def export_fortran_include_file(self):
        """导出Fortran包含文件，包含材料参数和物理参数"""
        if self.verbose:
            print("\n导出Fortran材料参数文件...")
        
        # 文件路径
        material_file = os.path.join(self.output_dir, 'material_parameters.inc')
        
        # 格式化参数为科学计数法
        E0 = f"{self.material_constants['E0']:.6E}"
        f_t = f"{self.material_constants['f_t']:.6E}"
        f_c = f"{self.material_constants['f_c']:.6E}"
        
        A_plus = f"{self.physics_params['A_plus']:.6E}"
        B_plus = f"{self.physics_params['B_plus']:.6E}"
        xi_plus = f"{self.physics_params['xi_plus']:.6E}"
        
        A_minus = f"{self.physics_params['A_minus']:.6E}"
        B_minus = f"{self.physics_params['B_minus']:.6E}"
        xi_minus = f"{self.physics_params['xi_minus']:.6E}"
        
        # 生成文件内容
        content = f"""C     Material parameters for concrete damage model
C     Generated from PINN identification
C
C     Material constants
      PARAMETER (E0_INIT = {E0})
      PARAMETER (FT_INIT = {f_t})
      PARAMETER (FC_INIT = {f_c})
C
C     Tensile damage parameters
      PARAMETER (A_PLUS = {A_plus})
      PARAMETER (B_PLUS = {B_plus})
      PARAMETER (XI_PLUS = {xi_plus})
C
C     Compressive damage parameters
      PARAMETER (A_MINUS = {A_minus})
      PARAMETER (B_MINUS = {B_minus})
      PARAMETER (XI_MINUS = {xi_minus})
"""
        
        # 写入文件
        with open(material_file, 'w') as f:
            f.write(content)
        
        if self.verbose:
            print(f"材料参数文件已保存到: {material_file}")
        
        return material_file
    
    def export_network_weights_file(self):
        """导出神经网络权重文件，用于高级VUMAT实现"""
        if self.verbose:
            print("\n导出神经网络权重文件...")
        
        # 确保权重已提取
        if not self.weights:
            self.extract_weights()
        
        # 文件路径
        weights_file = os.path.join(self.output_dir, 'network_weights.inc')
        
        # 生成文件内容
        content = "C     Neural network weights for PINN model\n"
        content += "C     Generated from PyTorch model\n"
        content += "C\n"
        
        # 处理每层的权重和偏置
        layer_idx = 0
        for name, weight in self.weights.items():
            if 'weight' in name:
                # 处理权重矩阵
                layer_name = name.split('.')[0]
                shape = weight.shape
                
                content += f"C     Layer {layer_idx}: {layer_name} - Shape: {shape}\n"
                
                # 对于每个权重，生成Fortran数组声明
                weight_flat = weight.flatten()
                content += f"      DIMENSION W{layer_idx}({shape[0]},{shape[1]})\n"
                
                # 生成权重数据
                for i in range(shape[0]):
                    for j in range(shape[1]):
                        val = weight[i, j]
                        content += f"      DATA W{layer_idx}({i+1},{j+1}) / {val:.10E} /\n"
                
                layer_idx += 1
        
        # 处理偏置
        layer_idx = 0
        for name, bias in self.weights.items():
            if 'bias' in name:
                # 处理偏置向量
                layer_name = name.split('.')[0]
                shape = bias.shape
                
                content += f"C     Bias {layer_idx}: {layer_name} - Shape: {shape}\n"
                
                # 生成偏置数组声明
                content += f"      DIMENSION B{layer_idx}({shape[0]})\n"
                
                # 生成偏置数据
                for i in range(shape[0]):
                    val = bias[i]
                    content += f"      DATA B{layer_idx}({i+1}) / {val:.10E} /\n"
                
                layer_idx += 1
        
        # 写入文件
        with open(weights_file, 'w') as f:
            f.write(content)
        
        if self.verbose:
            print(f"神经网络权重文件已保存到: {weights_file}")
        
        return weights_file
    
    def export_numpy_weights(self):
        """导出NumPy格式的权重文件，便于其他程序使用"""
        if self.verbose:
            print("\n导出NumPy格式权重文件...")
        
        # 确保权重已提取
        if not self.weights:
            self.extract_weights()
        
        # 文件路径
        weights_file = os.path.join(self.output_dir, 'pinn_weights.npz')
        
        # 保存为NumPy格式
        np.savez(weights_file, **self.weights)
        
        if self.verbose:
            print(f"NumPy权重文件已保存到: {weights_file}")
        
        return weights_file
    
    def export_summary_file(self):
        """导出模型摘要文件，包含模型结构和参数信息"""
        if self.verbose:
            print("\n导出模型摘要文件...")
        
        # 文件路径
        summary_file = os.path.join(self.output_dir, 'pinn_model_summary.txt')
        
        # 生成文件内容
        content = "PINN模型摘要\n"
        content += "=" * 50 + "\n\n"
        
        content += f"导出时间: {self.timestamp}\n"
        content += f"模型文件: {self.model_path}\n\n"
        
        # 模型结构
        content += "模型结构:\n"
        content += "-" * 50 + "\n"
        content += str(self.model) + "\n\n"
        
        # 材料常数
        content += "材料常数:\n"
        content += "-" * 50 + "\n"
        content += f"初始弹性模量 E0 = {self.material_constants['E0']:.2f} MPa\n"
        content += f"抗拉强度 f_t = {self.material_constants['f_t']:.2f} MPa\n"
        content += f"抗压强度 f_c = {self.material_constants['f_c']:.2f} MPa\n\n"
        
        # 物理参数
        content += "物理参数:\n"
        content += "-" * 50 + "\n"
        content += f"受拉损伤参数: A+ = {self.physics_params['A_plus']:.4f}, B+ = {self.physics_params['B_plus']:.4f}\n"
        content += f"受压损伤参数: A- = {self.physics_params['A_minus']:.4f}, B- = {self.physics_params['B_minus']:.4f}\n"
        content += f"塑性参数: ξ+ = {self.physics_params['xi_plus']:.4f}, ξ- = {self.physics_params['xi_minus']:.4f}\n\n"
        
        # 权重信息
        content += "权重信息:\n"
        content += "-" * 50 + "\n"
        for name, weight in self.weights.items():
            content += f"{name}: 形状{weight.shape}\n"
        
        # 写入文件
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        if self.verbose:
            print(f"模型摘要文件已保存到: {summary_file}")
        
        return summary_file
    
    def export_all(self):
        """导出所有文件"""
        self.load_model()
        self.extract_weights()
        
        # 导出各种格式
        material_file = self.export_fortran_include_file()
        weights_file = self.export_network_weights_file()
        numpy_file = self.export_numpy_weights()
        summary_file = self.export_summary_file()
        
        if self.verbose:
            print("\n导出完成!")
            print(f"材料参数文件: {material_file}")
            print(f"神经网络权重文件: {weights_file}")
            print(f"NumPy权重文件: {numpy_file}")
            print(f"模型摘要文件: {summary_file}")
        
        return {
            'material_file': material_file,
            'weights_file': weights_file,
            'numpy_file': numpy_file,
            'summary_file': summary_file
        }


def find_latest_model():
    """自动查找最新的模型文件"""
    results_dir = Path('results')
    
    if not results_dir.exists():
        raise FileNotFoundError("未找到results目录，请确保已训练过模型")
    
    # 获取所有session目录
    session_dirs = [d for d in results_dir.iterdir() 
                   if d.is_dir() and d.name.startswith('session_')]
    
    if not session_dirs:
        raise FileNotFoundError("未找到任何训练会话目录")
    
    # 按时间戳排序，获取最新的
    session_dirs.sort(key=lambda x: x.name, reverse=True)
    
    # 查找包含best_model.pth的最新会话
    for session_dir in session_dirs:
        model_path = session_dir / 'training' / 'best_model.pth'
        if model_path.exists():
            return str(model_path)
    
    raise FileNotFoundError("未找到任何best_model.pth文件")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PINN模型权重导出工具')
    
    parser.add_argument('--model', type=str, default=None,
                      help='训练好的PINN模型路径 (如果不指定，将自动查找最新模型)')
    parser.add_argument('--output-dir', type=str, default='fortran_vumat',
                      help='输出目录 (默认: fortran_vumat)')
    parser.add_argument('--quiet', action='store_true',
                      help='静默模式，不打印详细信息')
    
    args = parser.parse_args()
    
    # 确定模型路径
    if args.model is None:
        try:
            model_path = find_latest_model()
            if not args.quiet:
                print(f"自动找到最新模型: {model_path}")
        except FileNotFoundError as e:
            print(f"错误: {e}")
            print("请使用 --model 参数指定模型路径")
            sys.exit(1)
    else:
        model_path = args.model
        if not os.path.exists(model_path):
            print(f"错误: 指定的模型文件不存在: {model_path}")
            sys.exit(1)
    
    # 创建导出器
    exporter = PINNtoVUMATExporter(
        model_path=model_path,
        output_dir=args.output_dir,
        verbose=not args.quiet
    )
    
    # 导出所有文件
    exporter.export_all()


if __name__ == "__main__":
    main()