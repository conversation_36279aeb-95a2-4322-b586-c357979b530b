import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform
import os
import sys

def configure_chinese_font():
    """
    配置matplotlib使用中文字体
    """
    # 检测操作系统
    system = platform.system()
    
    # 查找系统中可用的中文字体
    chinese_fonts = [f.name for f in fm.fontManager.ttflist 
                    if any(x in f.name for x in ['黑体', 'SimHei', '宋体', 'SimSun', 'Microsoft YaHei', '微软雅黑'])]
    
    # 设置中文字体
    if system == 'Windows':
        # Windows系统优先使用微软雅黑，其次是SimHei(黑体)
        if chinese_fonts:
            # 使用系统中实际存在的中文字体
            plt.rcParams['font.sans-serif'] = chinese_fonts + ['Arial Unicode MS', 'Arial']
        else:
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'Arial Unicode MS', 'Arial']
    elif system == 'Darwin':  # macOS
        plt.rcParams['font.sans-serif'] = ['PingFang SC', 'Heiti SC', 'STHeiti', 'Arial Unicode MS', 'Arial']
    else:  # Linux等其他系统
        plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'Droid Sans Fallback', 'Arial Unicode MS', 'Arial']
    
    # 解决负号显示问题
    plt.rcParams['axes.unicode_minus'] = False
    
    # 设置DPI以获得更清晰的图像
    plt.rcParams['figure.dpi'] = 300
    
    # 设置全局字体大小
    plt.rcParams['font.size'] = 12
    plt.rcParams['axes.titlesize'] = 14
    plt.rcParams['axes.labelsize'] = 12
    
    # 强制使用指定的字体
    plt.rcParams['font.family'] = 'sans-serif'
    
    print(f"已配置字体: {plt.rcParams['font.sans-serif'][0]}")
    print(f"可用中文字体: {chinese_fonts}")

def check_fonts():
    """
    检查系统中可用的中文字体
    """
    chinese_fonts = [f.name for f in fm.fontManager.ttflist 
                   if any(x in f.name for x in ['黑体', 'SimHei', '宋体', 'SimSun', 'Microsoft YaHei', '微软雅黑'])]
    
    print("系统中可用的中文字体:")
    for font in chinese_fonts:
        print(f"- {font}")
    
    # 检查matplotlib当前默认字体
    print("\nMatplotlib当前默认字体:")
    print(f"- {plt.rcParams['font.family']}")
    print(f"- {plt.rcParams['font.sans-serif']}")
    
    return chinese_fonts

# 自动配置中文字体
configure_chinese_font()