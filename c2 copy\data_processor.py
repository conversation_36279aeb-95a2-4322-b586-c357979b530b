"""
数据处理模块
处理实验数据，提取材料属性，识别加卸载状态
"""

import pandas as pd
import numpy as np
import torch
from typing import Dict, Tu<PERSON>, List
import matplotlib.pyplot as plt
from font_config import setup_font

class DataProcessor:
    def __init__(self, file_path: str):
        """
        初始化数据处理器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.data = None
        self.strain = None
        self.stress = None
        self.strain_increments = None
        self.material_properties = None
        
    def load_data(self):
        """加载Excel数据"""
        try:
            self.data = pd.read_excel(self.file_path)
            if 'strain' not in self.data.columns or 'stress' not in self.data.columns:
                raise ValueError("Excel文件必须包含'strain'和'stress'列")
            
            self.strain = self.data['strain'].values
            self.stress = self.data['stress'].values
            
            # 计算应变增量
            self.strain_increments = np.diff(self.strain, prepend=0)
            
            print(f"成功加载数据: {len(self.strain)} 个数据点")
            print(f"应变范围: [{self.strain.min():.6f}, {self.strain.max():.6f}]")
            print(f"应力范围: [{self.stress.min():.2f}, {self.stress.max():.2f}] MPa")
            
        except Exception as e:
            raise Exception(f"加载数据失败: {str(e)}")
    
    def get_material_properties(self) -> Dict[str, float]:
        """
        从数据中估算材料属性
        
        Returns:
            包含材料属性的字典: E0, f_t, f_c
        """
        if self.strain is None or self.stress is None:
            raise ValueError("请先加载数据")
        
        # 估算初始弹性模量 E0 (使用初始线性段)
        # 找到应力为正的初始段
        positive_stress_idx = np.where(self.stress > 0.1)[0]
        if len(positive_stress_idx) > 5:
            # 使用前5-10个点进行线性拟合
            n_points = min(10, len(positive_stress_idx))
            idx = positive_stress_idx[:n_points]
            
            # 线性拟合
            p = np.polyfit(self.strain[idx], self.stress[idx], 1)
            E0 = p[0]  # 斜率即为弹性模量
        else:
            # 如果数据点太少，使用简单估算
            E0 = 30000.0  # 默认值 30 GPa
        
        # 估算抗拉强度 f_t (拉伸段最大应力)
        tensile_idx = np.where(self.strain > 0)[0]
        if len(tensile_idx) > 0:
            f_t = np.max(self.stress[tensile_idx])
        else:
            f_t = 3.0  # 默认值 3 MPa
        
        # 估算抗压强度 f_c (压缩段最大应力的绝对值)
        compressive_idx = np.where(self.strain < 0)[0]
        if len(compressive_idx) > 0:
            f_c = np.abs(np.min(self.stress[compressive_idx]))
        else:
            f_c = 30.0  # 默认值 30 MPa
        
        # 确保值在合理范围内
        E0 = np.clip(E0, 10000, 50000)  # 10-50 GPa
        f_t = np.clip(f_t, 0.5, 10.0)   # 0.5-10 MPa
        f_c = np.clip(f_c, 10.0, 100.0) # 10-100 MPa
        
        self.material_properties = {
            'E0': E0,
            'f_t': f_t,
            'f_c': f_c
        }
        
        print(f"\n材料属性估算:")
        print(f"初始弹性模量 E0 = {E0:.0f} MPa")
        print(f"抗拉强度 f_t = {f_t:.2f} MPa")
        print(f"抗压强度 f_c = {f_c:.2f} MPa")
        
        return self.material_properties
    
    def identify_loading_states(self) -> np.ndarray:
        """
        识别每个时间步的加卸载状态
        
        Returns:
            loading_states: 数组，1表示加载，-1表示卸载，0表示初始状态
        """
        n_steps = len(self.strain)
        loading_states = np.zeros(n_steps, dtype=int)
        
        # 跟踪历史最大/最小应变
        max_strain_history = -np.inf
        min_strain_history = np.inf
        
        for i in range(n_steps):
            current_strain = self.strain[i]
            
            if i == 0:
                loading_states[i] = 0  # 初始状态
                max_strain_history = current_strain
                min_strain_history = current_strain
            else:
                strain_increment = self.strain_increments[i]
                
                # 判断加卸载状态
                if strain_increment > 0:  # 应变增加
                    if current_strain > max_strain_history:
                        loading_states[i] = 1  # 拉伸加载
                        max_strain_history = current_strain
                    else:
                        loading_states[i] = -1  # 压缩卸载（向拉伸方向）
                elif strain_increment < 0:  # 应变减少
                    if current_strain < min_strain_history:
                        loading_states[i] = 1  # 压缩加载
                        min_strain_history = current_strain
                    else:
                        loading_states[i] = -1  # 拉伸卸载（向压缩方向）
                else:
                    loading_states[i] = loading_states[i-1]  # 保持前一状态
        
        return loading_states
    
    def process_data(self) -> Dict[str, torch.Tensor]:
        """
        处理数据并转换为张量格式
        
        Returns:
            包含处理后数据的字典
        """
        if self.material_properties is None:
            self.get_material_properties()
        
        # 识别加卸载状态
        loading_states = self.identify_loading_states()
        
        # 转换为PyTorch张量
        strain_tensor = torch.tensor(self.strain, dtype=torch.float32)
        stress_tensor = torch.tensor(self.stress, dtype=torch.float32)
        strain_increment_tensor = torch.tensor(self.strain_increments, dtype=torch.float32)
        loading_states_tensor = torch.tensor(loading_states, dtype=torch.float32)
        
        # 创建应变符号标记（用于区分拉压状态）
        strain_sign = torch.sign(strain_tensor)
        
        return {
            'strain': strain_tensor,
            'stress': stress_tensor,
            'strain_increments': strain_increment_tensor,
            'loading_states': loading_states_tensor,
            'strain_sign': strain_sign,
            'E0': self.material_properties['E0'],
            'f_t': self.material_properties['f_t'],
            'f_c': self.material_properties['f_c']
        }
    
    def plot_data(self, save_path: str = None):
        """
        绘制原始数据
        
        Args:
            save_path: 保存路径
        """
        if self.strain is None or self.stress is None:
            raise ValueError("请先加载数据")
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 应力-应变曲线
        ax1.plot(self.strain, self.stress, 'b-', linewidth=2, label='实验数据')
        ax1.set_xlabel('应变')
        ax1.set_ylabel('应力 (MPa)')
        ax1.set_title('应力-应变曲线')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 标记拉压区域
        ax1.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        ax1.axvline(x=0, color='k', linestyle='--', alpha=0.5)
        
        # 时程曲线
        time_steps = np.arange(len(self.strain))
        ax2.plot(time_steps, self.strain, 'g-', label='应变', linewidth=2)
        ax2.set_xlabel('时间步')
        ax2.set_ylabel('应变')
        ax2.set_title('应变时程')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # 添加应变增量信息
        ax2_twin = ax2.twinx()
        ax2_twin.plot(time_steps[1:], self.strain_increments[1:], 'r--', 
                     label='应变增量', alpha=0.6)
        ax2_twin.set_ylabel('应变增量')
        ax2_twin.legend(loc='upper right')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()

# 测试代码
if __name__ == "__main__":
    # 测试数据处理器
    processor = DataProcessor("cyclic_data.xlsx")
    processor.load_data()
    processor.get_material_properties()
    data_dict = processor.process_data()
    processor.plot_data("test_data_plot.png") 