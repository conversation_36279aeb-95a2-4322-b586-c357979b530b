# -*- coding: utf-8 -*-
"""
主程序入口

混凝土损伤本构PINN建模项目的统一入口
"""

import os
import sys
import argparse
from font_config import configure_chinese_font

def main():
    """
    主程序入口
    """
    # 配置中文字体
    configure_chinese_font()
    
    parser = argparse.ArgumentParser(description='混凝土损伤本构PINN建模项目')
    parser.add_argument('--mode', choices=['train', 'predict', 'data', 'all'], 
                       default='all', help='运行模式')
    parser.add_argument('--data', default='d:/column/F3/data.xlsx', help='数据文件路径')
    parser.add_argument('--model', default='d:/column/F3/concrete_pinn_model.pth', help='模型文件路径')
    parser.add_argument('--epochs', type=int, default=10000, help='训练轮数')
    parser.add_argument('--lr', type=float, default=0.001, help='学习率')
    parser.add_argument('--hidden_size', type=int, default=32, help='隐藏层大小')
    parser.add_argument('--num_layers', type=int, default=4, help='隐藏层数')
    
    args = parser.parse_args()
    
    print("=" * 70)
    print("混凝土损伤本构AI建模项目")
    print("基于物理信息神经网络(PINN)的损伤本构建模")
    print("=" * 70)
    print(f"运行模式: {args.mode}")
    print(f"数据文件: {args.data}")
    print(f"模型文件: {args.model}")
    print("=" * 70)
    
    if args.mode == 'data' or args.mode == 'all':
        print("\n>>> 数据处理模式")
        run_data_processing(args.data)
    
    if args.mode == 'train' or args.mode == 'all':
        print("\n>>> 训练模式")
        run_training(args)
    
    if args.mode == 'predict':
        print("\n>>> 预测模式")
        run_prediction(args.model)
    
    print("\n" + "=" * 70)
    print("程序执行完成!")
    print("=" * 70)

def run_data_processing(data_file):
    """
    运行数据处理
    """
    try:
        from data_processor import DataProcessor
        
        print(f"正在处理数据文件: {data_file}")
        
        processor = DataProcessor(data_file)
        
        if processor.load_data():
            if processor.preprocess_data():
                processor.plot_raw_data('raw_data_analysis.png')
                training_data = processor.get_training_data()
                
                if training_data:
                    print("数据处理完成!")
                    print(f"  应变范围: {training_data['strain_min']:.6f} ~ {training_data['strain_max']:.6f}")
                    print(f"  应力范围: {training_data['stress_min']:.2f} ~ {training_data['stress_max']:.2f} MPa")
                    print(f"  数据点数: {len(training_data['strain'])}")
                    return True
        
        print("数据处理失败!")
        return False
        
    except Exception as e:
        print(f"数据处理出错: {e}")
        return False

def run_training(args):
    """
    运行模型训练
    """
    try:
        import torch
        from data_processor import DataProcessor
        from pinn_model import ConstitutivePINN, PINNTrainer
        
        print(f"开始训练模型...")
        print(f"  训练轮数: {args.epochs}")
        print(f"  学习率: {args.lr}")
        print(f"  网络结构: {args.hidden_size} x {args.num_layers}")
        
        # 数据处理
        processor = DataProcessor(args.data)
        if not processor.load_data() or not processor.preprocess_data():
            print("数据加载失败!")
            return False
        
        training_data = processor.get_training_data()
        if not training_data:
            print("训练数据获取失败!")
            return False
        
        # 转换为张量
        strain_tensor = torch.tensor(training_data['strain_normalized'], dtype=torch.float32).reshape(-1, 1)
        stress_tensor = torch.tensor(training_data['stress_normalized'], dtype=torch.float32).reshape(-1, 1)
        
        # 创建模型
        model = ConstitutivePINN(hidden_size=args.hidden_size, num_layers=args.num_layers)
        trainer = PINNTrainer(model, learning_rate=args.lr)
        
        # 训练
        trainer.train(
            strain_tensor, 
            stress_tensor, 
            epochs=args.epochs,
            physics_weight=1.0,
            data_weight=10.0,
            print_interval=max(1, args.epochs // 10),
            save_path=args.model
        )
        
        # 绘制训练历史
        trainer.plot_training_history('training_history.png')
        
        print("模型训练完成!")
        return True
        
    except Exception as e:
        print(f"训练出错: {e}")
        return False

def run_prediction(model_file):
    """
    运行模型预测
    """
    try:
        from predict import PINNPredictor
        
        print(f"加载模型: {model_file}")
        
        if not os.path.exists(model_file):
            print(f"模型文件不存在: {model_file}")
            print("请先运行训练模式生成模型文件")
            return False
        
        predictor = PINNPredictor(model_file)
        
        if predictor.load_model():
            # 设置数据统计信息（示例值）
            predictor.set_data_stats(
                strain_min=0.0,
                strain_max=0.01,
                stress_min=0.0,
                stress_max=300.0
            )
            
            # 材料响应分析
            predictor.analyze_material_response(
                strain_range=(0, 0.012),
                save_path='material_response_analysis.png'
            )
            
            print("预测分析完成!")
            return True
        
        print("模型加载失败!")
        return False
        
    except Exception as e:
        print(f"预测出错: {e}")
        return False

def check_dependencies():
    """
    检查依赖包
    """
    required_packages = [
        'torch', 'numpy', 'pandas', 'matplotlib', 'openpyxl'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装:")
        print("pip install -r requirements.txt")
        return False
    
    return True

if __name__ == "__main__":
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 运行主程序
    main()