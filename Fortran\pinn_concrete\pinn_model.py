import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
import os

class Swish(nn.Module):
    """
    Swish激活函数: x * sigmoid(x)
    比ReLU有更好的平滑性和梯度特性
    """
    def forward(self, x):
        return x * torch.sigmoid(x)

class Mish(nn.Module):
    """
    Mish激活函数: x * tanh(softplus(x))
    比ReLU和Swish有更好的性能
    """
    def forward(self, x):
        return x * torch.tanh(F.softplus(x))

class PINN(nn.Module):
    def __init__(self, hidden_layers=8, hidden_units=384, history_length=15):
        """
        初始化增强版物理信息神经网络模型，添加滞回特性建模能力
        
        参数:
        hidden_layers: 隐藏层数量
        hidden_units: 每层隐藏单元数量
        history_length: 历史状态跟踪长度
        """
        super(PINN, self).__init__()
        
        # 历史长度参数 - 显著增加历史长度以更好地捕捉滞回特性
        self.history_length = history_length
        
        # 输入特征维度 = 当前应变 + 历史应变 + 应变增量 + 加载方向 + 历史加载方向 + 累积应变 + 应变加速度 + 循环计数
        input_dim = 1 + history_length + 1 + 1 + history_length + 1 + 1 + 1
        
        # 输入层 - 增强特征提取能力
        self.input_layer = nn.Sequential(
            nn.Linear(input_dim, hidden_units),
            nn.LayerNorm(hidden_units),
            Mish(),  # 使用Mish激活函数提高非线性表达能力
            nn.Dropout(0.15)  # 增加dropout以防止过拟合
        )
        
        # 滞回特性捕捉层 - 使用更深的LSTM网络
        self.hysteresis_layer = nn.LSTM(
            input_size=hidden_units,
            hidden_size=hidden_units,
            num_layers=4,  # 增加层数以捕捉更复杂的时序依赖
            batch_first=True,
            dropout=0.2,
            bidirectional=True  # 使用双向LSTM捕捉前后文信息
        )
        
        # LSTM输出调整层 - 合并双向输出
        self.lstm_adapter = nn.Linear(hidden_units * 2, hidden_units)
        
        # 滞回记忆门控单元 - 专门用于记忆加载历史
        self.memory_gate = nn.Sequential(
            nn.Linear(hidden_units + 2, hidden_units),  # +2表示加入当前应变和加载方向
            nn.Sigmoid()
        )
        
        # 滞回记忆更新单元
        self.memory_update = nn.Sequential(
            nn.Linear(hidden_units + 2, hidden_units),
            nn.Tanh()
        )
        
        # 隐藏层 - 使用更深的网络结构和更先进的激活函数
        self.hidden_layers = nn.ModuleList()
        for i in range(hidden_layers):
            layer = nn.Sequential(
                nn.Linear(hidden_units, hidden_units),
                nn.LayerNorm(hidden_units),
                Mish(),
                nn.Dropout(0.2 if i < hidden_layers-1 else 0.1)  # 逐渐减小dropout
            )
            self.hidden_layers.append(layer)
        
        # 残差连接 - 帮助深层网络训练
        self.skip_connections = nn.ModuleList()
        if hidden_layers > 1:
            for i in range(hidden_layers-1):
                skip = nn.Sequential(
                    nn.Linear(hidden_units, hidden_units),
                    nn.LayerNorm(hidden_units)
                )
                self.skip_connections.append(skip)
        
        # 滞回特性专用层 - 显式建模滞回行为
        self.hysteresis_specific = nn.Sequential(
            nn.Linear(hidden_units, hidden_units),
            nn.LayerNorm(hidden_units),
            Mish(),
            nn.Dropout(0.1),
            nn.Linear(hidden_units, hidden_units),
            nn.LayerNorm(hidden_units),
            Mish(),
            nn.Linear(hidden_units, hidden_units // 2)
        )
        
        # 循环加载检测层 - 专门用于识别循环加载模式
        self.cycle_detection = nn.Sequential(
            nn.Linear(history_length * 2, 64),  # 历史应变和历史加载方向
            nn.LayerNorm(64),
            Mish(),
            nn.Linear(64, 32),
            nn.LayerNorm(32),
            Mish(),
            nn.Linear(32, 16),
            nn.Tanh()
        )
        
        # Bouc-Wen参数层 - 用于物理约束
        self.bouc_wen_params = nn.Sequential(
            nn.Linear(hidden_units + 16, 128),  # 增加中间层宽度，并融合循环检测信息
            nn.LayerNorm(128),
            Mish(),
            nn.Dropout(0.15),
            nn.Linear(128, 64),
            nn.LayerNorm(64),
            Mish(),
            nn.Linear(64, 5)  # [alpha, beta, gamma, A, n]
        )
        
        # 输出层 - 添加额外处理以提高精度
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_units + hidden_units // 2 + 16, 128),  # 合并主干特征、滞回特征和循环检测特征
            nn.LayerNorm(128),
            Mish(),
            nn.Dropout(0.15),
            nn.Linear(128, 64),
            nn.LayerNorm(64),
            Mish(),
            nn.Linear(64, 3)  # [应力, 损伤变量, 滞回变量z]
        )
        
        # 初始化隐藏状态和记忆单元
        self.hidden_state = None
        self.cell_state = None
        self.hysteresis_memory = None
    
    def forward(self, x):
        """
        前向传播
        
        参数:
        x: 输入张量 [batch_size, features] 包含当前应变、历史应变、应变增量、加载方向等特征
        
        返回:
        output: 输出张量 [应力, 损伤变量, 滞回变量z]
        bouc_wen_params: Bouc-Wen模型参数 [alpha, beta, gamma, A, n]
        """
        batch_size = x.size(0)
        
        # 提取当前应变和加载方向 - 用于滞回记忆门控
        current_strain = x[:, 0:1]  # 当前应变
        loading_direction = x[:, 1 + self.history_length + 1:1 + self.history_length + 2]  # 加载方向
        history_strains = x[:, 1:1+self.history_length]  # 历史应变
        history_directions = x[:, 1+self.history_length+2:1+self.history_length*2+2]  # 历史加载方向
        
        # 输入层
        features = self.input_layer(x)
        
        # 滞回特性捕捉层 - 使用LSTM处理序列信息
        # 重塑输入以适应LSTM [batch, seq_len=1, features]
        features_reshaped = features.unsqueeze(1)
        
        # 如果是第一次调用或批次大小变化，初始化隐藏状态和细胞状态
        if self.hidden_state is None or self.hidden_state.size(1) != batch_size:
            # 对于双向LSTM，第一维是num_layers * num_directions
            self.hidden_state = torch.zeros(4 * 2, batch_size, features.size(1), 
                                           device=x.device)
            self.cell_state = torch.zeros(4 * 2, batch_size, features.size(1), 
                                         device=x.device)
            self.hysteresis_memory = torch.zeros(batch_size, features.size(1), 
                                              device=x.device)
        
        # 前向传播LSTM
        lstm_out, (self.hidden_state, self.cell_state) = self.hysteresis_layer(
            features_reshaped, (self.hidden_state, self.cell_state)
        )
        
        # 重塑输出 [batch, features*2]（因为是双向LSTM）
        features = lstm_out.squeeze(1)
        
        # 通过适配层调整LSTM输出维度
        features = self.lstm_adapter(features)
        
        # 滞回记忆机制 - 使用门控单元更新记忆
        memory_input = torch.cat([features, current_strain, loading_direction], dim=1)
        memory_gate_value = self.memory_gate(memory_input)
        memory_update_value = self.memory_update(memory_input)
        
        # 更新滞回记忆 - 类似LSTM的记忆单元更新机制
        self.hysteresis_memory = memory_gate_value * self.hysteresis_memory + \
                               (1 - memory_gate_value) * memory_update_value
        
        # 循环加载检测 - 分析历史应变和加载方向模式
        cycle_input = torch.cat([history_strains, history_directions], dim=1)
        cycle_features = self.cycle_detection(cycle_input)
        
        # 隐藏层（带残差连接）
        for i, layer in enumerate(self.hidden_layers):
            residual = features
            features = layer(features)
            
            # 添加残差连接（如果有）
            if i < len(self.skip_connections):
                features = features + self.skip_connections[i](residual)
        
        # 滞回特性专用处理 - 融合记忆信息
        features_with_memory = features + 0.3 * self.hysteresis_memory  # 添加记忆影响
        hysteresis_features = self.hysteresis_specific(features_with_memory)
        
        # 计算Bouc-Wen参数 - 融合循环检测特征
        bouc_wen_input = torch.cat([features, cycle_features], dim=1)
        bouc_wen_raw = self.bouc_wen_params(bouc_wen_input)
        
        # 应用参数约束 - 调整参数范围以增强滞回效应
        alpha = torch.sigmoid(bouc_wen_raw[:, 0:1]) * 0.7 + 0.15  # 范围[0.15,0.85]，更适合滞回模型
        beta = 0.8 + torch.abs(bouc_wen_raw[:, 1:2]) * 0.8      # 范围[0.8,~]，显著增强滞回效应
        gamma = 0.5 + torch.abs(bouc_wen_raw[:, 2:3]) * 1.0     # 范围[0.5,~]，显著增强滞回效应
        A = 1.0 + torch.abs(bouc_wen_raw[:, 3:4]) * 0.5         # 范围[1.0,~]，增强滞回效应
        n = 1.5 + torch.abs(bouc_wen_raw[:, 4:5])               # 范围[1.5,~]，增强非线性
        
        # 合并主干特征、滞回特性特征和循环检测特征
        combined_features = torch.cat([features, hysteresis_features, cycle_features], dim=1)
        
        # 输出层
        output = self.output_layer(combined_features)
        
        # 返回输出和Bouc-Wen参数
        return output, torch.cat([alpha, beta, gamma, A, n], dim=1)
    
    def reset_state(self):
        """
        重置LSTM隐藏状态和细胞状态，用于新序列的开始
        """
        self.hidden_state = None
        self.cell_state = None

class PINNTrainer:
    def __init__(self, model, E=30000.0, eps0=0.0001, k=100.0, device='cpu', history_length=None):
        """
        初始化增强版PINN训练器，添加滞回特性建模能力
        
        参数:
        model: PINN模型实例
        E: 弹性模量 (MPa)
        eps0: 损伤起始应变阈值
        k: 损伤演化参数
        device: 计算设备 ('cpu' 或 'cuda')
        history_length: 历史状态跟踪长度，如果为None则使用模型的history_length
        """
        self.model = model
        self.E = E  # 弹性模量 (MPa)
        self.eps0 = eps0  # 损伤起始应变阈值
        self.k = k  # 损伤演化参数
        self.device = device
        # 使用模型的history_length，确保一致性
        self.history_length = history_length if history_length is not None else model.history_length
        self.model.to(device)
        
        # Bouc-Wen模型参数默认值
        self.default_alpha = 0.5  # 线性刚度占比
        self.default_beta = 0.5   # 滞回形状参数
        self.default_gamma = 0.5  # 滞回形状参数
        self.default_A = 1.0      # 滞回形状参数
        self.default_n = 1.0      # 滞回形状参数
        
        # 训练历史记录 - 增加更多监控指标
        self.history = {
            'epoch': [],
            'train_loss': [],
            'val_loss': [],
            'data_loss': [],
            'physics_loss': [],
            'damage_loss': [],
            'smoothness_loss': [],
            'hysteresis_loss': [],
            'bouc_wen_loss': [],
            'learning_rate': []
        }
    
    def constitutive_stress(self, strain, damage):
        """
        根据本构关系计算应力
        σ = (1 - d) * E * ε
        
        参数:
        strain: 应变张量
        damage: 损伤变量张量
        
        返回:
        stress: 应力张量
        """
        return (1 - damage) * self.E * strain
    
    def damage_evolution(self, strain):
        """
        根据损伤演化规律计算损伤变量
        d = 1 - exp(-k * (ε - ε0)^+)
        
        参数:
        strain: 应变张量
        
        返回:
        damage: 损伤变量张量
        """
        # 使用ReLU确保只有当应变大于阈值时才计算损伤
        strain_diff = torch.relu(strain - self.eps0)
        return 1 - torch.exp(-self.k * strain_diff)
    
    def prepare_history_features(self, strain):
        """
        准备增强的历史特征输入，包括历史应变、应变增量、加载方向、历史加载方向、累积应变、应变加速度和循环计数
        
        参数:
        strain: 原始应变张量 [batch_size, 1]
        
        返回:
        history_features: 增强的特征张量 [batch_size, 1+history_length+1+1+history_length+1+1+1]
        """
        batch_size = strain.size(0)
        
        # 1. 计算应变增量
        strain_increments = torch.zeros_like(strain)
        if batch_size > 1:
            strain_increments[1:] = strain[1:] - strain[:-1]
        
        # 2. 计算加载方向 (1:加载, -1:卸载, 0:初始点)
        loading_direction = torch.zeros_like(strain)
        if batch_size > 1:
            loading_direction[1:] = torch.sign(strain_increments[1:])
        
        # 3. 准备历史应变 (使用零填充)
        history_strains = torch.zeros((batch_size, self.history_length), device=self.device)
        for i in range(batch_size):
            for j in range(self.history_length):
                if i - j - 1 >= 0:
                    history_strains[i, j] = strain[i - j - 1, 0]
        
        # 4. 准备历史加载方向 (使用零填充)
        history_directions = torch.zeros((batch_size, self.history_length), device=self.device)
        for i in range(batch_size):
            for j in range(self.history_length):
                if i - j - 1 >= 0 and i - j < batch_size:
                    history_directions[i, j] = loading_direction[i - j - 1, 0]
        
        # 5. 计算累积应变 - 捕捉加载历史
        cumulative_strain = torch.zeros_like(strain)
        if batch_size > 1:
            # 累积绝对应变增量
            abs_increments = torch.abs(strain_increments)
            cumulative_strain = torch.cumsum(abs_increments, dim=0)
        
        # 6. 计算应变变化率 - 二阶导数特征
        strain_acceleration = torch.zeros_like(strain)
        if batch_size > 2:
            # 使用中心差分计算二阶导数
            strain_acceleration[1:-1] = strain[2:] - 2*strain[1:-1] + strain[:-2]
        
        # 7. 计算循环计数 - 检测加载方向变化次数
        cycle_count = torch.zeros_like(strain)
        if batch_size > 2:
            # 计算加载方向变化
            direction_changes = torch.zeros_like(loading_direction)
            direction_changes[1:] = torch.abs(loading_direction[1:] - loading_direction[:-1]) / 2.0
            # 累积方向变化次数
            cycle_count = torch.cumsum(direction_changes, dim=0)
        
        # 8. 组合所有特征
        history_features = torch.cat([
            strain,                          # 当前应变 [batch_size, 1]
            history_strains,                 # 历史应变 [batch_size, history_length]
            strain_increments,               # 应变增量 [batch_size, 1]
            loading_direction,               # 加载方向 [batch_size, 1]
            history_directions,              # 历史加载方向 [batch_size, history_length]
            cumulative_strain,               # 累积应变 [batch_size, 1]
            strain_acceleration,             # 应变加速度 [batch_size, 1]
            cycle_count                      # 循环计数 [batch_size, 1]
        ], dim=1)
        
        return history_features
    
    def compute_bouc_wen_residual(self, strain, strain_increments, pred_z, bouc_wen_params):
        """
        计算Bouc-Wen模型残差
        
        参数:
        strain: 应变张量
        strain_increments: 应变增量张量
        pred_z: 预测的滞回变量
        bouc_wen_params: Bouc-Wen模型参数 [alpha, beta, gamma, A, n]
        
        返回:
        bouc_wen_residual: Bouc-Wen模型残差
        """
        # 为了避免反向传播问题，我们将使用detach()创建张量的副本
        # 这样可以防止多次反向传播导致的错误
        with torch.no_grad():
            # 确保所有输入张量维度一致
            batch_size = min(strain.size(0), strain_increments.size(0), pred_z.size(0), bouc_wen_params.size(0))
            
            # 截取所有张量，使其长度一致
            strain = strain[:batch_size]
            strain_increments = strain_increments[:batch_size]
            pred_z = pred_z[:batch_size]
            bouc_wen_params = bouc_wen_params[:batch_size]
            
            # 提取Bouc-Wen参数
            alpha = bouc_wen_params[:, 0:1]
            beta = bouc_wen_params[:, 1:2]
            gamma = bouc_wen_params[:, 2:3]
            A = bouc_wen_params[:, 3:4]
            n = bouc_wen_params[:, 4:5]
            
            # 计算滞回变量残差: dz/dx - (A - beta*sign(dx)*|z|^n - gamma*|z|^n)
            sign_dx = torch.sign(strain_increments)
            dz_dx_physics = A - beta * sign_dx * torch.abs(pred_z)**n - gamma * torch.abs(pred_z)**n
            
            # 计算z对x的近似导数 (使用有限差分)
            dz_dx_approx = torch.zeros_like(pred_z)
            
            # 使用前向差分计算dz/dx
            if batch_size > 1:
                # 计算相邻z值的差分
                z_diff = torch.zeros_like(pred_z)
                z_diff[:-1] = pred_z[1:] - pred_z[:-1]
                z_diff[-1] = z_diff[-2]  # 对最后一个点使用前一个差分值
                
                # 计算dz/dx
                dz_dx_approx = z_diff / (strain_increments + 1e-8)
            
            # 计算残差
            bouc_wen_residual = torch.abs(dz_dx_approx - dz_dx_physics)
            residual_value = torch.mean(bouc_wen_residual).item()
        
        # 返回一个不参与反向传播的常量值
        return torch.tensor(residual_value, device=self.device, requires_grad=False)
    
    def compute_loss(self, strain, target_stress, lambda1=1.0, lambda2=1.0, lambda3=0.05, lambda4=2.0, lambda5=2.0, lambda6=1.5):
        """
        计算增强版总损失函数，添加更多物理约束和滞回特性建模
        
        参数:
        strain: 输入应变张量
        target_stress: 目标应力张量
        lambda1: 物理损失权重
        lambda2: 损伤损失权重
        lambda3: 平滑约束权重
        lambda4: 滞回特性约束权重
        lambda5: Bouc-Wen模型约束权重
        lambda6: 循环路径一致性约束权重
        
        返回:
        total_loss: 总损失
        data_loss: 数据损失
        physics_loss: 物理损失
        damage_loss: 损伤损失
        smoothness_loss: 平滑约束损失
        hysteresis_loss: 滞回特性约束损失
        bouc_wen_loss: Bouc-Wen模型约束损失
        """
        # 每次计算损失前重置隐藏状态，避免多次反向传播问题
        self.model.reset_state()
        
        # 准备历史特征输入
        history_features = self.prepare_history_features(strain)
        
        # 前向传播获取预测值
        pred, bouc_wen_params = self.model(history_features)
        pred_stress, pred_damage, pred_z = pred[:, 0:1], pred[:, 1:2], pred[:, 2:3]
        
        # 计算本构应力 - 结合Bouc-Wen模型
        alpha = bouc_wen_params[:, 0:1]
        beta = bouc_wen_params[:, 1:2]
        gamma = bouc_wen_params[:, 2:3]
        A = bouc_wen_params[:, 3:4]
        n = bouc_wen_params[:, 4:5]
        
        # 增强的本构模型 - 更好地捕捉滞回特性
        constitutive_stress = alpha * self.E * strain * (1 - pred_damage) + (1 - alpha) * self.E * pred_z
        
        # 计算损伤演化
        damage_evolution = self.damage_evolution(torch.abs(strain))  # 使用绝对应变计算损伤
        
        # 基本损失项 - 使用加权MSE和L1损失
        mse = nn.MSELoss()
        l1_loss = nn.L1Loss()
        
        # 增强数据损失 - 更加关注滞回曲线的形状
        data_loss = 0.4 * mse(pred_stress, target_stress) + 0.6 * l1_loss(pred_stress, target_stress)
        
        # 物理损失 - 确保模型遵循物理规律
        physics_loss = mse(pred_stress, constitutive_stress)
        
        # 损伤损失 - 确保损伤演化符合物理规律
        damage_loss = mse(pred_damage, damage_evolution)
        
        # 平滑约束 - 确保应力和损伤变化平滑
        smoothness_loss = torch.tensor(0.0, device=self.device)
        if strain.size(0) > 2:
            # 计算应力的二阶差分
            stress_second_diff = pred_stress[2:] - 2 * pred_stress[1:-1] + pred_stress[:-2]
            # 计算损伤的二阶差分
            damage_second_diff = pred_damage[2:] - 2 * pred_damage[1:-1] + pred_damage[:-2]
            # 综合平滑损失
            smoothness_loss = torch.mean(torch.abs(stress_second_diff)) + torch.mean(torch.abs(damage_second_diff))
        
        # 计算应变增量
        strain_increments = torch.zeros_like(strain)
        if strain.size(0) > 1:
            strain_increments[1:] = strain[1:] - strain[:-1]
        
        # 计算加载方向
        loading_direction = torch.zeros_like(strain)
        if strain.size(0) > 1:
            loading_direction[1:] = torch.sign(strain_increments[1:])
        
        # 增强的滞回特性约束 - 直接在计算图中实现
        hysteresis_loss = torch.tensor(0.0, device=self.device)
        
        # 滞回特性约束 - 鼓励在加载和卸载过程中产生不同的应力路径
        if strain.size(0) > 10:  # 确保有足够的数据点
            # 分离加载和卸载点
            loading_mask = (loading_direction > 0).float()
            unloading_mask = (loading_direction < 0).float()
            
            # 确保有足够的加载和卸载点
            if torch.sum(loading_mask) > 5 and torch.sum(unloading_mask) > 5:
                # 计算加载和卸载路径的应力-应变斜率
                loading_points = (loading_mask * pred_stress) / (loading_mask * strain + 1e-8)
                unloading_points = (unloading_mask * pred_stress) / (unloading_mask * strain + 1e-8)
                
                # 计算加载和卸载路径的平均斜率
                loading_slope = torch.sum(loading_mask * loading_points) / (torch.sum(loading_mask) + 1e-8)
                unloading_slope = torch.sum(unloading_mask * unloading_points) / (torch.sum(unloading_mask) + 1e-8)
                
                # 鼓励加载和卸载路径有不同的斜率 - 增大路径差异
                path_diff = torch.abs(loading_slope - unloading_slope)
                hysteresis_loss = -8.0 * path_diff  # 负号使得路径差异越大，损失越小，增大系数
                
                # 添加路径形状约束 - 鼓励加载和卸载路径形成闭合的滞回环
                # 计算加载和卸载路径的曲率
                if torch.sum(loading_mask) > 10 and torch.sum(unloading_mask) > 10:
                    # 提取加载和卸载路径的应力和应变
                    loading_strain = strain * loading_mask
                    loading_stress = pred_stress * loading_mask
                    unloading_strain = strain * unloading_mask
                    unloading_stress = pred_stress * unloading_mask
                    
                    # 计算路径闭合度 - 鼓励滞回环的闭合
                    # 找到加载和卸载路径的起点和终点
                    loading_start_idx = torch.nonzero(loading_mask).min().item() if torch.sum(loading_mask) > 0 else 0
                    loading_end_idx = torch.nonzero(loading_mask).max().item() if torch.sum(loading_mask) > 0 else 0
                    unloading_start_idx = torch.nonzero(unloading_mask).min().item() if torch.sum(unloading_mask) > 0 else 0
                    unloading_end_idx = torch.nonzero(unloading_mask).max().item() if torch.sum(unloading_mask) > 0 else 0
                    
                    # 计算路径闭合度损失
                    if loading_end_idx > 0 and unloading_start_idx > 0:
                        path_closure_loss = torch.abs(pred_stress[loading_end_idx] - pred_stress[unloading_start_idx])
                        hysteresis_loss = hysteresis_loss - 2.0 * torch.mean(path_closure_loss)
        
        # Bouc-Wen模型约束 - 确保滞回变量z的演化符合Bouc-Wen模型
        bouc_wen_loss = torch.tensor(0.0, device=self.device)
        
        if strain.size(0) > 2:
            # 计算z对应变的导数 (使用有限差分)
            dz_dstrain = torch.zeros_like(pred_z)
            dz_dstrain[1:-1] = (pred_z[2:] - pred_z[:-2]) / (strain[2:] - strain[:-2] + 1e-8)
            
            # 计算Bouc-Wen模型预测的dz/dstrain
            sign_dstrain = torch.sign(strain_increments)
            dz_dstrain_model = A - (beta * sign_dstrain * torch.abs(pred_z)**n + gamma * torch.abs(pred_z)**n)
            
            # 计算Bouc-Wen模型约束损失
            valid_indices = torch.abs(strain_increments) > 1e-6  # 只在应变变化明显的地方计算
            if torch.sum(valid_indices[1:-1]) > 0:
                bouc_wen_loss = mse(dz_dstrain[1:-1][valid_indices[1:-1]], 
                                    dz_dstrain_model[1:-1][valid_indices[1:-1]])
        
        # 循环路径一致性约束 - 确保相似的加载历史产生相似的应力响应
        cycle_consistency_loss = torch.tensor(0.0, device=self.device)
        
        if strain.size(0) > 20:  # 确保有足够的数据点进行循环分析
            # 寻找相似的加载历史段
            for i in range(10, strain.size(0) - 10):
                for j in range(i + 5, strain.size(0) - 5):
                    # 计算历史窗口的相似度
                    window_i = strain[i-5:i+5]
                    window_j = strain[j-5:j+5]
                    similarity = torch.mean(torch.abs(window_i - window_j))
                    
                    # 如果历史窗口相似，则应力响应也应相似
                    if similarity < 0.05:  # 相似度阈值
                        stress_i = pred_stress[i]
                        stress_j = pred_stress[j]
                        cycle_consistency_loss = cycle_consistency_loss + torch.abs(stress_i - stress_j)
            
            # 归一化循环一致性损失
            if cycle_consistency_loss > 0:
                cycle_consistency_loss = cycle_consistency_loss / strain.size(0)
        
        # 计算总损失 - 增加滞回特性和Bouc-Wen模型约束的权重
        total_loss = data_loss + lambda1 * physics_loss + lambda2 * damage_loss + \
                     lambda3 * smoothness_loss + lambda4 * hysteresis_loss + \
                     lambda5 * bouc_wen_loss + lambda6 * cycle_consistency_loss
        
        return total_loss, data_loss, physics_loss, damage_loss, smoothness_loss, hysteresis_loss, bouc_wen_loss
    
    def train(self, train_loader, val_loader, optimizer, num_epochs=3000, 
              lambda1_schedule=None, lambda2_schedule=None, lambda3_schedule=None, lambda4_schedule=None, lambda5_schedule=None, lambda6_schedule=None,
              checkpoint_dir='./checkpoints', save_freq=100, patience=150):
        """
        训练增强版PINN模型，添加学习率调度和早停机制，增强滞回特性建模
        
        参数:
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        optimizer: 优化器
        num_epochs: 训练轮数
        lambda1_schedule: 物理损失权重调度函数
        lambda2_schedule: 损伤损失权重调度函数
        lambda3_schedule: 平滑约束权重调度函数
        lambda4_schedule: 滞回特性约束权重调度函数
        lambda5_schedule: Bouc-Wen模型约束权重调度函数
        checkpoint_dir: 模型检查点保存目录
        save_freq: 保存模型的频率(每多少个epoch保存一次)
        patience: 早停耐心值，如果验证损失在这么多epoch内没有改善，则停止训练
        
        返回:
        history: 训练历史记录
        """
        import time
        
        # 创建检查点目录
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # 默认权重调度 - 显著增强滞回特性建模
        if lambda1_schedule is None:
            # 物理损失权重随训练进行逐渐增加
            lambda1_schedule = lambda epoch: min(2.0, 0.5 + epoch / 500)
        if lambda2_schedule is None:
            # 损伤损失权重保持适中
            lambda2_schedule = lambda epoch: 1.0
        if lambda3_schedule is None:
            # 平滑约束权重保持较小
            lambda3_schedule = lambda epoch: 0.05
        if lambda4_schedule is None:
            # 滞回特性约束权重随训练进行显著增加 - 大幅提高权重
            lambda4_schedule = lambda epoch: min(5.0, 1.0 + epoch / 200)
        if lambda5_schedule is None:
            # Bouc-Wen模型约束权重随训练进行逐渐增加 - 提高权重
            lambda5_schedule = lambda epoch: min(3.0, 1.0 + epoch / 300)
        if lambda6_schedule is None:
            # 循环路径一致性约束权重 - 训练后期增加
            lambda6_schedule = lambda epoch: min(2.5, 0.5 + epoch / 400)
        
        # 学习率调度器 - 使用带有热重启的余弦退火调度，更长的周期和更慢的衰减
        scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer, T_0=200, T_mult=2, eta_min=5e-7
        )
        
        # 早停机制
        best_val_loss = float('inf')
        patience_counter = 0
        best_model_path = os.path.join(checkpoint_dir, "best_pinn_model.pth")
        
        # 记录训练开始时间
        start_time = time.time()
        
        for epoch in range(1, num_epochs + 1):
            # 设置模型为训练模式
            self.model.train()
            
            # 获取当前epoch的损失权重
            lambda1 = lambda1_schedule(epoch)
            lambda2 = lambda2_schedule(epoch)
            lambda3 = lambda3_schedule(epoch)
            lambda4 = lambda4_schedule(epoch)
            lambda5 = lambda5_schedule(epoch)
            lambda6 = lambda6_schedule(epoch)
            
            # 训练一个epoch
            train_total_loss = 0.0
            train_data_loss = 0.0
            train_physics_loss = 0.0
            train_damage_loss = 0.0
            train_smoothness_loss = 0.0
            train_hysteresis_loss = 0.0
            train_bouc_wen_loss = 0.0
            
            for strain, target_stress in train_loader:
                strain = strain.to(self.device)
                target_stress = target_stress.to(self.device)
                
                # 计算损失
                optimizer.zero_grad()
                loss, data_loss, physics_loss, damage_loss, smoothness_loss, hysteresis_loss, bouc_wen_loss = self.compute_loss(
                    strain, target_stress, lambda1, lambda2, lambda3, lambda4, lambda5, lambda6
                )
                
                # 反向传播和优化
                loss.backward()
                
                # 梯度裁剪，防止梯度爆炸
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                optimizer.step()
                
                # 累加损失
                train_total_loss += loss.item()
                train_data_loss += data_loss.item()
                train_physics_loss += physics_loss.item()
                train_damage_loss += damage_loss.item()
                train_smoothness_loss += smoothness_loss.item()
                train_hysteresis_loss += hysteresis_loss.item()
                train_bouc_wen_loss += bouc_wen_loss.item()
            
            # 更新学习率
            scheduler.step()
            current_lr = optimizer.param_groups[0]['lr']
            
            # 计算平均训练损失
            train_total_loss /= len(train_loader)
            train_data_loss /= len(train_loader)
            train_physics_loss /= len(train_loader)
            train_damage_loss /= len(train_loader)
            train_smoothness_loss /= len(train_loader)
            train_hysteresis_loss /= len(train_loader)
            train_bouc_wen_loss /= len(train_loader)
            
            # 验证
            self.model.eval()
            val_total_loss = 0.0
            val_data_loss = 0.0
            val_physics_loss = 0.0
            val_damage_loss = 0.0
            val_smoothness_loss = 0.0
            val_hysteresis_loss = 0.0
            val_bouc_wen_loss = 0.0
            
            with torch.no_grad():
                for strain, target_stress in val_loader:
                    strain = strain.to(self.device)
                    target_stress = target_stress.to(self.device)
                    
                    # 计算验证损失
                    loss, data_loss, physics_loss, damage_loss, smoothness_loss, hysteresis_loss, bouc_wen_loss = self.compute_loss(
                        strain, target_stress, lambda1, lambda2, lambda3, lambda4, lambda5, lambda6
                    )
                    val_total_loss += loss.item()
                    val_data_loss += data_loss.item()
                    val_physics_loss += physics_loss.item()
                    val_damage_loss += damage_loss.item()
                    val_smoothness_loss += smoothness_loss.item()
                    val_hysteresis_loss += hysteresis_loss.item()
                    val_bouc_wen_loss += bouc_wen_loss.item()
            
            # 计算平均验证损失
            val_total_loss /= len(val_loader)
            val_data_loss /= len(val_loader)
            val_physics_loss /= len(val_loader)
            val_damage_loss /= len(val_loader)
            val_smoothness_loss /= len(val_loader)
            val_hysteresis_loss /= len(val_loader)
            
            # 记录训练历史
            self.history['epoch'].append(epoch)
            self.history['train_loss'].append(train_total_loss)
            self.history['val_loss'].append(val_total_loss)
            self.history['data_loss'].append(train_data_loss)
            self.history['physics_loss'].append(train_physics_loss)
            self.history['damage_loss'].append(train_damage_loss)
            self.history['smoothness_loss'].append(train_smoothness_loss)
            self.history['hysteresis_loss'].append(train_hysteresis_loss)
            self.history['learning_rate'].append(current_lr)
            
            # 打印训练进度
            if epoch % 10 == 0 or epoch == 1:
                elapsed_time = time.time() - start_time
                print(f"Epoch {epoch}/{num_epochs} - "
                      f"Train Loss: {train_total_loss:.6f}, "
                      f"Val Loss: {val_total_loss:.6f}, "
                      f"Data Loss: {train_data_loss:.6f}, "
                      f"Physics Loss: {train_physics_loss:.6f}, "
                      f"Damage Loss: {train_damage_loss:.6f}, "
                      f"Smoothness: {train_smoothness_loss:.6f}, "
                      f"Hysteresis: {train_hysteresis_loss:.6f}, "
                      f"LR: {current_lr:.6f}, "
                      f"Time: {elapsed_time:.1f}s")
            
            # 早停检查
            if val_total_loss < best_val_loss:
                best_val_loss = val_total_loss
                patience_counter = 0
                
                # 保存最佳模型
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'scheduler_state_dict': scheduler.state_dict(),
                    'train_loss': train_total_loss,
                    'val_loss': val_total_loss,
                    'history': self.history
                }, best_model_path)
                print(f"Epoch {epoch}: 保存最佳模型，验证损失: {best_val_loss:.6f}")
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    print(f"早停: {patience}个epoch内验证损失没有改善")
                    break
            
            # 保存检查点
            if epoch % save_freq == 0 or epoch == num_epochs:
                checkpoint_path = os.path.join(checkpoint_dir, f"pinn_model_epoch_{epoch}.pth")
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'scheduler_state_dict': scheduler.state_dict(),
                    'train_loss': train_total_loss,
                    'val_loss': val_total_loss,
                    'history': self.history
                }, checkpoint_path)
                print(f"Checkpoint saved to {checkpoint_path}")
        
        # 加载最佳模型
        checkpoint = torch.load(best_model_path)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        
        # 保存最终模型
        final_model_path = os.path.join(checkpoint_dir, "pinn_model.pth")
        torch.save({
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'train_loss': train_total_loss,
            'val_loss': val_total_loss,
            'history': self.history
        }, final_model_path)
        print(f"Final model saved to {final_model_path}")
        
        # 计算总训练时间
        total_time = time.time() - start_time
        print(f"总训练时间: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")
        
        return self.history
    
    def plot_history(self, save_path=None):
        """
        绘制增强版训练历史，包含更多监控指标
        
        参数:
        save_path: 保存图像的路径，如果为None则显示图像
        """
        # 配置中文字体
        try:
            from font_config import configure_chinese_font
            configure_chinese_font()
        except ImportError:
            print("警告: 未找到font_config模块，中文显示可能不正确")
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'Arial']
            plt.rcParams['axes.unicode_minus'] = False
        
        if not self.history:
            print("没有训练历史数据")
            return False
        
        if not self.history['epoch']:
            print("没有训练历史记录")
            return False
        
        # 创建图像 - 使用3个子图展示更多信息
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 15))
        
        # 绘制总损失曲线
        ax1.plot(self.history['epoch'], self.history['train_loss'], 'b-', label='训练损失')
        ax1.plot(self.history['epoch'], self.history['val_loss'], 'r-', label='验证损失')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('总损失')
        ax1.set_title('训练和验证损失')
        ax1.legend()
        ax1.grid(True)
        
        # 绘制物理损失项曲线
        ax2.plot(self.history['epoch'], self.history['data_loss'], 'g-', label='数据损失')
        ax2.plot(self.history['epoch'], self.history['physics_loss'], 'm-', label='物理损失')
        ax2.plot(self.history['epoch'], self.history['damage_loss'], 'c-', label='损伤损失')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('损失值')
        ax2.set_title('物理损失项')
        ax2.legend()
        ax2.grid(True)
        
        # 绘制额外损失项和学习率
        ax3.plot(self.history['epoch'], self.history['smoothness_loss'], 'y-', label='平滑约束损失')
        ax3.plot(self.history['epoch'], self.history['hysteresis_loss'], 'k-', label='滞回特性损失')
        ax3_lr = ax3.twinx()  # 创建共享x轴的第二个y轴
        ax3_lr.plot(self.history['epoch'], self.history['learning_rate'], 'r--', label='学习率')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('损失值')
        ax3_lr.set_ylabel('学习率')
        ax3.set_title('额外约束损失和学习率')
        
        # 合并两个y轴的图例
        lines1, labels1 = ax3.get_legend_handles_labels()
        lines2, labels2 = ax3_lr.get_legend_handles_labels()
        ax3.legend(lines1 + lines2, labels1 + labels2, loc='upper right')
        ax3.grid(True)
        
        plt.tight_layout()
        
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"训练历史图像已保存至 {save_path}")
        else:
            plt.show()
        
        return True
    
    def predict(self, strain):
        """
        使用训练好的模型进行预测
        
        参数:
        strain: 输入应变张量
        
        返回:
        pred_stress: 预测应力
        pred_damage: 预测损伤变量
        """
        self.model.eval()
        # 重置模型状态，确保预测从干净状态开始
        self.model.reset_state()
        
        with torch.no_grad():
            # 转换输入为张量
            strain_tensor = torch.FloatTensor(strain).to(self.device)
            if strain_tensor.dim() == 1:
                strain_tensor = strain_tensor.unsqueeze(1)
            
            # 使用prepare_history_features处理输入数据，确保维度匹配
            history_features = self.prepare_history_features(strain_tensor)
            
            # 前向传播获取预测值
            pred, _ = self.model(history_features)
            pred_stress = pred[:, 0:1]
            pred_damage = pred[:, 1:2]
        
        return pred_stress.cpu().numpy(), pred_damage.cpu().numpy()