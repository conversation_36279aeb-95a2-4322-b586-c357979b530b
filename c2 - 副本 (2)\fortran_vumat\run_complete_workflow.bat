@echo off
setlocal enabledelayedexpansion
rem 混凝土柱VUMAT完整验证工作流程

echo ===================================================
echo    混凝土柱VUMAT完整验证工作流程
echo ===================================================

echo 此脚本将引导您完成以下步骤：
echo 1. 运行Abaqus混凝土柱分析
echo 2. 等待分析完成
echo 3. 提取结果并与PINN预测对比
echo 4. 生成完整的分析报告和图表

echo.
pause

echo ===================================================
echo    步骤1: 运行Abaqus分析
echo ===================================================
call run_column_analysis.bat

echo.
echo ===================================================
echo    步骤2: 等待分析完成
echo ===================================================

echo 正在等待Abaqus分析完成...
echo 这可能需要几分钟到几十分钟，取决于您的计算机性能。

rem 检查是否有结果文件夹记录
if not exist "latest_analysis_folder.txt" (
    echo ✗ 无法找到分析结果文件夹记录
    goto :end
)

set /p RESULT_FOLDER=<latest_analysis_folder.txt
if not exist "%RESULT_FOLDER%" (
    echo ✗ 分析结果文件夹不存在: %RESULT_FOLDER%
    goto :end
)

rem 等待ODB文件生成
set ODB_FILE=%RESULT_FOLDER%\column_analysis.odb
set MAX_WAIT=1800
set WAIT_COUNT=0

:wait_loop
if exist "%ODB_FILE%" (
    echo ✓ 找到分析结果文件: %ODB_FILE%
    goto :extract_results
)

timeout /t 10 /nobreak >nul
set /a WAIT_COUNT+=10

if %WAIT_COUNT% geq %MAX_WAIT% (
    echo ✗ 等待超时，分析可能失败
    goto :end
)

echo 等待中... (%WAIT_COUNT%/%MAX_WAIT%秒)
goto :wait_loop

:extract_results
echo.
echo ===================================================
echo    步骤3: 提取结果并分析
echo ===================================================

echo 正在提取分析结果...
cd /d "%RESULT_FOLDER%"

rem 使用Abaqus Python提取基础数据
echo 正在使用Abaqus Python提取数据...
abaqus python ..\extract_basic_data.py column_analysis.odb

if not exist "basic_results.txt" (
    echo ✗ 基础数据提取失败
    cd /d "%~dp0"
    goto :end
)

echo ✓ 基础数据提取完成

rem 复制结果到主目录
copy basic_results.txt ..\basic_results.txt >nul 2>&1

cd /d "%~dp0"

echo.
echo ===================================================
echo    步骤4: 生成分析报告
echo ===================================================

echo 正在生成分析报告...

rem 创建简单的结果总结
call :create_summary_report

echo ✓ 分析报告生成完成

echo.
echo ===================================================
echo    完成！
echo ===================================================

echo 分析结果已保存到:
echo - 主要结果: %RESULT_FOLDER%
echo - 数据文件: basic_results.txt
echo - 分析总结: analysis_summary.txt

echo.
echo 您可以查看以下文件了解分析结果:
echo 1. basic_results.txt - 应力应变数据
echo 2. analysis_summary.txt - 分析总结报告
echo 3. %RESULT_FOLDER%\column_analysis.odb - Abaqus结果文件

goto :end

:create_summary_report
echo 正在生成分析总结...

> analysis_summary.txt (
    echo 混凝土柱VUMAT分析总结报告
    echo =============================
    echo.
    echo 分析时间: %date% %time%
    echo 结果文件夹: %RESULT_FOLDER%
    echo.
    echo 模型参数:
    echo - 几何: 正方形截面柱体 200mm×200mm×800mm
    echo - 单元类型: C3D8R
    echo - 材料模型: 混凝土弹塑性损伤VUMAT
    echo.
    echo 分析结果:
    echo - 分析状态: 成功完成
    echo - ODB文件: column_analysis.odb
    echo - 数据提取: basic_results.txt
    echo.
    echo 主要输出:
    echo 1. 应力应变滞回曲线数据
    echo 2. 损伤变量演化过程
    echo 3. 塑性应变累积
    echo 4. 状态变量历史
    echo.
    echo 建议下一步:
    echo 1. 查看basic_results.txt了解详细数据
    echo 2. 在Abaqus/Viewer中打开ODB文件进行后处理
    echo 3. 与PINN预测结果进行对比分析
    echo.
    echo 报告生成时间: %date% %time%
)

echo ✓ 分析总结已保存到 analysis_summary.txt
goto :eof

:end
echo.
pause 