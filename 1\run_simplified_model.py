import torch
import numpy as np
import matplotlib.pyplot as plt
import os
import argparse
import pandas as pd

from simplified_column_hysteresis_model import (
    load_excel_data, preprocess_data, SimplifiedColumnHysteresisModel,
    train_enhanced_model, enhanced_hysteresis_loss
)
from simplified_column_hysteresis_model_plot import (
    plot_hysteresis_curve, plot_combined_hysteresis_curve
)

# 设置随机种子，确保结果可复现
torch.manual_seed(42)
np.random.seed(42)

# 设备配置
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="增强版柱子滞回曲线模型训练和预测")
    parser.add_argument("--excel_path", type=str, default="D:\\column\\column\\滞回曲线\\column1.xlsx", help="Excel数据文件路径")
    parser.add_argument("--mode", type=str, choices=["train", "predict"], default="train", help="运行模式: train或predict")
    parser.add_argument("--model_path", type=str, default="d:\\column\\simplified_column_hysteresis_model.pth", help="模型保存/加载路径")
    parser.add_argument("--epochs", type=int, default=300, help="训练轮数")
    parser.add_argument("--batch_size", type=int, default=16, help="批次大小")
    parser.add_argument("--learning_rate", type=float, default=5e-4, help="学习率")
    parser.add_argument("--hidden_dim", type=int, default=128, help="隐藏层维度")
    
    args = parser.parse_args()
    
    # 加载数据
    print(f"\n加载数据: {args.excel_path}")
    static_params, dynamic_data = load_excel_data(args.excel_path)
    
    # 预处理数据
    print("\n预处理数据...")
    X, y, scalers, original_data = preprocess_data(static_params, dynamic_data)
    
    # 获取数值型静态特征数量
    numeric_params = {}
    for key, value in static_params.items():
        if isinstance(value, (int, float)) and not isinstance(value, bool) and not pd.isna(value):
            numeric_params[key] = value
    
    static_feature_count = len(numeric_params)
    print(f"数值型静态特征数量: {static_feature_count}")
    
    # 创建模型
    input_dim = X.shape[1]
    model = SimplifiedColumnHysteresisModel(input_dim, static_feature_count, hidden_dim=args.hidden_dim).to(device)
    print(f"\n创建增强版模型: 输入维度={input_dim}, 隐藏层维度={args.hidden_dim}")
    
    if args.mode == "train":
        # 训练模式
        print(f"\n开始训练增强版模型...")
        model, history = train_enhanced_model(
            model, X, y, static_feature_count,
            epochs=args.epochs,
            batch_size=args.batch_size,
            lr=args.learning_rate,
            patience=30,  # 早停耐心值
            lambda_data=1.0,  # 数据拟合损失权重
            lambda_hysteresis=2.5,  # 滞回特性损失权重 - 大幅增加权重
            lambda_smoothness=0.1,  # 平滑度损失权重 - 降低平滑度约束
            lambda_energy=2.0  # 能量耗散损失权重 - 大幅增加权重
        )
        
        # 保存模型
        torch.save(model.state_dict(), args.model_path)
        print(f"\n增强版模型已保存到: {args.model_path}")
        
        # 绘制滞回曲线
        print("\n绘制滞回曲线...")
        plot_hysteresis_curve(
            model, X, y, static_feature_count,
            scalers['force'], scalers['disp'], original_data,
            title="增强版模型滞回曲线对比 (训练数据)"
        )
        
        # 绘制叠加的滞回曲线
        plot_combined_hysteresis_curve(
            model, X, y, static_feature_count,
            scalers['force'], scalers['disp'], original_data
        )
        
    elif args.mode == "predict":
        # 预测模式
        print(f"\n模型结构已更新，需要重新训练模型")
        # 训练模式
        print(f"\n开始训练增强版模型...")
        model, history = train_enhanced_model(
            model, X, y, static_feature_count,
            epochs=100,  # 减少训练轮数
            batch_size=16,
            lr=5e-4,
            patience=10,  # 减少早停耐心值
            lambda_data=1.0,  # 数据拟合损失权重
            lambda_hysteresis=2.5,  # 滞回特性损失权重 - 大幅增加权重
            lambda_smoothness=0.1,  # 平滑度损失权重 - 降低平滑度约束
            lambda_energy=2.0  # 能量耗散损失权重 - 大幅增加权重
        )
        
        # 保存模型
        torch.save(model.state_dict(), args.model_path)
        print(f"\n增强版模型已保存到: {args.model_path}")
        
        model.eval()
        
        # 绘制滞回曲线
        print("\n绘制预测滞回曲线...")
        metrics = plot_hysteresis_curve(
            model, X, y, static_feature_count,
            scalers['force'], scalers['disp'], original_data,
            title="增强版模型滞回曲线预测"
        )
        
        # 绘制叠加的滞回曲线
        plot_combined_hysteresis_curve(
            model, X, y, static_feature_count,
            scalers['force'], scalers['disp'], original_data
        )
        
        # 打印预测指标
        print("\n预测性能指标:")
        print(f"  平均绝对误差 (MAE): {metrics['mae']:.4f} kN")
        print(f"  均方根误差 (RMSE): {metrics['rmse']:.4f} kN")
        print(f"  最大误差: {metrics['max_error']:.4f} kN")
        print(f"  能量耗散误差: {metrics['energy_error']:.2f}%")
        print(f"  真实能量耗散: {metrics['true_energy']:.2f} kN·mm")
        print(f"  预测能量耗散: {metrics['pred_energy']:.2f} kN·mm")

if __name__ == "__main__":
    main()