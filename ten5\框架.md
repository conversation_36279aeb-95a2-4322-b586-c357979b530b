
---

### **最终方案：基于增量式输入的混凝土损伤参数识别PINN框架**

#### **1. 项目概述**

本项目旨在构建并训练一个**物理信息神经网络 (PINN)**，其核心任务是从单条混凝土单轴拉伸试验的应力-应变曲线中，以**增量式输入**的方式，**端到端地识别**材料的损伤本构参数。训练完成的模型将成为一个能够精确预测应力响应和内部损伤演化的“数字孪生”材料点模型。

#### **2. 核心物理模型 (增量式)**

本框架遵循的物理定律源自相关文献，并以适用于数值积分的增量形式表述。所有状态在第 $i$ 个增量步更新。

| 方程类别                 | 物理约束表达式                                                                                                                                                                                                                                                                                                | 变量说明                                                                                                                     |
| :----------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :--------------------------------------------------------------------------------------------------------------------------- |
| **应变累积**             | $\varepsilon_i = \varepsilon_{i-1} + \Delta\varepsilon_i$                                                                                                                                                                                                                                                     | 当前总应变是上一步总应变与当前应变增量的累加。                                                                               |
| **塑性应变演化**         | $\Delta\varepsilon^p_i = \xi \cdot \max(0, \Delta\varepsilon_i)$ <br> $\varepsilon^p_i = \varepsilon^p_{i-1} + \Delta\varepsilon^p_i$                                                                                                                                                                         | 塑性应变增量与总应变增量成正比，比例系数$\xi$为待识别参数。                                                                  |
| **本构关系**             | $\sigma_i = (1 - d_i) E_0 (\varepsilon_i - \varepsilon^p_i)$                                                                                                                                                                                                                                                  | 名义应力由**当前**时刻的总应变、总塑性应变和总损伤共同决定。                                                                 |
| **损伤驱动力**           | $Y_i = E_0 (\varepsilon_i - \varepsilon^p_i)$                                                                                                                                                                                                                                                                 | 损伤驱动力由**当前**时刻的有效应变（弹性应变）决定。                                                                         |
| **损伤演化（条件逻辑）** | **if** $Y_i > r_{i-1}$: <br>&nbsp;&nbsp;&nbsp;&nbsp;$r_i = Y_i$ <br>&nbsp;&nbsp;&nbsp;&nbsp;$d_i = 1 - \frac{f_t}{r_i} \left( (1-A^+) + A^+ \exp\left[B^+\left(1-\frac{r_i}{f_t}\right)\right] \right)$ <br>**else**: <br>&nbsp;&nbsp;&nbsp;&nbsp;$r_i = r_{i-1}$ <br>&nbsp;&nbsp;&nbsp;&nbsp;$d_i = d_{i-1}$ | **核心状态更新逻辑**：只有当驱动力突破历史最大阈值$r$时，损伤$d$才会演化。否则，损伤和阈值均保持不变，体现了损伤的不可逆性。 |

#### **3. 识别目标与已知条件**

-   **待识别物理参数 (Learnable)**:
    -   $A^+, B^+$: 控制损伤演化曲线形状和速率的材料参数。
    -   $\xi$: 控制塑性应变发展的比例系数。
-   **已知物理常量 (Fixed Constants)**:
    -   $E_0$: 初始弹性模量。
    -   $f_t$: 单轴抗拉强度（初始损伤阈值）。

#### **4. PINN 模型架构**

| 组件         | 设计与说明                                                                                                                                                                                                                      |
| :----------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **输入层**   | **应变增量序列** $\Delta\varepsilon_{\text{seq}} = [\Delta\varepsilon_1, \Delta\varepsilon_2, ..., \Delta\varepsilon_N]$。这是模型处理的基本单位，与有限元中的增量步思想一致。                                                  |
| **神经网络** | **核心**: GRU (Gated Recurrent Unit) 或 LSTM。这类循环神经网络（RNN）的隐藏状态能够自然地对历史增量进行积分，并记忆当前材料点（如损伤、塑性应变）的状态。 <br> **结构**: 2-3个GRU/LSTM层，后接1-2个全连接层，以输出所需物理量。 |
| **输出层**   | 在每个增量步 $i$ 之后，网络并行输出该时刻的**三个状态总量**: <br>1.  名义应力 $\hat{\sigma}_i$ <br>2.  损伤变量 $\hat{d}_i$ <br>3.  塑性应变 $\hat{\varepsilon}^p_i$                                                            |

#### **5. 损失函数设计**

总损失由四部分加权构成，旨在同时驱动数据拟合和物理规律的遵守。

$L_{total} = \lambda_{\text{data}} L_{\text{data}} + \lambda_{\text{stress}} L_{\text{stress}} + \lambda_{\text{damage}} L_{\text{damage}} + \lambda_{\text{plastic}} L_{\text{plastic}}$

| 损失项                   | 目标与解释                                                                                                                                                                               |
| :----------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **$L_{\text{data}}$**    | **数据拟合**: 强制网络预测的应力序列 $(\hat{\sigma}_{\text{seq}})$ 与试验测得的应力序列 $(\sigma^{\text{exp}}_{\text{seq}})$ 之间的均方误差最小。                                        |
| **$L_{\text{stress}}$**  | **本构自洽**: 强制网络输出的三个量 $(\hat{\sigma}_i, \hat{d}_i, \hat{\varepsilon}^p_i)$ 必须在每个时间步都满足本构关系 $\sigma = (1 - d) E_0 (\varepsilon - \varepsilon^p)$。            |
| **$L_{\text{damage}}$**  | **损伤物理**: 强制网络预测的损伤序列 $(\hat{d}_{\text{seq}})$ 与根据**条件损伤演化法则**逐步积分计算出的物理损伤序列 $(d^{\text{phy}}_{\text{seq}})$ 一致。                              |
| **$L_{\text{plastic}}$** | **塑性物理**: 强制网络预测的塑性应变序列 $(\hat{\varepsilon}^p_{\text{seq}})$ 与根据**塑性演化法则**逐步积分计算出的物理塑性应变序列 $(\varepsilon^{p, \text{phy}}_{\text{seq}})$ 一致。 |

#### **6. 训练与预测工作流（伪代码）**

```python
import torch
import torch.nn as nn
from torch.optim import Adam

# --- 0. 定义全局物理常量 ---
E0 = 30000.0
f_t = 3.0

# --- 1. 初始化模型及可训练物理参数 ---
model = DamagePINN(...) # GRU/LSTM based model
A_plus = torch.nn.Parameter(torch.tensor(0.5, requires_grad=True))
B_plus = torch.nn.Parameter(torch.tensor(1.0, requires_grad=True))
xi = torch.nn.Parameter(torch.tensor(0.1, requires_grad=True))
optimizer = Adam(list(model.parameters()) + [A_plus, B_plus, xi], lr=0.001)

# --- 2. 准备增量式数据 ---
strain_total_exp_seq = torch.tensor([...], dtype=torch.float32) # 从实验文件加载
stress_exp_seq = torch.tensor([...], dtype=torch.float32)       # 从实验文件加载

# 核心数据转换：从总量计算增量
strain_increment_seq = torch.diff(strain_total_exp_seq, prepend=torch.tensor([0.0]))

# --- 3. 训练循环 ---
model.train()
for epoch in range(num_epochs):
    
    # --- 步骤 A: 神经网络前向传播 (输入为应变增量) ---
    sigma_hat_seq, d_hat_seq, ep_hat_seq = model(strain_increment_seq.unsqueeze(0).unsqueeze(-1))
    sigma_hat_seq, d_hat_seq, ep_hat_seq = sigma_hat_seq.squeeze(), d_hat_seq.squeeze(), ep_hat_seq.squeeze()

    # --- 步骤 B: 物理约束目标的增量式计算 ---
    # 初始化 t=0 时刻的状态变量
    epsilon_phy = torch.tensor(0.0) # 当前总应变
    ep_phy = torch.tensor(0.0)      # 当前塑性应变
    d_phy = torch.tensor(0.0)       # 当前损伤
    r_max_phy = torch.tensor(f_t)   # 历史最大损伤阈值
    
    # 用于存储每一步计算结果的列表
    d_phy_seq, ep_phy_seq = [], []

    # 按增量步进行循环积分
    for delta_epsilon in strain_increment_seq:
        # 1. 更新应变状态
        epsilon_phy += delta_epsilon
        ep_phy += xi * torch.relu(delta_epsilon)
        
        # 2. 计算损伤驱动力
        Y_phy = E0 * (epsilon_phy - ep_phy)
        
        # 3. 条件性地更新损伤和阈值
        if Y_phy > r_max_phy:
            r_max_phy = Y_phy
            term1 = f_t / r_max_phy * (1 - A_plus)
            term2 = A_plus * torch.exp(B_plus * (1 - r_max_phy / f_t))
            d_phy = 1 - (term1 + term2)
        # 否则, d_phy 和 r_max_phy 保持不变
        
        # 4. 记录当前步的状态
        d_phy_seq.append(d_phy)
        ep_phy_seq.append(ep_phy)
        
    d_phy_seq = torch.stack(d_phy_seq)
    ep_phy_seq = torch.stack(ep_phy_seq)

    # --- 步骤 C: 计算总损失 ---
    loss_data = torch.mean((sigma_hat_seq - stress_exp_seq)**2)
    loss_stress = torch.mean((sigma_hat_seq - (1 - d_hat_seq) * E0 * (strain_total_exp_seq - ep_hat_seq))**2)
    loss_damage = torch.mean((d_hat_seq - d_phy_seq)**2)
    loss_plastic = torch.mean((ep_hat_seq - ep_phy_seq)**2)
    total_loss = (1.0 * loss_data + 1.0 * loss_stress + 0.8 * loss_damage + 0.5 * loss_plastic)
    
    # --- 步骤 D: 反向传播与优化 ---
    optimizer.zero_grad()
    total_loss.backward()
    optimizer.step()
    # (打印训练进度)

# --- 4. 预测与评估 ---
print("\nTraining Finished. Identified Parameters:")
print(f"A+ = {A_plus.item():.4f}, B+ = {B_plus.item():.4f}, xi = {xi.item():.4f}")
# (使用训练好的模型进行预测，并绘制应力-应变和应变-损伤曲线进行对比)
```

#### **7. 方案优势**

-   **物理忠实度高**: 增量式计算和条件损伤演化逻辑，精确地复现了计算力学中材料点积分的数值过程。
-   **端到端自动化**: 将传统的、需要专家经验和多次试错的参数标定过程，转化为一个统一的、自动化的机器学习优化问题。
-   **路径依赖建模**: 采用RNN，并结合显式的状态变量更新，完美地捕捉了材料力学行为的路径依赖性和不可逆性。
-   **解释性强**: 模型不仅输出结果，其内部的物理参数($A^+, B^+, \xi$)具有明确的物理意义，可以直接用于材料性能评价。
-   


#### **8. 构建要求**
- 不需要emoji表情
- 所有的输出图片和训练结果需要单独放置于一个文件夹
- 实验数据有两列strain和stress，在excel内，输入是正确引用路径
- 整一条曲线都作为训练集不要拆分
- 注意图像中文字体的配置
- 输出图像要包含：应力应变的曲线（实验数据和预测数据对比）、损伤变量随应变的更新、塑形应变的更新