# -*- coding: utf-8 -*-
"""
主训练脚本

整合数据处理和PINN模型训练的完整流程
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import os
from data_processor import DataProcessor
from pinn_model import ConstitutivePINN, PINNTrainer
from font_config import configure_chinese_font

def main():
    """
    主训练流程
    """
    # 配置中文字体
    configure_chinese_font()
    
    print("=" * 60)
    print("混凝土损伤本构PINN建模项目")
    print("=" * 60)
    
    # 1. 数据处理
    print("\n1. 数据加载与预处理...")
    processor = DataProcessor('d:/column/F3/data.xlsx')
    
    if not processor.load_data():
        print("数据加载失败，程序退出")
        return
    
    if not processor.preprocess_data():
        print("数据预处理失败，程序退出")
        return
    
    # 绘制原始数据
    processor.plot_raw_data('raw_data_analysis.png')
    
    # 获取训练数据
    training_data = processor.get_training_data()
    if training_data is None:
        print("训练数据获取失败，程序退出")
        return
    
    # 转换为PyTorch张量
    strain_tensor = torch.tensor(training_data['strain_normalized'], dtype=torch.float32).reshape(-1, 1)
    stress_tensor = torch.tensor(training_data['stress_normalized'], dtype=torch.float32).reshape(-1, 1)
    
    print(f"训练数据准备完成:")
    print(f"  数据点数: {len(strain_tensor)}")
    print(f"  应变范围: {training_data['strain_min']:.6f} ~ {training_data['strain_max']:.6f}")
    print(f"  应力范围: {training_data['stress_min']:.2f} ~ {training_data['stress_max']:.2f} MPa")
    
    # 2. 模型创建
    print("\n2. 创建PINN模型...")
    model = ConstitutivePINN(hidden_size=32, num_layers=4)
    trainer = PINNTrainer(model, learning_rate=0.001)
    
    print(f"模型结构:")
    print(f"  隐藏层大小: 32")
    print(f"  隐藏层数: 4")
    print(f"  总参数数: {sum(p.numel() for p in model.parameters())}")
    
    # 3. 模型训练
    print("\n3. 开始训练...")
    
    # 训练参数
    epochs = 10000
    physics_weight = 1.0
    data_weight = 10.0  # 增加数据拟合权重
    
    trainer.train(
        strain_tensor, 
        stress_tensor, 
        epochs=epochs,
        physics_weight=physics_weight,
        data_weight=data_weight,
        print_interval=1000,
        save_path='concrete_pinn_model.pth'
    )
    
    # 4. 绘制训练历史
    print("\n4. 绘制训练历史...")
    trainer.plot_training_history('training_history.png')
    
    # 5. 模型评估和预测
    print("\n5. 模型评估...")
    model.eval()
    
    with torch.no_grad():
        # 在训练数据上预测
        stress_pred, damage_pred = model(strain_tensor)
        
        # 反归一化
        strain_original = training_data['strain']
        stress_original = training_data['stress']
        stress_pred_original = stress_pred.numpy().flatten() * (training_data['stress_max'] - training_data['stress_min']) + training_data['stress_min']
        
        # 计算误差
        mse = np.mean((stress_original - stress_pred_original) ** 2)
        rmse = np.sqrt(mse)
        mae = np.mean(np.abs(stress_original - stress_pred_original))
        r2 = 1 - np.sum((stress_original - stress_pred_original) ** 2) / np.sum((stress_original - np.mean(stress_original)) ** 2)
        
        print(f"模型性能指标:")
        print(f"  均方误差 (MSE): {mse:.4f}")
        print(f"  均方根误差 (RMSE): {rmse:.4f} MPa")
        print(f"  平均绝对误差 (MAE): {mae:.4f} MPa")
        print(f"  决定系数 (R²): {r2:.4f}")
    
    # 6. 绘制预测结果
    print("\n6. 绘制预测结果...")
    plot_predictions(strain_original, stress_original, stress_pred_original, 
                    damage_pred.numpy().flatten(), training_data)
    
    # 7. 生成扩展预测
    print("\n7. 生成扩展预测...")
    generate_extended_predictions(model, training_data)
    
    print("\n" + "=" * 60)
    print("训练完成！")
    print("=" * 60)

def plot_predictions(strain, stress_true, stress_pred, damage, training_data):
    """
    绘制预测结果
    """
    plt.figure(figsize=(15, 10))
    
    # 应力-应变对比
    plt.subplot(2, 3, 1)
    plt.plot(strain, stress_true, 'b-', linewidth=2, label='试验数据', alpha=0.7)
    plt.plot(strain, stress_pred, 'r--', linewidth=2, label='PINN预测')
    plt.xlabel('应变')
    plt.ylabel('应力 (MPa)')
    plt.title('应力-应变曲线对比')
    plt.legend()
    plt.grid(True)
    
    # 应力预测误差
    plt.subplot(2, 3, 2)
    error = stress_true - stress_pred
    plt.plot(strain, error, 'g-', linewidth=1)
    plt.xlabel('应变')
    plt.ylabel('应力误差 (MPa)')
    plt.title('应力预测误差')
    plt.grid(True)
    
    # 损伤变量演化
    plt.subplot(2, 3, 3)
    plt.plot(strain, damage, 'purple', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('损伤变量')
    plt.title('损伤变量演化')
    plt.grid(True)
    
    # 应力散点图
    plt.subplot(2, 3, 4)
    plt.scatter(stress_true, stress_pred, alpha=0.6, s=10)
    min_stress = min(np.min(stress_true), np.min(stress_pred))
    max_stress = max(np.max(stress_true), np.max(stress_pred))
    plt.plot([min_stress, max_stress], [min_stress, max_stress], 'r--', linewidth=2)
    plt.xlabel('试验应力 (MPa)')
    plt.ylabel('预测应力 (MPa)')
    plt.title('应力预测散点图')
    plt.grid(True)
    
    # 残差分布
    plt.subplot(2, 3, 5)
    plt.hist(error, bins=50, alpha=0.7, edgecolor='black')
    plt.xlabel('应力误差 (MPa)')
    plt.ylabel('频次')
    plt.title('残差分布')
    plt.grid(True)
    
    # 损伤-应力关系
    plt.subplot(2, 3, 6)
    plt.scatter(damage, stress_true, alpha=0.6, s=10, c=strain, cmap='viridis')
    plt.xlabel('损伤变量')
    plt.ylabel('应力 (MPa)')
    plt.title('损伤-应力关系')
    plt.colorbar(label='应变')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('prediction_results.png', dpi=300, bbox_inches='tight')
    print("预测结果图已保存至: prediction_results.png")
    plt.show()

def generate_extended_predictions(model, training_data):
    """
    生成扩展预测（超出训练数据范围）
    """
    model.eval()
    
    # 创建扩展的应变范围
    strain_max = training_data['strain_max']
    strain_extended = np.linspace(0, strain_max * 1.5, 1000)
    strain_normalized_extended = (strain_extended - training_data['strain_min']) / (training_data['strain_max'] - training_data['strain_min'])
    
    strain_tensor_extended = torch.tensor(strain_normalized_extended, dtype=torch.float32).reshape(-1, 1)
    
    with torch.no_grad():
        stress_pred_extended, damage_pred_extended = model(strain_tensor_extended)
        
        # 反归一化
        stress_pred_extended_original = stress_pred_extended.numpy().flatten() * (training_data['stress_max'] - training_data['stress_min']) + training_data['stress_min']
        damage_extended = damage_pred_extended.numpy().flatten()
    
    # 绘制扩展预测
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.plot(training_data['strain'], training_data['stress'], 'b-', linewidth=2, label='训练数据')
    plt.plot(strain_extended, stress_pred_extended_original, 'r--', linewidth=2, label='PINN扩展预测')
    plt.xlabel('应变')
    plt.ylabel('应力 (MPa)')
    plt.title('扩展应力-应变预测')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 2, 2)
    plt.plot(strain_extended, damage_extended, 'purple', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('损伤变量')
    plt.title('扩展损伤演化预测')
    plt.grid(True)
    
    plt.subplot(2, 2, 3)
    # 有效应力（考虑损伤）
    effective_stress = stress_pred_extended_original * (1 - damage_extended)
    plt.plot(strain_extended, stress_pred_extended_original, 'r-', linewidth=2, label='名义应力')
    plt.plot(strain_extended, effective_stress, 'g-', linewidth=2, label='有效应力')
    plt.xlabel('应变')
    plt.ylabel('应力 (MPa)')
    plt.title('名义应力 vs 有效应力')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 2, 4)
    # 切线模量
    if len(strain_extended) > 1:
        tangent_modulus = np.gradient(stress_pred_extended_original, strain_extended)
        plt.plot(strain_extended, tangent_modulus, 'orange', linewidth=2)
        plt.xlabel('应变')
        plt.ylabel('切线模量 (MPa)')
        plt.title('切线模量演化')
        plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('extended_predictions.png', dpi=300, bbox_inches='tight')
    print("扩展预测图已保存至: extended_predictions.png")
    plt.show()
    
    # 保存扩展预测数据
    extended_data = {
        'strain': strain_extended,
        'stress': stress_pred_extended_original,
        'damage': damage_extended
    }
    
    import pandas as pd
    df_extended = pd.DataFrame(extended_data)
    df_extended.to_excel('extended_predictions.xlsx', index=False)
    print("扩展预测数据已保存至: extended_predictions.xlsx")

if __name__ == "__main__":
    main()