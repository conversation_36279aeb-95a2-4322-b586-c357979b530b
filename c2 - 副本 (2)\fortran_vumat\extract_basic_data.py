"""
从Abaqus ODB文件中提取基本数据
此脚本只依赖Abaqus Python API，不需要额外的库
"""
import os
import sys

# 尝试导入Abaqus模块
try:
    from odbAccess import *
    from abaqusConstants import *
    print("成功导入Abaqus Python API")
except ImportError:
    print("错误: 此脚本必须在Abaqus Python环境中运行")
    print("请使用: abaqus python extract_basic_data.py")
    sys.exit(1)

def extract_basic_data(odb_file='my_analysis.odb'):
    """从ODB文件中提取基本数据"""
    print(f"正在打开ODB文件: {odb_file}")
    try:
        odb = openOdb(path=odb_file)
    except Exception as e:
        print(f"打开ODB文件失败: {e}")
        return False
    
    print("\n基本模型信息:")
    print(f"模型名称: {odb.name}")
    print(f"分析步骤: {list(odb.steps.keys())}")
    
    # 获取最后一步
    step_name = list(odb.steps.keys())[0]
    step = odb.steps[step_name]
    print(f"\n分析步骤 '{step_name}' 信息:")
    print(f"总帧数: {len(step.frames)}")
    print(f"总历史区域数: {len(step.historyRegions)}")
    
    # 提取历史输出数据
    try:
        region_key = list(step.historyRegions.keys())[0]
        region = step.historyRegions[region_key]
        print(f"\n历史区域 '{region_key}' 可用输出:")
        output_keys = list(region.historyOutputs.keys())
        print(", ".join(output_keys))
        
        # 获取应力数据
        if 'S11' in output_keys:
            stress_data = region.historyOutputs['S11'].data
            print(f"\n应力 (S11) 数据样本 (前5个点):")
            for i, (time, value) in enumerate(stress_data[:5]):
                print(f"  时间: {time:.4f}, 应力: {value:.4f}")
            
            # 计算最大/最小应力
            max_stress = max([s for _, s in stress_data])
            min_stress = min([s for _, s in stress_data])
            print(f"最大应力: {max_stress:.4f}")
            print(f"最小应力: {min_stress:.4f}")
        
        # 获取应变数据
        if 'LE11' in output_keys:
            strain_data = region.historyOutputs['LE11'].data
            print(f"\n应变 (LE11) 数据样本 (前5个点):")
            for i, (time, value) in enumerate(strain_data[:5]):
                print(f"  时间: {time:.4f}, 应变: {value:.6f}")
            
            # 计算最大/最小应变
            max_strain = max([e for _, e in strain_data])
            min_strain = min([e for _, e in strain_data])
            print(f"最大应变: {max_strain:.6f}")
            print(f"最小应变: {min_strain:.6f}")
        
        # 获取状态变量
        sdv_keys = [k for k in output_keys if k.startswith('SDV')]
        if sdv_keys:
            print(f"\n状态变量数据:")
            for sdv_key in sdv_keys:
                sdv_data = region.historyOutputs[sdv_key].data
                last_value = sdv_data[-1][1]
                print(f"  {sdv_key} 最终值: {last_value:.6f}")
        
        # 保存数据到文本文件
        print("\n保存数据到文件...")
        with open('basic_results.txt', 'w') as f:
            f.write(f"混凝土VUMAT模型基本结果\n")
            f.write(f"======================\n\n")
            f.write(f"模型名称: {odb.name}\n")
            f.write(f"分析步骤: {step_name}\n")
            f.write(f"总帧数: {len(step.frames)}\n\n")
            
            if 'S11' in output_keys and 'LE11' in output_keys:
                f.write("应力-应变数据:\n")
                f.write("时间,应变,应力\n")
                for i in range(min(len(stress_data), len(strain_data))):
                    time = stress_data[i][0]
                    strain = strain_data[i][1]
                    stress = stress_data[i][1]
                    f.write(f"{time:.4f},{strain:.6f},{stress:.4f}\n")
            
            f.write("\n关键结果指标:\n")
            if 'S11' in output_keys:
                f.write(f"最大应力: {max_stress:.4f}\n")
                f.write(f"最小应力: {min_stress:.4f}\n")
            if 'LE11' in output_keys:
                f.write(f"最大应变: {max_strain:.6f}\n")
                f.write(f"最小应变: {min_strain:.6f}\n")
            
            if sdv_keys:
                f.write("\n状态变量最终值:\n")
                for sdv_key in sdv_keys:
                    sdv_data = region.historyOutputs[sdv_key].data
                    last_value = sdv_data[-1][1]
                    f.write(f"{sdv_key}: {last_value:.6f}\n")
        
        print(f"数据已保存到 basic_results.txt")
        
    except Exception as e:
        print(f"提取数据时出错: {e}")
    
    # 关闭ODB文件
    odb.close()
    return True

if __name__ == "__main__":
    print("混凝土VUMAT模型基本数据提取")
    print("==========================")
    
    # 获取命令行参数中的ODB文件名
    odb_file = 'my_analysis.odb'
    if len(sys.argv) > 1:
        odb_file = sys.argv[1]
    
    success = extract_basic_data(odb_file)
    if success:
        print("\n数据提取完成!")
    else:
        print("\n数据提取失败!")
        sys.exit(1) 