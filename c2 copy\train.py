"""
训练脚本
支持双向损伤演化（受拉和受压）的PINN训练
"""

import torch
import torch.nn as nn
from torch.optim import Adam
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os
import json
from pathlib import Path

from pinn_model_v2 import DamagePINNV2, PhysicsCalculatorV2, LossCalculator
from data_processor import DataProcessor
from font_config import setup_font
from model_utils import safe_save_model

class Trainer:
    """
    PINN训练器
    支持双向损伤演化和塑性应变累积
    """
    
    def __init__(self, config=None):
        # 默认配置
        self.config = {
            'E0': 30000.0,           # 初始弹性模量
            'f_t': 3.0,              # 单轴抗拉强度
            'f_c': 30.0,             # 单轴抗压强度
            'num_epochs': 3000,      # 训练轮数
            'learning_rate': 0.001,  # 学习率
            'hidden_size': 64,       # 隐藏层大小
            'num_layers': 6,         # 网络层数
            'save_interval': 100,    # 保存间隔
            'print_interval': 50,    # 打印间隔
            'batch_size': 32,        # 批次大小
        }
        if config:
            self.config.update(config)
        
        # 创建带时间戳的结果文件夹
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.results_dir = f'results/session_{self.timestamp}'
        self.training_dir = f'{self.results_dir}/training'
        self.prediction_dir = f'{self.results_dir}/prediction'
        
        Path(self.training_dir).mkdir(parents=True, exist_ok=True)
        Path(self.prediction_dir).mkdir(parents=True, exist_ok=True)
        
        print(f"结果将保存到: {self.results_dir}")
        
        # 保存会话信息
        self.save_session_info()
        
        # 初始化组件
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        self.model = None
        self.physics_calc = None
        self.loss_calc = None
        self.optimizer = None
        self.training_data = None
        
        # 可训练物理参数
        self.A_plus = None
        self.B_plus = None
        self.A_minus = None
        self.B_minus = None
        self.xi_plus = None
        self.xi_minus = None
        
        # 训练历史
        self.loss_history = []
        self.param_history = []
        
    def save_session_info(self):
        """保存会话信息"""
        session_info = {
            'timestamp': self.timestamp,
            'session_type': 'tensile_compression_hysteresis_training',
            'config': self.config,
            'results_directory': self.results_dir,
            'training_directory': self.training_dir,
            'prediction_directory': self.prediction_dir
        }
        
        with open(f'{self.results_dir}/session_summary.txt', 'w', encoding='utf-8') as f:
            f.write("混凝土受拉受压全滞回曲线PINN训练会话\n")
            f.write("=" * 60 + "\n")
            f.write(f"时间戳: {self.timestamp}\n")
            f.write(f"结果目录: {self.results_dir}\n")
            f.write(f"训练目录: {self.training_dir}\n")
            f.write(f"预测目录: {self.prediction_dir}\n")
            f.write("=" * 60 + "\n")
        
    def initialize_model(self):
        """
        初始化模型和组件
        """
        # 初始化PINN模型
        self.model = DamagePINNV2(
            input_size=1,
            hidden_size=self.config['hidden_size'],
            num_layers=self.config['num_layers'],
            output_size=3
        ).to(self.device)
        
        # 初始化物理计算器
        self.physics_calc = PhysicsCalculatorV2(
            E0=self.config['E0'],
            f_t=self.config['f_t'],
            f_c=self.config['f_c']
        )
        
        # 初始化损失计算器
        self.loss_calc = LossCalculator(
            lambda_data=1.0,
            lambda_stress=1.0,
            lambda_damage_plus=1.0,
            lambda_damage_minus=1.0,
            lambda_plastic=2.0  # 增加塑性损失权重，确保塑性行为被正确学习
        )
        
        # 初始化可训练物理参数
        # 受拉参数
        self.A_plus = torch.nn.Parameter(torch.tensor(0.5, requires_grad=True, device=self.device))
        self.B_plus = torch.nn.Parameter(torch.tensor(1.0, requires_grad=True, device=self.device))
        self.xi_plus = torch.nn.Parameter(torch.tensor(0.05, requires_grad=True, device=self.device))  # 增大初始值
        
        # 受压参数（根据文献经验值初始化）
        self.A_minus = torch.nn.Parameter(torch.tensor(1.5, requires_grad=True, device=self.device))
        self.B_minus = torch.nn.Parameter(torch.tensor(0.5, requires_grad=True, device=self.device))
        self.xi_minus = torch.nn.Parameter(torch.tensor(0.1, requires_grad=True, device=self.device))  # 增大初始值
        
        # 初始化优化器
        self.optimizer = Adam(
            list(self.model.parameters()) + 
            [self.A_plus, self.B_plus, self.xi_plus, self.A_minus, self.B_minus, self.xi_minus],
            lr=self.config['learning_rate']
        )
        
        print("模型初始化完成")
        print(f"  模型参数数量: {sum(p.numel() for p in self.model.parameters())}")
        print(f"  受拉参数: A+={self.A_plus.item():.3f}, B+={self.B_plus.item():.3f}, xi+={self.xi_plus.item():.3f}")
        print(f"  受压参数: A-={self.A_minus.item():.3f}, B-={self.B_minus.item():.3f}, xi-={self.xi_minus.item():.3f}")
    
    def load_data(self, excel_path="cyclic_data.xlsx"):
        """
        加载训练数据
        """
        processor = DataProcessor(excel_path)
        processor.load_data()
        
        # 获取材料属性
        material_props = processor.get_material_properties()
        self.config['E0'] = material_props['E0']
        self.config['f_t'] = material_props['f_t']
        self.config['f_c'] = material_props['f_c']
        
        # 更新物理计算器的材料属性
        if self.physics_calc is not None:
            self.physics_calc.E0 = self.config['E0']
            self.physics_calc.f_t = self.config['f_t']
            self.physics_calc.f_c = self.config['f_c']
        
        # 处理数据
        self.training_data = processor.process_data()
        
        # 将数据移动到设备
        for key in self.training_data:
            if isinstance(self.training_data[key], torch.Tensor):
                self.training_data[key] = self.training_data[key].to(self.device)
        
        # 绘制实验数据
        processor.plot_data(save_path=f"{self.training_dir}/experimental_data.png")
        
        print(f"数据加载完成，数据点数: {len(self.training_data['strain'])}")
        
    def prepare_batch_data(self):
        """
        准备批次训练数据
        """
        seq_len = len(self.training_data['strain'])
        batch_size = min(self.config['batch_size'], seq_len)
        
        # 创建时间步索引
        t = torch.arange(seq_len, dtype=torch.float32, device=self.device).unsqueeze(-1)
        
        # 将数据整理为批次
        # 这里简化处理，将整个序列作为一个批次
        strain_increments = self.training_data['strain_increments'].unsqueeze(0)  # [1, seq_len]
        strain_total = self.training_data['strain'].unsqueeze(0)  # [1, seq_len]
        stress_exp = self.training_data['stress'].unsqueeze(0)  # [1, seq_len]
        
        return t, strain_increments, strain_total, stress_exp
        
    def train_epoch(self):
        """
        训练一个epoch
        """
        self.model.train()
        
        # 准备批次数据
        t, strain_increments, strain_total, stress_exp = self.prepare_batch_data()
        
        # --- 步骤 A: 神经网络前向传播 ---
        # 对每个时间步进行预测
        model_output = self.model(t)  # [seq_len, 3]
        
        # 分离输出
        sigma_hat_seq = model_output[:, 0].unsqueeze(0)  # [1, seq_len]
        d_hat_seq = torch.sigmoid(model_output[:, 1]).unsqueeze(0)  # [1, seq_len]
        xi_hat_seq = torch.sigmoid(model_output[:, 2]).unsqueeze(0) * 0.5  # [1, seq_len] 增大到0.5
        
        # --- 步骤 B: 物理约束计算 ---
        d_plus_phy_seq, d_minus_phy_seq, ep_phy_seq, stress_phy_seq = \
            self.physics_calc.calculate_physics_constraints(
                strain_increments, 
                self.A_plus, self.B_plus, self.A_minus, self.B_minus,
                self.xi_plus, self.xi_minus
        )
        
        # --- 步骤 C: 计算总损失 ---
        total_loss, loss_dict = self.loss_calc.calculate_total_loss(
            sigma_hat_seq, d_hat_seq, xi_hat_seq,
            stress_exp, strain_total,
            d_plus_phy_seq, d_minus_phy_seq, ep_phy_seq, stress_phy_seq,
            self.config['E0']
        )
        
        # --- 步骤 D: 反向传播与优化 ---
        self.optimizer.zero_grad()
        total_loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(
            list(self.model.parameters()) + 
            [self.A_plus, self.B_plus, self.xi_plus, self.A_minus, self.B_minus, self.xi_minus],
            max_norm=1.0
        )
        
        self.optimizer.step()
        
        # 约束物理参数在合理范围内
        with torch.no_grad():
            self.A_plus.data.clamp_(0.01, 0.99)
            self.B_plus.data.clamp_(0.1, 10.0)
            self.xi_plus.data.clamp_(0.001, 0.5)  # 放宽上限到0.5
            
            self.A_minus.data.clamp_(0.5, 2.0)
            self.B_minus.data.clamp_(0.1, 5.0)
            self.xi_minus.data.clamp_(0.005, 0.8)  # 放宽上限到0.8
        
        return loss_dict
    
    def train(self):
        """
        完整训练过程
        """
        print(f"\n开始训练，共{self.config['num_epochs']}轮")
        print("=" * 60)
        
        best_loss = float('inf')
        best_epoch = 0
        
        for epoch in range(self.config['num_epochs']):
            # 训练一个epoch
            loss_dict = self.train_epoch()
            
            # 记录历史
            self.loss_history.append(loss_dict)
            self.param_history.append({
                'A_plus': self.A_plus.item(),
                'B_plus': self.B_plus.item(),
                'xi_plus': self.xi_plus.item(),
                'A_minus': self.A_minus.item(),
                'B_minus': self.B_minus.item(),
                'xi_minus': self.xi_minus.item()
            })
            
            # 更新最佳损失
            if loss_dict['total_loss'] < best_loss:
                best_loss = loss_dict['total_loss']
                best_epoch = epoch + 1
                self.save_best_model()
            
            # 打印训练进度
            if (epoch + 1) % self.config['print_interval'] == 0:
                print(f"Epoch {epoch+1:4d}/{self.config['num_epochs']} | "
                      f"Loss: {loss_dict['total_loss']:.6f} | "
                      f"Best: {best_loss:.6f} @{best_epoch}")
                print(f"     Data: {loss_dict['loss_data']:.6f} | "
                      f"D+: {loss_dict['loss_damage_plus']:.6f} | "
                      f"D-: {loss_dict['loss_damage_minus']:.6f} | "
                      f"Plastic: {loss_dict['loss_plastic']:.6f}")
                print(f"     受拉参数: A+={self.A_plus.item():.4f}, "
                      f"B+={self.B_plus.item():.4f}, xi+={self.xi_plus.item():.4f}")
                print(f"     受压参数: A-={self.A_minus.item():.4f}, "
                      f"B-={self.B_minus.item():.4f}, xi-={self.xi_minus.item():.4f}")
            
            # 保存检查点
            if (epoch + 1) % self.config['save_interval'] == 0:
                self.save_checkpoint(epoch + 1)
        
        print("=" * 60)
        print("训练完成!")
        print(f"\n训练总结:")
        print(f"  最佳损失: {best_loss:.6f} (第{best_epoch}轮)")
        print(f"  最终损失: {self.loss_history[-1]['total_loss']:.6f}")
        print(f"\n最终识别的参数:")
        print(f"  受拉: A+ = {self.A_plus.item():.4f}, B+ = {self.B_plus.item():.4f}, xi+ = {self.xi_plus.item():.4f}")
        print(f"  受压: A- = {self.A_minus.item():.4f}, B- = {self.B_minus.item():.4f}, xi- = {self.xi_minus.item():.4f}")
        
        # 保存最终模型
        self.save_final_model()
        
        # 绘制训练历史
        self.plot_training_history()
        
    def save_checkpoint(self, epoch):
        """保存训练检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'physics_parameters': {
                'A_plus': float(self.A_plus.item()),
                'B_plus': float(self.B_plus.item()),
                'xi_plus': float(self.xi_plus.item()),
                'A_minus': float(self.A_minus.item()),
                'B_minus': float(self.B_minus.item()),
                'xi_minus': float(self.xi_minus.item())
            },
            'optimizer_state_dict': self.optimizer.state_dict(),
            'config': self.config,
            'loss_history': self.loss_history,
            'param_history': self.param_history
        }
        
        torch.save(checkpoint, f'{self.training_dir}/checkpoint_epoch_{epoch}.pth')
    
    def save_best_model(self):
        """保存最佳模型（静默保存）"""
        model_path = f'{self.training_dir}/best_model.pth'
        
        model_info = {
            'model_state_dict': self.model.state_dict(),
            'physics_parameters': {
                'A_plus': float(self.A_plus.item()),
                'B_plus': float(self.B_plus.item()),
                'xi_plus': float(self.xi_plus.item()),
                'A_minus': float(self.A_minus.item()),
                'B_minus': float(self.B_minus.item()),
                'xi_minus': float(self.xi_minus.item())
            },
            'material_constants': {
                'E0': float(self.config['E0']),
                'f_t': float(self.config['f_t']),
                'f_c': float(self.config['f_c'])
            }
        }
        
        # 静默保存，不打印信息
        import torch
        torch.save(model_info, model_path)
    
    def save_final_model(self):
        """保存最终模型"""
        model_path = f'{self.training_dir}/final_model.pth'
        
        model_info = {
            'model_state_dict': self.model.state_dict(),
            'physics_parameters': {
                'A_plus': float(self.A_plus.item()),
                'B_plus': float(self.B_plus.item()),
                'xi_plus': float(self.xi_plus.item()),
                'A_minus': float(self.A_minus.item()),
                'B_minus': float(self.B_minus.item()),
                'xi_minus': float(self.xi_minus.item())
            },
            'material_constants': {
                'E0': float(self.config['E0']),
                'f_t': float(self.config['f_t']),
                'f_c': float(self.config['f_c'])
            },
            'config': self.config,
            'loss_history': self.loss_history,
            'param_history': self.param_history,
            'timestamp': self.timestamp,
            'results_dir': self.results_dir
        }
        
        torch.save(model_info, model_path)
        
        # 保存参数到JSON文件
        params_path = f'{self.training_dir}/identified_parameters.json'
        with open(params_path, 'w', encoding='utf-8') as f:
            json.dump({
                'physics_parameters': model_info['physics_parameters'],
                'material_constants': model_info['material_constants'],
                'training_config': self.config,
                'final_loss': self.loss_history[-1] if self.loss_history else None,
                'timestamp': self.timestamp
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n模型保存完成:")
        print(f"  最终模型: {model_path}")
        print(f"  识别参数: {params_path}")
    
    def plot_training_history(self):
        """绘制训练历史"""
        if not self.loss_history:
            return
        
        epochs = range(1, len(self.loss_history) + 1)
        
        # 创建图形
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 总损失
        total_losses = [h['total_loss'] for h in self.loss_history]
        ax1.plot(epochs, total_losses, 'b-', linewidth=2)
        ax1.set_title('总损失演化')
        ax1.set_xlabel('训练轮数')
        ax1.set_ylabel('损失值')
        ax1.grid(True, alpha=0.3)
        ax1.set_yscale('log')
        
        # 2. 各项损失
        data_losses = [h['loss_data'] for h in self.loss_history]
        damage_plus_losses = [h['loss_damage_plus'] for h in self.loss_history]
        damage_minus_losses = [h['loss_damage_minus'] for h in self.loss_history]
        plastic_losses = [h['loss_plastic'] for h in self.loss_history]
        
        ax2.plot(epochs, data_losses, 'r-', label='数据拟合', linewidth=2)
        ax2.plot(epochs, damage_plus_losses, 'g-', label='受拉损伤', linewidth=2)
        ax2.plot(epochs, damage_minus_losses, 'b-', label='受压损伤', linewidth=2)
        ax2.plot(epochs, plastic_losses, 'm-', label='塑性物理', linewidth=2)
        ax2.set_title('各项损失')
        ax2.set_xlabel('训练轮数')
        ax2.set_ylabel('损失值')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_yscale('log')
        
        # 3. 损伤参数演化
        A_plus_history = [h['A_plus'] for h in self.param_history]
        B_plus_history = [h['B_plus'] for h in self.param_history]
        A_minus_history = [h['A_minus'] for h in self.param_history]
        B_minus_history = [h['B_minus'] for h in self.param_history]
        
        ax3.plot(epochs, A_plus_history, 'r-', label='A+', linewidth=2)
        ax3.plot(epochs, B_plus_history, 'r--', label='B+', linewidth=2)
        ax3.plot(epochs, A_minus_history, 'b-', label='A-', linewidth=2)
        ax3.plot(epochs, B_minus_history, 'b--', label='B-', linewidth=2)
        ax3.set_title('损伤参数演化')
        ax3.set_xlabel('训练轮数')
        ax3.set_ylabel('参数值')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 塑性参数演化
        xi_plus_history = [h['xi_plus'] for h in self.param_history]
        xi_minus_history = [h['xi_minus'] for h in self.param_history]
        
        ax4.plot(epochs, xi_plus_history, 'r-', label='ξ+ (拉伸)', linewidth=2)
        ax4.plot(epochs, xi_minus_history, 'b-', label='ξ- (压缩)', linewidth=2)
        ax4.set_title('塑性参数演化')
        ax4.set_xlabel('训练轮数')
        ax4.set_ylabel('参数值')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        save_path = f'{self.training_dir}/training_history.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"  训练历史图: {save_path}")


def main():
    """主训练函数"""
    print("混凝土双向损伤PINN训练器")
    print("=" * 60)
    
    # 创建训练器
    trainer = Trainer()
    
    # 加载数据
    trainer.load_data("cyclic_data.xlsx")
    
    # 初始化模型
    trainer.initialize_model()
    
    # 开始训练
    trainer.train()
    
    print("\n训练完成!")


if __name__ == "__main__":
    main() 