#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的单轴拉伸测试结果后处理脚本
只使用Abaqus内置模块，不依赖pandas
"""

import os
import sys
import numpy as np
from datetime import datetime

# 导入Abaqus Python模块
try:
    from odbAccess import openOdb
    from abaqusConstants import *
    IN_ABAQUS = True
    print("成功导入Abaqus模块")
except ImportError:
    print("错误: 未在Abaqus Python环境中运行")
    IN_ABAQUS = False
    sys.exit(1)


def extract_data_from_odb(odb_path):
    """从Abaqus ODB文件中提取数据"""
    print(f"正在从ODB文件提取数据: {odb_path}")
    
    try:
        odb = openOdb(path=odb_path)
        print("ODB文件打开成功")
    except Exception as e:
        print(f"打开ODB文件失败: {e}")
        return None
    
    try:
        # 获取分析步
        step_names = odb.steps.keys()
        print(f"找到分析步: {step_names}")
        step = odb.steps[step_names[-1]]  # 使用最后一个分析步
        print(f"使用分析步: {step_names[-1]}")
        
        # 获取节点集
        print("可用的节点集:", odb.rootAssembly.nodeSets.keys())
        
        # 直接使用顶部节点 (节点5,6,7,8)
        top_nodes = []
        for instance in odb.rootAssembly.instances.values():
            for node in instance.nodes:
                if node.label in [5, 6, 7, 8]:  # 顶部节点
                    top_nodes.append(node)
        
        if not top_nodes:
            print("错误: 未找到顶部节点")
            odb.close()
            return None
        
        print(f"找到 {len(top_nodes)} 个顶部节点")
        
        # 获取单元集
        print("可用的单元集:", odb.rootAssembly.elementSets.keys())
        
        # 直接使用单元1 (我们只有一个单元)
        target_element = None
        for instance in odb.rootAssembly.instances.values():
            for element in instance.elements:
                if element.label == 1:  # 单元1
                    target_element = element
                    break
        
        if target_element is None:
            print("错误: 未找到目标单元")
            odb.close()
            return None
        
        print(f"找到目标单元: {target_element.label}")
        
        print(f"总共有 {len(step.frames)} 个时间步")
        
        # 初始化数据列表
        time_data = []
        displacement_data = []
        reaction_data = []
        plastic_strain_data = []
        tensile_damage_data = []
        compressive_damage_data = []
        
        # 遍历所有时间步
        for i, frame in enumerate(step.frames):
            if i % 10 == 0:  # 每10步打印一次进度
                print(f"处理第 {i+1}/{len(step.frames)} 个时间步")
            
            time_data.append(frame.frameValue)
            
            # 提取位移
            try:
                displacement_field = frame.fieldOutputs['U']
                u3_values = []
                for instance in odb.rootAssembly.instances.values():
                    for node in instance.nodes:
                        if node.label in [5, 6, 7, 8]:  # 顶部节点
                            node_disp = displacement_field.getSubset(region=node)
                            if node_disp.values:
                                u3_values.append(node_disp.values[0].data[2])
                
                if u3_values:
                    avg_u3 = sum(u3_values) / len(u3_values)
                    displacement_data.append(avg_u3)
                else:
                    displacement_data.append(0.0)
            except Exception as e:
                print(f"提取位移数据失败 (步骤 {i}): {e}")
                displacement_data.append(0.0)
            
            # 提取反力
            try:
                reaction_field = frame.fieldOutputs['RF']
                rf3_values = []
                for instance in odb.rootAssembly.instances.values():
                    for node in instance.nodes:
                        if node.label in [5, 6, 7, 8]:  # 顶部节点
                            node_rf = reaction_field.getSubset(region=node)
                            if node_rf.values:
                                rf3_values.append(node_rf.values[0].data[2])
                
                if rf3_values:
                    sum_rf3 = sum(rf3_values)
                    reaction_data.append(sum_rf3)
                else:
                    reaction_data.append(0.0)
            except Exception as e:
                print(f"提取反力数据失败 (步骤 {i}): {e}")
                reaction_data.append(0.0)
            
            # 提取状态变量
            try:
                # 塑性应变 (SDV1)
                sdv1_field = frame.fieldOutputs['SDV1']
                ep_value = 0.0
                for instance in odb.rootAssembly.instances.values():
                    for element in instance.elements:
                        if element.label == 1:
                            element_sdv1 = sdv1_field.getSubset(region=element)
                            if element_sdv1.values:
                                ep_value = element_sdv1.values[0].data
                            break
                plastic_strain_data.append(ep_value)
                
                # 拉伸损伤 (SDV2)
                sdv2_field = frame.fieldOutputs['SDV2']
                d_plus_value = 0.0
                for instance in odb.rootAssembly.instances.values():
                    for element in instance.elements:
                        if element.label == 1:
                            element_sdv2 = sdv2_field.getSubset(region=element)
                            if element_sdv2.values:
                                d_plus_value = element_sdv2.values[0].data
                            break
                tensile_damage_data.append(d_plus_value)
                
                # 压缩损伤 (SDV3)
                sdv3_field = frame.fieldOutputs['SDV3']
                d_minus_value = 0.0
                for instance in odb.rootAssembly.instances.values():
                    for element in instance.elements:
                        if element.label == 1:
                            element_sdv3 = sdv3_field.getSubset(region=element)
                            if element_sdv3.values:
                                d_minus_value = element_sdv3.values[0].data
                            break
                compressive_damage_data.append(d_minus_value)
                
            except Exception as e:
                print(f"提取状态变量失败 (步骤 {i}): {e}")
                plastic_strain_data.append(0.0)
                tensile_damage_data.append(0.0)
                compressive_damage_data.append(0.0)
        
        odb.close()
        print("数据提取完成")
        
        return {
            'time': np.array(time_data),
            'displacement': np.array(displacement_data),
            'reaction': np.array(reaction_data),
            'plastic_strain': np.array(plastic_strain_data),
            'tensile_damage': np.array(tensile_damage_data),
            'compressive_damage': np.array(compressive_damage_data)
        }
        
    except Exception as e:
        print(f"数据提取过程中发生错误: {e}")
        odb.close()
        return None


def save_results_to_csv(data, output_file):
    """将结果保存到CSV文件"""
    print(f"保存结果到: {output_file}")
    
    # 计算应变和应力
    original_length = 250.0  # mm
    cross_section_area = 100.0 * 100.0  # mm²
    
    strain = data['displacement'] / original_length
    stress = data['reaction'] / cross_section_area
    
    # 写入CSV文件
    with open(output_file, 'w') as f:
        # 写入标题行
        f.write('Time,Displacement,Reaction,Strain,Stress,PlasticStrain,TensileDamage,CompressiveDamage\n')
        
        # 写入数据行
        for i in range(len(data['time'])):
            f.write(f"{data['time'][i]:.6f},")
            f.write(f"{data['displacement'][i]:.6f},")
            f.write(f"{data['reaction'][i]:.6f},")
            f.write(f"{strain[i]:.6f},")
            f.write(f"{stress[i]:.6f},")
            f.write(f"{data['plastic_strain'][i]:.6f},")
            f.write(f"{data['tensile_damage'][i]:.6f},")
            f.write(f"{data['compressive_damage'][i]:.6f}\n")
    
    print(f"结果已保存到: {output_file}")


def print_summary(data):
    """打印结果摘要"""
    print("\n=== 分析结果摘要 ===")
    print(f"时间步数: {len(data['time'])}")
    print(f"最大位移: {max(data['displacement']):.6f} mm")
    print(f"最大反力: {max(data['reaction']):.6f} N")
    print(f"最大塑性应变: {max(data['plastic_strain']):.6f}")
    print(f"最大拉伸损伤: {max(data['tensile_damage']):.6f}")
    print(f"最大压缩损伤: {max(data['compressive_damage']):.6f}")
    
    # 计算应变和应力
    original_length = 250.0
    cross_section_area = 100.0 * 100.0
    strain = data['displacement'] / original_length
    stress = data['reaction'] / cross_section_area
    
    print(f"最大应变: {max(strain):.6f}")
    print(f"最大应力: {max(stress):.6f} MPa")
    print("=" * 30)


def main():
    """主函数"""
    print("混凝土单轴拉伸测试结果处理 (简化版)")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        odb_path = sys.argv[1]
    else:
        odb_path = "tensile_test.odb"
    
    # 检查ODB文件是否存在
    if not os.path.exists(odb_path):
        print(f"错误: ODB文件 {odb_path} 不存在")
        return 1
    
    # 提取数据
    data = extract_data_from_odb(odb_path)
    if data is None:
        print("数据提取失败")
        return 1
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"tensile_results_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存结果
    csv_file = os.path.join(output_dir, "tensile_test_results.csv")
    save_results_to_csv(data, csv_file)
    
    # 打印摘要
    print_summary(data)
    
    print(f"\n处理完成! 结果已保存到目录: {output_dir}")
    return 0


if __name__ == "__main__":
    sys.exit(main())