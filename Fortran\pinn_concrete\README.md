# 混凝土单轴本构PINN建模项目

## 项目概述

本项目基于物理信息神经网络（Physics-Informed Neural Network, PINN）实现混凝土单轴加载下的非线性应力-应变及损伤演化关系建模。项目利用实验获得的力-位移数据，结合本构关系与损伤演化规律的物理约束，训练神经网络模型预测应力和损伤变量。

### 主要功能

- 输入：总应变（ε）
- 输出：应力（σ）与损伤变量（d）
- 物理约束：结合本构关系与损伤演化规律
- 训练数据：实验数据与合成数据

## 项目结构

```
pinn_concrete/
├── main.py               # 主程序（训练入口）
├── predict.py            # 预测脚本
├── data_processor.py     # 数据处理模块
├── pinn_model.py         # PINN模型定义
├── visualization.py      # 可视化模块
├── results/              # 结果保存目录
└── checkpoints/          # 模型检查点保存目录
```

## 安装依赖

本项目需要以下Python库：

```bash
pip install torch numpy matplotlib pandas
```

## 使用方法

### 1. 训练模型

```bash
python main.py
```

训练过程将自动加载`../data.xlsx`中的实验数据，并在`./results`目录下保存可视化结果，在`./checkpoints`目录下保存模型检查点。

### 2. 使用训练好的模型进行预测

#### 使用实验数据进行预测

```bash
python predict.py --model ./checkpoints/pinn_model.pth --data ../data.xlsx
```

#### 使用自定义应变范围进行预测

```bash
python predict.py --model ./checkpoints/pinn_model.pth --custom --min_strain 0.0 --max_strain 0.01 --num_points 100
```

### 3. 自定义材料参数

可以通过命令行参数自定义材料参数：

```bash
python predict.py --model ./checkpoints/pinn_model.pth --E 30000.0 --eps0 0.0001 --k 100.0
```

参数说明：
- `--E`: 弹性模量(MPa)
- `--eps0`: 损伤起始应变阈值
- `--k`: 损伤演化参数

## 物理约束

### 本构关系

```
σ = (1 - d) * E * ε
```

其中，σ是应力，d是损伤变量，E是弹性模量，ε是应变。

### 损伤演化规律

```
d = 1 - exp(-k * (ε - ε0)^+)
```

其中，ε0是应变阈值，k是材料参数，(ε - ε0)^+表示max(ε - ε0, 0)。

## 结果说明

训练完成后，在`./results`目录下将生成以下文件：

- `raw_data.png`: 原始应力-应变曲线
- `stress_strain_comparison.png`: 预测应力-应变曲线与实验曲线对比
- `damage_evolution.png`: 损伤变量随应变演化曲线
- `constitutive_verification.png`: 本构关系验证图
- `training_history.png`: 训练历史曲线

## 参考说明

- 可根据实际实验数据结构调整输入输出定义
- 损伤演化参数(k, ε0)可通过拟合或先验给定
- 本项目基于`pinn_stress_strain_script.md`脚本实现