C
C     混凝土本构"查表法"VUMAT子程序
C     基于PINN训练结果的查找表进行二维插值
C     
C     状态变量:
C       STATEV(1) = r_max     应变幅值包络（损伤历史）
C       STATEV(2) = eps_total 总应变
C
C    
C    
C
      SUBROUTINE VUMAT(
C     Read only (unmodifiable) variables -
     1  NBLOCK, NDIR, NSHR, NSTATEV, NFIELDV, NPROPS, LANNEAL,
     2  STEPTIME, TOTALTIME, DT, CMNAME, COORDMP, CHARLENGTH,
     3  PROPS, DENSITY, STRAININC, RELSPININC,
     4  TEMPOLD, STRETCHOLD, DEFGRADOLD, FIELDOLD,
     5  STRESSOLD, STATEOLD, ENERINTERNOLD, ENERINELASOLD,
     6  TEMPNEW, STRETCHNEW, DEFGRADNEW, FIELDNEW,
C     Write only (modifiable) variables -
     7  STRESSNEW, STAT<PERSON>E<PERSON>, ENERINTERN, ENERINEL
     8  )
     
      INCLUDE 'VABA_PARAM.INC'
C
C     参数声明
      DIMENSION PROPS(NPROPS), DENSITY(NBLOCK),
     1  COORDMP(NBLOCK,*),
     2  CHARLENGTH(NBLOCK), STRAININC(NBLOCK,NDIR+NSHR),
     3  RELSPININC(NBLOCK,NSHR), TEMPOLD(NBLOCK),
     4  STRETCHOLD(NBLOCK,NDIR+NSHR), 
     5  DEFGRADOLD(NBLOCK,NDIR+NSHR+NSHR),
     6  FIELDOLD(NBLOCK,NFIELDV), STRESSOLD(NBLOCK,NDIR+NSHR),
     7  STATEOLD(NBLOCK,NSTATEV), ENERINTERNOLD(NBLOCK),
     8  ENERINELASOLD(NBLOCK), TEMPNEW(NBLOCK),
     9  STRETCHNEW(NBLOCK,NDIR+NSHR),
     1  DEFGRADNEW(NBLOCK,NDIR+NSHR+NSHR),
     2  FIELDNEW(NBLOCK,NFIELDV),
     3  STRESSNEW(NBLOCK,NDIR+NSHR), STATENEW(NBLOCK,NSTATEV),
     4  ENERINTERN(NBLOCK), ENERINEL(NBLOCK)
      
      CHARACTER*80 CMNAME

C     查找表相关参数
      PARAMETER (MAXDATA = 100000)  ! 最大数据点数
      COMMON /LOOKUP_TABLE/ LOOKUP_DATA(MAXDATA,3), NDATA_TOTAL
      LOGICAL, SAVE :: INITIALIZED = .FALSE.
      
C     局部变量
      DOUBLE PRECISION R_MAX_OLD, EPS_TOTAL_OLD
      DOUBLE PRECISION R_MAX_NEW, EPS_TOTAL_NEW
      DOUBLE PRECISION EPS_INCREMENT, STRESS_NEW
      INTEGER K

C     初始化查找表（仅在第一次调用时执行）
      IF (.NOT. INITIALIZED) THEN
         CALL INIT_LOOKUP_TABLE()
         INITIALIZED = .TRUE.
         WRITE(*,*) 'VUMAT: 查找表初始化完成, 数据点数 = ', NDATA_TOTAL
      END IF

C     主循环：处理每个积分点
      DO K = 1, NBLOCK
         
C        获取旧状态变量
         R_MAX_OLD = STATEOLD(K, 1)
         EPS_TOTAL_OLD = STATEOLD(K, 2)
         
C        计算应变增量（仅考虑轴向分量，适用于1D问题）
         EPS_INCREMENT = STRAININC(K, 1)
         EPS_TOTAL_NEW = EPS_TOTAL_OLD + EPS_INCREMENT
         
C        更新应变幅值包络（损伤历史的关键）
         R_MAX_NEW = MAX(R_MAX_OLD, ABS(EPS_TOTAL_NEW))
         
C        通过二维插值计算新应力
         CALL INTERPOLATE_STRESS(EPS_TOTAL_NEW, R_MAX_NEW, STRESS_NEW)
         
C        更新应力张量（1D情况下只有轴向应力）
         STRESSNEW(K, 1) = STRESS_NEW
         IF (NDIR .GT. 1) STRESSNEW(K, 2) = 0.0D0
         IF (NDIR .GT. 2) STRESSNEW(K, 3) = 0.0D0
         IF (NSHR .GT. 0) THEN
            DO I = NDIR+1, NDIR+NSHR
               STRESSNEW(K, I) = 0.0D0
            END DO
         END IF
         
C        更新状态变量
         STATENEW(K, 1) = R_MAX_NEW
         STATENEW(K, 2) = EPS_TOTAL_NEW
         
C        能量计算（简化处理）
         ENERINTERN(K) = ENERINTERNOLD(K) + 
     1      0.5D0 * (STRESSOLD(K,1) + STRESSNEW(K,1)) * EPS_INCREMENT
         ENERINEL(K) = ENERINELASOLD(K)
         
      END DO
      
      RETURN
      END

C     ================================================================
C     初始化查找表子程序
C     ================================================================
      SUBROUTINE INIT_LOOKUP_TABLE()
      
      INCLUDE 'VABA_PARAM.INC'
      
      PARAMETER (MAXDATA = 100000)
      COMMON /LOOKUP_TABLE/ LOOKUP_DATA(MAXDATA,3), NDATA_TOTAL
      
      CHARACTER*256 LINE
      CHARACTER*80 FILENAME
      DOUBLE PRECISION R_MAX, STRAIN, STRESS
      INTEGER IUNIT, IOS, ICOUNT
      
C     查找表文件名
      FILENAME = 'lookup_table_1d.dat'
      IUNIT = 15
      ICOUNT = 0
      
C     打开文件
      OPEN(UNIT=IUNIT, FILE=FILENAME, STATUS='OLD', IOSTAT=IOS)
      IF (IOS .NE. 0) THEN
         WRITE(*,*) 'ERROR: 无法打开查找表文件: ', FILENAME
         STOP 'VUMAT: 查找表文件打开失败'
      END IF
      
      WRITE(*,*) 'VUMAT: 开始读取查找表文件: ', FILENAME
      
C     读取数据（跳过注释行）
      DO WHILE (.TRUE.)
         READ(IUNIT, '(A)', IOSTAT=IOS) LINE
         IF (IOS .NE. 0) EXIT  ! 文件结束或读取错误
         
C        跳过注释行和空行
         IF (LINE(1:1) .EQ. '#' .OR. LEN_TRIM(LINE) .EQ. 0) CYCLE
         
C        解析数据行: r_max, strain, stress
         READ(LINE, *, IOSTAT=IOS) R_MAX, STRAIN, STRESS
         IF (IOS .NE. 0) THEN
            WRITE(*,*) 'WARNING: 跳过无效数据行: ', TRIM(LINE)
            CYCLE
         END IF
         
         ICOUNT = ICOUNT + 1
         IF (ICOUNT .GT. MAXDATA) THEN
            WRITE(*,*) 'ERROR: 查找表数据超过最大容量 ', MAXDATA
            STOP 'VUMAT: 查找表数据容量超限'
         END IF
         
C        存储数据
         LOOKUP_DATA(ICOUNT, 1) = R_MAX
         LOOKUP_DATA(ICOUNT, 2) = STRAIN  
         LOOKUP_DATA(ICOUNT, 3) = STRESS
         
      END DO
      
      CLOSE(IUNIT)
      
      NDATA_TOTAL = ICOUNT
      WRITE(*,*) 'VUMAT: 查找表读取完成, 总数据点数: ', NDATA_TOTAL
      
C     数据验证
      IF (NDATA_TOTAL .LT. 10) THEN
         WRITE(*,*) 'ERROR: 查找表数据点过少'
         STOP 'VUMAT: 查找表数据不足'
      END IF
      
      RETURN
      END

C     ================================================================
C     二维插值子程序
C     ================================================================  
      SUBROUTINE INTERPOLATE_STRESS(TARGET_STRAIN, TARGET_R_MAX, 
     1                              STRESS_RESULT)
      
      INCLUDE 'VABA_PARAM.INC'
      
      PARAMETER (MAXDATA = 100000)
      COMMON /LOOKUP_TABLE/ LOOKUP_DATA(MAXDATA,3), NDATA_TOTAL
      
      DOUBLE PRECISION TARGET_STRAIN, TARGET_R_MAX, STRESS_RESULT
      DOUBLE PRECISION R1, R2, STRESS1, STRESS2, WEIGHT_R
      LOGICAL FOUND_R_BRACKET
      INTEGER I
      
C     初始化
      FOUND_R_BRACKET = .FALSE.
      R1 = 0.0D0
      R2 = 0.0D0
      
C     步骤1: 寻找r_max的包围区间
      DO I = 1, NDATA_TOTAL-1
         IF (LOOKUP_DATA(I,1) .LE. TARGET_R_MAX .AND. 
     1       LOOKUP_DATA(I+1,1) .GT. TARGET_R_MAX) THEN
            R1 = LOOKUP_DATA(I,1)
            R2 = LOOKUP_DATA(I+1,1)
            FOUND_R_BRACKET = .TRUE.
            EXIT
         END IF
      END DO
      
C     处理边界情况
      IF (.NOT. FOUND_R_BRACKET) THEN
         IF (TARGET_R_MAX .LE. LOOKUP_DATA(1,1)) THEN
C           目标r_max小于表中最小值，使用最小r_max的数据
            R1 = LOOKUP_DATA(1,1)
            R2 = LOOKUP_DATA(1,1)
         ELSE
C           目标r_max大于表中最大值，使用最大r_max的数据
            R1 = LOOKUP_DATA(NDATA_TOTAL,1)
            R2 = LOOKUP_DATA(NDATA_TOTAL,1)
         END IF
      END IF
      
C     步骤2: 在r_max=R1和R2对应的曲线上插值
      CALL INTERP_1D_STRAIN(TARGET_STRAIN, R1, STRESS1)
      CALL INTERP_1D_STRAIN(TARGET_STRAIN, R2, STRESS2)
      
C     步骤3: 在r_max方向上插值
      IF (ABS(R2 - R1) .LT. 1.0D-12) THEN
         STRESS_RESULT = STRESS1
      ELSE
         WEIGHT_R = (TARGET_R_MAX - R1) / (R2 - R1)
         STRESS_RESULT = STRESS1 + WEIGHT_R * (STRESS2 - STRESS1)
      END IF
      
      RETURN
      END

C     ================================================================
C     一维应变插值子程序
C     ================================================================
      SUBROUTINE INTERP_1D_STRAIN(TARGET_STRAIN, FIXED_R_MAX, 
     1                            STRESS_RESULT)
     
      INCLUDE 'VABA_PARAM.INC'
      
      PARAMETER (MAXDATA = 100000)
      COMMON /LOOKUP_TABLE/ LOOKUP_DATA(MAXDATA,3), NDATA_TOTAL
      
      DOUBLE PRECISION TARGET_STRAIN, FIXED_R_MAX, STRESS_RESULT
      DOUBLE PRECISION S1, S2, STRESS1, STRESS2, WEIGHT_S
      DOUBLE PRECISION R_MAX_TOL
      LOGICAL FOUND_S_BRACKET
      INTEGER I, ISTART, IEND
      
C     容差参数
      R_MAX_TOL = 1.0D-8
      
C     初始化
      FOUND_S_BRACKET = .FALSE.
      S1 = 0.0D0
      S2 = 0.0D0
      STRESS1 = 0.0D0
      STRESS2 = 0.0D0
      
C     找到固定r_max对应的数据段
      ISTART = 0
      IEND = 0
      
      DO I = 1, NDATA_TOTAL
         IF (ABS(LOOKUP_DATA(I,1) - FIXED_R_MAX) .LT. R_MAX_TOL) THEN
            IF (ISTART .EQ. 0) ISTART = I
            IEND = I
         END IF
      END DO
      
      IF (ISTART .EQ. 0) THEN
C        没有找到匹配的r_max，返回0应力
         STRESS_RESULT = 0.0D0
         RETURN
      END IF
      
C     在固定r_max的数据段中寻找应变的包围区间
      DO I = ISTART, IEND-1
         IF (LOOKUP_DATA(I,2) .LE. TARGET_STRAIN .AND.
     1       LOOKUP_DATA(I+1,2) .GT. TARGET_STRAIN) THEN
            S1 = LOOKUP_DATA(I,2)
            S2 = LOOKUP_DATA(I+1,2)
            STRESS1 = LOOKUP_DATA(I,3)
            STRESS2 = LOOKUP_DATA(I+1,3)
            FOUND_S_BRACKET = .TRUE.
            EXIT
         END IF
      END DO
      
C     处理边界情况
      IF (.NOT. FOUND_S_BRACKET) THEN
         IF (TARGET_STRAIN .LE. LOOKUP_DATA(ISTART,2)) THEN
C           使用起始点的应力
            STRESS_RESULT = LOOKUP_DATA(ISTART,3)
         ELSE
C           使用终点的应力
            STRESS_RESULT = LOOKUP_DATA(IEND,3)
         END IF
         RETURN
      END IF
      
C     线性插值
      IF (ABS(S2 - S1) .LT. 1.0D-12) THEN
         STRESS_RESULT = STRESS1
      ELSE
         WEIGHT_S = (TARGET_STRAIN - S1) / (S2 - S1)
         STRESS_RESULT = STRESS1 + WEIGHT_S * (STRESS2 - STRESS1)
      END IF
      
      RETURN
      END 