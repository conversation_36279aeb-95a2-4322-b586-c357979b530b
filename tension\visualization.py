#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化模块
用于生成PINN模型的各种图表和结果展示
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Optional
import os

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

class PINNVisualizer:
    """
    PINN模型结果可视化器
    """
    
    def __init__(self, figsize=(12, 8), dpi=300):
        self.figsize = figsize
        self.dpi = dpi
        self.colors = {
            'experimental': '#1f77b4',
            'predicted': '#ff7f0e', 
            'damage': '#2ca02c',
            'plastic': '#d62728',
            'elastic': '#9467bd',
            'modulus': '#8c564b'
        }
    
    def plot_stress_strain_comparison(self, 
                                    strain_exp: np.ndarray,
                                    stress_exp: np.ndarray,
                                    strain_pred: np.ndarray,
                                    stress_pred: np.ndarray,
                                    save_path: Optional[str] = None) -> None:
        """
        绘制应力-应变对比图
        """
        plt.figure(figsize=self.figsize)
        
        plt.plot(strain_exp, stress_exp, 'o', 
                color=self.colors['experimental'], 
                markersize=4, alpha=0.7, label='试验数据')
        plt.plot(strain_pred, stress_pred, '-', 
                color=self.colors['predicted'], 
                linewidth=2, label='PINN预测')
        
        plt.xlabel('应变', fontsize=12)
        plt.ylabel('应力 (MPa)', fontsize=12)
        plt.title('应力-应变关系对比', fontsize=14, fontweight='bold')
        plt.legend(fontsize=11)
        plt.grid(True, alpha=0.3)
        
        # 添加统计信息
        r2 = self._calculate_r2(stress_exp, np.interp(strain_exp, strain_pred, stress_pred))
        rmse = self._calculate_rmse(stress_exp, np.interp(strain_exp, strain_pred, stress_pred))
        
        plt.text(0.05, 0.95, f'R² = {r2:.4f}\nRMSE = {rmse:.4f} MPa', 
                transform=plt.gca().transAxes, 
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                verticalalignment='top', fontsize=10)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        
        plt.show()
    
    def plot_damage_evolution(self, 
                            strain: np.ndarray,
                            damage_pred: np.ndarray,
                            damage_theoretical: Optional[np.ndarray] = None,
                            save_path: Optional[str] = None) -> None:
        """
        绘制损伤演化图
        """
        plt.figure(figsize=self.figsize)
        
        plt.plot(strain, damage_pred, '-', 
                color=self.colors['damage'], 
                linewidth=2, label='PINN预测损伤')
        
        if damage_theoretical is not None:
            plt.plot(strain, damage_theoretical, '--', 
                    color='red', 
                    linewidth=2, alpha=0.7, label='理论损伤模型')
        
        plt.xlabel('应变', fontsize=12)
        plt.ylabel('损伤变量 D', fontsize=12)
        plt.title('损伤演化规律', fontsize=14, fontweight='bold')
        plt.legend(fontsize=11)
        plt.grid(True, alpha=0.3)
        plt.ylim(0, 1)
        
        # 添加损伤阶段标注
        plt.axhline(y=0.1, color='gray', linestyle=':', alpha=0.5)
        plt.axhline(y=0.5, color='gray', linestyle=':', alpha=0.5)
        plt.axhline(y=0.9, color='gray', linestyle=':', alpha=0.5)
        
        plt.text(0.02, 0.05, '轻微损伤', transform=plt.gca().transAxes, fontsize=9, alpha=0.7)
        plt.text(0.02, 0.25, '中等损伤', transform=plt.gca().transAxes, fontsize=9, alpha=0.7)
        plt.text(0.02, 0.55, '严重损伤', transform=plt.gca().transAxes, fontsize=9, alpha=0.7)
        plt.text(0.02, 0.95, '完全破坏', transform=plt.gca().transAxes, fontsize=9, alpha=0.7)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        
        plt.show()
    
    def plot_strain_decomposition(self, 
                                strain_total: np.ndarray,
                                strain_elastic: np.ndarray,
                                strain_plastic: np.ndarray,
                                save_path: Optional[str] = None) -> None:
        """
        绘制应变分解图
        """
        plt.figure(figsize=self.figsize)
        
        plt.plot(strain_total, strain_elastic, '-', 
                color=self.colors['elastic'], 
                linewidth=2, label='弹性应变')
        plt.plot(strain_total, strain_plastic, '-', 
                color=self.colors['plastic'], 
                linewidth=2, label='塑性应变')
        plt.plot(strain_total, strain_total, '--', 
                color='black', 
                linewidth=1, alpha=0.5, label='总应变')
        
        plt.xlabel('总应变', fontsize=12)
        plt.ylabel('应变分量', fontsize=12)
        plt.title('应变分解: ε = εₑ + εₚ', fontsize=14, fontweight='bold')
        plt.legend(fontsize=11)
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        
        plt.show()
    
    def plot_effective_modulus(self, 
                             strain: np.ndarray,
                             effective_modulus: np.ndarray,
                             initial_modulus: float,
                             save_path: Optional[str] = None) -> None:
        """
        绘制有效弹性模量演化图
        """
        plt.figure(figsize=self.figsize)
        
        plt.plot(strain, effective_modulus, '-', 
                color=self.colors['modulus'], 
                linewidth=2, label='有效弹性模量')
        plt.axhline(y=initial_modulus, color='red', linestyle='--', 
                   alpha=0.7, label=f'初始模量 E₀ = {initial_modulus:.0f} MPa')
        
        plt.xlabel('应变', fontsize=12)
        plt.ylabel('有效弹性模量 (MPa)', fontsize=12)
        plt.title('有效弹性模量演化: E_eff = (1-D)·E₀', fontsize=14, fontweight='bold')
        plt.legend(fontsize=11)
        plt.grid(True, alpha=0.3)
        
        # 计算模量退化率
        degradation = (initial_modulus - effective_modulus[-1]) / initial_modulus * 100
        plt.text(0.05, 0.95, f'最终模量退化: {degradation:.1f}%', 
                transform=plt.gca().transAxes, 
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                verticalalignment='top', fontsize=10)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        
        plt.show()
    
    def plot_comprehensive_results(self, 
                                 results: Dict[str, np.ndarray],
                                 exp_data: Optional[Dict[str, np.ndarray]] = None,
                                 save_path: Optional[str] = None) -> None:
        """
        绘制综合结果图（2x2子图）
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        strain = results['strain']
        
        # 1. 应力-应变关系
        axes[0,0].plot(strain, results['stress'], '-', 
                      color=self.colors['predicted'], linewidth=2, label='PINN预测')
        if exp_data:
            axes[0,0].plot(exp_data['strain'], exp_data['stress'], 'o', 
                          color=self.colors['experimental'], markersize=3, alpha=0.7, label='试验数据')
        axes[0,0].set_xlabel('应变')
        axes[0,0].set_ylabel('应力 (MPa)')
        axes[0,0].set_title('应力-应变关系')
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)
        
        # 2. 损伤演化
        axes[0,1].plot(strain, results['damage'], '-', 
                      color=self.colors['damage'], linewidth=2, label='损伤变量')
        if 'damage_theoretical' in results:
            axes[0,1].plot(strain, results['damage_theoretical'], '--', 
                          color='red', linewidth=2, alpha=0.7, label='理论模型')
        axes[0,1].set_xlabel('应变')
        axes[0,1].set_ylabel('损伤变量 D')
        axes[0,1].set_title('损伤演化')
        axes[0,1].legend()
        axes[0,1].grid(True, alpha=0.3)
        axes[0,1].set_ylim(0, 1)
        
        # 3. 应变分解
        axes[1,0].plot(strain, results['elastic_strain'], '-', 
                      color=self.colors['elastic'], linewidth=2, label='弹性应变')
        axes[1,0].plot(strain, results['plastic_strain'], '-', 
                      color=self.colors['plastic'], linewidth=2, label='塑性应变')
        axes[1,0].set_xlabel('总应变')
        axes[1,0].set_ylabel('应变分量')
        axes[1,0].set_title('应变分解')
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)
        
        # 4. 有效弹性模量
        axes[1,1].plot(strain, results['effective_modulus'], '-', 
                      color=self.colors['modulus'], linewidth=2)
        axes[1,1].set_xlabel('应变')
        axes[1,1].set_ylabel('有效弹性模量 (MPa)')
        axes[1,1].set_title('有效弹性模量演化')
        axes[1,1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        
        plt.show()
    
    def plot_training_history(self, 
                            training_history: Dict,
                            save_path: Optional[str] = None) -> None:
        """
        绘制训练历史
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 总损失和数据损失
        epochs = range(len(training_history['total_loss']))
        axes[0,0].plot(epochs, training_history['total_loss'], 
                      label='总损失', linewidth=2)
        axes[0,0].plot(epochs, training_history['data_loss'], 
                      label='数据损失', linewidth=2)
        axes[0,0].set_xlabel('Epoch')
        axes[0,0].set_ylabel('Loss')
        axes[0,0].set_title('训练损失曲线')
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)
        axes[0,0].set_yscale('log')
        
        # 2. 物理约束损失
        for loss_name, loss_history in training_history['physics_losses'].items():
            axes[0,1].plot(epochs, loss_history, label=loss_name, linewidth=1.5)
        axes[0,1].set_xlabel('Epoch')
        axes[0,1].set_ylabel('Physics Loss')
        axes[0,1].set_title('物理约束损失')
        axes[0,1].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        axes[0,1].grid(True, alpha=0.3)
        axes[0,1].set_yscale('log')
        
        # 3. 损失收敛性分析
        window_size = max(10, len(training_history['total_loss']) // 20)
        smoothed_loss = self._moving_average(training_history['total_loss'], window_size)
        axes[1,0].plot(epochs, training_history['total_loss'], alpha=0.3, label='原始损失')
        axes[1,0].plot(epochs[:len(smoothed_loss)], smoothed_loss, 
                      linewidth=2, label=f'平滑损失 (窗口={window_size})')
        axes[1,0].set_xlabel('Epoch')
        axes[1,0].set_ylabel('Total Loss')
        axes[1,0].set_title('损失收敛性分析')
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)
        axes[1,0].set_yscale('log')
        
        # 4. 最终损失分布
        if training_history['physics_losses']:
            final_losses = [losses[-1] for losses in training_history['physics_losses'].values()]
            loss_names = list(training_history['physics_losses'].keys())
            
            bars = axes[1,1].bar(range(len(final_losses)), final_losses, 
                               color=plt.cm.Set3(np.linspace(0, 1, len(final_losses))))
            axes[1,1].set_xticks(range(len(loss_names)))
            axes[1,1].set_xticklabels(loss_names, rotation=45, ha='right')
            axes[1,1].set_ylabel('Final Loss Value')
            axes[1,1].set_title('最终物理损失分布')
            axes[1,1].set_yscale('log')
            
            # 添加数值标签
            for bar, value in zip(bars, final_losses):
                axes[1,1].text(bar.get_x() + bar.get_width()/2, bar.get_height(),
                              f'{value:.2e}', ha='center', va='bottom', 
                              rotation=90, fontsize=8)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        
        plt.show()
    
    def plot_material_parameters_evolution(self, 
                                         parameter_history: Dict[str, List[float]],
                                         save_path: Optional[str] = None) -> None:
        """
        绘制材料参数演化图
        """
        n_params = len(parameter_history)
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        axes = axes.flatten()
        
        for i, (param_name, param_values) in enumerate(parameter_history.items()):
            if i < len(axes):
                epochs = range(len(param_values))
                axes[i].plot(epochs, param_values, linewidth=2)
                axes[i].set_xlabel('Epoch')
                axes[i].set_ylabel(param_name)
                axes[i].set_title(f'{param_name} 演化')
                axes[i].grid(True, alpha=0.3)
                
                # 添加最终值标注
                final_value = param_values[-1]
                axes[i].axhline(y=final_value, color='red', linestyle='--', alpha=0.5)
                axes[i].text(0.05, 0.95, f'最终值: {final_value:.4f}', 
                           transform=axes[i].transAxes, 
                           bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                           verticalalignment='top')
        
        # 隐藏多余的子图
        for i in range(n_params, len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        
        plt.show()
    
    def plot_error_analysis(self, 
                          strain_exp: np.ndarray,
                          stress_exp: np.ndarray,
                          stress_pred: np.ndarray,
                          save_path: Optional[str] = None) -> None:
        """
        绘制误差分析图
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 计算误差
        error = stress_pred - stress_exp
        relative_error = error / (stress_exp + 1e-8) * 100  # 避免除零
        
        # 1. 绝对误差分布
        axes[0,0].scatter(stress_exp, error, alpha=0.6, s=20)
        axes[0,0].axhline(y=0, color='red', linestyle='--')
        axes[0,0].set_xlabel('试验应力 (MPa)')
        axes[0,0].set_ylabel('预测误差 (MPa)')
        axes[0,0].set_title('绝对误差分布')
        axes[0,0].grid(True, alpha=0.3)
        
        # 2. 相对误差分布
        axes[0,1].scatter(stress_exp, relative_error, alpha=0.6, s=20)
        axes[0,1].axhline(y=0, color='red', linestyle='--')
        axes[0,1].set_xlabel('试验应力 (MPa)')
        axes[0,1].set_ylabel('相对误差 (%)')
        axes[0,1].set_title('相对误差分布')
        axes[0,1].grid(True, alpha=0.3)
        
        # 3. 预测vs实验散点图
        axes[1,0].scatter(stress_exp, stress_pred, alpha=0.6, s=20)
        min_stress = min(stress_exp.min(), stress_pred.min())
        max_stress = max(stress_exp.max(), stress_pred.max())
        axes[1,0].plot([min_stress, max_stress], [min_stress, max_stress], 
                      'r--', label='理想预测线')
        axes[1,0].set_xlabel('试验应力 (MPa)')
        axes[1,0].set_ylabel('预测应力 (MPa)')
        axes[1,0].set_title('预测 vs 试验')
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)
        
        # 添加统计信息
        r2 = self._calculate_r2(stress_exp, stress_pred)
        rmse = self._calculate_rmse(stress_exp, stress_pred)
        mae = np.mean(np.abs(error))
        
        axes[1,0].text(0.05, 0.95, f'R² = {r2:.4f}\nRMSE = {rmse:.4f}\nMAE = {mae:.4f}', 
                      transform=axes[1,0].transAxes, 
                      bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                      verticalalignment='top')
        
        # 4. 误差直方图
        axes[1,1].hist(relative_error, bins=30, alpha=0.7, edgecolor='black')
        axes[1,1].axvline(x=0, color='red', linestyle='--')
        axes[1,1].set_xlabel('相对误差 (%)')
        axes[1,1].set_ylabel('频次')
        axes[1,1].set_title('相对误差分布直方图')
        axes[1,1].grid(True, alpha=0.3)
        
        # 添加统计信息
        mean_error = np.mean(relative_error)
        std_error = np.std(relative_error)
        axes[1,1].text(0.05, 0.95, f'均值: {mean_error:.2f}%\n标准差: {std_error:.2f}%', 
                      transform=axes[1,1].transAxes, 
                      bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                      verticalalignment='top')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        
        plt.show()
    
    def _calculate_r2(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        计算R²决定系数
        """
        ss_res = np.sum((y_true - y_pred) ** 2)
        ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
        return 1 - (ss_res / (ss_tot + 1e-8))
    
    def _calculate_rmse(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        计算均方根误差
        """
        return np.sqrt(np.mean((y_true - y_pred) ** 2))
    
    def _moving_average(self, data: List[float], window_size: int) -> List[float]:
        """
        计算移动平均
        """
        if window_size >= len(data):
            return [np.mean(data)]
        
        smoothed = []
        for i in range(len(data) - window_size + 1):
            smoothed.append(np.mean(data[i:i + window_size]))
        
        return smoothed
    
    def save_all_plots(self, 
                      results: Dict[str, np.ndarray],
                      training_history: Dict,
                      exp_data: Optional[Dict[str, np.ndarray]] = None,
                      output_dir: str = "./results") -> None:
        """
        保存所有图表
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # 综合结果图
        self.plot_comprehensive_results(
            results, exp_data, 
            os.path.join(output_dir, 'comprehensive_results.png')
        )
        
        # 训练历史
        self.plot_training_history(
            training_history,
            os.path.join(output_dir, 'training_history.png')
        )
        
        # 应力-应变对比
        if exp_data:
            self.plot_stress_strain_comparison(
                exp_data['strain'], exp_data['stress'],
                results['strain'], results['stress'],
                os.path.join(output_dir, 'stress_strain_comparison.png')
            )
            
            # 误差分析
            stress_pred_interp = np.interp(exp_data['strain'], results['strain'], results['stress'])
            self.plot_error_analysis(
                exp_data['strain'], exp_data['stress'], stress_pred_interp,
                os.path.join(output_dir, 'error_analysis.png')
            )
        
        # 损伤演化
        damage_theoretical = results.get('damage_theoretical')
        self.plot_damage_evolution(
            results['strain'], results['damage'], damage_theoretical,
            os.path.join(output_dir, 'damage_evolution.png')
        )
        
        # 应变分解
        self.plot_strain_decomposition(
            results['strain'], results['elastic_strain'], results['plastic_strain'],
            os.path.join(output_dir, 'strain_decomposition.png')
        )
        
        # 有效弹性模量
        initial_modulus = results['effective_modulus'][0]
        self.plot_effective_modulus(
            results['strain'], results['effective_modulus'], initial_modulus,
            os.path.join(output_dir, 'effective_modulus.png')
        )
        
        print(f"所有图表已保存到: {output_dir}")