#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的PINN训练脚本
整合所有模块，提供完整的训练和评估流程
"""

import os
import sys
import json
import time
import warnings
from datetime import datetime
from typing import Dict, Tuple, Optional

import numpy as np
import pandas as pd
import torch
import torch.utils.data as data
from sklearn.model_selection import train_test_split

# 导入自定义模块
from config import Config, ConfigTemplates
from data_processor import TensionDataProcessor
from pinn_model import ConcreteDAMAGE_PINN, PINNTrainer
from visualization import PINNVisualizer

warnings.filterwarnings('ignore')

class PINNExperiment:
    """
    PINN实验管理器
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.device = torch.device(config.system.device)
        
        # 创建输出目录
        os.makedirs(config.output.output_dir, exist_ok=True)
        
        # 初始化组件
        self.data_processor = None
        self.model = None
        self.trainer = None
        self.visualizer = PINNVisualizer(
            figsize=config.output.figure_size,
            dpi=config.output.figure_dpi
        )
        
        # 实验记录
        self.experiment_log = {
            'start_time': None,
            'end_time': None,
            'config': config.to_dict(),
            'data_info': {},
            'training_info': {},
            'results': {}
        }
        
        print(f"PINN实验初始化完成")
        print(f"输出目录: {config.output.output_dir}")
        print(f"计算设备: {self.device}")
    
    def load_and_process_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        加载和处理数据
        """
        print("\n" + "="*50)
        print("数据加载和处理")
        print("="*50)
        
        # 初始化数据处理器
        self.data_processor = TensionDataProcessor(self.config.data.data_file)
        
        # 加载数据
        success = self.data_processor.load_data()
        
        # 数据清洗
        strain_data, stress_data = self.data_processor.clean_data()
        
        # 数据分析
        analysis_results = self.data_processor.analyze_data()
        self.experiment_log['data_info'] = analysis_results
        
        # 绘制原始数据
        if self.config.output.save_plots:
            self.data_processor.plot_raw_data(
                os.path.join(self.config.output.output_dir, 'raw_data.png')
            )
        
        # 保存处理后的数据
        if self.config.output.save_data:
            self.data_processor.save_processed_data(
                os.path.join(self.config.output.output_dir, 'processed_data.csv')
            )
        
        print(f"数据处理完成: {len(strain_data)} 个数据点")
        
        return strain_data, stress_data
    
    def prepare_training_data(self, 
                            strain_data: np.ndarray, 
                            stress_data: np.ndarray) -> Tuple[data.DataLoader, data.DataLoader]:
        """
        准备训练数据
        """
        print("\n" + "="*50)
        print("准备训练数据")
        print("="*50)
        
        # 数据标准化
        if self.config.data.normalization_method != 'none':
            strain_norm, stress_norm = self.data_processor.normalize_data(
                self.config.data.normalization_method
            )
        else:
            strain_norm = strain_data
            stress_norm = stress_data
        
        # 数据分割
        if self.config.data.validation_ratio > 0:
            strain_train, strain_val, stress_train, stress_val = train_test_split(
                strain_norm, stress_norm,
                test_size=self.config.data.validation_ratio,
                random_state=self.config.data.random_seed
            )
        else:
            strain_train, stress_train = strain_norm, stress_norm
            strain_val, stress_val = None, None
        
        # 转换为张量
        strain_train_tensor = torch.FloatTensor(strain_train.reshape(-1, 1)).to(self.device)
        stress_train_tensor = torch.FloatTensor(stress_train.reshape(-1, 1)).to(self.device)
        
        # 创建数据集和数据加载器
        train_dataset = data.TensorDataset(strain_train_tensor, stress_train_tensor)
        train_loader = data.DataLoader(
            train_dataset,
            batch_size=self.config.training.batch_size,
            shuffle=True,
            num_workers=self.config.system.num_workers,
            pin_memory=self.config.system.pin_memory
        )
        
        val_loader = None
        if strain_val is not None:
            strain_val_tensor = torch.FloatTensor(strain_val.reshape(-1, 1)).to(self.device)
            stress_val_tensor = torch.FloatTensor(stress_val.reshape(-1, 1)).to(self.device)
            val_dataset = data.TensorDataset(strain_val_tensor, stress_val_tensor)
            val_loader = data.DataLoader(
                val_dataset,
                batch_size=self.config.training.batch_size,
                shuffle=False,
                num_workers=self.config.system.num_workers,
                pin_memory=self.config.system.pin_memory
            )
        
        print(f"训练数据: {len(strain_train)} 个样本")
        if strain_val is not None:
            print(f"验证数据: {len(strain_val)} 个样本")
        print(f"批次大小: {self.config.training.batch_size}")
        
        return train_loader, val_loader
    
    def create_model(self) -> ConcreteDAMAGE_PINN:
        """
        创建PINN模型
        """
        print("\n" + "="*50)
        print("创建PINN模型")
        print("="*50)
        
        # 创建模型
        self.model = ConcreteDAMAGE_PINN(
            input_dim=self.config.model.input_dim,
            hidden_dims=self.config.model.hidden_dims,
            output_dim=self.config.model.output_dim,
            activation=self.config.model.activation
        ).to(self.device)
        
        # 设置材料参数初值
        with torch.no_grad():
            self.model.E0.data = torch.tensor(self.config.material.E0_init)
            self.model.ft.data = torch.tensor(self.config.material.ft_init)
            self.model.Gf.data = torch.tensor(self.config.material.Gf_init)
            self.model.alpha.data = torch.tensor(self.config.material.alpha_init)
            self.model.beta.data = torch.tensor(self.config.material.beta_init)
            self.model.lch.data = torch.tensor(self.config.material.lch_init)
        
        # 打印模型信息
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        print(f"模型结构: {self.config.model.hidden_dims}")
        print(f"激活函数: {self.config.model.activation}")
        print(f"总参数数量: {total_params}")
        print(f"可训练参数: {trainable_params}")
        
        # 打印初始材料参数
        print("\n初始材料参数:")
        params = self.model.get_material_parameters()
        for name, value in params.items():
            print(f"  {name}: {value:.4f}")
        
        return self.model
    
    def train_model(self, train_loader: data.DataLoader, val_loader: Optional[data.DataLoader] = None) -> Dict:
        """
        训练模型
        """
        print("\n" + "="*50)
        print("开始训练PINN模型")
        print("="*50)
        
        # 创建训练器
        self.trainer = PINNTrainer(self.model, str(self.device))
        
        # 记录训练开始时间
        start_time = time.time()
        self.experiment_log['start_time'] = datetime.now().isoformat()
        
        # 训练模型
        training_history = self.trainer.train(
            train_loader=train_loader,
            num_epochs=self.config.training.num_epochs,
            lr=self.config.training.learning_rate,
            loss_weights=self.config.training.loss_weights,
            scheduler_params={
                'step_size': self.config.training.step_size,
                'gamma': self.config.training.gamma
            } if self.config.training.use_scheduler else None
        )
        
        # 记录训练结束时间
        end_time = time.time()
        self.experiment_log['end_time'] = datetime.now().isoformat()
        training_time = end_time - start_time
        
        print(f"\n训练完成! 用时: {training_time:.2f} 秒")
        
        # 记录训练信息
        self.experiment_log['training_info'] = {
            'training_time_seconds': training_time,
            'final_loss': training_history['total_loss'][-1],
            'final_data_loss': training_history['data_loss'][-1],
            'epochs_completed': len(training_history['total_loss']),
            'final_material_parameters': self.model.get_material_parameters()
        }
        
        return training_history
    
    def evaluate_model(self, strain_data: np.ndarray, stress_data: np.ndarray) -> Dict[str, np.ndarray]:
        """
        评估模型性能
        """
        print("\n" + "="*50)
        print("模型评估")
        print("="*50)
        
        # 生成预测结果
        strain_range = np.linspace(strain_data.min(), strain_data.max(), 200)
        
        # 如果数据被标准化，需要先标准化预测范围
        if self.config.data.normalization_method != 'none':
            strain_range_norm = self.data_processor.strain_scaler.transform(strain_range.reshape(-1, 1)).flatten()
            results = self.model.predict_full_response(strain_range_norm)
            
            # 反标准化结果
            results['strain'] = strain_range
            results['stress'] = self.data_processor.inverse_transform_stress(results['stress'].reshape(-1, 1)).flatten()
        else:
            results = self.model.predict_full_response(strain_range)
        
        # 计算评估指标
        stress_pred_interp = np.interp(strain_data, results['strain'], results['stress'])
        
        r2 = self._calculate_r2(stress_data, stress_pred_interp)
        rmse = self._calculate_rmse(stress_data, stress_pred_interp)
        mae = np.mean(np.abs(stress_data - stress_pred_interp))
        mape = np.mean(np.abs((stress_data - stress_pred_interp) / (stress_data + 1e-8))) * 100
        
        evaluation_metrics = {
            'R2': r2,
            'RMSE': rmse,
            'MAE': mae,
            'MAPE': mape
        }
        
        print("评估指标:")
        for metric, value in evaluation_metrics.items():
            print(f"  {metric}: {value:.6f}")
        
        # 记录评估结果
        self.experiment_log['results'] = {
            'evaluation_metrics': evaluation_metrics,
            'final_material_parameters': self.model.get_material_parameters()
        }
        
        return results
    
    def save_results(self, 
                    results: Dict[str, np.ndarray], 
                    training_history: Dict,
                    strain_data: np.ndarray,
                    stress_data: np.ndarray) -> None:
        """
        保存所有结果
        """
        print("\n" + "="*50)
        print("保存结果")
        print("="*50)
        
        output_dir = self.config.output.output_dir
        
        # 保存模型
        if self.config.output.save_model:
            model_path = os.path.join(output_dir, self.config.output.model_filename)
            torch.save(self.model.state_dict(), model_path)
            print(f"模型已保存: {model_path}")
        
        # 保存材料参数
        if self.config.output.save_parameters:
            params_path = os.path.join(output_dir, self.config.output.parameters_filename)
            params_df = pd.DataFrame([self.model.get_material_parameters()])
            params_df.to_csv(params_path, index=False)
            print(f"材料参数已保存: {params_path}")
        
        # 保存预测结果
        if self.config.output.save_data:
            predictions_path = os.path.join(output_dir, self.config.output.predictions_filename)
            results_df = pd.DataFrame(results)
            results_df.to_csv(predictions_path, index=False)
            print(f"预测结果已保存: {predictions_path}")
        
        # 保存训练历史
        history_path = os.path.join(output_dir, self.config.output.training_history_filename)
        with open(history_path, 'w') as f:
            json.dump(training_history, f, indent=2)
        print(f"训练历史已保存: {history_path}")
        
        # 保存实验日志
        log_path = os.path.join(output_dir, 'experiment_log.json')
        with open(log_path, 'w', encoding='utf-8') as f:
            json.dump(self.experiment_log, f, indent=2, ensure_ascii=False)
        print(f"实验日志已保存: {log_path}")
        
        # 保存配置
        config_path = os.path.join(output_dir, 'config_used.json')
        self.config.save_config(config_path)
        
        # 生成图表
        if self.config.output.save_plots:
            print("\n生成可视化图表...")
            
            exp_data = {'strain': strain_data, 'stress': stress_data}
            
            # 保存所有图表
            self.visualizer.save_all_plots(
                results, training_history, exp_data, output_dir
            )
            
            # 绘制训练历史
            self.trainer.plot_training_history(
                os.path.join(output_dir, 'detailed_training_history.png')
            )
        
        print(f"\n所有结果已保存到: {output_dir}")
    
    def run_experiment(self) -> Dict:
        """
        运行完整实验
        """
        print("\n" + "="*60)
        print("开始PINN混凝土拉伸损伤演化实验")
        print("="*60)
        
        try:
            # 1. 加载和处理数据
            strain_data, stress_data = self.load_and_process_data()
            
            # 2. 准备训练数据
            train_loader, val_loader = self.prepare_training_data(strain_data, stress_data)
            
            # 3. 创建模型
            self.create_model()
            
            # 4. 训练模型
            training_history = self.train_model(train_loader, val_loader)
            
            # 5. 评估模型
            results = self.evaluate_model(strain_data, stress_data)
            
            # 6. 保存结果
            self.save_results(results, training_history, strain_data, stress_data)
            
            print("\n" + "="*60)
            print("实验完成!")
            print("="*60)
            
            return {
                'results': results,
                'training_history': training_history,
                'experiment_log': self.experiment_log
            }
            
        except Exception as e:
            print(f"\n实验过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _calculate_r2(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """计算R²决定系数"""
        ss_res = np.sum((y_true - y_pred) ** 2)
        ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
        return 1 - (ss_res / (ss_tot + 1e-8))
    
    def _calculate_rmse(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """计算均方根误差"""
        return np.sqrt(np.mean((y_true - y_pred) ** 2))

def main():
    """
    主函数
    """
    print("PINN混凝土拉伸损伤演化模型训练程序")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建配置
    config = Config()
    
    # 可以选择不同的配置模板
    # config = ConfigTemplates.quick_test()  # 快速测试
    # config = ConfigTemplates.high_accuracy()  # 高精度
    # config = ConfigTemplates.robust_training()  # 鲁棒训练
    
    # 打印配置信息
    config.print_config()
    
    # 创建实验管理器
    experiment = PINNExperiment(config)
    
    # 运行实验
    experiment_results = experiment.run_experiment()
    
    if experiment_results:
        print("\n实验成功完成!")
        
        # 打印最终结果摘要
        final_params = experiment_results['experiment_log']['results']['final_material_parameters']
        metrics = experiment_results['experiment_log']['results']['evaluation_metrics']
        
        print("\n最终材料参数:")
        for name, value in final_params.items():
            print(f"  {name}: {value:.6f}")
        
        print("\n模型性能指标:")
        for metric, value in metrics.items():
            print(f"  {metric}: {value:.6f}")
    
    else:
        print("\n实验失败!")
    
    print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()