"""
训练脚本
严格按照框架中的伪代码实现训练过程
"""

import torch
import torch.nn as nn
from torch.optim import Adam
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os
import json
from pathlib import Path

from pinn_model_v2 import DamagePINNV2, PhysicsCalculatorV2, LossCalculator
from data_processor import DataProcessor
import font_config

class Trainer:
    """
    PINN训练器
    严格按照框架要求的训练工作流
    """
    
    def __init__(self, config=None):
        # 默认配置
        self.config = {
            'E0': 30000.0,           # 初始弹性模量
            'f_t': 3.0,              # 单轴抗拉强度
            'num_epochs': 2000,      # 训练轮数
            'learning_rate': 0.001,  # 学习率
            'hidden_size': 64,       # 隐藏层大小
            'num_layers': 2,         # GRU层数
            'save_interval': 100,    # 保存间隔
            'print_interval': 50,    # 打印间隔
        }
        if config:
            self.config.update(config)
        
        # 创建带时间戳的结果文件夹
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.results_dir = f'results/training_{self.timestamp}'
        Path(self.results_dir).mkdir(parents=True, exist_ok=True)
        print(f"结果将保存到: {self.results_dir}")
        
        # 初始化组件
        self.model = None
        self.physics_calc = None
        self.loss_calc = None
        self.optimizer = None
        self.training_data = None
        
        # 可训练物理参数
        self.A_plus = None
        self.B_plus = None
        self.xi = None
        
        # 训练历史
        self.loss_history = []
        self.param_history = []
        
    def initialize_model(self):
        """
        初始化模型和组件
        """
        # 初始化PINN模型
        self.model = DamagePINNV2(
            input_size=1,
            hidden_size=self.config['hidden_size'],
            num_layers=self.config['num_layers'],
            output_size=3
        )
        
        # 初始化物理计算器
        self.physics_calc = PhysicsCalculatorV2(
            E0=self.config['E0'],
            f_t=self.config['f_t']
        )
        
        # 初始化损失计算器
        self.loss_calc = LossCalculator(
            lambda_data=1.0,
            lambda_stress=1.0,
            lambda_damage=0.8,
            lambda_plastic=2.0  # 大幅增加塑性物理损失权重
        )
        
        # 初始化可训练物理参数 - 按照框架要求
        self.A_plus = torch.nn.Parameter(torch.tensor(0.5, requires_grad=True))
        self.B_plus = torch.nn.Parameter(torch.tensor(1.0, requires_grad=True))
        self.xi = torch.nn.Parameter(torch.tensor(0.05, requires_grad=True))  # 增大初始值
        
        # 初始化优化器
        self.optimizer = Adam(
            list(self.model.parameters()) + [self.A_plus, self.B_plus, self.xi],
            lr=self.config['learning_rate']
        )
        
        print("模型初始化完成")
        print(f"  模型参数数量: {sum(p.numel() for p in self.model.parameters())}")
        print(f"  可训练物理参数: A+={self.A_plus.item():.3f}, B+={self.B_plus.item():.3f}, xi={self.xi.item():.3f}")
    
    def load_data(self, excel_path="tension.xlsx"):
        """
        加载训练数据
        """
        processor = DataProcessor(excel_path)
        
        if not processor.load_experimental_data():
            raise ValueError("数据加载失败")
        
        if not processor.validate_data_quality():
            raise ValueError("数据质量验证失败")
        
        # 准备训练数据
        self.training_data = processor.prepare_training_data()
        
        # 绘制实验数据到指定文件夹
        processor.plot_experimental_data(save_path=f"{self.results_dir}/experimental_data.png")
        
        # 获取材料属性并更新配置
        properties = processor.get_material_properties()
        self.config['E0'] = properties['E0_estimated']
        self.config['f_t'] = properties['f_t_estimated']
        
        print(f"数据加载完成，序列长度: {self.training_data['sequence_length']}")
        
    def train_epoch(self):
        """
        训练一个epoch
        严格按照框架中的伪代码
        """
        self.model.train()
        
        # 获取训练数据
        strain_increment_input = self.training_data['strain_increment_input']
        strain_total_exp = self.training_data['strain_total_exp']
        stress_exp = self.training_data['stress_exp']
        strain_increment = self.training_data['strain_increment']
        
        # --- 步骤 A: 神经网络前向传播 (输入为应变增量) ---
        sigma_hat_seq, d_hat_seq, ep_hat_seq = self.model(strain_increment_input)
        sigma_hat_seq = sigma_hat_seq.squeeze()
        d_hat_seq = d_hat_seq.squeeze()
        ep_hat_seq = ep_hat_seq.squeeze()
        
        # --- 步骤 B: 物理约束目标的增量式计算 ---
        d_phy_seq, ep_phy_seq = self.physics_calc.calculate_physics_constraints(
            strain_increment, self.A_plus, self.B_plus, self.xi
        )
        
        # --- 步骤 C: 计算总损失 ---
        total_loss, loss_dict = self.loss_calc.calculate_total_loss(
            sigma_hat_seq, d_hat_seq, ep_hat_seq,
            stress_exp, strain_total_exp,
            d_phy_seq, ep_phy_seq, self.config['E0']
        )
        
        # --- 步骤 D: 反向传播与优化 ---
        self.optimizer.zero_grad()
        total_loss.backward()
        
        # 梯度裁剪，防止梯度爆炸
        torch.nn.utils.clip_grad_norm_(
            list(self.model.parameters()) + [self.A_plus, self.B_plus, self.xi],
            max_norm=1.0
        )
        
        self.optimizer.step()
        
        # 约束物理参数在合理范围内
        with torch.no_grad():
            self.A_plus.data.clamp_(0.01, 0.99)
            self.B_plus.data.clamp_(0.1, 10.0)
            self.xi.data.clamp_(0.005, 0.2)  # 调整xi的约束范围
        
        return loss_dict
    
    def train(self):
        """
        完整训练过程
        """
        print(f"\n开始训练，共{self.config['num_epochs']}轮")
        print("=" * 60)
        
        for epoch in range(self.config['num_epochs']):
            # 训练一个epoch
            loss_dict = self.train_epoch()
            
            # 记录历史
            self.loss_history.append(loss_dict)
            self.param_history.append({
                'A_plus': self.A_plus.item(),
                'B_plus': self.B_plus.item(),
                'xi': self.xi.item()
            })
            
            # 打印训练进度
            if (epoch + 1) % self.config['print_interval'] == 0:
                print(f"Epoch {epoch+1:4d}/{self.config['num_epochs']} | "
                      f"Total Loss: {loss_dict['total_loss']:.6f} | "
                      f"Data: {loss_dict['loss_data']:.6f} | "
                      f"Stress: {loss_dict['loss_stress']:.6f} | "
                      f"Damage: {loss_dict['loss_damage']:.6f} | "
                      f"Plastic: {loss_dict['loss_plastic']:.6f}")
                print(f"     Parameters: A+={self.A_plus.item():.4f}, "
                      f"B+={self.B_plus.item():.4f}, xi={self.xi.item():.4f}")
            
            # 保存检查点
            if (epoch + 1) % self.config['save_interval'] == 0:
                self.save_checkpoint(epoch + 1)
        
        print("=" * 60)
        print("训练完成!")
        print(f"最终识别的参数:")
        print(f"  A+ = {self.A_plus.item():.4f}")
        print(f"  B+ = {self.B_plus.item():.4f}")
        print(f"  xi = {self.xi.item():.4f}")
        
        # 保存最终模型
        self.save_final_model()
        
        # 绘制训练历史
        self.plot_training_history()
        
    def save_checkpoint(self, epoch):
        """
        保存训练检查点
        """
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'A_plus': self.A_plus.item(),
            'B_plus': self.B_plus.item(),
            'xi': self.xi.item(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'config': self.config,
            'loss_history': self.loss_history,
            'param_history': self.param_history
        }
        
        torch.save(checkpoint, f'{self.results_dir}/checkpoint_epoch_{epoch}.pth')
    
    def save_final_model(self):
        """
        保存最终模型
        """
        model_path = f'{self.results_dir}/pinn_model_{self.timestamp}.pth'
        
        # 保存完整模型信息
        model_info = {
            'model_state_dict': self.model.state_dict(),
            'physics_parameters': {
                'A_plus': self.A_plus.item(),
                'B_plus': self.B_plus.item(),
                'xi': self.xi.item()
            },
            'material_constants': {
                'E0': self.config['E0'],
                'f_t': self.config['f_t']
            },
            'config': self.config,
            'loss_history': self.loss_history,
            'param_history': self.param_history,
            'timestamp': self.timestamp,
            'results_dir': self.results_dir  # 保存结果目录路径
        }
        
        torch.save(model_info, model_path)
        
        # 保存参数到JSON文件
        params_path = f'{self.results_dir}/identified_parameters_{self.timestamp}.json'
        with open(params_path, 'w', encoding='utf-8') as f:
            json.dump({
                'physics_parameters': model_info['physics_parameters'],
                'material_constants': model_info['material_constants'],
                'training_config': self.config,
                'final_loss': self.loss_history[-1] if self.loss_history else None,
                'timestamp': self.timestamp,
                'results_dir': self.results_dir
            }, f, indent=2, ensure_ascii=False)
        
        print(f"最终模型已保存至: {model_path}")
        print(f"识别参数已保存至: {params_path}")
    
    def plot_training_history(self):
        """
        绘制训练历史
        """
        if not self.loss_history:
            return
        
        epochs = range(1, len(self.loss_history) + 1)
        
        # 绘制损失历史
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 总损失
        total_losses = [h['total_loss'] for h in self.loss_history]
        ax1.plot(epochs, total_losses, 'b-', linewidth=2)
        ax1.set_title('总损失')
        ax1.set_xlabel('训练轮数')
        ax1.set_ylabel('损失值')
        ax1.grid(True, alpha=0.3)
        ax1.set_yscale('log')
        
        # 各项损失
        data_losses = [h['loss_data'] for h in self.loss_history]
        stress_losses = [h['loss_stress'] for h in self.loss_history]
        damage_losses = [h['loss_damage'] for h in self.loss_history]
        plastic_losses = [h['loss_plastic'] for h in self.loss_history]
        
        ax2.plot(epochs, data_losses, 'r-', label='数据拟合', linewidth=2)
        ax2.plot(epochs, stress_losses, 'g-', label='本构自洽', linewidth=2)
        ax2.plot(epochs, damage_losses, 'b-', label='损伤物理', linewidth=2)
        ax2.plot(epochs, plastic_losses, 'm-', label='塑性物理', linewidth=2)
        ax2.set_title('各项损失')
        ax2.set_xlabel('训练轮数')
        ax2.set_ylabel('损失值')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_yscale('log')
        
        # 参数演化
        A_plus_history = [h['A_plus'] for h in self.param_history]
        B_plus_history = [h['B_plus'] for h in self.param_history]
        xi_history = [h['xi'] for h in self.param_history]
        
        ax3.plot(epochs, A_plus_history, 'r-', label='A+', linewidth=2)
        ax3.plot(epochs, B_plus_history, 'g-', label='B+', linewidth=2)
        ax3.set_title('损伤参数演化')
        ax3.set_xlabel('训练轮数')
        ax3.set_ylabel('参数值')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        ax4.plot(epochs, xi_history, 'b-', linewidth=2)
        ax4.set_title('塑性参数演化')
        ax4.set_xlabel('训练轮数')
        ax4.set_ylabel('xi值')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        save_path = f'{self.results_dir}/training_history.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"训练历史图像已保存至: {save_path}")


def main():
    """
    主训练函数
    """
    print("初始化混凝土损伤参数识别PINN训练器")
    print("=" * 60)
    
    # 创建训练器
    trainer = Trainer()
    
    # 加载数据
    trainer.load_data("tension.xlsx")
    
    # 初始化模型
    trainer.initialize_model()
    
    # 开始训练
    trainer.train()
    
    print("训练程序完成!")


if __name__ == "__main__":
    main() 