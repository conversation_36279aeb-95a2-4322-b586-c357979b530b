SUBROUTINE VUMAT(
     1                   NBLOCK, NDIR, NSHR, NSTATEV, NFIELDV, NPROPS,
     2                   PROPS, TEMP, DTIME, STRAN, DSTR<PERSON>, TIME,
     3                   STRESS, STATEV, DDSDDE, CELENT)
C
      INCLUDE 'vaba_param.inc'
C
C     参数定义
      PARAMETER (ZERO=0.D0, ONE=1.D0, TWO=2.D0, THREE=3.D0)
      PARAMETER (TOLER=1.D-6, MAXDAM=0.99D0)
C
C     数组维数声明
      DIMENSION PROPS(NPROPS), STRESS(NBLOCK, NDIR+NSHR)
      DIMENSION STATEV(NBLOCK, NSTATEV), STRAN(NBLOCK, NDIR+NSHR)
      DIMENSION DSTRAN(NBLOCK, NDIR+NSHR)
      DIMENSION DDSDDE(NBLOCK, NDIR+NSHR, NDIR+NSHR)
      DIMENSION TIME(2)
C
C     局部变量声明
      REAL*8 E0, FT, FC, A_PLUS, B_PLUS, XI_PLUS
      REAL*8 A_MINUS, B_MINUS, XI_MINUS, NU
      REAL*8 DAMAGE_PLUS_OLD, DAMAGE_MINUS_OLD
      REAL*8 R_MAX_PLUS_OLD, R_MAX_MINUS_OLD, EP_OLD
      REAL*8 DAMAGE_PLUS_NEW, DAMAGE_MINUS_NEW
      REAL*8 R_MAX_PLUS_NEW, R_MAX_MINUS_NEW, EP_NEW
      REAL*8 STRAIN_11, DELTA_STRAIN, ELASTIC_STRAIN
      REAL*8 Y_PLUS, Y_MINUS, RATIO, EXP_TERM
      REAL*8 DAMAGE_EFF, STRESS_11, DELTA_EP, E_EFF
      REAL*8 C11, C12, C44, FAC1, FAC2, STEPTIME
      LOGICAL IS_FIRST_STEP
      INTEGER I, J
C
C     开始计算循环
      DO K = 1, NBLOCK
C
C       0. 检查是否为第一个时间步或初始化
         STEPTIME = TIME(1)
         IS_FIRST_STEP = (STEPTIME .LT. TOLER .OR. TIME(2) .LT. TOLER)
C
C       0.1 初始化刚度矩阵
         DO I = 1, NDIR+NSHR
            DO J = 1, NDIR+NSHR
               DDSDDE(K, I, J) = ZERO
            END DO
         END DO
C
C       1. 读取材料参数 (10个)
         E0 = PROPS(1)
         FT = PROPS(2)
         FC = PROPS(3)
         A_PLUS = PROPS(4)
         B_PLUS = PROPS(5)
         XI_PLUS = PROPS(6)
         A_MINUS = PROPS(7)
         B_MINUS = PROPS(8)
         XI_MINUS = PROPS(9)
         NU = PROPS(10)
C        安全检查：确保泊松比在合理范围内
         IF (NU .LT. ZERO) NU = 0.1D0
         IF (NU .GE. 0.5D0) NU = 0.49D0
C
C       2. 读取状态变量 (5个)
         DAMAGE_PLUS_OLD = STATEV(K, 1)
         DAMAGE_MINUS_OLD = STATEV(K, 2)
         R_MAX_PLUS_OLD = STATEV(K, 3)
         R_MAX_MINUS_OLD = STATEV(K, 4)
         EP_OLD = STATEV(K, 5)
C
C       2.1 初始化状态变量（第一次调用时）
        IF (R_MAX_PLUS_OLD .LT. FT) THEN
            R_MAX_PLUS_OLD = FT
            DAMAGE_PLUS_OLD = ZERO
        END IF
        IF (R_MAX_MINUS_OLD .LT. FC) THEN
            R_MAX_MINUS_OLD = FC
            DAMAGE_MINUS_OLD = ZERO
        END IF
C
C       3. 计算当前总应变 (单轴，第1方向)
        STRAIN_11 = STRAN(K, 1)
        DELTA_STRAIN = DSTRAN(K, 1)
C
C       4. 计算试验弹性应变和损伤驱动力
        ELASTIC_STRAIN = STRAIN_11 - EP_OLD
        IF (ELASTIC_STRAIN .GT. ZERO) THEN
            Y_PLUS = E0 * ELASTIC_STRAIN
        ELSE
            Y_PLUS = ZERO
        END IF
        IF (-ELASTIC_STRAIN .GT. ZERO) THEN
            Y_MINUS = E0 * (-ELASTIC_STRAIN)
        ELSE
            Y_MINUS = ZERO
        END IF
C
C       5. 更新受拉损伤
        DAMAGE_PLUS_NEW = DAMAGE_PLUS_OLD
        R_MAX_PLUS_NEW = R_MAX_PLUS_OLD
        IF (Y_PLUS .GT. R_MAX_PLUS_OLD + TOLER) THEN
            R_MAX_PLUS_NEW = Y_PLUS
          RATIO = R_MAX_PLUS_NEW / FT
          EXP_TERM = DEXP(B_PLUS * (ONE - RATIO))
          DAMAGE_PLUS_NEW = ONE - (ONE/RATIO) * 
     &                     ((ONE - A_PLUS) + A_PLUS * EXP_TERM)
          IF (DAMAGE_PLUS_NEW .LT. ZERO) DAMAGE_PLUS_NEW = ZERO
          IF (DAMAGE_PLUS_NEW .GT. MAXDAM) DAMAGE_PLUS_NEW = MAXDAM
         END IF
C
C       6. 更新受压损伤
        DAMAGE_MINUS_NEW = DAMAGE_MINUS_OLD
        R_MAX_MINUS_NEW = R_MAX_MINUS_OLD
        IF (Y_MINUS .GT. R_MAX_MINUS_OLD + TOLER) THEN
            R_MAX_MINUS_NEW = Y_MINUS
          RATIO = R_MAX_MINUS_NEW / FC
          EXP_TERM = DEXP(B_MINUS * (ONE - RATIO))
          DAMAGE_MINUS_NEW = ONE - (ONE/RATIO) * 
     &                      ((ONE - A_MINUS) + A_MINUS * EXP_TERM)
          IF (DAMAGE_MINUS_NEW .LT. ZERO) DAMAGE_MINUS_NEW = ZERO
          IF (DAMAGE_MINUS_NEW .GT. MAXDAM) DAMAGE_MINUS_NEW = MAXDAM
         END IF
C
C       7. 更新塑性应变
         DELTA_EP = ZERO
        IF (DELTA_STRAIN .GT. TOLER) THEN
            DELTA_EP = XI_PLUS * DELTA_STRAIN
        ELSE IF (DELTA_STRAIN .LT. -TOLER) THEN
            DELTA_EP = XI_MINUS * DELTA_STRAIN
         END IF
         EP_NEW = EP_OLD + DELTA_EP
C
C       8. 重新计算弹性应变和应力
        ELASTIC_STRAIN = STRAIN_11 - EP_NEW
        IF (ELASTIC_STRAIN .GE. ZERO) THEN
            DAMAGE_EFF = DAMAGE_PLUS_NEW
         ELSE
            DAMAGE_EFF = DAMAGE_MINUS_NEW
         END IF
C
C       8.1 第一步强制为纯弹性（确保初始膨胀模量为正）
         IF (IS_FIRST_STEP) THEN
             DAMAGE_EFF = ZERO
             EP_NEW = ZERO
             ELASTIC_STRAIN = STRAIN_11
             E_EFF = E0
         ELSE
             E_EFF = (ONE - DAMAGE_EFF) * E0
         END IF
C
        STRESS_11 = E_EFF * ELASTIC_STRAIN
C
C       9. 设置应力分量
        STRESS(K, 1) = STRESS_11
         STRESS(K, 2) = ZERO
         STRESS(K, 3) = ZERO
        IF (NSHR .GE. 1) STRESS(K, NDIR+1) = ZERO
        IF (NSHR .GE. 2) STRESS(K, NDIR+2) = ZERO
        IF (NSHR .GE. 3) STRESS(K, NDIR+3) = ZERO
C
C       9.1 计算各向同性刚度矩阵常数
         FAC1 = (ONE + NU) * (ONE - TWO * NU)
         FAC2 = TWO * (ONE + NU)
C        安全检查：确保分母不为零
         IF (FAC1 .LT. TOLER) FAC1 = TOLER
         IF (FAC2 .LT. TOLER) FAC2 = TOLER
C        第一步或初始化时强制使用原始弹性模量
         IF (IS_FIRST_STEP) THEN
             C11 = E0 * (ONE - NU) / FAC1
             C12 = E0 * NU / FAC1
             C44 = E0 / FAC2
         ELSE
             C11 = E_EFF * (ONE - NU) / FAC1
             C12 = E_EFF * NU / FAC1
             C44 = E_EFF / FAC2
         END IF
C
C       9.2 设置刚度矩阵 (各向同性)
         DDSDDE(K, 1, 1) = C11
         DDSDDE(K, 2, 2) = C11
         DDSDDE(K, 3, 3) = C11
         DDSDDE(K, 1, 2) = C12
         DDSDDE(K, 1, 3) = C12
         DDSDDE(K, 2, 1) = C12
         DDSDDE(K, 2, 3) = C12
         DDSDDE(K, 3, 1) = C12
         DDSDDE(K, 3, 2) = C12
         IF (NSHR .GE. 1) DDSDDE(K, NDIR+1, NDIR+1) = C44
         IF (NSHR .GE. 2) DDSDDE(K, NDIR+2, NDIR+2) = C44
         IF (NSHR .GE. 3) DDSDDE(K, NDIR+3, NDIR+3) = C44
C
C       10. 更新状态变量
         IF (IS_FIRST_STEP) THEN
C           第一步：确保状态变量为初始弹性状态
             STATEV(K, 1) = ZERO          ! d+
             STATEV(K, 2) = ZERO          ! d-
             STATEV(K, 3) = FT            ! r_max+
             STATEV(K, 4) = FC            ! r_max-
             STATEV(K, 5) = ZERO          ! ep
         ELSE
C           正常更新
             STATEV(K, 1) = DAMAGE_PLUS_NEW
             STATEV(K, 2) = DAMAGE_MINUS_NEW
             STATEV(K, 3) = R_MAX_PLUS_NEW
             STATEV(K, 4) = R_MAX_MINUS_NEW
             STATEV(K, 5) = EP_NEW
         END IF
C
      END DO
C
      RETURN
      END