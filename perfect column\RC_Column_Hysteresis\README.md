# 钢筋混凝土柱滞回曲线拟合与预测

## 项目概述
本项目基于物理信息神经网络(PINN)实现钢筋混凝土柱在循环荷载下的滞回曲线拟合与预测。通过融合Bouc-Wen物理模型与深度学习方法，实现对柱构件滞回行为的高精度建模。

## 项目结构
```
RC_Column_Hysteresis/
├── README.md                 # 项目说明文档
├── data/                    # 数据文件夹
│   └── data_utils.py        # 数据处理工具
├── models/                  # 模型定义
│   ├── __init__.py
│   └── bouc_wen_pinn.py     # Bouc-Wen PINN模型定义
├── train/                   # 训练相关
│   ├── train.py             # 训练主脚本
│   └── loss_functions.py    # 损失函数定义
├── utils/                   # 工具函数
│   ├── __init__.py
│   ├── visualization.py     # 可视化工具
│   └── metrics.py           # 评估指标
├── predict/                 # 预测功能
│   └── predict.py           # 预测脚本
└── results/                 # 结果输出
    ├── figures/             # 图表输出
    ├── models/              # 模型保存
    └── metrics/             # 性能评估
```

## 功能特点
1. **数据处理**：从Excel文件中提取柱构件的静态参数和力-位移数据，进行标准化处理
2. **物理约束建模**：基于Bouc-Wen模型实现滞回行为的物理约束
3. **神经网络架构**：设计适合滞回曲线特性的深度神经网络结构
4. **损失函数设计**：融合数据匹配损失与物理残差损失
5. **训练策略**：实现两阶段优化、正则化和动态学习率调整
6. **预测与验证**：提供完整的预测流程和多种评估指标
7. **可视化分析**：生成滞回曲线对比图和训练过程可视化

## 技术栈
- Python 3.8+
- PyTorch 1.8+
- Pandas, NumPy, Matplotlib
- Scikit-learn

## 使用方法
1. 数据准备：将柱构件的Excel数据文件放入data文件夹
2. 训练模型：运行train.py脚本
3. 预测分析：运行predict.py脚本生成预测结果
4. 结果查看：在results文件夹中查看生成的图表和评估指标