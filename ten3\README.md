# 混凝土拉伸损伤曲线PINN模型 - 整合版本

## 概述

这是一个基于物理信息神经网络(PINN)的混凝土拉伸损伤曲线识别模型的整合版本。该模型能够预测混凝土在拉伸载荷下的应力-应变关系、损伤演化和塑性应变发展。

## 功能特点

1. **数据处理**: 从Excel文件自动加载和预处理实验数据
2. **PINN模型**: 结合物理约束的神经网络架构
3. **损伤演化**: 基于热力学原理的损伤演化模型
4. **塑性应变**: 塑性应变的演化预测
5. **结果可视化**: 自动生成多种对比图表
6. **中文字体支持**: 图表中文字体正常显示

## 文件结构

```
ten3/
├── concrete_pinn_integrated.py  # 主程序文件（所有功能整合）
├── tension.xlsx                 # 实验数据文件
├── requirements.txt             # 依赖包列表
├── README.md                    # 说明文档
├── figures/                     # 生成的图像文件夹
│   ├── training_history.png    # 训练历史曲线
│   ├── stress_strain_comparison.png  # 应力-应变对比
│   ├── damage_evolution.png    # 损伤演化曲线
│   ├── plastic_strain_evolution.png  # 塑性应变演化
│   └── comprehensive_results.png     # 综合结果图
└── models/                      # 训练模型保存文件夹
    └── concrete_pinn_model_*.pth
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 准备数据

确保Excel文件 `tension.xlsx` 包含以下列：
- 应变数据列（列名包含'strain'、'应变'、'epsilon'等）
- 应力数据列（列名包含'stress'、'应力'、'sigma'等）

### 2. 运行训练

```bash
python concrete_pinn_integrated.py
```

### 3. 查看结果

训练完成后将自动生成：
- **figures/** 文件夹：包含所有结果图像
- **models/** 文件夹：包含训练好的模型文件
- **training.log**：详细的训练日志

## 输出图像说明

### 1. 应力-应变曲线对比 (stress_strain_comparison.png)
- 蓝色点：实验数据
- 红色线：PINN模型预测

### 2. 损伤演化曲线 (damage_evolution.png)
- 绿色线：损伤变量随应变的变化

### 3. 塑性应变演化曲线 (plastic_strain_evolution.png)
- 紫色线：塑性应变随总应变的发展

### 4. 训练历史曲线 (training_history.png)
- 总损失、数据损失、物理损失等的训练过程

### 5. 综合结果图 (comprehensive_results.png)
- 包含所有主要结果的四合一图表
- 右下角显示训练得到的材料参数

## 模型参数

### 固定参数
- `E0`: 初始弹性模量 (30000 MPa)
- `ft`: 抗拉强度 (3.0 MPa)

### 可训练参数
- `A⁺`: 损伤演化形状参数
- `B⁺`: 损伤演化速率参数  
- `ξ`: 塑性应变比例系数

## 配置选项

可以在 `main()` 函数中修改 `config` 字典来调整：

```python
config = {
    'E0': 30000.0,          # 初始弹性模量
    'ft': 3.0,              # 抗拉强度
    'hidden_size': 64,      # LSTM隐藏层大小
    'num_layers': 2,        # LSTM层数
    'learning_rate': 0.001, # 学习率
    'num_epochs': 1000,     # 训练轮数
    'normalize': True,      # 是否归一化数据
    'smooth_data': True,    # 是否平滑数据
    # ... 其他参数
}
```

## 硬件要求

- **CPU**: 支持所有平台
- **GPU**: 自动检测CUDA，如有GPU将自动使用加速训练
- **内存**: 建议8GB以上

## 故障排除

### 1. 字体显示问题
如果图表中文字体显示异常，程序会自动回退到英文字体。

### 2. Excel读取问题
- 确保安装了 `openpyxl`: `pip install openpyxl`
- 检查Excel文件格式和列名

### 3. 训练不收敛
- 增加训练轮数 (`num_epochs`)
- 调整学习率 (`learning_rate`)
- 修改损失权重 (`loss_weights`)

## 技术特点

1. **物理约束**: 严格遵循连续损伤力学理论
2. **LSTM记忆**: 捕捉材料历史相关性
3. **多损失函数**: 数据拟合 + 物理约束 + 参数合理性
4. **自动化流程**: 从数据加载到结果可视化全自动
5. **中文支持**: 图表和界面完全支持中文显示

## 引用

如果使用此代码，请引用相关的PINN和混凝土损伤力学文献。

## 联系方式

如有问题，请联系PINN团队。 