import torch
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader, TensorDataset

class ConcretePINN(nn.Module):
    """
    混凝土拉伸损伤曲线的物理信息神经网络(PINN)模型
    """
    def __init__(self, input_size=1, hidden_size=64, num_layers=2, output_size=3):
        super(ConcretePINN, self).__init__()
        
        # LSTM网络用于捕捉历史依赖性
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        
        # 输出层：应力、损伤变量、塑性应变
        self.output_layer = nn.Linear(hidden_size, output_size)
        
        # 激活函数
        self.activation = nn.Tanh()
        
    def forward(self, strain_seq):
        """
        前向传播
        输入: strain_seq (batch_size, seq_len, 1)
        输出: sigma_hat, d_hat, ep_hat (batch_size, seq_len, 1)
        """
        lstm_out, _ = self.lstm(strain_seq)
        output = self.output_layer(lstm_out)
        
        # 分离三个输出
        sigma_hat = output[:, :, 0:1]  # 应力
        d_hat = torch.sigmoid(output[:, :, 1:2])  # 损伤变量 [0,1]
        ep_hat = torch.relu(output[:, :, 2:3])    # 塑性应变 [0,+∞)
        
        return sigma_hat, d_hat, ep_hat

class ConcretePINNTrainer:
    """
    PINN模型训练器
    """
    def __init__(self, model, E0=30000.0, ft=3.0, device='cpu'):
        self.model = model.to(device)
        self.device = device
        
        # 固定物理参数
        self.E0 = E0  # 初始弹性模量 (MPa)
        self.ft = ft  # 抗拉强度 (MPa)
        self.r0 = ft  # 初始损伤阈值
        
        # 可训练物理参数
        self.A_plus = nn.Parameter(torch.tensor(0.8, device=device))
        self.B_plus = nn.Parameter(torch.tensor(1.0, device=device))
        self.xi = nn.Parameter(torch.tensor(0.1, device=device))
        
        # 损失函数权重
        self.lambda_data = 1.0
        self.lambda_stress = 0.8
        self.lambda_damage = 0.5
        self.lambda_plastic = 0.5
        
    def compute_physics_quantities(self, strain_seq):
        """
        计算物理约束量
        """
        batch_size, seq_len = strain_seq.shape[0], strain_seq.shape[1]
        device = strain_seq.device
        
        # 初始化
        r_max_phy = torch.full((batch_size,), self.ft, device=device)
        epsilon_p_phy = torch.zeros((batch_size,), device=device)
        
        d_phy_seq = []
        ep_phy_seq = []
        
        for i in range(seq_len):
            # 塑性累积
            if i > 0:
                delta_strain = strain_seq[:, i, 0] - strain_seq[:, i-1, 0]
                epsilon_p_phy = epsilon_p_phy + self.xi * delta_strain
            
            # 损伤驱动力
            Y_phy_i = self.E0 * (strain_seq[:, i, 0] - epsilon_p_phy)
            
            # 更新损伤阈值
            r_max_phy = torch.max(r_max_phy, Y_phy_i)
            
            # 损伤演化
            term1 = self.r0 / r_max_phy * (1 - self.A_plus)
            term2 = self.A_plus * torch.exp(self.B_plus * (1 - r_max_phy / self.r0))
            d_phy_i = 1 - (term1 + term2)
            
            # 确保损伤在合理范围内
            d_phy_i = torch.clamp(d_phy_i, 0.0, 0.99)
            
            d_phy_seq.append(d_phy_i.unsqueeze(1))
            ep_phy_seq.append(epsilon_p_phy.unsqueeze(1))
        
        d_phy_seq = torch.stack(d_phy_seq, dim=1)
        ep_phy_seq = torch.stack(ep_phy_seq, dim=1)
        
        return d_phy_seq, ep_phy_seq
    
    def compute_loss(self, strain_seq, stress_seq, sigma_hat, d_hat, ep_hat):
        """
        计算总损失
        """
        # 计算物理约束目标
        d_phy_seq, ep_phy_seq = self.compute_physics_quantities(strain_seq)
        
        # 数据拟合损失
        loss_data = torch.mean((sigma_hat.squeeze(-1) - stress_seq)**2)
        
        # 本构关系损失
        stress_constitutive = self.E0 * (1 - d_hat.squeeze(-1)) * (strain_seq.squeeze(-1) - ep_hat.squeeze(-1))
        loss_stress = torch.mean((sigma_hat.squeeze(-1) - stress_constitutive)**2)
        
        # 损伤演化损失
        loss_damage = torch.mean((d_hat.squeeze(-1) - d_phy_seq.squeeze(-1))**2)
        
        # 塑性累积损失
        loss_plastic = torch.mean((ep_hat.squeeze(-1) - ep_phy_seq.squeeze(-1))**2)
        
        # 总损失
        total_loss = (self.lambda_data * loss_data + 
                     self.lambda_stress * loss_stress + 
                     self.lambda_damage * loss_damage + 
                     self.lambda_plastic * loss_plastic)
        
        return total_loss, loss_data, loss_stress, loss_damage, loss_plastic
    
    def train(self, strain_seq, stress_seq, num_epochs=2000, lr=0.001):
        """
        训练模型
        """
        # 数据预处理
        strain_tensor = torch.tensor(strain_seq, dtype=torch.float32, device=self.device).unsqueeze(0).unsqueeze(-1)
        stress_tensor = torch.tensor(stress_seq, dtype=torch.float32, device=self.device).unsqueeze(0)
        
        # 优化器
        optimizer = optim.Adam(list(self.model.parameters()) + [self.A_plus, self.B_plus, self.xi], lr=lr)
        
        # 训练历史
        loss_history = []
        
        print("开始训练...")
        self.model.train()
        
        for epoch in range(num_epochs):
            optimizer.zero_grad()
            
            # 前向传播
            sigma_hat, d_hat, ep_hat = self.model(strain_tensor)
            
            # 计算损失
            total_loss, loss_data, loss_stress, loss_damage, loss_plastic = self.compute_loss(
                strain_tensor, stress_tensor, sigma_hat, d_hat, ep_hat
            )
            
            # 反向传播
            total_loss.backward()
            optimizer.step()
            
            # 约束参数范围
            with torch.no_grad():
                self.A_plus.data = torch.clamp(self.A_plus.data, 0.1, 1.0)
                self.B_plus.data = torch.clamp(self.B_plus.data, 0.1, 10.0)
                self.xi.data = torch.clamp(self.xi.data, 0.0, 1.0)
            
            loss_history.append(total_loss.item())
            
            if epoch % 200 == 0:
                print(f"Epoch {epoch:4d}, Loss: {total_loss.item():.6f}, "
                      f"A+: {self.A_plus.item():.4f}, B+: {self.B_plus.item():.4f}, xi: {self.xi.item():.4f}")
                print(f"  Data: {loss_data.item():.6f}, Stress: {loss_stress.item():.6f}, "
                      f"Damage: {loss_damage.item():.6f}, Plastic: {loss_plastic.item():.6f}")
        
        print("训练完成！")
        print(f"最终参数: A+ = {self.A_plus.item():.4f}, B+ = {self.B_plus.item():.4f}, xi = {self.xi.item():.4f}")
        
        return loss_history
    
    def predict(self, strain_seq):
        """
        预测模式
        """
        self.model.eval()
        with torch.no_grad():
            strain_tensor = torch.tensor(strain_seq, dtype=torch.float32, device=self.device).unsqueeze(0).unsqueeze(-1)
            sigma_pred, d_pred, ep_pred = self.model(strain_tensor)
            
            # 转换为numpy数组
            sigma_pred = sigma_pred.squeeze().cpu().numpy()
            d_pred = d_pred.squeeze().cpu().numpy()
            ep_pred = ep_pred.squeeze().cpu().numpy()
            
            return sigma_pred, d_pred, ep_pred

def load_data(file_path):
    """
    加载实验数据
    """
    try:
        # 尝试读取Excel文件
        df = pd.read_excel(file_path)
    except:
        # 如果Excel读取失败，尝试CSV
        df = pd.read_csv(file_path)
    
    # 假设数据包含'strain'和'stress'列
    if 'strain' in df.columns and 'stress' in df.columns:
        strain = df['strain'].values
        stress = df['stress'].values
    else:
        # 如果列名不同，假设前两列是应变和应力
        strain = df.iloc[:, 0].values
        stress = df.iloc[:, 1].values
    
    return strain, stress

def plot_results(strain_exp, stress_exp, strain_pred, stress_pred, damage_pred):
    """
    绘制结果对比图
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 应力-应变曲线对比
    ax1.plot(strain_exp, stress_exp, 'ro-', label='实验数据', markersize=4, linewidth=2)
    ax1.plot(strain_pred, stress_pred, 'b-', label='PINN预测', linewidth=2)
    ax1.set_xlabel('应变')
    ax1.set_ylabel('应力 (MPa)')
    ax1.set_title('应力-应变曲线对比')
    ax1.legend()
    ax1.grid(True)
    
    # 损伤-应变曲线
    ax2.plot(strain_pred, damage_pred, 'g-', label='损伤演化', linewidth=2)
    ax2.set_xlabel('应变')
    ax2.set_ylabel('损伤变量')
    ax2.set_title('损伤-应变曲线')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    # 保存图片
    plt.savefig('pinn_results.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """
    主函数
    """
    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载数据 - 修正路径
    import os
    if os.path.exists('tension.xlsx'):
        data_path = 'tension.xlsx'
    elif os.path.exists('ten4/tension.xlsx'):
        data_path = 'ten4/tension.xlsx'
    else:
        data_path = None
    
    try:
        if data_path:
            strain_exp, stress_exp = load_data(data_path)
            print(f"成功加载数据，共 {len(strain_exp)} 个数据点")
        else:
            raise FileNotFoundError("找不到数据文件")
    except Exception as e:
        print(f"数据加载失败: {e}")
        # 生成示例数据
        print("使用示例数据...")
        strain_exp = np.linspace(0, 0.001, 100)
        E0, ft = 30000, 3.0
        stress_exp = np.where(strain_exp <= ft/E0, E0 * strain_exp, 
                             ft * np.exp(-1000 * (strain_exp - ft/E0)))
    
    # 创建模型
    model = ConcretePINN(input_size=1, hidden_size=64, num_layers=2, output_size=3)
    trainer = ConcretePINNTrainer(model, E0=30000.0, ft=3.0, device=device)
    
    # 训练模型
    loss_history = trainer.train(strain_exp, stress_exp, num_epochs=2000, lr=0.001)
    
    # 预测
    print("\n开始预测...")
    stress_pred, damage_pred, plastic_pred = trainer.predict(strain_exp)
    
    # 计算预测误差
    mse = np.mean((stress_exp - stress_pred)**2)
    r2 = 1 - np.sum((stress_exp - stress_pred)**2) / np.sum((stress_exp - np.mean(stress_exp))**2)
    print(f"预测性能: MSE = {mse:.6f}, R² = {r2:.4f}")
    
    # 绘制结果
    plot_results(strain_exp, stress_exp, strain_exp, stress_pred, damage_pred)
    
    # 保存模型
    model_save_path = 'concrete_pinn_model.pth'
    torch.save({
        'model_state_dict': model.state_dict(),
        'A_plus': trainer.A_plus.item(),
        'B_plus': trainer.B_plus.item(),
        'xi': trainer.xi.item(),
        'E0': trainer.E0,
        'ft': trainer.ft
    }, model_save_path)
    print(f"模型已保存为 {model_save_path}")
    
    # 保存训练历史
    np.savetxt('loss_history.txt', loss_history, fmt='%.6f')
    print("损失历史已保存为 loss_history.txt")
    
    # 保存预测结果
    results = np.column_stack((strain_exp, stress_exp, stress_pred, damage_pred, plastic_pred))
    np.savetxt('prediction_results.txt', results, 
               header='strain_exp stress_exp stress_pred damage_pred plastic_pred', 
               fmt='%.6f')
    print("预测结果已保存为 prediction_results.txt")

if __name__ == "__main__":
    main() 