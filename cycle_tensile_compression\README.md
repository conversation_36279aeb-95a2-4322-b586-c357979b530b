# 混凝土拉压循环损伤参数识别系统

本项目实现了基于物理信息神经网络(PINN)的混凝土拉压循环损伤参数识别系统，支持完整的拉压循环滞回曲线建模。

## 项目特色

- **完整的拉压双向损伤模型**: 区分受拉损伤(d+)和受压损伤(d-)
- **统一本构框架**: 基于文献中的统一损伤本构模型
- **智能文件组织**: 每次运行的所有结果都保存在独立的会话文件夹中
- **自动化工作流**: 从训练到预测的完整自动化流程

## 文件结构

```
cycle_tensile_compression/
├── main.py                          # 主程序入口
├── pinn_model_v2_extended.py        # 扩展的PINN模型
├── data_processor_extended.py       # 扩展的数据处理器
├── train_extended.py                # 扩展的训练器
├── predict_cyclic_extended.py       # 拉压循环预测器
├── font_config.py                   # 中文字体配置
├── README.md                        # 项目说明文档
└── cyclic_data.xlsx                 # 拉压循环实验数据
```

## 结果文件组织

每次运行都会创建一个独立的会话文件夹，包含所有相关结果：

```
results/
└── session_YYYYMMDD_HHMMSS/         # 会话文件夹
    ├── training/                     # 训练阶段结果
    │   ├── pinn_model_YYYYMMDD_HHMMSS.pth           # 训练好的模型
    │   ├── identified_parameters_YYYYMMDD_HHMMSS.json # 识别的参数
    │   ├── training_history.png      # 训练历史图像
    │   ├── experimental_data.png     # 实验数据图像
    │   └── checkpoint_epoch_*.pth    # 训练检查点
    ├── prediction/                   # 预测阶段结果
    │   ├── prediction_results_YYYYMMDD_HHMMSS.xlsx  # 预测结果数据
    │   ├── analysis_results_YYYYMMDD_HHMMSS.json    # 分析结果
    │   └── cyclic_analysis_YYYYMMDD_HHMMSS.png      # 循环分析图像
    └── session_summary.txt           # 会话总结报告
```

## 运行方式

### 1. 完整运行（训练+预测）

```bash
python main.py --data cyclic_data.xlsx --epochs 1500
```

### 2. 仅训练

```bash
python main.py --train-only --data cyclic_data.xlsx --epochs 1500
```

### 3. 仅预测（使用已有模型）

```bash
python main.py --predict-only --model-path results/session_20231201_143022/training/pinn_model_20231201_143022.pth
```

### 4. 自定义参数

```bash
python main.py --data cyclic_data.xlsx --epochs 2000 --lr 0.0005
```

## 参数说明

### 命令行参数

- `--data`: 训练数据文件路径（默认：cyclic_data.xlsx）
- `--epochs`: 训练轮数（默认：1500）
- `--lr`: 学习率（默认：0.001）
- `--train-only`: 仅执行训练阶段
- `--predict-only`: 仅执行预测阶段
- `--model-path`: 用于预测的模型文件路径

### 物理参数

模型识别以下损伤本构参数：

**受拉参数**:
- A+ ∈ [0.01, 0.99]: 拉伸损伤形状参数
- B+ ∈ [0.1, 10.0]: 拉伸损伤演化参数
- ξ+ ∈ [0.005, 0.2]: 拉伸塑性系数

**受压参数**:
- A- ∈ [0.1, 5.0]: 压缩损伤形状参数
- B- ∈ [0.1, 10.0]: 压缩损伤演化参数
- ξ- ∈ [0.005, 0.2]: 压缩塑性系数

**材料常数**:
- E0: 初始弹性模量
- ft: 抗拉强度
- fc: 抗压强度

## 数据格式要求

输入的Excel文件应包含两列数据：
- `strain`: 总应变
- `stress`: 应力（MPa）

数据应包含完整的拉压循环过程，程序会自动识别加载/卸载状态。

## 输出结果

### 训练阶段输出

1. **模型文件**: 包含网络权重和识别参数的完整模型
2. **参数文件**: JSON格式的识别参数和材料常数
3. **训练历史图**: 损失函数和参数演化曲线
4. **实验数据图**: 原始实验数据的可视化
5. **检查点文件**: 训练过程中的中间模型

### 预测阶段输出

1. **预测结果**: Excel格式的详细预测数据
2. **分析结果**: JSON格式的循环分析结果
3. **综合分析图**: 包含9个子图的完整分析可视化：
   - 滞回曲线
   - 骨架曲线
   - 损伤演化
   - 塑性应变演化
   - 应变分量演化
   - 应变加载路径
   - 峰值应力演化
   - 能量耗散
   - 刚度退化

### 会话总结

每次运行结束后会生成一个总结报告，包含：
- 会话目录信息
- 运行时间记录
- 生成文件列表
- 运行状态总结

## 技术特点

### 损伤演化模型

基于统一损伤本构理论，采用双向损伤演化：

- **拉伸损伤**: d+ = 1 - (ft/r+)[(1-A+) + A+·exp(B+(1-r+/ft))]
- **压缩损伤**: d- = 1 - (fc/r-)[(1-A-) + A-·exp(B-(1-r-/fc))]

### 物理信息神经网络

- **数据拟合损失**: 确保模型输出与实验数据匹配
- **本构自洽损失**: 强制满足应力-应变关系
- **损伤物理损失**: 约束损伤演化规律
- **塑性物理损失**: 约束塑性应变演化

### 加载状态识别

自动识别实验数据中的加载/卸载状态：
- 通过应力变化和应变变化的符号关系判断
- 支持复杂的拉压循环路径
- 自动处理状态转换

## 依赖环境

```
torch >= 1.8.0
numpy >= 1.19.0
matplotlib >= 3.3.0
pandas >= 1.2.0
openpyxl >= 3.0.0
```

## 注意事项

1. **数据质量**: 确保输入数据包含完整的拉压循环
2. **参数范围**: 模型参数会自动约束在物理合理范围内
3. **收敛监控**: 训练过程包含早停机制和学习率调度
4. **内存使用**: 长序列数据可能需要较大内存
5. **文件管理**: 每次运行都会创建新的会话文件夹，注意磁盘空间

## 常见问题

**Q: 如何处理训练不收敛的情况？**
A: 可以尝试调整学习率、增加训练轮数或检查数据质量。

**Q: 预测结果如何解读？**
A: 查看生成的分析图像，重点关注滞回曲线、能量耗散和刚度退化。

**Q: 如何使用自己的数据？**
A: 将数据整理为两列（strain, stress）的Excel格式即可。

**Q: 模型可以用于不同材料吗？**
A: 模型基于通用的损伤本构理论，理论上适用于各种准脆性材料。

## 更新日志

- v2.0: 增加拉压双向损伤支持，优化文件组织结构
- v1.0: 初始版本，仅支持受拉损伤

## 联系方式

如有问题或建议，请创建Issue或联系开发团队。 