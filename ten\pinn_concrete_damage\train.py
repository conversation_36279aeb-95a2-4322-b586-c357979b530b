#!/usr/bin/env python
# -*- coding: utf-8 -*-

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
import os
from tqdm import tqdm
from sklearn.metrics import mean_squared_error, r2_score

# 导入自定义模块
from utils.data_processor import TensileDataProcessor, create_dataloader
from models.pinn_model import ConcreteDamagePINN
from utils.font_config import setup_chinese_font

# 设置中文字体
setup_chinese_font()

class FixedConcreteDamagePINN(ConcreteDamagePINN):
    """
    修复的混凝土损伤PINN模型
    """
    
    def compute_loss(self, strain_seq, stress_true, loss_weights=None):
        """
        修复的损失函数计算
        """
        # 修改损失权重，增加数据拟合损失的权重
        default_weights = {
            'data_fit': 10.0,           # 大幅增加数据拟合损失权重
            'positive_stress': 5.0,     # 应力正值约束权重
            'damage_monotonic': 1.0,    # 损伤单调性约束权重
            'plastic_constraint': 1.0   # 塑性应变约束权重
        }
        
        weights = default_weights if loss_weights is None else loss_weights
        
        # 前向传播获取预测值
        stress_pred, damage_pred, plastic_strain_pred = self.forward(strain_seq, return_components=True)
        
        # 1. 数据拟合损失 - 使用更强的MSE损失
        data_loss = nn.MSELoss()(stress_pred, stress_true)
        
        # 获取当前应变
        current_strain = strain_seq[:, -1, :]
        
        # 2. 应力正值约束损失
        positive_stress_loss = torch.mean(torch.relu(-stress_pred))  # 惩罚负应力
        
        # 3. 损伤演化物理约束损失
        # 损伤应该随应变增加而增加
        damage_monotonic_loss = torch.tensor(0.0, device=damage_pred.device)
        if len(strain_seq) > 1:
            strain_diff = current_strain - strain_seq[:, -2, :]
            damage_diff = damage_pred - self.damage_history
            # 当应变增加时，损伤不应该减少
            damage_monotonic_loss = torch.mean(torch.relu(-damage_diff * torch.relu(strain_diff)))
        
        # 4. 塑性应变物理约束损失
        # 塑性应变应该为正值且合理范围内
        plastic_constraint_loss = torch.mean(torch.relu(-plastic_strain_pred)) + \
                                torch.mean(torch.relu(plastic_strain_pred - current_strain))
        
        # 5. 应力范围约束损失
        stress_range_loss = torch.tensor(0.0, device=stress_pred.device)
        if stress_pred.max() - stress_pred.min() < 0.5:  # 如果预测应力范围太小
            stress_range_loss = 1.0 / (stress_pred.max() - stress_pred.min() + 1e-6)
        
        # 计算总损失
        total_loss = (
            weights['data_fit'] * data_loss +
            weights['positive_stress'] * positive_stress_loss +
            weights['damage_monotonic'] * damage_monotonic_loss +
            weights['plastic_constraint'] * plastic_constraint_loss +
            0.1 * stress_range_loss  # 添加应力范围约束
        )
        
        # 返回损失字典
        loss_dict = {
            'total': total_loss,
            'data_fit': data_loss,
            'positive_stress': positive_stress_loss,
            'damage_monotonic': damage_monotonic_loss,
            'plastic_constraint': plastic_constraint_loss,
            'stress_range': stress_range_loss
        }
        
        return loss_dict

def train_fixed_model():
    """
    训练修复的模型
    """
    print("=== 开始训练修复的PINN模型 ===")
    
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 加载数据
    data_path = '../tension.xlsx'
    processor = TensileDataProcessor(data_path)
    strain_data, stress_data = processor.preprocess_data(normalize=True)
    
    print(f"数据加载成功，形状: {strain_data.shape}, {stress_data.shape}")
    print(f"归一化后应变范围: {strain_data.min():.6f} 到 {strain_data.max():.6f}")
    print(f"归一化后应力范围: {stress_data.min():.6f} 到 {stress_data.max():.6f}")
    
    # 分割数据
    (train_strain, train_stress), (test_strain, test_stress) = \
        processor.split_data(strain_data, stress_data, train_ratio=0.8)
    
    # 创建数据加载器
    train_loader = create_dataloader(
        train_strain, train_stress, 
        batch_size=16,  # 减小批次大小
        sequence_length=3,  # 减小序列长度
        shuffle=True
    )
    
    test_loader = create_dataloader(
        test_strain, test_stress, 
        batch_size=16, 
        sequence_length=3, 
        shuffle=False
    )
    
    # 初始化修复的模型
    model = FixedConcreteDamagePINN(
        input_dim=1,
        hidden_dim=32,  # 减小隐藏层维度
        lstm_layers=1,  # 减少LSTM层数
        fc_layers=2,
        dropout=0.1
    ).to(device)
    
    # 优化器 - 使用更大的学习率专注于数据拟合
    optimizer = optim.Adam(model.parameters(), lr=0.01, weight_decay=1e-4)
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=50, gamma=0.8)
    
    # 训练参数
    epochs = 200
    best_test_loss = float('inf')
    train_losses = []
    test_losses = []
    
    print(f"\n开始训练，总轮数: {epochs}")
    
    for epoch in range(epochs):
        # 训练模式
        model.train()
        train_loss = 0.0
        
        # 训练循环
        for strain_seq, stress_true in train_loader:
            strain_seq = strain_seq.to(device)
            stress_true = stress_true.to(device)
            
            optimizer.zero_grad()
            
            # 重置模型状态
            model.reset_state(strain_seq.size(0))
            
            # 计算损失
            loss_dict = model.compute_loss(strain_seq, stress_true)
            loss = loss_dict['total']
            
            # 检查损失是否有效
            if torch.isnan(loss) or torch.isinf(loss):
                print(f"警告: 检测到无效损失，跳过此批次")
                continue
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            # 更新参数
            optimizer.step()
            
            train_loss += loss.item()
        
        # 计算平均训练损失
        train_loss /= len(train_loader)
        train_losses.append(train_loss)
        
        # 评估模式
        model.eval()
        test_loss = 0.0
        
        with torch.no_grad():
            for strain_seq, stress_true in test_loader:
                strain_seq = strain_seq.to(device)
                stress_true = stress_true.to(device)
                
                # 重置模型状态
                model.reset_state(strain_seq.size(0))
                
                # 计算损失
                loss_dict = model.compute_loss(strain_seq, stress_true)
                loss = loss_dict['total']
                
                test_loss += loss.item()
        
        test_loss /= len(test_loader)
        test_losses.append(test_loss)
        
        # 更新学习率
        scheduler.step()
        
        # 打印进度
        if (epoch + 1) % 20 == 0:
            print(f"Epoch {epoch+1}/{epochs}:")
            print(f"  训练损失: {train_loss:.6f}")
            print(f"  测试损失: {test_loss:.6f}")
            print(f"  学习率: {optimizer.param_groups[0]['lr']:.6f}")
            
            # 打印材料参数
            print(f"  材料参数: E_0={model.E_0.item():.1f}, r_0={model.r_0.item():.6f}")
        
        # 保存最佳模型
        if test_loss < best_test_loss:
            best_test_loss = test_loss
            
            # 保存模型
            os.makedirs('./saved_models', exist_ok=True)
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'test_loss': test_loss,
                'material_params': {
                    'E_0': model.E_0.item(),
                    'r_0': model.r_0.item(),
                    'A_plus': model.A_plus.item(),
                    'B_plus': model.B_plus.item(),
                    'xi_plus': model.xi_plus.item()
                }
            }, './saved_models/fixed_best_model.pth')
    
    print(f"\n训练完成！最佳测试损失: {best_test_loss:.6f}")
    
    # 绘制训练曲线
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='训练损失')
    plt.plot(test_losses, label='测试损失')
    plt.xlabel('轮次')
    plt.ylabel('损失')
    plt.title('训练过程')
    plt.legend()
    plt.grid(True)
    plt.yscale('log')
    plt.savefig('./results/fixed_training_curve.png')
    plt.show()
    
    return model, processor

def test_fixed_model(model, processor):
    """
    测试修复的模型
    """
    print("\n=== 测试修复的模型 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.eval()
    
    # 加载测试数据
    strain_data, stress_data = processor.preprocess_data(normalize=True)
    
    # 创建测试数据加载器
    test_loader = create_dataloader(
        strain_data, stress_data, 
        batch_size=16, 
        sequence_length=3, 
        shuffle=False
    )
    
    # 进行预测
    all_predictions = []
    all_targets = []
    all_damage = []
    all_plastic_strain = []
    
    with torch.no_grad():
        for strain_seq, stress_true in test_loader:
            strain_seq = strain_seq.to(device)
            stress_true = stress_true.to(device)
            
            # 重置模型状态
            model.reset_state(strain_seq.size(0))
            
            # 预测
            stress_pred, damage_pred, plastic_strain_pred = model(strain_seq, return_components=True)
            
            all_predictions.append(stress_pred.cpu().numpy())
            all_targets.append(stress_true.cpu().numpy())
            all_damage.append(damage_pred.cpu().numpy())
            all_plastic_strain.append(plastic_strain_pred.cpu().numpy())
    
    # 合并结果
    predictions = np.vstack(all_predictions)
    targets = np.vstack(all_targets)
    damage_values = np.vstack(all_damage)
    plastic_strain_values = np.vstack(all_plastic_strain)
    
    print(f"预测结果统计:")
    print(f"  归一化预测应力范围: {predictions.min():.6f} 到 {predictions.max():.6f}")
    print(f"  归一化目标应力范围: {targets.min():.6f} 到 {targets.max():.6f}")
    
    # 反归一化
    _, predictions_orig = processor.inverse_transform(None, predictions)
    _, targets_orig = processor.inverse_transform(None, targets)
    
    # 损伤变量和塑性应变不需要反归一化，因为它们本身就是物理量
    damage_orig = damage_values
    plastic_strain_orig = plastic_strain_values
    
    print(f"  原始预测应力范围: {predictions_orig.min():.6f} 到 {predictions_orig.max():.6f}")
    print(f"  原始目标应力范围: {targets_orig.min():.6f} 到 {targets_orig.max():.6f}")
    
    # 计算评估指标
    mse = mean_squared_error(targets_orig, predictions_orig)
    r2 = r2_score(targets_orig, predictions_orig)
    
    print(f"\n评估指标:")
    print(f"  均方误差 (MSE): {mse:.6f}")
    print(f"  决定系数 (R²): {r2:.6f}")
    
    # 绘制结果
    strain_orig = processor.strain_scaler.inverse_transform(strain_data)[:len(predictions_orig)]
    
    # 首先绘制包含损伤变量和塑性应变的综合图
    plt.figure(figsize=(20, 15))
    
    # 第一行：应力-应变曲线和损伤演化
    plt.subplot(3, 4, 1)
    plt.plot(strain_orig, targets_orig, 'b-', label='真实应力', linewidth=2)
    plt.plot(strain_orig, predictions_orig, 'r--', label='预测应力', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('应力 (MPa)')
    plt.title('应力-应变曲线对比')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(3, 4, 2)
    plt.plot(strain_orig, damage_orig, 'g-', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('损伤变量 d^+')
    plt.title('损伤演化曲线')
    plt.grid(True)
    
    plt.subplot(3, 4, 3)
    plt.plot(strain_orig, plastic_strain_orig, 'm-', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('塑性应变')
    plt.title('塑性应变演化曲线')
    plt.grid(True)
    
    plt.subplot(3, 4, 4)
    scatter = plt.scatter(damage_orig, predictions_orig, alpha=0.6, c=strain_orig, cmap='viridis')
    plt.colorbar(scatter, label='应变')
    plt.xlabel('损伤变量 d^+')
    plt.ylabel('应力 (MPa)')
    plt.title('损伤-应力关系')
    plt.grid(True)
    
    # 第二行：预测vs真实和误差分析
    plt.subplot(3, 4, 5)
    plt.scatter(targets_orig, predictions_orig, alpha=0.6)
    min_val = min(targets_orig.min(), predictions_orig.min())
    max_val = max(targets_orig.max(), predictions_orig.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'k--', linewidth=2)
    plt.xlabel('真实应力 (MPa)')
    plt.ylabel('预测应力 (MPa)')
    plt.title('预测vs真实应力')
    plt.grid(True)
    
    plt.subplot(3, 4, 6)
    errors = predictions_orig - targets_orig
    plt.hist(errors.flatten(), bins=30, alpha=0.7, color='blue')
    plt.xlabel('预测误差 (MPa)')
    plt.ylabel('频数')
    plt.title('预测误差分布')
    plt.grid(True)
    
    plt.subplot(3, 4, 7)
    plt.plot(strain_orig, errors, 'g-', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('预测误差 (MPa)')
    plt.title('误差随应变变化')
    plt.grid(True)
    
    plt.subplot(3, 4, 8)
    relative_errors = np.abs(errors) / (np.abs(targets_orig) + 1e-6) * 100
    plt.plot(strain_orig, relative_errors, 'm-', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('相对误差 (%)')
    plt.title('相对误差随应变变化')
    plt.grid(True)
    
    # 第三行：损伤和塑性应变的详细分析
    plt.subplot(3, 4, 9)
    plt.scatter(strain_orig, damage_orig, alpha=0.6, c=predictions_orig, cmap='plasma')
    plt.colorbar(label='应力 (MPa)')
    plt.xlabel('应变')
    plt.ylabel('损伤变量 d^+')
    plt.title('应变-损伤关系（按应力着色）')
    plt.grid(True)
    
    plt.subplot(3, 4, 10)
    plt.scatter(strain_orig, plastic_strain_orig, alpha=0.6, c=damage_orig, cmap='coolwarm')
    plt.colorbar(label='损伤变量')
    plt.xlabel('应变')
    plt.ylabel('塑性应变')
    plt.title('应变-塑性应变关系（按损伤着色）')
    plt.grid(True)
    
    plt.subplot(3, 4, 11)
    plt.scatter(damage_orig, plastic_strain_orig, alpha=0.6, c=strain_orig, cmap='viridis')
    plt.colorbar(label='应变')
    plt.xlabel('损伤变量 d^+')
    plt.ylabel('塑性应变')
    plt.title('损伤-塑性应变关系')
    plt.grid(True)
    
    plt.subplot(3, 4, 12)
    cumulative_error = np.cumsum(np.abs(errors))
    plt.plot(strain_orig, cumulative_error, 'c-', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('累积绝对误差 (MPa)')
    plt.title('累积误差')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('./results/comprehensive_damage_strain_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 单独绘制损伤变量和应变的关系图
    plt.figure(figsize=(15, 10))
    
    # 损伤变量和应变关系的专门分析
    plt.subplot(2, 3, 1)
    plt.plot(strain_orig, damage_orig, 'g-', linewidth=3, label='损伤演化')
    plt.xlabel('应变')
    plt.ylabel('损伤变量 d^+')
    plt.title('损伤变量随应变演化')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 3, 2)
    plt.plot(strain_orig, plastic_strain_orig, 'm-', linewidth=3, label='塑性应变演化')
    plt.xlabel('应变')
    plt.ylabel('塑性应变')
    plt.title('塑性应变随应变演化')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 3, 3)
    plt.plot(damage_orig, plastic_strain_orig, 'r-', linewidth=2, alpha=0.7)
    plt.xlabel('损伤变量 d^+')
    plt.ylabel('塑性应变')
    plt.title('损伤变量与塑性应变关系')
    plt.grid(True)
    
    plt.subplot(2, 3, 4)
    # 双y轴图：同时显示损伤和塑性应变随应变的变化
    ax1 = plt.gca()
    color = 'tab:green'
    ax1.set_xlabel('应变')
    ax1.set_ylabel('损伤变量 d^+', color=color)
    line1 = ax1.plot(strain_orig, damage_orig, color=color, linewidth=2, label='损伤变量')
    ax1.tick_params(axis='y', labelcolor=color)
    ax1.grid(True)
    
    ax2 = ax1.twinx()
    color = 'tab:purple'
    ax2.set_ylabel('塑性应变', color=color)
    line2 = ax2.plot(strain_orig, plastic_strain_orig, color=color, linewidth=2, label='塑性应变')
    ax2.tick_params(axis='y', labelcolor=color)
    
    # 添加图例
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax1.legend(lines, labels, loc='upper left')
    plt.title('损伤变量和塑性应变同步演化')
    
    plt.subplot(2, 3, 5)
    # 损伤率（损伤变量的变化率）
    if len(damage_orig) > 1:
        damage_rate = np.gradient(damage_orig.flatten(), strain_orig.flatten())
        plt.plot(strain_orig, damage_rate, 'orange', linewidth=2)
        plt.xlabel('应变')
        plt.ylabel('损伤率 (dd^+/dε)')
        plt.title('损伤发展速率')
        plt.grid(True)
    
    plt.subplot(2, 3, 6)
    # 塑性应变率
    if len(plastic_strain_orig) > 1:
        plastic_rate = np.gradient(plastic_strain_orig.flatten(), strain_orig.flatten())
        plt.plot(strain_orig, plastic_rate, 'brown', linewidth=2)
        plt.xlabel('应变')
        plt.ylabel('塑性应变率 (dεp/dε)')
        plt.title('塑性应变发展速率')
        plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('./results/damage_strain_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 保持原有的简化结果图
    plt.figure(figsize=(15, 10))
    
    # 应力-应变曲线
    plt.subplot(2, 3, 1)
    plt.plot(strain_orig, targets_orig, 'b-', label='真实应力', linewidth=2)
    plt.plot(strain_orig, predictions_orig, 'r--', label='预测应力', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('应力 (MPa)')
    plt.title('修复后应力-应变曲线对比')
    plt.legend()
    plt.grid(True)
    
    # 预测vs真实
    plt.subplot(2, 3, 2)
    plt.scatter(targets_orig, predictions_orig, alpha=0.6)
    min_val = min(targets_orig.min(), predictions_orig.min())
    max_val = max(targets_orig.max(), predictions_orig.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'k--', linewidth=2)
    plt.xlabel('真实应力 (MPa)')
    plt.ylabel('预测应力 (MPa)')
    plt.title('预测vs真实应力')
    plt.grid(True)
    
    # 误差分布
    plt.subplot(2, 3, 3)
    errors = predictions_orig - targets_orig
    plt.hist(errors.flatten(), bins=30, alpha=0.7, color='blue')
    plt.xlabel('预测误差 (MPa)')
    plt.ylabel('频数')
    plt.title('预测误差分布')
    plt.grid(True)
    
    # 误差随应变变化
    plt.subplot(2, 3, 4)
    plt.plot(strain_orig, errors, 'g-', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('预测误差 (MPa)')
    plt.title('误差随应变变化')
    plt.grid(True)
    
    # 相对误差
    plt.subplot(2, 3, 5)
    relative_errors = np.abs(errors) / (np.abs(targets_orig) + 1e-6) * 100
    plt.plot(strain_orig, relative_errors, 'm-', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('相对误差 (%)')
    plt.title('相对误差随应变变化')
    plt.grid(True)
    
    # 累积误差
    plt.subplot(2, 3, 6)
    cumulative_error = np.cumsum(np.abs(errors))
    plt.plot(strain_orig, cumulative_error, 'c-', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('累积绝对误差 (MPa)')
    plt.title('累积误差')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('./results/fixed_model_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return predictions_orig, targets_orig, damage_orig, plastic_strain_orig

if __name__ == '__main__':
    # 创建结果目录
    os.makedirs('./results', exist_ok=True)
    
    # 训练修复的模型
    model, processor = train_fixed_model()
    
    # 测试修复的模型
    predictions, targets = test_fixed_model(model, processor)
    
    print("\n=== 修复完成 ===")
    print("修复的模型已保存到 './saved_models/fixed_best_model.pth'")
    print("结果图表已保存到 './results/' 目录")