"""
PINN模型 V2版本
包含双向损伤演化（受拉d+和受压d-）和塑性应变累积
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, Tuple

class DamagePINNV2(nn.Module):
    """
    损伤PINN神经网络模型
    输入：时间步索引
    输出：应力、通用损伤变量、通用塑性应变增量系数
    """
    def __init__(self, input_size=1, hidden_size=64, output_size=3, num_layers=6):
        super(DamagePINNV2, self).__init__()
        
        # 构建深度神经网络
        layers = []
        
        # 输入层
        layers.append(nn.Linear(input_size, hidden_size))
        layers.append(nn.Tanh())
        
        # 隐藏层
        for _ in range(num_layers - 2):
            layers.append(nn.Linear(hidden_size, hidden_size))
            layers.append(nn.Tanh())
        
        # 输出层
        layers.append(nn.Linear(hidden_size, output_size))
        
        self.network = nn.Sequential(*layers)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """使用Xavier初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_normal_(m.weight)
                nn.init.zeros_(m.bias)
    
    def forward(self, t):
        """
        前向传播
        
        Args:
            t: 时间步索引张量 [batch_size, 1]
            
        Returns:
            output: [batch_size, 3] 包含 (应力, 损伤, 塑性系数)
        """
        return self.network(t)


class PhysicsCalculatorV2:
    """
    物理约束计算器 V2版本
    实现双向损伤演化和塑性应变累积的物理规律
    """
    def __init__(self, E0: float, f_t: float, f_c: float):
        """
        初始化物理计算器
        
        Args:
            E0: 初始弹性模量 (MPa)
            f_t: 抗拉强度 (MPa)
            f_c: 抗压强度 (MPa)
        """
        self.E0 = E0
        self.f_t = f_t
        self.f_c = f_c
    
    def calculate_physics_constraints(self, 
                                    strain_increment_seq: torch.Tensor,
                                    A_plus: torch.Tensor,
                                    B_plus: torch.Tensor,
                                    A_minus: torch.Tensor,
                                    B_minus: torch.Tensor,
                                    xi_plus: torch.Tensor,
                                    xi_minus: torch.Tensor) -> Tuple[torch.Tensor, ...]:
        """
        计算物理约束
        
        Args:
            strain_increment_seq: 应变增量序列
            A_plus, B_plus: 受拉损伤参数
            A_minus, B_minus: 受压损伤参数
            xi_plus: 受拉塑性系数
            xi_minus: 受压塑性系数
            
        Returns:
            d_plus_phy_seq: 受拉损伤序列
            d_minus_phy_seq: 受压损伤序列
            ep_phy_seq: 塑性应变序列
            stress_phy_seq: 物理应力序列
        """
        device = strain_increment_seq.device
        batch_size, seq_len = strain_increment_seq.shape
        
        # 初始化状态变量
        epsilon_total = torch.zeros(batch_size, device=device)
        ep_total = torch.zeros(batch_size, device=device)
        d_plus = torch.zeros(batch_size, device=device)
        d_minus = torch.zeros(batch_size, device=device)
        r_max_plus = torch.full((batch_size,), self.f_t, device=device)
        r_max_minus = torch.full((batch_size,), self.f_c, device=device)
        
        # 存储序列
        d_plus_phy_seq = []
        d_minus_phy_seq = []
        ep_phy_seq = []
        stress_phy_seq = []
        
        for i in range(seq_len):
            delta_epsilon = strain_increment_seq[:, i]
            
            # 更新总应变
            epsilon_total = epsilon_total + delta_epsilon
            
            # 计算当前有效弹性应变
            current_elastic_strain = epsilon_total - ep_total
            
            # 计算损伤驱动力
            Y_plus_current = self.E0 * torch.relu(current_elastic_strain)
            Y_minus_current = self.E0 * torch.relu(-current_elastic_strain)
            
            # 受拉损伤演化
            # 只有当Y_plus > r_max_plus时才更新损伤
            update_mask_plus = Y_plus_current > r_max_plus
            if update_mask_plus.any():
                r_max_plus = torch.where(update_mask_plus, Y_plus_current, r_max_plus)
                
                # 损伤演化公式: d+ = 1 - (f_t/r+) * ((1-A+) + A+ * exp[B+(1-r+/f_t)])
                ratio_plus = r_max_plus / self.f_t
                exp_term_plus = torch.exp(B_plus * (1 - ratio_plus))
                d_plus_new = 1 - (1/ratio_plus) * ((1 - A_plus) + A_plus * exp_term_plus)
                d_plus_new = torch.clamp(d_plus_new, 0.0, 0.99)
                
                d_plus = torch.where(update_mask_plus, d_plus_new, d_plus)
            
            # 受压损伤演化
            # 只有当Y_minus > r_max_minus时才更新损伤
            update_mask_minus = Y_minus_current > r_max_minus
            if update_mask_minus.any():
                r_max_minus = torch.where(update_mask_minus, Y_minus_current, r_max_minus)
                
                # 损伤演化公式: d- = 1 - (f_c/r-) * ((1-A-) + A- * exp[B-(1-r-/f_c)])
                ratio_minus = r_max_minus / self.f_c
                exp_term_minus = torch.exp(B_minus * (1 - ratio_minus))
                d_minus_new = 1 - (1/ratio_minus) * ((1 - A_minus) + A_minus * exp_term_minus)
                d_minus_new = torch.clamp(d_minus_new, 0.0, 0.99)
                
                d_minus = torch.where(update_mask_minus, d_minus_new, d_minus)
            
            # 塑性应变演化
            # 根据应变增量的符号选择塑性系数
            # 确保塑性应变方向与应变增量方向一致
            delta_ep_tensile = xi_plus * torch.relu(delta_epsilon)  # 拉伸塑性（正值）
            delta_ep_compressive = -xi_minus * torch.relu(-delta_epsilon)  # 压缩塑性（负值）
            
            delta_ep = torch.where(
                delta_epsilon > 0,
                delta_ep_tensile,    # 拉伸时的塑性增量
                delta_ep_compressive # 压缩时的塑性增量
            )
            ep_total = ep_total + delta_ep
            
            # 【新增】: 重新计算最终的有效弹性应变
            final_elastic_strain = epsilon_total - ep_total
            
            # 【修改】: 根据最终弹性应变的符号选择损伤变量
            d_effective = torch.where(final_elastic_strain >= 0, d_plus, d_minus)
            
            # 【修改】: 使用最终弹性应变计算物理应力
            current_stress_phy = (1 - d_effective) * self.E0 * final_elastic_strain
            
            # 记录当前步的状态
            d_plus_phy_seq.append(d_plus.clone())
            d_minus_phy_seq.append(d_minus.clone())
            ep_phy_seq.append(ep_total.clone())
            stress_phy_seq.append(current_stress_phy.clone())
        
        # 堆叠序列
        d_plus_phy_seq = torch.stack(d_plus_phy_seq, dim=1)
        d_minus_phy_seq = torch.stack(d_minus_phy_seq, dim=1)
        ep_phy_seq = torch.stack(ep_phy_seq, dim=1)
        stress_phy_seq = torch.stack(stress_phy_seq, dim=1)
        
        return d_plus_phy_seq, d_minus_phy_seq, ep_phy_seq, stress_phy_seq


class LossCalculator:
    """
    损失函数计算器
    包含数据拟合、本构自洽、拉压损伤物理和塑性物理损失
    """
    def __init__(self, 
                 lambda_data: float = 1.0,
                 lambda_stress: float = 1.0,
                 lambda_damage_plus: float = 1.0,
                 lambda_damage_minus: float = 1.0,
                 lambda_plastic: float = 1.0):
        """
        初始化损失函数计算器
        
        Args:
            lambda_data: 数据拟合损失权重
            lambda_stress: 本构自洽损失权重
            lambda_damage_plus: 受拉损伤损失权重
            lambda_damage_minus: 受压损伤损失权重
            lambda_plastic: 塑性物理损失权重
        """
        self.lambda_data = lambda_data
        self.lambda_stress = lambda_stress
        self.lambda_damage_plus = lambda_damage_plus
        self.lambda_damage_minus = lambda_damage_minus
        self.lambda_plastic = lambda_plastic
    
    def calculate_total_loss(self,
                           sigma_hat_seq: torch.Tensor,
                           d_hat_seq: torch.Tensor,
                           xi_hat_seq: torch.Tensor,
                           stress_exp_seq: torch.Tensor,
                           strain_total_seq: torch.Tensor,
                           d_plus_phy_seq: torch.Tensor,
                           d_minus_phy_seq: torch.Tensor,
                           ep_phy_seq: torch.Tensor,
                           stress_phy_seq: torch.Tensor,
                           E0: float) -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        计算总损失
        
        Args:
            sigma_hat_seq: 神经网络预测的应力序列
            d_hat_seq: 神经网络预测的损伤序列
            xi_hat_seq: 神经网络预测的塑性系数序列
            stress_exp_seq: 实验应力序列
            strain_total_seq: 总应变序列
            d_plus_phy_seq: 物理计算的受拉损伤序列
            d_minus_phy_seq: 物理计算的受压损伤序列
            ep_phy_seq: 物理计算的塑性应变序列
            stress_phy_seq: 物理计算的应力序列
            E0: 弹性模量
        
        Returns:
            total_loss: 总损失
            loss_dict: 各项损失的字典
        """
        # 1. 数据拟合损失
        loss_data = torch.mean((sigma_hat_seq - stress_exp_seq) ** 2)
        
        # 2. 本构自洽损失
        # 从xi_hat计算累积塑性应变
        ep_hat_seq_cumsum = torch.cumsum(xi_hat_seq, dim=1)
        
        # 计算弹性应变
        elastic_strain_seq = strain_total_seq - ep_hat_seq_cumsum
        
        # 根据弹性应变符号选择损伤进行本构计算
        stress_constitutive = (1 - d_hat_seq) * E0 * elastic_strain_seq
        loss_stress = torch.mean((sigma_hat_seq - stress_constitutive) ** 2)
        
        # 3. 损伤物理损失 - 分别计算拉压损失
        # 创建拉压区域掩码（基于弹性应变）
        tensile_mask = (elastic_strain_seq >= 0).float()
        compressive_mask = (elastic_strain_seq < 0).float()
        
        # 受拉损伤损失（只在拉伸区域计算）
        loss_damage_plus = torch.sum((d_hat_seq - d_plus_phy_seq) ** 2 * tensile_mask) / (torch.sum(tensile_mask) + 1e-6)
        
        # 受压损伤损失（只在压缩区域计算）
        loss_damage_minus = torch.sum((d_hat_seq - d_minus_phy_seq) ** 2 * compressive_mask) / (torch.sum(compressive_mask) + 1e-6)
        
        # 4. 塑性物理损失
        loss_plastic = torch.mean((ep_hat_seq_cumsum - ep_phy_seq) ** 2)
        
        # 5. 应力一致性损失（确保神经网络预测的应力与物理计算的应力一致）
        loss_stress_physics = torch.mean((sigma_hat_seq - stress_phy_seq) ** 2)
        
        # 计算总损失
        total_loss = (self.lambda_data * loss_data + 
                     self.lambda_stress * loss_stress + 
                     self.lambda_damage_plus * loss_damage_plus +
                     self.lambda_damage_minus * loss_damage_minus +
                     self.lambda_plastic * loss_plastic +
                     0.5 * loss_stress_physics)  # 额外的物理一致性约束
        
        # 创建损失字典
        loss_dict = {
            'loss_data': loss_data.item(),
            'loss_stress': loss_stress.item(), 
            'loss_damage_plus': loss_damage_plus.item(),
            'loss_damage_minus': loss_damage_minus.item(),
            'loss_plastic': loss_plastic.item(),
            'loss_stress_physics': loss_stress_physics.item(),
            'total_loss': total_loss.item()
        } 
        
        return total_loss, loss_dict


# 测试代码
if __name__ == "__main__":
    # 测试模型组件
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    model = DamagePINNV2().to(device)
    physics_calc = PhysicsCalculatorV2(E0=30000, f_t=3.0, f_c=30.0)
    loss_calc = LossCalculator()
    
    # 创建测试数据
    batch_size, seq_len = 32, 100
    t = torch.linspace(0, 1, seq_len).unsqueeze(0).repeat(batch_size, 1).unsqueeze(-1).to(device)
    strain_increments = torch.randn(batch_size, seq_len).to(device) * 0.0001
    
    # 测试前向传播
    output = model(t.reshape(-1, 1))
    print(f"模型输出形状: {output.shape}")
    
    # 测试物理计算
    A_plus = torch.tensor(1.0, device=device)
    B_plus = torch.tensor(1.0, device=device)
    A_minus = torch.tensor(1.5, device=device)
    B_minus = torch.tensor(0.5, device=device)
    xi_plus = torch.tensor(0.01, device=device)
    xi_minus = torch.tensor(0.02, device=device)
    
    d_plus_phy, d_minus_phy, ep_phy, stress_phy = physics_calc.calculate_physics_constraints(
        strain_increments, A_plus, B_plus, A_minus, B_minus, xi_plus, xi_minus
    )
    
    print(f"物理损伤(拉)序列形状: {d_plus_phy.shape}")
    print(f"物理损伤(压)序列形状: {d_minus_phy.shape}")
    print(f"物理塑性应变序列形状: {ep_phy.shape}")
    print(f"物理应力序列形状: {stress_phy.shape}") 