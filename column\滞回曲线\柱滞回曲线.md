# 柱子结构损伤预测PINN模型项目脚本

## 1. 项目目标
构建基于物理信息的神经网络（PINN）模型，实现以下功能：

- 输入柱子的**静态参数**和**位移序列**，预测对应的**滞回力曲线**
- 捕捉路径依赖性和材料非线性行为
- 验证模型在未见过样本上的泛化能力

---

## 2. 数据流设计

### 2.1 原始数据结构

```python
# 示例数据结构示意
sample = {
    "static_params": {      # 来自"基础参数"表
        "concrete_strength": 38.0,   # 混凝土强度 (MPa)
        "diameter": 400,            # 直径 (mm)
        "reinforcement_ratio": 0.032, # 配筋率
        ... # 其他20+参数
    },
    "dynamic_sequence": [   # 来自"力的位移数据"表
        {"displacement": 0.0, "force": 0.0},
        {"displacement": -1.428, "force": 0.084},
        ... # 500+时间步
    ]
}
```
### 2.2 数据预处理流程

#### 静态参数提取

- 从Excel的"基础参数"表提取材料、几何、加固参数
- 删除无关列（单位、备注等）
- 数值型参数归一化（Min-Max Scaling）

#### 动态序列处理

- 位移标准化：(x - μ)/σ
- 构建位移增量特征：Δx_t = x_t - x_{t-1}
- 序列对齐：确保所有样本时间步长度一致（padding/cutting）

#### 数据配对

- 每个时间步输入 = [静态参数 | 当前位移 | Δ位移]
- 输出 = 当前力值

#### 数据集划分

- 训练集（70%）、验证集（15%）、测试集（15%）
- 按柱子ID划分，保证同一柱子的数据不跨集合

## 3. 模型架构设计

### 3.1 网络结构

```
Input Layer (N+2)
    ├─ [静态参数] → N维
    ├─ [当前位移] → 1维
    └─ [位移增量] → 1维
↓
Hidden Block 1
    ├─ Dense(256) → LayerNorm → Swish
    ├─ Dense(256) → LayerNorm → Swish
↓
Hidden Block 2
    ├─ Dense(128) → LayerNorm → Swish
    ├─ Dense(128) → LayerNorm → Swish
↓
Output Layer
    └─ Dense(1)  # 预测力值
```

### 3.2 关键设计

- **特征拼接**：静态参数与动态变量早期融合
- **归一化层**：每个隐藏层后接Layer Normalization
- **残差连接**：每两个隐藏层之间添加跳跃连接
- **物理约束**：通过自定义损失函数实现

## 4. 损失函数设计

### 4.1 数据保真项

```
MSE = 1/N Σ (F_pred - F_true)^2
```

### 4.2 物理约束项

- **滞回闭合约束**：
  ```
  closure_loss = (F_pred[0] - F_pred[-1])^2 + (x_pred[0] - x_pred[-1])^2
  ```
- **能量守恒约束**：
  ```
  energy_loss = |∮ F_pred dx - ∮ F_true dx|
  ```
- **对称性约束（可选）**：
  ```
  sym_loss = Σ |F_pred(x) + F_pred(-x)| 
  ```

### 4.3 复合损失函数

```
total_loss = MSE + λ1*closure_loss + λ2*energy_loss + λ3*sym_loss
```

（λ1=0.1, λ2=0.05, λ3=0.01 作为初始设置）

## 5. 训练策略

### 5.1 优化配置

- **优化器**：AdamW (β1=0.9, β2=0.999)
- **学习率**：初始1e-4 → 余弦衰减到1e-6
- **批大小**：32个完整滞回曲线（每个曲线含500+时间步）
- **早停机制**：验证集loss连续10个epoch不下降则终止

### 5.2 训练阶段

| 阶段   | 描述                       | 持续时间    |
| ------ | -------------------------- | ----------- |
| 预训练 | 仅使用MSE损失              | 200 epochs  |
| 精调   | 启用全部物理约束           | 500+ epochs |
| 稳定   | 冻结特征提取层，微调输出层 | 100 epochs  |
## 6. 验证与可视化

### 6.1 定量评估

| 指标         | 公式                      | 说明            |
| ------------ | ------------------------- | --------------- |
| RMSE         | √(1/N Σ(F_pred-F_true)^2) | 整体误差        |
| MAE          | 1/N Σ                     | F_pred-F_true   |         | 绝对误差 |
| Energy Error |                           | E_pred - E_true | /E_true | 能量误差 |

### 6.2 定性分析

- **滞回曲线对比图**：
  - 同一坐标系绘制预测/真实曲线
  - 用颜色区分加载/卸载路径

- **损伤演化图**：
  - 可视化预测的刚度退化过程
  - 叠加混凝土破碎观测数据

- **参数敏感性分析**：
  - 保持其他参数不变，改变单个参数（如混凝土强度）
  - 观察滞回曲线形态变化

7. 部署方案
7.1 输入接口设计
{
    "material_params": {
        "fc": 38.0,
        "fy_transverse": 300.0,
        ...
    },
    "loading_sequence": [0.0, 1.428, -2.856, ...] 
}
7.2 输出规范
{
    "predicted_force": [0.0, 0.084, -0.162, ...],
    "stiffness_degradation": 0.78,
    "damage_index": 0.35
}
