"""
单轴拉伸测试结果后处理脚本
提取并可视化应力-应变曲线和损伤演化
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import pandas as pd
from datetime import datetime

# 尝试导入Abaqus Python模块（如果在Abaqus Python环境中运行）
try:
    from odbAccess import openOdb
    from abaqusConstants import *
    IN_ABAQUS = True
except ImportError:
    print("警告: 未在Abaqus Python环境中运行，将尝试读取CSV结果文件")
    IN_ABAQUS = False

# 设置字体和图表样式
plt.rcParams['font.family'] = 'SimHei'  # 中文显示
plt.rcParams['axes.unicode_minus'] = False  # 负号显示
plt.rcParams['font.size'] = 12
plt.rcParams['figure.figsize'] = (10, 8)


def extract_data_from_odb(odb_path):
    """从Abaqus ODB文件中提取数据"""
    if not IN_ABAQUS:
        print(f"错误: 无法在当前Python环境中读取ODB文件 {odb_path}")
        return None, None, None, None, None
    
    print(f"正在从ODB文件提取数据: {odb_path}")
    odb = openOdb(path=odb_path)
    
    # 获取顶部节点的位移和反力
    step = odb.steps.values()[-1]
    top_node_set = odb.rootAssembly.nodeSets['TOP']
    
    # 提取位移
    displacement_data = []
    for frame in step.frames:
        displacement = frame.fieldOutputs['U'].getSubset(region=top_node_set)
        # 获取Z方向位移的平均值
        u3_values = [value.data[2] for value in displacement.values]
        avg_u3 = sum(u3_values) / len(u3_values)
        displacement_data.append((frame.frameValue, avg_u3))
    
    # 提取反力
    reaction_data = []
    for frame in step.frames:
        reaction = frame.fieldOutputs['RF'].getSubset(region=top_node_set)
        # 获取Z方向反力的总和
        rf3_values = [value.data[2] for value in reaction.values]
        sum_rf3 = sum(rf3_values)
        reaction_data.append((frame.frameValue, sum_rf3))
    
    # 获取单元的状态变量
    element_set = odb.rootAssembly.elementSets['CONCRETE_BLOCK']
    
    # 提取塑性应变
    plastic_strain_data = []
    for frame in step.frames:
        sdv1 = frame.fieldOutputs['SDV1'].getSubset(region=element_set)
        # 获取塑性应变的平均值
        ep_values = [value.data for value in sdv1.values]
        avg_ep = sum(ep_values) / len(ep_values)
        plastic_strain_data.append((frame.frameValue, avg_ep))
    
    # 提取拉伸损伤
    tensile_damage_data = []
    for frame in step.frames:
        sdv2 = frame.fieldOutputs['SDV2'].getSubset(region=element_set)
        # 获取拉伸损伤的平均值
        d_plus_values = [value.data for value in sdv2.values]
        avg_d_plus = sum(d_plus_values) / len(d_plus_values)
        tensile_damage_data.append((frame.frameValue, avg_d_plus))
    
    # 提取压缩损伤
    compressive_damage_data = []
    for frame in step.frames:
        sdv3 = frame.fieldOutputs['SDV3'].getSubset(region=element_set)
        # 获取压缩损伤的平均值
        d_minus_values = [value.data for value in sdv3.values]
        avg_d_minus = sum(d_minus_values) / len(d_minus_values)
        compressive_damage_data.append((frame.frameValue, avg_d_minus))
    
    odb.close()
    
    return (
        np.array(displacement_data),
        np.array(reaction_data),
        np.array(plastic_strain_data),
        np.array(tensile_damage_data),
        np.array(compressive_damage_data)
    )


def extract_data_from_csv(csv_path):
    """从CSV文件中提取数据（如果没有Abaqus Python环境）"""
    try:
        data = pd.read_csv(csv_path)
        
        # 假设CSV文件包含以下列
        # Time, Displacement, Force, PlasticStrain, TensileDamage, CompressiveDamage
        time = data['Time'].values
        displacement = data['Displacement'].values
        force = data['Force'].values
        plastic_strain = data['PlasticStrain'].values
        tensile_damage = data['TensileDamage'].values
        compressive_damage = data['CompressiveDamage'].values
        
        # 格式化为与ODB提取相同的格式
        displacement_data = np.column_stack((time, displacement))
        reaction_data = np.column_stack((time, force))
        plastic_strain_data = np.column_stack((time, plastic_strain))
        tensile_damage_data = np.column_stack((time, tensile_damage))
        compressive_damage_data = np.column_stack((time, compressive_damage))
        
        return (
            displacement_data,
            reaction_data,
            plastic_strain_data,
            tensile_damage_data,
            compressive_damage_data
        )
    
    except Exception as e:
        print(f"读取CSV文件失败: {e}")
        return None, None, None, None, None


def process_and_plot_results(displacement_data, reaction_data, plastic_strain_data,
                           tensile_damage_data, compressive_damage_data, output_dir=None):
    """处理并绘制结果"""
    if displacement_data is None or reaction_data is None:
        print("错误: 没有有效的数据可供处理")
        return
    
    if output_dir is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"tensile_results_{timestamp}"
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 计算应变（位移/原始长度）
    # 原始长度为250mm
    original_length = 250.0
    strain = displacement_data[:, 1] / original_length
    
    # 计算应力（力/截面积）
    # 截面积为100mm×100mm = 10000mm²
    cross_section_area = 100.0 * 100.0
    stress = reaction_data[:, 1] / cross_section_area
    
    # 创建应力-应变曲线
    plt.figure(figsize=(10, 6))
    plt.plot(strain, stress, 'b-', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('应力 (MPa)')
    plt.title('混凝土单轴拉伸应力-应变曲线')
    plt.grid(True, alpha=0.3)
    plt.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    plt.axvline(x=0, color='k', linestyle='--', alpha=0.5)
    plt.savefig(os.path.join(output_dir, 'stress_strain_curve.png'), dpi=300, bbox_inches='tight')
    
    # 创建损伤演化曲线
    plt.figure(figsize=(10, 6))
    plt.plot(strain, tensile_damage_data[:, 1], 'r-', label='拉伸损伤 (d+)', linewidth=2)
    plt.plot(strain, compressive_damage_data[:, 1], 'b-', label='压缩损伤 (d-)', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('损伤变量')
    plt.title('混凝土损伤演化曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.ylim(0, 1)
    plt.savefig(os.path.join(output_dir, 'damage_evolution.png'), dpi=300, bbox_inches='tight')
    
    # 创建塑性应变演化曲线
    plt.figure(figsize=(10, 6))
    plt.plot(strain, plastic_strain_data[:, 1], 'g-', linewidth=2)
    plt.xlabel('总应变')
    plt.ylabel('塑性应变')
    plt.title('混凝土塑性应变演化曲线')
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(output_dir, 'plastic_strain_evolution.png'), dpi=300, bbox_inches='tight')
    
    # 保存数据到CSV文件
    results_df = pd.DataFrame({
        'Strain': strain,
        'Stress': stress,
        'PlasticStrain': plastic_strain_data[:, 1],
        'TensileDamage': tensile_damage_data[:, 1],
        'CompressiveDamage': compressive_damage_data[:, 1]
    })
    results_df.to_csv(os.path.join(output_dir, 'tensile_test_results.csv'), index=False)
    
    # 创建综合图表
    plt.figure(figsize=(12, 10))
    
    # 应力-应变子图
    ax1 = plt.subplot(2, 2, 1)
    ax1.plot(strain, stress, 'b-', linewidth=2)
    ax1.set_xlabel('应变')
    ax1.set_ylabel('应力 (MPa)')
    ax1.set_title('应力-应变曲线')
    ax1.grid(True, alpha=0.3)
    
    # 损伤演化子图
    ax2 = plt.subplot(2, 2, 2)
    ax2.plot(strain, tensile_damage_data[:, 1], 'r-', label='拉伸损伤 (d+)', linewidth=2)
    ax2.plot(strain, compressive_damage_data[:, 1], 'b-', label='压缩损伤 (d-)', linewidth=2)
    ax2.set_xlabel('应变')
    ax2.set_ylabel('损伤变量')
    ax2.set_title('损伤演化')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 1)
    
    # 塑性应变演化子图
    ax3 = plt.subplot(2, 2, 3)
    ax3.plot(strain, plastic_strain_data[:, 1], 'g-', linewidth=2)
    ax3.set_xlabel('总应变')
    ax3.set_ylabel('塑性应变')
    ax3.set_title('塑性应变演化')
    ax3.grid(True, alpha=0.3)
    
    # 有效模量演化子图
    effective_modulus = np.zeros_like(stress)
    for i in range(1, len(strain)):
        if abs(strain[i] - strain[i-1]) > 1e-10:
            effective_modulus[i] = (stress[i] - stress[i-1]) / (strain[i] - strain[i-1])
    
    ax4 = plt.subplot(2, 2, 4)
    ax4.plot(strain[1:], effective_modulus[1:], 'm-', linewidth=2)
    ax4.set_xlabel('应变')
    ax4.set_ylabel('有效模量 (MPa)')
    ax4.set_title('有效模量演化')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'comprehensive_results.png'), dpi=300, bbox_inches='tight')
    
    print(f"结果已保存到目录: {output_dir}")
    
    return results_df


def main():
    """主函数"""
    print("混凝土单轴拉伸测试结果处理")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        # 默认ODB文件路径
        file_path = "tensile_test.odb"
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        
        # 尝试寻找CSV文件
        csv_path = file_path.replace('.odb', '.csv')
        if os.path.exists(csv_path):
            print(f"找到CSV文件: {csv_path}")
            file_path = csv_path
        else:
            print("错误: 未找到任何可用的结果文件")
            return 1
    
    # 根据文件类型提取数据
    if file_path.lower().endswith('.odb'):
        if not IN_ABAQUS:
            print("错误: 无法在当前Python环境中读取ODB文件")
            return 1
        
        data = extract_data_from_odb(file_path)
    elif file_path.lower().endswith('.csv'):
        data = extract_data_from_csv(file_path)
    else:
        print(f"错误: 不支持的文件类型: {file_path}")
        return 1
    
    # 处理并绘制结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"tensile_results_{timestamp}"
    results_df = process_and_plot_results(*data, output_dir=output_dir)
    
    print("\n分析完成!")
    print(f"结果已保存到目录: {output_dir}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main()) 