** ==========================================================
** INP Template for Single Element Uniaxial Compression Test
** ==========================================================
*HEADING
Single Element Uniaxial Compression Test with VUMAT
**
** PART DEFINITION: 10x10x100mm Column
*PART, NAME=COLUMN
*NODE
  1,   0.0,   0.0,   0.0
  2,  10.0,   0.0,   0.0
  3,  10.0,  10.0,   0.0
  4,   0.0,  10.0,   0.0
  5,   0.0,   0.0, 100.0
  6,  10.0,   0.0, 100.0
  7,  10.0,  10.0, 100.0
  8,   0.0,  10.0, 100.0
*ELEMENT, TYPE=C3D8R, ELSET=E_COLUMN
  1, 1, 2, 3, 4, 5, 6, 7, 8
*NSET, NSET=N_BOTTOM
  1, 2, 3, 4
*NSET, NSET=N_TOP
  5, 6, 7, 8
** Section assignment points to a material name that will be defined
** in the included file.
*SOLID SECTION, ELSET=E_COLUMN, MATERIAL=CONCRETE_VUMAT
*END PART
**
** ASSEMBLY
*ASSEMBLY, NAME=Assm
*INSTANCE, NAME=COLUMN-1, PART=COLUMN
*END INSTANCE
*NSET, NSET=SET_BOTTOM, INSTANCE=COLUMN-1
N_BOTTOM
*NSET, NSET=SET_TOP, INSTANCE=COLUMN-1
N_TOP
*END ASSEMBLY
**
** MATERIAL DEFINITION WILL BE INCLUDED FROM ANOTHER FILE
*INCLUDE, INPUT=material_for_abaqus.inp
**
** AMPLITUDE for smooth loading
*AMPLITUDE, NAME=AMP-LINEAR, DEFINITION=TABULAR
0.0, 0.0, 1.0, 1.0
**
** STEP DEFINITION
*STEP, NAME=ApplyCompression
*DYNAMIC, EXPLICIT
, 1.0
**
** BOUNDARY CONDITIONS
*BOUNDARY
SET_BOTTOM, ENCASTRE
** Applied compression of 3mm in negative Z-direction (U3)
SET_TOP, 3, 3, -3.0, AMP=AMP-LINEAR
**
** OUTPUT REQUESTS
*OUTPUT, FIELD, FREQUENCY=20
*ELEMENT OUTPUT, ELSET=E_COLUMN
S, E, SDV  ! SDV is for state variables
*NODE OUTPUT
U, RF
*OUTPUT, HISTORY, FREQUENCY=1
*NODE OUTPUT, NSET=SET_TOP
U3
*NODE OUTPUT, NSET=SET_BOTTOM
RF3
*END STEP 