import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt

class PINN(nn.Module):
    def __init__(self, hidden_layers=3, neurons=32):
        """
        初始化物理信息神经网络(PINN)模型
        
        Args:
            hidden_layers: 隐藏层数量
            neurons: 每层神经元数量
        """
        super(PINN, self).__init__()
        
        # 输入层 -> 第一隐藏层 (输入维度为2：当前应变和应变增量)
        self.input_layer = nn.Linear(2, neurons)
        
        # 隐藏层
        self.hidden_layers = nn.ModuleList()
        for _ in range(hidden_layers - 1):
            self.hidden_layers.append(nn.Linear(neurons, neurons))
        
        # 输出层 (应力、损伤和增量点标识)
        self.output_layer = nn.Linear(neurons, 3)
        
        # 激活函数 - 使用Tanh激活函数以获得更平滑的输出
        self.activation = nn.Tanh()
        
        # 物理参数
        self.E = 30000.0  # 混凝土弹性模量 (MPa)
        self.eps0 = 0.0001  # 损伤起始应变阈值
        self.k = 500.0  # 损伤演化参数
        self.alpha = 0.8  # 损伤平滑参数
        
        # 存储上一个应变状态
        self.prev_strain = None
        # 存储当前损伤状态
        self.current_damage = None
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入张量，包含应变和应变增量 [strain, delta_strain]
            
        Returns:
            outputs: 包含应力、损伤和增量点标识的张量
        """
        # 提取应变和应变增量
        if x.shape[1] == 2:
            strain = x[:, 0:1]  # 当前应变
            delta_strain = x[:, 1:2]  # 应变增量
        else:
            # 兼容旧版本输入
            strain = x
            delta_strain = torch.zeros_like(x)  # 默认增量为0
        
        # 保存原始输入用于残差连接
        original_x = x
        
        # 输入层
        x = self.activation(self.input_layer(x))
        
        # 隐藏层 - 添加残差连接以改善梯度流动
        prev_x = x
        for i, layer in enumerate(self.hidden_layers):
            x = self.activation(layer(x))
            # 每两层添加一个残差连接
            if i % 2 == 1 and i > 0:
                x = x + prev_x
                prev_x = x
        
        # 输出层
        outputs = self.output_layer(x)
        
        # 对于大应变区域，添加特殊处理
        strain_magnitude = torch.abs(strain)
        large_strain_mask = (strain_magnitude > 0.03).float()
        
        # 应力输出需要在大应变区域有更好的非线性表现
        stress_output = outputs[:, 0:1]
        damage_output = outputs[:, 1:2]
        increment_point = outputs[:, 2:3]  # 增量点标识
        
        # 对大应变区域的应力进行额外的非线性变换
        stress_sign = torch.sign(strain)
        stress_adjustment = stress_sign * torch.pow(strain_magnitude, 1.5) * 10.0 * large_strain_mask
        adjusted_stress = stress_output + stress_adjustment
        
        # 计算增量点标识 - 判断应变增量是否超过损伤阈值
        delta_strain_abs = torch.abs(delta_strain)
        is_increment_point = (delta_strain_abs > self.eps0).float()
        
        # 使用sigmoid函数使输出更平滑
        increment_point = torch.sigmoid(increment_point) * is_increment_point
        
        # 组合调整后的输出
        adjusted_outputs = torch.cat([adjusted_stress, damage_output, increment_point], dim=1)
        
        return adjusted_outputs
    
    def get_stress_damage(self, strain, delta_strain=None):
        """
        获取应力、损伤值和增量点标识
        改进版本：更精确地处理应变变化量，特别是在卸载阶段
        
        Args:
            strain: 应变输入
            delta_strain: 应变增量输入，如果为None则默认为零增量
            
        Returns:
            stress: 应力输出
            damage: 损伤输出
            increment_point: 增量点标识 (1表示是增量点，0表示不是)
        """
        if not isinstance(strain, torch.Tensor):
            strain = torch.tensor(strain, dtype=torch.float32).reshape(-1, 1)
        
        # 处理应变增量
        if delta_strain is None:
            # 如果没有提供应变增量，则计算应变增量
            if self.prev_strain is None:
                delta_strain = torch.zeros_like(strain)  # 第一个点的增量为0
            else:
                delta_strain = strain - self.prev_strain
            # 更新上一个应变状态
            self.prev_strain = strain.clone().detach()
        elif not isinstance(delta_strain, torch.Tensor):
            delta_strain = torch.tensor(delta_strain, dtype=torch.float32).reshape(-1, 1)
        
        # 区分加载和卸载阶段
        loading_mask = (delta_strain >= 0).float()
        unloading_mask = (delta_strain < 0).float()
        
        # 组合输入
        model_input = torch.cat([strain, delta_strain], dim=1)
            
        outputs = self.forward(model_input)
        stress = outputs[:, 0:1]  # 第一个输出是应力
        damage = outputs[:, 1:2]  # 第二个输出是损伤
        increment_point = outputs[:, 2:3]  # 第三个输出是增量点标识
        
        # 考虑应变变化方向对损伤的影响
        # 在卸载阶段（应变减少时），损伤不应增加
        if self.current_damage is not None:
            # 在加载阶段，损伤可以增加；在卸载阶段，损伤保持不变
            # 使用更精确的损伤计算方法
            damage = loading_mask * torch.max(damage, self.current_damage) + \
                     unloading_mask * self.current_damage
            
            # 更新当前损伤状态
            self.current_damage = damage.clone().detach()
        else:
            # 初始化当前损伤状态
            self.current_damage = damage.clone().detach()
        
        # 改进增量点标识的计算
        # 使用is_increment_point函数获取更准确的增量点标识
        is_incr, _ = self.is_increment_point(strain, self.prev_strain)
        
        # 将布尔值转换为浮点数，并与模型预测的增量点标识结合
        # 这样可以同时考虑模型的预测和实际的应变变化
        increment_factor = is_incr.float().reshape_as(increment_point)
        increment_point = torch.sigmoid(increment_point) * increment_factor
        
        return stress, damage, increment_point
    
    def constitutive_stress(self, strain, damage):
        """
        根据本构关系计算应力: σ = (1-d)Eε
        
        Args:
            strain: 应变
            damage: 损伤变量
            
        Returns:
            stress: 根据本构关系计算的应力
        """
        return (1 - damage) * self.E * strain
    
    def damage_evolution(self, strain, delta_strain=None):
        """
        改进的损伤演化规律: d = 1 - exp(-k(ε-ε0)^+)^alpha
        添加alpha参数使损伤演化更平滑
        考虑应变变化量的方向，在卸载阶段（应变减少）时不增加损伤
        增加对应变历史最大值的跟踪，以更准确地模拟损伤演化
        
        Args:
            strain: 应变
            delta_strain: 应变变化量，如果为None则假设为加载阶段
            
        Returns:
            damage: 根据演化规律计算的损伤
        """
        # 计算超过阈值的应变部分，使用平滑版本的ReLU以避免不连续点
        strain_pos = torch.log(1 + torch.exp(10 * (strain - self.eps0))) / 10
        
        # 计算基础损伤值，使用alpha参数使曲线更平滑
        base_damage = 1 - torch.exp(-self.k * torch.pow(strain_pos, self.alpha))
        
        # 如果提供了应变变化量，考虑加载/卸载状态
        if delta_strain is not None:
            # 创建加载状态掩码：正变化量表示加载，负变化量表示卸载
            loading_mask = (delta_strain >= 0).float()
            unloading_mask = (delta_strain < 0).float()
            
            # 在当前损伤状态为None的情况下初始化为基础损伤
            if self.current_damage is None:
                self.current_damage = base_damage.clone().detach()
            
            # 在加载阶段，损伤可以增加但不减少
            # 在卸载阶段，损伤保持不变
            # 使用更精确的损伤计算方法，考虑应变历史最大值
            damage = loading_mask * torch.max(base_damage, self.current_damage) + \
                     unloading_mask * self.current_damage
            
            # 更新当前损伤状态
            self.current_damage = damage.clone().detach()
        else:
            # 如果没有提供应变变化量，使用基础损伤计算
            damage = base_damage
        
        # 确保损伤值在[0,1]范围内
        damage = torch.clamp(damage, 0.0, 1.0)
        
        return damage
        
    def is_increment_point(self, strain, prev_strain=None):
        """
        判断当前应变点是否为增量点（是否超过损伤阈值）
        改进版本：区分加载和卸载阶段，更精确地识别应变变化量
        
        Args:
            strain: 当前应变
            prev_strain: 上一个应变状态，如果为None则使用存储的上一个状态
            
        Returns:
            is_increment: 是否为增量点的布尔值
            delta_strain: 计算的应变增量
        """
        if prev_strain is None:
            if self.prev_strain is None:
                # 如果没有上一个状态，假设增量为0
                delta_strain = torch.zeros_like(strain)
            else:
                delta_strain = strain - self.prev_strain
        else:
            delta_strain = strain - prev_strain
        
        # 更新上一个应变状态
        self.prev_strain = strain.clone().detach()
        
        # 计算增量的绝对值
        delta_strain_abs = torch.abs(delta_strain)
        
        # 区分加载和卸载阶段
        loading_mask = (delta_strain >= 0).float()
        unloading_mask = (delta_strain < 0).float()
        
        # 在加载阶段使用标准阈值，在卸载阶段使用更合理的阈值
        # 加载阶段阈值保持不变
        loading_threshold = self.eps0
        # 卸载阶段阈值调整为原阈值的0.5倍，使其更合理且不会过于敏感
        unloading_threshold = self.eps0 * 0.5
        
        # 判断是否超过损伤阈值，区分加载和卸载阶段
        is_loading_increment = loading_mask * (delta_strain_abs > loading_threshold).float()
        is_unloading_increment = unloading_mask * (delta_strain_abs > unloading_threshold).float()
        
        # 合并加载和卸载阶段的判断结果
        is_increment = (is_loading_increment + is_unloading_increment) > 0
        
        # 保存应变变化量信息，用于后续分析
        self.last_delta_strain = delta_strain.clone().detach()
        self.last_loading_mask = loading_mask.clone().detach()
        self.last_unloading_mask = unloading_mask.clone().detach()
        
        return is_increment, delta_strain

class PINNLoss(nn.Module):
    def __init__(self, model, lambda_phys=2.0, lambda_damage=0.5, lambda_extreme=1.5, lambda_increment=1.0):
        """
        初始化PINN损失函数
        
        Args:
            model: PINN模型
            lambda_phys: 物理损失权重
            lambda_damage: 损伤损失权重
            lambda_extreme: 极限状态损失权重
            lambda_increment: 增量点标识损失权重
        """
        super(PINNLoss, self).__init__()
        self.model = model
        self.lambda_phys = lambda_phys
        self.lambda_damage = lambda_damage
        self.lambda_extreme = lambda_extreme
        self.lambda_increment = lambda_increment
        self.mse = nn.MSELoss()
        self.bce = nn.BCELoss()  # 二元交叉熵损失，用于增量点标识
    
    def forward(self, inputs, target_stress, target_increment=None):
        """
        计算总损失
        改进版本：更精确地处理卸载阶段的损失计算
        
        Args:
            inputs: 输入数据，可以是单独的应变或包含应变和应变增量的张量
            target_stress: 目标应力
            target_increment: 目标增量点标识，如果为None则根据应变增量自动计算
            
        Returns:
            total_loss: 总损失
            loss_data: 数据损失
            loss_phys: 物理损失
            loss_damage: 损伤损失
            loss_increment: 增量点标识损失
        """
        # 处理输入数据
        if inputs.shape[1] == 2:
            strain = inputs[:, 0:1]  # 当前应变
            delta_strain = inputs[:, 1:2]  # 应变增量
        else:
            strain = inputs
            delta_strain = torch.zeros_like(strain)  # 默认增量为0
        
        # 区分加载和卸载阶段
        loading_mask = (delta_strain >= 0).float()
        unloading_mask = (delta_strain < 0).float()
        
        # 如果没有提供目标增量点标识，则根据应变增量自动计算
        # 改进：区分加载和卸载阶段的阈值
        if target_increment is None:
            delta_strain_abs = torch.abs(delta_strain)
            loading_threshold = self.model.eps0
            unloading_threshold = self.model.eps0 * 0.5  # 卸载阶段使用更小的阈值
            
            # 区分加载和卸载阶段的增量点判断
            is_loading_increment = loading_mask * (delta_strain_abs > loading_threshold).float()
            is_unloading_increment = unloading_mask * (delta_strain_abs > unloading_threshold).float()
            
            # 合并加载和卸载阶段的判断结果
            target_increment = is_loading_increment + is_unloading_increment
        
        # 获取模型预测
        pred_stress, pred_damage, pred_increment = self.model.get_stress_damage(strain, delta_strain)
        
        # 数据损失：预测应力与目标应力的MSE
        loss_data = self.mse(pred_stress, target_stress)
        
        # 物理损失：预测应力与本构应力的MSE
        constitutive_stress = self.model.constitutive_stress(strain, pred_damage)
        loss_phys = self.mse(pred_stress, constitutive_stress)
        
        # 损伤损失：预测损伤与损伤演化的MSE
        # 传递应变增量以考虑加载/卸载状态
        damage_evolution = self.model.damage_evolution(strain, delta_strain)
        loss_damage = self.mse(pred_damage, damage_evolution)
        
        # 增量点标识损失：预测增量点与目标增量点的BCE
        loss_increment = self.bce(pred_increment, target_increment)
        
        # 极限状态损失：对大应变区域增加额外权重
        # 计算应变的绝对值
        strain_abs = torch.abs(strain)
        # 找出大应变区域（应变绝对值大于0.03的部分）
        extreme_mask = (strain_abs > 0.03).float()
        # 如果存在极限状态点
        if torch.sum(extreme_mask) > 0:
            # 计算极限状态点的损失
            extreme_stress = pred_stress * extreme_mask
            extreme_target = target_stress * extreme_mask
            loss_extreme = torch.sum((extreme_stress - extreme_target)**2) / (torch.sum(extreme_mask) + 1e-6)
        else:
            loss_extreme = 0.0
        
        # 卸载状态损失：对卸载阶段（应变减少）增加额外权重
        # 改进：增加卸载阶段的权重，使模型更关注卸载行为
        if torch.sum(unloading_mask) > 0:
            # 计算卸载状态点的损失
            unloading_stress = pred_stress * unloading_mask
            unloading_target = target_stress * unloading_mask
            loss_unloading = torch.sum((unloading_stress - unloading_target)**2) / (torch.sum(unloading_mask) + 1e-6)
            # 增加卸载损失权重，从1.2提高到1.5，使模型更关注卸载行为
            loss_extreme = loss_extreme + 1.5 * loss_unloading
            
            # 额外添加卸载阶段的应变变化量损失
            # 这有助于模型更准确地捕捉卸载阶段的应变变化
            unloading_delta = delta_strain * unloading_mask
            unloading_increment = pred_increment * unloading_mask
            if torch.sum(unloading_increment) > 0:
                loss_unloading_increment = torch.sum((unloading_increment - target_increment * unloading_mask)**2) / (torch.sum(unloading_mask) + 1e-6)
                loss_increment = loss_increment + 0.8 * loss_unloading_increment
        
        # 总损失
        total_loss = (loss_data + 
                      self.lambda_phys * loss_phys + 
                      self.lambda_damage * loss_damage + 
                      self.lambda_extreme * loss_extreme + 
                      self.lambda_increment * loss_increment)
        
        return total_loss, loss_data, loss_phys, loss_damage, loss_increment

# 测试代码
if __name__ == "__main__":
    # 创建模型
    model = PINN(hidden_layers=1, neurons=10)
    
    # 测试前向传播 - 使用应变和应变增量作为输入
    strain = torch.tensor([[0.0], [0.001], [0.002], [0.003]], dtype=torch.float32)
    delta_strain = torch.tensor([[0.0], [0.001], [0.0005], [0.0002]], dtype=torch.float32)
    
    # 组合输入
    model_input = torch.cat([strain, delta_strain], dim=1)
    
    outputs = model(model_input)
    print(f"输入应变: {strain.numpy().flatten()}")
    print(f"输入应变增量: {delta_strain.numpy().flatten()}")
    print(f"输出 (应力, 损伤, 增量点标识): {outputs.detach().numpy()}")
    
    # 测试get_stress_damage方法
    stress, damage, increment = model.get_stress_damage(strain, delta_strain)
    print(f"\n应力: {stress.detach().numpy().flatten()}")
    print(f"损伤: {damage.detach().numpy().flatten()}")
    print(f"增量点标识: {increment.detach().numpy().flatten()}")
    
    # 测试is_increment_point方法
    is_incr, delta = model.is_increment_point(strain)
    print(f"\n是否为增量点: {is_incr.detach().numpy().flatten()}")
    print(f"计算的应变增量: {delta.detach().numpy().flatten()}")
    
    # 测试损失函数
    loss_fn = PINNLoss(model)
    target_stress = torch.tensor([[0.0], [20.0], [25.0], [28.0]], dtype=torch.float32)
    total_loss, loss_data, loss_phys, loss_damage, loss_increment = loss_fn(model_input, target_stress)
    
    print(f"\n总损失: {total_loss.item():.4f}")
    print(f"数据损失: {loss_data.item():.4f}")
    print(f"物理损失: {loss_phys.item():.4f}")
    print(f"损伤损失: {loss_damage.item():.4f}")
    print(f"增量点标识损失: {loss_increment.item():.4f}")