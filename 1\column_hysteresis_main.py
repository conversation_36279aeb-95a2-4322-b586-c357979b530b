import os
import torch
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import argparse
from column_hysteresis_model import (
    load_excel_data, 
    preprocess_data, 
    ColumnHysteresisPINN, 
    train_model, 
    plot_hysteresis_curve
)

# 设置环境变量，避免某些Windows系统上的OMP错误
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 设备配置
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

# 导入中文字体配置
from font_config import configure_chinese_font

# 配置中文字体
configure_chinese_font()

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='柱子滞回曲线PINN模型训练与预测')
    parser.add_argument('--excel_path', type=str, default=os.path.join(os.path.dirname(__file__), "column", "滞回曲线", "column1.xlsx"),
                        help='Excel数据文件路径')
    parser.add_argument('--epochs', type=int, default=500,
                        help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=64,
                        help='批次大小')
    parser.add_argument('--lr', type=float, default=5e-4,
                        help='学习率')
    parser.add_argument('--save_dir', type=str, default=os.path.dirname(__file__),
                        help='保存模型和图表的目录')
    parser.add_argument('--mode', type=str, choices=['train', 'predict'], default='train',
                        help='模式：训练或预测')
    parser.add_argument('--model_path', type=str, default=None,
                        help='预测模式下加载的模型路径')
    
    args = parser.parse_args()
    
    # 确保保存目录存在
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 1. 加载数据
    print("加载数据...")
    try:
        static_params, dynamic_data = load_excel_data(args.excel_path)
        print(f"成功加载Excel数据: {args.excel_path}")
    except Exception as e:
        print(f"加载数据失败: {str(e)}")
        return
    
    # 2. 数据预处理
    print("数据预处理...")
    X, y, scalers, original_data = preprocess_data(static_params, dynamic_data)
    
    # 3. 创建或加载模型
    print("创建模型...")
    input_dim = X.shape[1]
    disp_idx = input_dim - 3  # 假设位移特征在输入的倒数第三个位置
    
    model = ColumnHysteresisPINN(input_dim, hidden_dims=[256, 256, 128, 64]).to(device)
    
    if args.mode == 'predict' and args.model_path:
        try:
            model.load_state_dict(torch.load(args.model_path))
            print(f"成功加载模型: {args.model_path}")
        except Exception as e:
            print(f"加载模型失败: {str(e)}")
            return
    
    # 4. 训练或预测
    if args.mode == 'train':
        print(f"开始训练模型，共{args.epochs}轮...")
        model, history = train_model(
            model, X, y, disp_idx, 
            epochs=args.epochs, 
            batch_size=args.batch_size, 
            lr=args.lr
        )
        
        # 保存训练历史
        save_training_history(history, os.path.join(args.save_dir, 'training_history.png'))
    
    # 5. 可视化结果
    print("可视化滞回曲线...")
    metrics = plot_hysteresis_curve(
        model, X, y, disp_idx, 
        scalers['force'], scalers['disp'], 
        original_data,
        title=f"柱子滞回曲线 - {os.path.basename(args.excel_path)}"
    )
    
    # 6. 打印评估指标
    print("\n模型评估指标:")
    print(f"平均绝对误差 (MAE): {metrics['mae']:.4f} kN")
    print(f"均方根误差 (RMSE): {metrics['rmse']:.4f} kN")
    print(f"最大误差: {metrics['max_error']:.4f} kN")
    
    print("\n处理完成!")

def save_training_history(history, save_path):
    """保存训练历史曲线"""
    plt.figure(figsize=(12, 10))
    
    # 绘制总损失
    plt.subplot(2, 2, 1)
    plt.plot(history['train_loss'], label='训练损失')
    plt.plot(history['val_loss'], label='验证损失')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('总损失')
    plt.legend()
    plt.grid(True)
    
    # 绘制MSE损失
    plt.subplot(2, 2, 2)
    plt.plot(history['train_mse'], label='训练MSE')
    plt.plot(history['val_mse'], label='验证MSE')
    plt.xlabel('Epoch')
    plt.ylabel('MSE')
    plt.title('MSE损失')
    plt.legend()
    plt.grid(True)
    
    # 绘制闭合约束损失
    plt.subplot(2, 2, 3)
    plt.plot(history['train_closure'], label='训练闭合约束')
    plt.plot(history['val_closure'], label='验证闭合约束')
    plt.xlabel('Epoch')
    plt.ylabel('Closure Loss')
    plt.title('闭合约束损失')
    plt.legend()
    plt.grid(True)
    
    # 绘制能量约束损失
    plt.subplot(2, 2, 4)
    plt.plot(history['train_energy'], label='训练能量约束')
    plt.plot(history['val_energy'], label='验证能量约束')
    plt.xlabel('Epoch')
    plt.ylabel('Energy Loss')
    plt.title('能量约束损失')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"训练历史已保存至: {save_path}")

if __name__ == "__main__":
    main()