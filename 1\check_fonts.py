import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 查找系统中的中文字体
chinese_fonts = [f.name for f in fm.fontManager.ttflist 
               if any(x in f.name for x in ['黑体', 'Sim<PERSON><PERSON>', '宋体', 'SimSun', 'Microsoft YaHei', '微软雅黑'])]

print("系统中可用的中文字体:")
for font in chinese_fonts:
    print(f"- {font}")

# 检查matplotlib当前默认字体
print("\nMatplotlib当前默认字体:")
print(f"- {plt.rcParams['font.family']}")
print(f"- {plt.rcParams['font.sans-serif']}")