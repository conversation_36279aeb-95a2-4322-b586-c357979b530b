"""
基于增量式输入的混凝土损伤参数识别PINN模型 - 扩展版（支持拉压）
使用物理引导的塑性应变建模，支持受拉和受压状态
"""

import torch
import torch.nn as nn
import numpy as np

class DamagePINNV2Extended(nn.Module):
    """
    混凝土损伤PINN模型 - 扩展版
    基于GRU架构，输入应变增量序列，输出应力、损伤、塑性应变增量系数
    支持受拉和受压状态
    """
    
    def __init__(self, input_size=1, hidden_size=64, num_layers=2, output_size=3):
        super(DamagePINNV2Extended, self).__init__()
        
        # GRU层 - 用于处理增量式序列输入
        self.gru = nn.GRU(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=0.1 if num_layers > 1 else 0
        )
        
        # 全连接层 - 输出三个物理量
        self.fc_layers = nn.Sequential(
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_size, output_size)
        )
        
        # 激活函数确保物理约束
        self.sigmoid = nn.Sigmoid()  # 用于损伤变量 [0,1]
        
    def forward(self, strain_increment_seq):
        """
        前向传播
        
        Args:
            strain_increment_seq: 应变增量序列 [batch_size, seq_len, 1]
            
        Returns:
            sigma_seq: 应力序列 [batch_size, seq_len]
            d_seq: 损伤变量序列 [batch_size, seq_len] 
            xi_seq: 塑性应变系数序列 [batch_size, seq_len]
        """
        batch_size, seq_len, _ = strain_increment_seq.shape
        
        # GRU处理序列
        gru_out, _ = self.gru(strain_increment_seq)  # [batch_size, seq_len, hidden_size]
        
        # 全连接层输出
        output = self.fc_layers(gru_out)  # [batch_size, seq_len, 3]
        
        # 分离三个输出并应用物理约束
        sigma_seq = output[:, :, 0]  # 应力可以为负
        d_seq = self.sigmoid(output[:, :, 1])  # 损伤 [0,1]
        
        # 输出塑性应变系数xi，使用sigmoid确保在合理范围内
        xi_seq = self.sigmoid(output[:, :, 2]) * 0.1  # xi在[0, 0.1]范围内
        
        return sigma_seq, d_seq, xi_seq


class PhysicsCalculatorV2Extended:
    """
    物理约束计算器 - 扩展版
    支持受拉和受压的损伤演化
    """
    
    def __init__(self, E0=30000.0, f_t=3.0, f_c=30.0):
        self.E0 = E0    # 初始弹性模量
        self.f_t = f_t  # 单轴抗拉强度
        self.f_c = f_c  # 单轴抗压强度
    
    def calculate_physics_constraints(self, strain_increment_seq, 
                                    A_plus, B_plus, A_minus, B_minus, 
                                    xi_plus, xi_minus):
        """
        按增量步计算物理约束目标
        支持拉压双向损伤和塑性演化
        
        Args:
            strain_increment_seq: 应变增量序列 [seq_len]
            A_plus, B_plus: 受拉损伤参数
            A_minus, B_minus: 受压损伤参数
            xi_plus: 受拉塑性应变系数
            xi_minus: 受压塑性应变系数
            
        Returns:
            d_plus_phy_seq: 受拉物理损伤序列
            d_minus_phy_seq: 受压物理损伤序列
            ep_phy_seq: 物理塑性应变序列
            stress_phy_seq: 物理应力序列
        """
        # 初始化状态变量
        device = strain_increment_seq.device
        epsilon_total = torch.tensor(0.0, device=device, dtype=torch.float32)
        ep_total = torch.tensor(0.0, device=device, dtype=torch.float32)
        d_plus = torch.tensor(0.0, device=device, dtype=torch.float32)
        d_minus = torch.tensor(0.0, device=device, dtype=torch.float32)
        
        # 修正：损伤阈值初始化为0，而不是强度值
        r_max_plus = torch.tensor(0.0, device=device, dtype=torch.float32)
        r_max_minus = torch.tensor(0.0, device=device, dtype=torch.float32)
        
        # 用于存储每一步计算结果的列表
        d_plus_phy_seq = []
        d_minus_phy_seq = []
        ep_phy_seq = []
        stress_phy_seq = []
        
        # 按增量步进行循环积分
        for i, delta_epsilon in enumerate(strain_increment_seq):
            # 1. 更新总应变
            epsilon_total = epsilon_total + delta_epsilon
            
            # 2. 计算当前有效应变 (弹性应变)
            current_elastic_strain = epsilon_total - ep_total
            
            # 3. 损伤演化（基于有效应变的正负判断）
            if current_elastic_strain > 0:  # 拉伸区域
                Y_plus_current = self.E0 * current_elastic_strain
                if Y_plus_current > r_max_plus:  # 新的损伤加载
                    r_max_plus = Y_plus_current
                    # 修正：降低损伤触发阈值，使用更敏感的损伤演化
                    # 当弹性应变超过一个较小的阈值时就开始损伤
                    strain_threshold_plus = self.f_t / self.E0 * 0.3  # 降低到30%的弹性极限
                    if current_elastic_strain > strain_threshold_plus:
                        # 使用等效应力作为损伤驱动力
                        equivalent_stress = self.E0 * current_elastic_strain
                        ratio = equivalent_stress / self.f_t
                        term1 = (1 - A_plus)
                        term2 = A_plus * torch.exp(B_plus * (1 - ratio))
                        d_plus = 1 - (self.f_t / equivalent_stress) * (term1 + term2)
                        d_plus = torch.clamp(d_plus, 0.0, 0.99)
                    else:
                        # 即使未达到阈值，也给予小量损伤以确保演化
                        d_plus = torch.clamp(current_elastic_strain / (self.f_t / self.E0) * 0.1, 0.0, 0.1)
                
            elif current_elastic_strain < 0:  # 压缩区域
                Y_minus_current = self.E0 * torch.abs(current_elastic_strain)
                if Y_minus_current > r_max_minus:  # 新的损伤加载
                    r_max_minus = Y_minus_current
                    # 修正：降低损伤触发阈值
                    strain_threshold_minus = self.f_c / self.E0 * 0.3  # 降低到30%的弹性极限
                    if torch.abs(current_elastic_strain) > strain_threshold_minus:
                        # 使用等效应力作为损伤驱动力
                        equivalent_stress = self.E0 * torch.abs(current_elastic_strain)
                        ratio = equivalent_stress / self.f_c
                        term1 = (1 - A_minus)
                        term2 = A_minus * torch.exp(B_minus * (1 - ratio))
                        d_minus = 1 - (self.f_c / equivalent_stress) * (term1 + term2)
                        d_minus = torch.clamp(d_minus, 0.0, 0.99)
                    else:
                        # 即使未达到阈值，也给予小量损伤
                        d_minus = torch.clamp(torch.abs(current_elastic_strain) / (self.f_c / self.E0) * 0.1, 0.0, 0.1)
            
            # 4. 塑性应变演化
            # 修正：更敏感的塑性应变触发条件
            if delta_epsilon > 0:  # 拉伸加载
                # 降低塑性触发阈值，当应变超过弹性极限的20%时就开始塑性
                elastic_limit_plus = self.f_t / self.E0 * 0.2
                if current_elastic_strain > elastic_limit_plus:
                    # 塑性应变与当前损伤和应变水平相关
                    plastic_factor = 1.0 + d_plus * 3.0  # 损伤增强塑性
                    strain_factor = current_elastic_strain / (self.f_t / self.E0)  # 应变水平因子
                    delta_ep = xi_plus * delta_epsilon * plastic_factor * strain_factor
                else:
                    # 即使在弹性阶段，也给予微小塑性以确保演化
                    delta_ep = xi_plus * delta_epsilon * 0.1
            elif delta_epsilon < 0:  # 压缩加载
                # 降低塑性触发阈值
                elastic_limit_minus = self.f_c / self.E0 * 0.2
                if torch.abs(current_elastic_strain) > elastic_limit_minus:
                    # 塑性应变与当前损伤和应变水平相关
                    plastic_factor = 1.0 + d_minus * 3.0  # 损伤增强塑性
                    strain_factor = torch.abs(current_elastic_strain) / (self.f_c / self.E0)
                    delta_ep = xi_minus * torch.abs(delta_epsilon) * plastic_factor * strain_factor
                    delta_ep = -delta_ep  # 压缩塑性为负值
                else:
                    # 即使在弹性阶段，也给予微小塑性
                    delta_ep = -xi_minus * torch.abs(delta_epsilon) * 0.1
            else:
                delta_ep = torch.tensor(0.0, device=device, dtype=torch.float32)
            
            ep_total = ep_total + delta_ep
            
            # 5. 计算物理应力（用于可视化和损失计算）
            current_elastic_strain_updated = epsilon_total - ep_total
            if current_elastic_strain_updated >= 0:
                current_d_effective = d_plus
            else:
                current_d_effective = d_minus
            
            current_stress_phy = (1 - current_d_effective) * self.E0 * current_elastic_strain_updated
            
            # 6. 记录当前步的物理状态
            d_plus_phy_seq.append(d_plus.clone())
            d_minus_phy_seq.append(d_minus.clone())
            ep_phy_seq.append(ep_total.clone())
            stress_phy_seq.append(current_stress_phy.clone())
        
        return (torch.stack(d_plus_phy_seq), 
                torch.stack(d_minus_phy_seq), 
                torch.stack(ep_phy_seq), 
                torch.stack(stress_phy_seq))
    
    def calculate_stress_from_state(self, strain_total_seq, d_seq, ep_seq):
        """
        根据本构关系计算应力
        σ = (1 - d) * E0 * (ε - εp)
        """
        return (1 - d_seq) * self.E0 * (strain_total_seq - ep_seq)


class LossCalculatorExtended:
    """
    损失函数计算器 - 扩展版
    实现框架中定义的四部分损失，支持拉压区分
    """
    
    def __init__(self, lambda_data=1.0, lambda_stress=1.0, 
                 lambda_damage_plus=0.8, lambda_damage_minus=0.8, 
                 lambda_plastic=0.5):
        self.lambda_data = lambda_data
        self.lambda_stress = lambda_stress
        self.lambda_damage_plus = lambda_damage_plus
        self.lambda_damage_minus = lambda_damage_minus
        self.lambda_plastic = lambda_plastic
    
    def calculate_total_loss(self, sigma_hat_seq, d_hat_seq, xi_hat_seq,
                           stress_exp_seq, strain_total_seq, strain_increment_seq,
                           d_plus_phy_seq, d_minus_phy_seq, ep_phy_seq, 
                           stress_phy_seq, E0):
        """
        计算总损失，区分拉压状态
        """
        # 修正：对应力进行归一化以避免数值过大
        stress_scale = torch.max(torch.abs(stress_exp_seq)) + 1e-6  # 防止除零
        
        # L_data: 数据拟合损失（归一化）
        loss_data = torch.mean(((sigma_hat_seq - stress_exp_seq) / stress_scale)**2)
        
        # 计算累积塑性应变（从xi序列）
        # 根据应变增量符号累积塑性应变
        ep_hat_seq = torch.zeros_like(strain_total_seq)
        for i in range(len(strain_increment_seq)):
            if i == 0:
                if strain_increment_seq[i] > 0:
                    ep_hat_seq[i] = xi_hat_seq[i] * strain_increment_seq[i]
                else:
                    ep_hat_seq[i] = xi_hat_seq[i] * torch.abs(strain_increment_seq[i])
            else:
                if strain_increment_seq[i] > 0:
                    ep_hat_seq[i] = ep_hat_seq[i-1] + xi_hat_seq[i] * strain_increment_seq[i]
                else:
                    ep_hat_seq[i] = ep_hat_seq[i-1] + xi_hat_seq[i] * torch.abs(strain_increment_seq[i])
        
        # L_stress: 本构自洽损失（归一化）
        elastic_strain = strain_total_seq - ep_hat_seq
        stress_physics = (1 - d_hat_seq) * E0 * elastic_strain
        loss_stress = torch.mean(((sigma_hat_seq - stress_physics) / stress_scale)**2)
        
        # L_damage: 损伤物理损失 - 分为拉伸和压缩部分
        # 根据弹性应变符号，动态惩罚 d_hat_seq 与 d_plus_phy_seq 或 d_minus_phy_seq 的差异
        elastic_strain_phy = strain_total_seq - ep_phy_seq
        damage_loss_mask_plus = (elastic_strain_phy >= 0).float()
        damage_loss_mask_minus = (elastic_strain_phy < 0).float()
        
        # 计算拉压损伤损失
        loss_damage_plus = torch.mean((d_hat_seq - d_plus_phy_seq)**2 * damage_loss_mask_plus)
        loss_damage_minus = torch.mean((d_hat_seq - d_minus_phy_seq)**2 * damage_loss_mask_minus)
        
        # L_plastic: 塑性物理损失
        loss_plastic = torch.mean((ep_hat_seq - ep_phy_seq)**2)
        
        # 总损失
        total_loss = (self.lambda_data * loss_data + 
                     self.lambda_stress * loss_stress + 
                     self.lambda_damage_plus * loss_damage_plus +
                     self.lambda_damage_minus * loss_damage_minus +
                     self.lambda_plastic * loss_plastic)
        
        return total_loss, {
            'loss_data': loss_data.item(),
            'loss_stress': loss_stress.item(), 
            'loss_damage_plus': loss_damage_plus.item(),
            'loss_damage_minus': loss_damage_minus.item(),
            'loss_plastic': loss_plastic.item(),
            'total_loss': total_loss.item()
        } 