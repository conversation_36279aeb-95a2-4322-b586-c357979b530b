# 柱子滞回曲线模拟系统

## 项目概述

本项目基于物理信息神经网络(PINN)构建了一个柱子滞回曲线模拟系统，能够根据柱子的静态参数和位移序列，预测对应的滞回力曲线。系统能够捕捉材料的非线性行为和路径依赖性，为结构工程分析提供有力工具。

## 功能特点

- **数据驱动**: 利用实验数据训练模型，捕捉真实物理行为
- **物理约束**: 融合物理定律作为神经网络的软约束，提高预测准确性
- **路径依赖**: 特殊设计的网络结构能够捕捉滞回曲线的路径依赖特性
- **可视化**: 直观展示预测结果与实验数据的对比
- **泛化能力**: 对未见过的柱子参数组合具有良好的预测能力

## 系统要求

- Python 3.7+
- PyTorch 1.8+
- NumPy
- Pandas
- Matplotlib
- scikit-learn

## 项目结构

```
.
├── column_hysteresis_model.py  # 模型定义和数据处理函数
├── column_hysteresis_main.py   # 主程序入口
├── run_column_hysteresis.py    # 运行脚本（包含命令行参数）
└── README.md                   # 项目说明文档
```

## 使用方法

### 1. 数据准备

准备Excel文件，包含两个工作表：
- **基础参数**: 包含柱子的静态参数（混凝土强度、直径、配筋率等）
- **力的位移数据**: 包含实验测得的力-位移数据点

### 2. 训练模型

```bash
python run_column_hysteresis.py --excel_path "path/to/your/data.xlsx" --mode train --epochs 500
```

### 3. 预测新数据

```bash
python run_column_hysteresis.py --excel_path "path/to/new/data.xlsx" --mode predict --model_path "path/to/saved/model.pth"
```

### 4. 参数说明

- `--excel_path`: Excel数据文件路径
- `--epochs`: 训练轮数（默认500）
- `--batch_size`: 批次大小（默认64）
- `--lr`: 学习率（默认5e-4）
- `--save_dir`: 保存模型和图表的目录（默认"d:\column\"）
- `--mode`: 模式，train或predict（默认train）
- `--model_path`: 预测模式下加载的模型路径

## 输出结果

运行后，系统会生成以下文件：
- 训练历史曲线图（training_history.png）
- 滞回曲线对比图（hysteresis_curve_comparison.png）
- 滞回曲线叠加图（hysteresis_curve_overlay.png）
- 训练好的模型（column_hysteresis_model.pth）

## 模型评估

系统会输出以下评估指标：
- 平均绝对误差（MAE）
- 均方根误差（RMSE）
- 最大误差

## 技术原理

本系统基于物理信息神经网络（PINN）构建，结合了数据驱动和物理约束的优势：

1. **数据预处理**：归一化静态参数和动态序列，构建特征包括位移增量和方向变化标志
2. **网络结构**：多层神经网络结合LSTM捕捉时序依赖，使用注意力机制增强特征提取
3. **物理约束**：在损失函数中加入滞回闭合约束、能量守恒约束和平滑约束
4. **训练策略**：采用AdamW优化器，学习率自适应调整，早停机制防止过拟合

## 注意事项

- Excel文件格式必须符合要求，包含正确的工作表名称和数据列
- 训练时间取决于数据量和训练轮数，可能需要较长时间
- GPU加速可显著提高训练速度