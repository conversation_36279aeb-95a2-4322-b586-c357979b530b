import pandas as pd

def check_excel_file(file_path):
    try:
        # 读取Excel文件
        xls = pd.ExcelFile(file_path)
        print(f'Excel文件包含的工作表: {xls.sheet_names}')
        
        # 遍历每个工作表
        for sheet in xls.sheet_names:
            df = pd.read_excel(file_path, sheet_name=sheet)
            print(f'\n工作表 {sheet} 的形状: {df.shape}')
            print(f'工作表 {sheet} 的列名: {list(df.columns)}')
            print(f'\n工作表 {sheet} 的前5行:\n{df.head()}')
    
    except Exception as e:
        print(f'读取Excel文件时出错: {e}')

if __name__ == "__main__":
    file_path = 'd:/column/F2/data.xlsx'
    check_excel_file(file_path)