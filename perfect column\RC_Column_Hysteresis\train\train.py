# -*- coding: utf-8 -*-
"""
训练主脚本

实现钢筋混凝土柱滞回曲线PINN模型的训练流程。
"""

import os
import time
import numpy as np
import torch
import torch.optim as optim
from torch.optim.lr_scheduler import ReduceLROnPlateau
import matplotlib.pyplot as plt

# 导入自定义模块
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.data_utils import prepare_data
from models.bouc_wen_pinn import BoucWenPINN
from train.loss_functions import PINNLoss, EarlyStoppingLoss


def train_model(excel_path, results_dir, batch_size=32, epochs=1000, 
               hidden_dim=100, hidden_layers=4, lr=1e-3, lambda_data=1.0, 
               lambda_physics=0.5, patience=20, l2_reg=1e-5, use_lbfgs=True):
    """
    训练PINN模型
    
    参数:
        excel_path (str): Excel数据文件路径
        results_dir (str): 结果保存目录
        batch_size (int): 批次大小
        epochs (int): 训练轮数
        hidden_dim (int): 隐藏层维度
        hidden_layers (int): 隐藏层数量
        lr (float): 学习率
        lambda_data (float): 数据匹配损失权重
        lambda_physics (float): 物理残差损失权重
        patience (int): 早停耐心值
        l2_reg (float): L2正则化系数
        use_lbfgs (bool): 是否使用L-BFGS优化器进行精细调参
        
    返回:
        tuple: (训练好的模型, 训练历史记录)
    """
    # 创建结果目录
    os.makedirs(results_dir, exist_ok=True)
    models_dir = os.path.join(results_dir, "models")
    figures_dir = os.path.join(results_dir, "figures")
    os.makedirs(models_dir, exist_ok=True)
    os.makedirs(figures_dir, exist_ok=True)
    
    # 准备数据
    print("正在加载和处理数据...")
    data = prepare_data(excel_path, batch_size=batch_size, requires_grad=True)
    train_loader = data["train_loader"]
    test_loader = data["test_loader"]
    
    # 保存归一化参数，用于后续反归一化
    normalization_params = {
        "x_min": data["x_min"],
        "x_max": data["x_max"],
        "F_min": data["F_min"],
        "F_max": data["F_max"]
    }
    
    # 初始化模型
    print("初始化模型...")
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = BoucWenPINN(input_dim=6, hidden_dim=hidden_dim, hidden_layers=hidden_layers).to(device)
    
    # 初始化损失函数和优化器
    criterion = PINNLoss(lambda_data=lambda_data, lambda_physics=lambda_physics)
    optimizer = optim.Adam(model.parameters(), lr=lr, weight_decay=l2_reg)
    scheduler = ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=patience//2, verbose=True)
    early_stopping = EarlyStoppingLoss(patience=patience)
    
    # 训练历史记录
    history = {
        "train_loss": [],
        "val_loss": [],
        "data_loss": [],
        "physics_loss": [],
        "lr": []
    }
    
    # 训练循环
    print("开始训练...")
    best_val_loss = float('inf')
    start_time = time.time()
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        data_loss_sum = 0.0
        physics_loss_sum = 0.0
        
        for batch in train_loader:
            inputs = batch["input"].to(device)
            targets = batch["output"].to(device)
            
            # 提取位移和计算位移差分
            x = inputs[:, 0].unsqueeze(1).requires_grad_(True)
            # 计算位移差分 - 保持与x相同的批次大小
            delta_x = torch.zeros_like(x)
            # 对于批次中除最后一个样本外的所有样本，计算相邻样本间的位移差分
            if x.size(0) > 1:
                delta_x[:-1] = x[1:] - x[:-1]
            # 最后一个样本的差分设为0
            
            # 前向传播
            F_pred, z_pred = model(inputs)
            
            # 计算物理残差
            residual_F, residual_z = model.compute_physics_residuals(x, delta_x, F_pred, z_pred)
            
            # 计算损失
            loss, data_loss, physics_loss = criterion(F_pred, targets, residual_F, residual_z)
            
            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)  # 梯度裁剪
            optimizer.step()
            
            # 累计损失
            train_loss += loss.item()
            data_loss_sum += data_loss.item()
            physics_loss_sum += physics_loss.item()
        
        # 计算平均训练损失
        train_loss /= len(train_loader)
        data_loss_avg = data_loss_sum / len(train_loader)
        physics_loss_avg = physics_loss_sum / len(train_loader)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for batch in test_loader:
                inputs = batch["input"].to(device)
                targets = batch["output"].to(device)
                
                # 前向传播
                F_pred, _ = model(inputs)
                
                # 计算MSE损失（仅数据匹配部分）
                loss = torch.mean((F_pred - targets) ** 2)
                val_loss += loss.item()
        
        # 计算平均验证损失
        val_loss /= len(test_loader)
        
        # 更新学习率
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录训练历史
        history["train_loss"].append(train_loss)
        history["val_loss"].append(val_loss)
        history["data_loss"].append(data_loss_avg)
        history["physics_loss"].append(physics_loss_avg)
        history["lr"].append(current_lr)
        
        # 打印训练信息
        if (epoch + 1) % 10 == 0 or epoch == 0:
            print(f"Epoch {epoch+1}/{epochs} | "
                  f"Train Loss: {train_loss:.6f} | "
                  f"Val Loss: {val_loss:.6f} | "
                  f"Data Loss: {data_loss_avg:.6f} | "
                  f"Physics Loss: {physics_loss_avg:.6f} | "
                  f"LR: {current_lr:.6f}")
        
        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save({
                "epoch": epoch,
                "model_state_dict": model.state_dict(),
                "optimizer_state_dict": optimizer.state_dict(),
                "val_loss": val_loss,
                "normalization_params": normalization_params,
                "bouc_wen_params": model.get_bouc_wen_params()
            }, os.path.join(models_dir, "best_model.pth"))
        
        # 检查是否早停
        if early_stopping(val_loss):
            print(f"Early stopping at epoch {epoch+1}")
            break
    
    # 计算训练时间
    training_time = time.time() - start_time
    print(f"训练完成！总用时: {training_time:.2f}秒")
    
    # 保存最终模型
    torch.save({
        "epoch": epoch,
        "model_state_dict": model.state_dict(),
        "optimizer_state_dict": optimizer.state_dict(),
        "val_loss": val_loss,
        "normalization_params": normalization_params,
        "bouc_wen_params": model.get_bouc_wen_params(),
        "history": history
    }, os.path.join(models_dir, "final_model.pth"))
    
    # 绘制训练历史
    plot_training_history(history, figures_dir)
    
    # 如果启用L-BFGS优化器进行精细调参
    if use_lbfgs:
        print("\n使用L-BFGS优化器进行精细调参...")
        model = fine_tune_with_lbfgs(model, train_loader, criterion, device, models_dir, normalization_params)
    
    return model, history, normalization_params


def fine_tune_with_lbfgs(model, train_loader, criterion, device, models_dir, normalization_params, max_iter=50):
    """
    使用L-BFGS优化器进行精细调参
    
    参数:
        model (BoucWenPINN): 预训练模型
        train_loader (DataLoader): 训练数据加载器
        criterion (PINNLoss): 损失函数
        device (torch.device): 计算设备
        models_dir (str): 模型保存目录
        normalization_params (dict): 归一化参数
        max_iter (int): 最大迭代次数
        
    返回:
        BoucWenPINN: 精细调参后的模型
    """
    # 将所有训练数据合并为一个批次
    all_inputs = []
    all_targets = []
    
    for batch in train_loader:
        all_inputs.append(batch["input"])
        all_targets.append(batch["output"])
    
    inputs = torch.cat(all_inputs, dim=0).to(device)
    targets = torch.cat(all_targets, dim=0).to(device)
    
    # 提取位移和计算位移差分
    x = inputs[:, 0].unsqueeze(1).requires_grad_(True)
    delta_x = torch.cat([x[1:] - x[:-1], torch.zeros(1, 1, device=device)])
    
    # 定义闭包函数
    def closure():
        optimizer.zero_grad()
        F_pred, z_pred = model(inputs)
        residual_F, residual_z = model.compute_physics_residuals(x, delta_x, F_pred, z_pred)
        loss, _, _ = criterion(F_pred, targets, residual_F, residual_z)
        loss.backward()
        return loss
    
    # 初始化L-BFGS优化器
    optimizer = optim.LBFGS(model.parameters(), lr=0.1, max_iter=max_iter, line_search_fn="strong_wolfe")
    
    # 执行优化
    model.train()
    optimizer.step(closure)
    
    # 保存精细调参后的模型
    torch.save({
        "model_state_dict": model.state_dict(),
        "normalization_params": normalization_params,
        "bouc_wen_params": model.get_bouc_wen_params()
    }, os.path.join(models_dir, "fine_tuned_model.pth"))
    
    return model


def plot_training_history(history, figures_dir):
    """
    绘制训练历史曲线
    
    参数:
        history (dict): 训练历史记录
        figures_dir (str): 图表保存目录
    """
    # 创建图表
    plt.figure(figsize=(12, 10))
    
    # 绘制损失曲线
    plt.subplot(2, 2, 1)
    plt.plot(history["train_loss"], label="训练损失")
    plt.plot(history["val_loss"], label="验证损失")
    plt.xlabel("Epoch")
    plt.ylabel("损失")
    plt.title("训练与验证损失")
    plt.legend()
    plt.grid(True)
    
    # 绘制数据损失和物理损失
    plt.subplot(2, 2, 2)
    plt.plot(history["data_loss"], label="数据匹配损失")
    plt.plot(history["physics_loss"], label="物理残差损失")
    plt.xlabel("Epoch")
    plt.ylabel("损失")
    plt.title("损失组成")
    plt.legend()
    plt.grid(True)
    
    # 绘制学习率变化
    plt.subplot(2, 2, 3)
    plt.plot(history["lr"])
    plt.xlabel("Epoch")
    plt.ylabel("学习率")
    plt.title("学习率变化")
    plt.grid(True)
    
    # 保存图表
    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, "training_history.png"), dpi=300)
    plt.close()


if __name__ == "__main__":
    # 设置随机种子，确保结果可复现
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 设置文件路径
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    excel_path = os.path.join(os.path.dirname(current_dir), "column.xlsx")
    results_dir = os.path.join(current_dir, "results")
    
    # 训练模型
    model, history, normalization_params = train_model(
        excel_path=excel_path,
        results_dir=results_dir,
        batch_size=32,
        epochs=1000,
        hidden_dim=100,
        hidden_layers=4,
        lr=1e-3,
        lambda_data=1.0,
        lambda_physics=0.5,
        patience=20,
        l2_reg=1e-5,
        use_lbfgs=True
    )
    
    print("训练完成！模型已保存至:", os.path.join(results_dir, "models"))