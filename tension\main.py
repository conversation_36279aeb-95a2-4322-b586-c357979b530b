#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混凝土单轴拉伸损伤演化PINN模型主程序
基于思路.md文档构建的物理信息神经网络
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 导入PINN模型
from pinn_model import ConcreteDAMAGE_PINN
    


def load_data(file_path):
    """
    加载Excel数据
    """
    try:
        # 尝试读取Excel文件
        df = pd.read_excel(file_path)
        print(f"数据文件加载成功，数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        print(f"前5行数据:")
        print(df.head())
        return df
    except Exception as e:
        print(f"数据加载失败: {e}")
        # 生成模拟数据用于测试
        print("生成模拟数据用于测试...")
        strain = np.linspace(0, 0.01, 100)
        stress = 30000 * strain * np.exp(-100 * strain)  # 简化的应力-应变关系
        df = pd.DataFrame({
            'strain': strain,
            'stress': stress
        })
        return df

def train_model(model, train_loader, num_epochs=1000, lr=0.001):
    """
    训练PINN模型
    """
    optimizer = optim.Adam(model.parameters(), lr=lr)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=400, gamma=0.9)
    
    losses = []
    
    for epoch in range(num_epochs):
        epoch_loss = 0
        for batch_strain, batch_stress in train_loader:
            optimizer.zero_grad()
            
            # 前向传播
            pred_stress, pred_damage, pred_plastic = model(batch_strain)
            
            # 数据拟合损失
            data_loss = torch.mean((pred_stress - batch_stress)**2)
            
            # 计算物理约束损失 - 基于补充.md的计算思路
            physics_losses = model.physics_loss(
                batch_strain, pred_stress, pred_damage, pred_plastic
            )
            
            # 损失权重设置
            loss_weights = {
                'constitutive': 1.0,
                'yield_condition': 0.8,
                'damage_evolution': 0.5,
                'effective_stress': 1.0,
                'plastic_condition': 0.3,
                'plastic_monotonic': 0.1,
                'damage_monotonic': 0.1,
                'initial_condition': 1.0,
                'energy_dissipation': 0.1
            }
            
            # 计算加权物理损失
            physics_loss = sum(loss_weights[key] * physics_losses[key] for key in physics_losses.keys())
            
            # 总损失
            total_loss = 0.5 * data_loss + 1.0 * physics_loss
            
            total_loss.backward()
            optimizer.step()
            
            epoch_loss += total_loss.item()
        
        scheduler.step()
        losses.append(epoch_loss)
        
        if epoch % 100 == 0:
            print(f'Epoch {epoch}, Loss: {epoch_loss:.6f}')
            print(f'  E0: {model.E0.item():.2f}, ft: {model.ft.item():.4f}')
            print(f'  Gf: {model.Gf.item():.4f}, alpha: {model.alpha.item():.4f}')
    
    return losses

def visualize_results(model, strain_data, stress_data, save_dir):
    """
    可视化结果
    """
    model.eval()
    
    # 生成预测数据
    strain_tensor = torch.FloatTensor(strain_data.reshape(-1, 1))
    with torch.no_grad():
        pred_stress, pred_damage, pred_plastic = model(strain_tensor)
    
    pred_stress = pred_stress.numpy().flatten()
    pred_damage = pred_damage.numpy().flatten()
    pred_plastic = pred_plastic.numpy().flatten()
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 应力-应变曲线
    axes[0,0].plot(strain_data, stress_data, 'bo-', label='实验数据', markersize=3)
    axes[0,0].plot(strain_data, pred_stress, 'r-', label='PINN预测', linewidth=2)
    axes[0,0].set_xlabel('应变')
    axes[0,0].set_ylabel('应力 (MPa)')
    axes[0,0].set_title('应力-应变关系')
    axes[0,0].legend()
    axes[0,0].grid(True)
    
    # 损伤演化
    axes[0,1].plot(strain_data, pred_damage, 'g-', linewidth=2)
    axes[0,1].set_xlabel('应变')
    axes[0,1].set_ylabel('损伤变量 D')
    axes[0,1].set_title('损伤演化')
    axes[0,1].grid(True)
    
    # 塑性应变演化
    axes[1,0].plot(strain_data, pred_plastic, 'm-', linewidth=2)
    axes[1,0].set_xlabel('应变')
    axes[1,0].set_ylabel('塑性应变')
    axes[1,0].set_title('塑性应变演化')
    axes[1,0].grid(True)
    
    # 有效弹性模量
    effective_modulus = model.E0.item() * (1 - pred_damage)
    axes[1,1].plot(strain_data, effective_modulus, 'c-', linewidth=2)
    axes[1,1].set_xlabel('应变')
    axes[1,1].set_ylabel('有效弹性模量 (MPa)')
    axes[1,1].set_title('有效弹性模量演化')
    axes[1,1].grid(True)
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'pinn_results.png'), dpi=300, bbox_inches='tight')
    plt.show()
    
    # 保存预测结果
    results_df = pd.DataFrame({
        'strain': strain_data,
        'stress_exp': stress_data,
        'stress_pred': pred_stress,
        'damage': pred_damage,
        'plastic_strain': pred_plastic,
        'effective_modulus': effective_modulus
    })
    results_df.to_csv(os.path.join(save_dir, 'pinn_predictions.csv'), index=False)
    
    return results_df

def main():
    """
    主函数
    """
    print("=" * 60)
    print("混凝土单轴拉伸损伤演化PINN模型")
    print("=" * 60)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建输出目录
    output_dir = "d:/column/tension/results"
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载数据
    data_file = "d:/column/tension/tension.xlsx"
    df = load_data(data_file)
    
    # 数据预处理
    if 'strain' in df.columns and 'stress' in df.columns:
        strain_data = df['strain'].values
        stress_data = df['stress'].values
    else:
        # 假设第一列是应变，第二列是应力
        strain_data = df.iloc[:, 0].values
        stress_data = df.iloc[:, 1].values
    
    # 数据预处理 - 不进行标准化，保持物理意义
    # 只进行简单的数值范围检查和清理
    strain_data = np.maximum(strain_data, 0)  # 应变非负
    stress_data = np.maximum(stress_data, 0)  # 应力非负
    
    print(f"应变范围: {strain_data.min():.6f} ~ {strain_data.max():.6f}")
    print(f"应力范围: {stress_data.min():.2f} ~ {stress_data.max():.2f} MPa")
    
    # 转换为张量
    strain_tensor = torch.FloatTensor(strain_data.reshape(-1, 1)).to(device)
    stress_tensor = torch.FloatTensor(stress_data.reshape(-1, 1)).to(device)
    
    # 创建数据加载器
    dataset = torch.utils.data.TensorDataset(strain_tensor, stress_tensor)
    train_loader = torch.utils.data.DataLoader(dataset, batch_size=32, shuffle=True)
    
    # 创建模型
    model = ConcreteDAMAGE_PINN().to(device)
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters())}")
    
    # 训练模型
    print("\n开始训练...")
    losses = train_model(model, train_loader, num_epochs=2000, lr=0.002)
    
    # 保存训练损失
    plt.figure(figsize=(10, 6))
    plt.plot(losses)
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('训练损失曲线')
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'training_loss.png'), dpi=300, bbox_inches='tight')
    plt.show()
    
    # 可视化结果
    print("\n生成预测结果...")
    results_df = visualize_results(model, strain_data, stress_data, output_dir)
    
    # 保存模型
    torch.save(model.state_dict(), os.path.join(output_dir, 'pinn_model.pth'))
    
    # 输出训练得到的材料参数
    print("\n训练得到的材料参数:")
    print(f"初始弹性模量 E0: {model.E0.item():.2f} MPa")
    print(f"抗拉强度 ft: {model.ft.item():.4f} MPa")
    print(f"断裂能 Gf: {model.Gf.item():.4f} N/mm")
    print(f"损伤演化参数 alpha: {model.alpha.item():.4f}")
    print(f"损伤演化参数 beta: {model.beta.item():.2f}")
    
    # 保存参数到文件
    params_dict = {
        'E0': model.E0.item(),
        'ft': model.ft.item(),
        'Gf': model.Gf.item(),
        'alpha': model.alpha.item(),
        'beta': model.beta.item()
    }
    
    params_df = pd.DataFrame([params_dict])
    params_df.to_csv(os.path.join(output_dir, 'material_parameters.csv'), index=False)
    
    print(f"\n所有结果已保存到: {output_dir}")
    print("训练完成!")

if __name__ == "__main__":
    main()