"""
验证损伤演化和塑性应变修复效果的简单测试
"""
import torch
import numpy as np
import matplotlib.pyplot as plt
from pinn_model_v2_extended import PhysicsCalculatorV2Extended

# 启用字体配置
try:
    from font_config import setup_chinese_font
    setup_chinese_font()
except ImportError:
    pass

def test_damage_evolution():
    """测试损伤演化修复效果"""
    print("=" * 50)
    print("测试损伤演化和塑性应变修复效果")
    print("=" * 50)
    
    # 初始化物理计算器
    E0 = 30000.0  # MPa
    f_t = 3.0     # MPa
    f_c = 30.0    # MPa
    
    physics_calc = PhysicsCalculatorV2Extended(E0=E0, f_t=f_t, f_c=f_c)
    
    # 设置物理参数
    A_plus = torch.tensor(0.5)
    B_plus = torch.tensor(1.0)
    A_minus = torch.tensor(1.5)
    B_minus = torch.tensor(0.5)
    xi_plus = torch.tensor(0.05)
    xi_minus = torch.tensor(0.02)
    
    # 测试案例1：单调拉伸
    print("\n测试案例1：单调拉伸")
    strain_increment = torch.linspace(0.0001, 0.002, 50)
    
    d_plus_seq, d_minus_seq, ep_seq, stress_seq = physics_calc.calculate_physics_constraints(
        strain_increment, A_plus, B_plus, A_minus, B_minus, xi_plus, xi_minus
    )
    
    max_d_plus = torch.max(d_plus_seq).item()
    max_d_minus = torch.max(d_minus_seq).item()
    final_plastic = ep_seq[-1].item()
    
    print(f"  最大拉伸损伤: {max_d_plus:.4f}")
    print(f"  最大压缩损伤: {max_d_minus:.4f}")
    print(f"  最终塑性应变: {final_plastic:.6f}")
    
    # 测试案例2：单调压缩
    print("\n测试案例2：单调压缩")
    strain_increment = torch.linspace(-0.0001, -0.002, 50)
    
    d_plus_seq, d_minus_seq, ep_seq, stress_seq = physics_calc.calculate_physics_constraints(
        strain_increment, A_plus, B_plus, A_minus, B_minus, xi_plus, xi_minus
    )
    
    max_d_plus = torch.max(d_plus_seq).item()
    max_d_minus = torch.max(d_minus_seq).item()
    final_plastic = ep_seq[-1].item()
    
    print(f"  最大拉伸损伤: {max_d_plus:.4f}")
    print(f"  最大压缩损伤: {max_d_minus:.4f}")
    print(f"  最终塑性应变: {final_plastic:.6f}")
    
    # 测试案例3：拉压循环
    print("\n测试案例3：拉压循环")
    # 创建拉压循环加载
    n_points = 100
    strain_path = []
    
    # 拉伸段
    strain_path.extend(np.linspace(0, 0.002, n_points//4))
    # 卸载段
    strain_path.extend(np.linspace(0.002, -0.002, n_points//2))
    # 压缩段继续
    strain_path.extend(np.linspace(-0.002, 0, n_points//4))
    
    strain_increment = torch.tensor(np.diff(strain_path, prepend=0.0), dtype=torch.float32)
    
    d_plus_seq, d_minus_seq, ep_seq, stress_seq = physics_calc.calculate_physics_constraints(
        strain_increment, A_plus, B_plus, A_minus, B_minus, xi_plus, xi_minus
    )
    
    max_d_plus = torch.max(d_plus_seq).item()
    max_d_minus = torch.max(d_minus_seq).item()
    final_plastic = ep_seq[-1].item()
    max_stress = torch.max(stress_seq).item()
    min_stress = torch.min(stress_seq).item()
    
    print(f"  最大拉伸损伤: {max_d_plus:.4f}")
    print(f"  最大压缩损伤: {max_d_minus:.4f}")
    print(f"  最终塑性应变: {final_plastic:.6f}")
    print(f"  最大拉应力: {max_stress:.2f} MPa")
    print(f"  最大压应力: {min_stress:.2f} MPa")
    
    # 计算刚度退化（基于损伤）
    initial_stiffness = E0
    # 刚度退化应该基于损伤值计算：k_degraded = (1-d) * E0
    max_damage = max(max_d_plus, max_d_minus)
    stiffness_degradation = max_damage  # 损伤就是刚度退化
    
    print(f"  刚度退化: {stiffness_degradation:.4f} ({stiffness_degradation*100:.2f}%)")
    
    # 绘制结果
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 滞回曲线
    axes[0,0].plot(np.array(strain_path)*1000, stress_seq.numpy(), 'b-', linewidth=2)
    axes[0,0].set_xlabel('应变 (‰)')
    axes[0,0].set_ylabel('应力 (MPa)')
    axes[0,0].set_title('应力-应变滞回曲线')
    axes[0,0].grid(True, alpha=0.3)
    
    # 损伤演化
    axes[0,1].plot(d_plus_seq.numpy(), 'r-', linewidth=2, label='拉伸损伤')
    axes[0,1].plot(d_minus_seq.numpy(), 'b--', linewidth=2, label='压缩损伤')
    axes[0,1].set_xlabel('加载步')
    axes[0,1].set_ylabel('损伤变量')
    axes[0,1].set_title('损伤演化曲线')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)
    
    # 塑性应变演化
    axes[1,0].plot(ep_seq.numpy()*1000, 'g-', linewidth=2)
    axes[1,0].set_xlabel('加载步')
    axes[1,0].set_ylabel('塑性应变 (‰)')
    axes[1,0].set_title('塑性应变演化')
    axes[1,0].grid(True, alpha=0.3)
    
    # 应力演化
    axes[1,1].plot(stress_seq.numpy(), 'm-', linewidth=2)
    axes[1,1].set_xlabel('加载步')
    axes[1,1].set_ylabel('应力 (MPa)')
    axes[1,1].set_title('应力演化')
    axes[1,1].grid(True, alpha=0.3)
    
    plt.suptitle('损伤演化和塑性应变修复验证', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图像
    plt.savefig('verification_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n验证结果图像已保存为: verification_results.png")
    
    # 判断修复效果
    print("\n=" * 50)
    print("修复效果评估:")
    print("=" * 50)
    
    if max_d_plus > 0.01:
        print("✅ 拉伸损伤演化正常")
    else:
        print("❌ 拉伸损伤演化仍然异常")
    
    if max_d_minus > 0.01:
        print("✅ 压缩损伤演化正常")
    else:
        print("❌ 压缩损伤演化仍然异常")
    
    if abs(final_plastic) > 1e-6:
        print("✅ 塑性应变演化正常")
    else:
        print("❌ 塑性应变演化仍然异常")
    
    if stiffness_degradation > 0.01:
        print("✅ 刚度退化正常")
    else:
        print("❌ 刚度退化仍然异常")
    
    print("=" * 50)

if __name__ == "__main__":
    test_damage_evolution() 