      SUBROUTINE VUMAT(
     1                   NBLOCK, NDIR, NSHR, NSTATEV, NFIELDV, NPROPS,
     2                   PROPS, TEMP, DTIME, STRAN, DSTRAN, TIME,
     3                   STRESS, STATEV, DDSDDE, CELENT)
C
C     查找表VUMAT - 直接使用PINN预测结果
C     极简高效版本，完全避免复杂的损伤演化计算
C     
      INCLUDE 'vaba_param.inc'
C
C     参数定义
      PARAMETER (ZERO=0.D0, ONE=1.D0)
C
C     数组维数声明
      DIMENSION PROPS(NPROPS), STRESS(NBLOCK, NDIR+NSHR)
      DIMENSION STATEV(NBLOCK, NSTATEV), STRAN(NBLOCK, NDIR+NSHR)
      DIMENSION DSTRAN(NBLOCK, NDIR+NSHR)
      DIMENSION DDSDDE(NBLOCK, NDIR+NSHR, NDIR+NSHR)
C
C     局部变量
      REAL*8 STRAIN_11, STRESS_11, E_EFFECTIVE
C
C     开始计算循环
      DO K = 1, NBLOCK
C
C       1. 获取当前应变（一维问题，只考虑第1方向）
         STRAIN_11 = STRAN(K, 1)
C
C       2. 直接查表获取应力和有效模量
         CALL LOOKUP_STRESS_MODULUS(STRAIN_11, STRESS_11, E_EFFECTIVE)
C
C       3. 设置应力分量
         STRESS(K, 1) = STRESS_11
         STRESS(K, 2) = ZERO
         STRESS(K, 3) = ZERO
C
C       4. 设置刚度矩阵（一维简化）
         DDSDDE(K, 1, 1) = E_EFFECTIVE
         DDSDDE(K, 2, 2) = E_EFFECTIVE
         DDSDDE(K, 3, 3) = E_EFFECTIVE
C
C       5. 可选：存储查表结果到状态变量
         IF (NSTATEV .GE. 1) STATEV(K, 1) = STRESS_11
         IF (NSTATEV .GE. 2) STATEV(K, 2) = E_EFFECTIVE
C
      END DO
C
      RETURN
      END

C     包含查找表数据和子程序
      INCLUDE 'vumat_lookup_table.for'
