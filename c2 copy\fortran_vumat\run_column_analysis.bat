@echo off
setlocal enabledelayedexpansion
rem 混凝土柱VUMAT分析

echo ===================================================
echo    混凝土柱VUMAT分析
echo ===================================================

rem 检查Abaqus是否可用
where abaqus >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ 未找到Abaqus命令，请确保Abaqus已正确安装并添加到PATH
    pause
    exit /b 1
)

echo ✓ 找到Abaqus命令

echo.
echo 步骤1: 创建结果文件夹
echo -------------------

rem 生成时间戳
for /f "tokens=1-3 delims=/ " %%a in ('date /t') do set mydate=%%c%%b%%a
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set mytime=%%a%%b
set mytime=%mytime: =0%
set timestamp=%mydate:~-4%%mydate:~3,2%%mydate:~0,2%_%mytime:~0,2%%mytime:~3,2%%mytime:~6,2%

set RESULT_DIR=column_analysis_%timestamp%

echo ✓ 创建结果文件夹 %RESULT_DIR%
mkdir "%RESULT_DIR%"

rem 复制输入文件到结果文件夹
copy concrete_column.inp "%RESULT_DIR%\" >nul
copy vumat_concrete.f "%RESULT_DIR%\" >nul
copy material_parameters.inc "%RESULT_DIR%\" >nul

echo ✓ 输入文件已复制到结果文件夹

rem 记录最新分析文件夹路径
echo %RESULT_DIR% > latest_analysis_folder.txt

echo.
echo 步骤2: 切换到结果文件夹并运行Abaqus分析
echo -----------------------------------

cd /d "%RESULT_DIR%"

echo 正在运行Abaqus分析...
echo 命令: abaqus job=column_analysis user=vumat_concrete.f input=concrete_column.inp

abaqus job=column_analysis user=vumat_concrete.f input=concrete_column.inp

echo.
echo 分析已启动！
echo 结果将保存在文件夹: %RESULT_DIR%
echo.

cd /d "%~dp0"

echo ===================================================
echo    分析启动完成
echo ===================================================

echo 分析状态检查:
if exist "%RESULT_DIR%\column_analysis.odb" (
    echo ✓ 分析成功完成，ODB文件已生成
) else (
    echo ⚠ 分析可能仍在进行中，请等待完成
    echo   您可以查看 %RESULT_DIR%\column_analysis.log 了解进度
)

echo.
echo 下一步:
echo 1. 等待分析完成（查看.log文件了解进度）
echo 2. 运行 run_complete_workflow.bat 完成后续步骤
echo 3. 或手动提取结果：abaqus python extract_basic_data.py

echo.
pause 