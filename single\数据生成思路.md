我们根据文献中的单轴受拉试验数据生成模拟数据点。根据图1a和图1b，以及4.1.1和4.1.2节的参数，我们可以模拟高性能混凝土和普通混凝土的单轴受拉应力-应变曲线。

以4.1.1节的高性能混凝土为例：

弹性模量 E0 = 38000 MPa

抗拉强度 ft = 3.40 MPa (但文中给出f0+ = 3.68 MPa，这里我们取f0+作为初始损伤阈值)

参数：A+ = 0.0, B+ = 0.683

根据损伤模型，损伤变量 d+ 的计算公式为：

d+ = 1 - [ (r0+ / r+) * (1 - A+) + A+ ] * exp[ B+ * (1 - r+ / r0+) ]

在单轴拉伸情况下，有效应力 σ_eff = E0 * ε

损伤能释放率 Y+ = σ_eff   (因为单轴拉伸，应力张量分解后只有拉伸部分)

损伤阈值更新：在应变率无关情况下，r+ 取历史最大 Y+ 值（即 r+ = max(Y+)）

实际应力 σ = (1 - d+) * σ_eff

我们按照以下步骤生成数据：

1. 设定应变范围，从0到峰值应变（对应抗拉强度）的3倍左右（以覆盖软化段）。

2. 将应变离散为多个点（例如500个点）。

3. 对于每个应变点，计算有效应力（弹性应力）σ_eff = E0 * ε。

4. 更新损伤阈值 r+ = max(r+, σ_eff) （注意：r+初始值为f0+）。

5. 计算损伤变量 d+。

6. 计算实际应力 σ = (1 - d+) * σ_eff。

注意：由于是单调拉伸，没有卸载，所以直接按应变增加顺序计算即可。

对于普通混凝土（4.1.2节）：

E0 = 31000 MPa, f0+ = 3.57 MPa, A+ = 0.0, B+ = 0.518

我们将分别生成两组数据：高性能混凝土和普通混凝土。

数据点包括：应变（ε），应力（σ），损伤变量（d+），损伤阈值（r+）