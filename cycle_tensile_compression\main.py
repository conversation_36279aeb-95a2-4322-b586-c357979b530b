"""
混凝土拉压循环损伤参数识别主程序
整合训练和预测流程，确保结果文件组织有序
"""

import os
import sys
import argparse
from datetime import datetime
from pathlib import Path

# 导入训练和预测模块
from train_extended import TrainerExtended
from predict_cyclic_extended import CyclicPredictorExtended


def create_session_directory():
    """
    创建一个会话目录，用于存放本次运行的所有结果
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    session_dir = f"results/session_{timestamp}"
    Path(session_dir).mkdir(parents=True, exist_ok=True)
    
    print(f"创建会话目录: {session_dir}")
    return session_dir, timestamp


def train_model(data_file="cyclic_data.xlsx", session_dir=None, config=None):
    """
    训练模型
    """
    print("\n" + "="*60)
    print("开始训练阶段")
    print("="*60)
    
    # 创建训练器
    trainer = TrainerExtended(config)
    
    # 如果指定了会话目录，修改训练器的结果目录
    if session_dir:
        trainer.results_dir = os.path.join(session_dir, "training")
        Path(trainer.results_dir).mkdir(parents=True, exist_ok=True)
        trainer.timestamp = os.path.basename(session_dir).replace("session_", "")
    
    try:
        # 确保训练器结果目录设置正确
        if session_dir:
            print(f"训练结果将保存到: {trainer.results_dir}")
        else:
            Path(trainer.results_dir).mkdir(parents=True, exist_ok=True)
            print(f"训练结果将保存到: {trainer.results_dir}")
        
        # 加载数据
        print(f"加载训练数据: {data_file}")
        trainer.load_data(data_file)
        
        # 初始化模型
        trainer.initialize_model()
        
        # 开始训练
        trainer.train()
        
        print("训练完成成功!")
        return trainer.results_dir, True
        
    except Exception as e:
        print(f"训练过程中发生错误: {str(e)}")
        return None, False


def predict_with_model(model_path=None, session_dir=None, loading_scheme=None):
    """
    使用训练好的模型进行预测
    """
    print("\n" + "="*60)
    print("开始预测阶段")
    print("="*60)
    
    # 创建预测器
    predictor = CyclicPredictorExtended()
    
    # 如果指定了会话目录，设置预测结果目录
    if session_dir:
        predictor.prediction_dir = os.path.join(session_dir, "prediction")
        Path(predictor.prediction_dir).mkdir(parents=True, exist_ok=True)
    
    try:
        # 设置预测结果目录
        if session_dir:
            print(f"预测结果将保存到: {predictor.prediction_dir}")
        
        # 加载模型
        print(f"加载训练好的模型...")
        predictor.load_model(model_path)
        
        # 生成加载方案
        if loading_scheme is None:
            loading_scheme = generate_default_loading_scheme()
        
        print("\n加载方案:")
        for i, (strain, cycles) in enumerate(loading_scheme):
            direction = "拉伸" if strain > 0 else "压缩"
            print(f"  步骤 {i+1}: {direction}到 {abs(strain)*1000:.1f}‰ 应变, {cycles} 个循环")
        
        # 生成加载路径
        print("\n生成加载路径...")
        strain_path, loading_info = predictor.generate_cyclic_loading_path(loading_scheme)
        print(f"  总数据点数: {len(strain_path)}")
        print(f"  加载段数: {len(loading_info['segments'])}")
        print(f"  循环数: {len(loading_info['cycles'])}")
        
        # 预测响应
        print("\n使用PINN模型预测响应...")
        results = predictor.predict_response(strain_path, loading_info)
        
        # 分析结果
        print("\n分析滞回特性...")
        analysis = predictor.analyze_hysteresis_loops(results, loading_info)
        
        # 打印关键结果
        print("\n关键结果:")
        print(f"  最大拉应力: {analysis['overall']['max_tensile_stress']:.2f} MPa")
        print(f"  最大压应力: {analysis['overall']['max_compressive_stress']:.2f} MPa")
        print(f"  最大拉伸损伤: {analysis['overall']['max_tensile_damage']:.4f}")
        print(f"  最大压缩损伤: {analysis['overall']['max_compressive_damage']:.4f}")
        print(f"  最终塑性应变: {analysis['overall']['final_plastic_strain']*1000:.4f}‰")
        print(f"  总耗散能量: {analysis['overall']['total_energy_dissipated']:.6f}")
        
        # 绘制结果
        print("\n绘制分析图像...")
        plot_path = predictor.plot_results(results, loading_info, analysis)
        
        # 保存结果
        print("\n保存预测结果...")
        excel_path, json_path = predictor.save_results(results, loading_info, analysis)
        
        print("预测完成成功!")
        return predictor.prediction_dir, True
        
    except Exception as e:
        print(f"预测过程中发生错误: {str(e)}")
        return None, False


def generate_default_loading_scheme():
    """
    生成默认的拉压循环加载方案
    """
    # 逐级增加的拉压循环，确保能够触发损伤
    loading_scheme = [
        (0.0002, 1),   # 拉伸到0.02%应变（弹性阶段）
        (-0.0002, 1),  # 压缩到-0.02%应变（弹性阶段）
        (0.0005, 1),   # 拉伸到0.05%应变（接近弹性极限）
        (-0.0005, 1),  # 压缩到-0.05%应变（接近弹性极限）
        (0.001, 1),    # 拉伸到0.1%应变（超过弹性极限）
        (-0.001, 1),   # 压缩到-0.1%应变（超过弹性极限）
        (0.002, 1),    # 拉伸到0.2%应变（明显的塑性变形）
        (-0.002, 1),   # 压缩到-0.2%应变（明显的塑性变形）
        (0.004, 1),    # 拉伸到0.4%应变（更大的损伤）
        (-0.004, 1),   # 压缩到-0.4%应变（更大的损伤）
    ]
    return loading_scheme


def generate_summary_report(session_dir):
    """
    生成总结报告
    """
    print("\n" + "="*60)
    print("生成总结报告")
    print("="*60)
    
    report_path = os.path.join(session_dir, "session_summary.txt")
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("混凝土拉压循环损伤参数识别 - 会话总结报告\n")
        f.write("="*60 + "\n\n")
        
        f.write(f"会话目录: {session_dir}\n")
        f.write(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 检查训练结果
        training_dir = os.path.join(session_dir, "training")
        if os.path.exists(training_dir):
            f.write("训练阶段:\n")
            f.write(f"  训练结果目录: {training_dir}\n")
            
            # 查找模型文件
            model_files = list(Path(training_dir).glob("pinn_model_*.pth"))
            if model_files:
                f.write(f"  训练模型: {model_files[0].name}\n")
            
            # 查找参数文件
            param_files = list(Path(training_dir).glob("identified_parameters_*.json"))
            if param_files:
                f.write(f"  识别参数: {param_files[0].name}\n")
            
            # 查找图像文件
            image_files = list(Path(training_dir).glob("*.png"))
            f.write(f"  生成图像: {len(image_files)} 个\n")
            
        # 检查预测结果
        prediction_dir = os.path.join(session_dir, "prediction")
        if os.path.exists(prediction_dir):
            f.write("\n预测阶段:\n")
            f.write(f"  预测结果目录: {prediction_dir}\n")
            
            # 查找结果文件
            excel_files = list(Path(prediction_dir).glob("prediction_results_*.xlsx"))
            if excel_files:
                f.write(f"  预测结果: {excel_files[0].name}\n")
            
            json_files = list(Path(prediction_dir).glob("analysis_results_*.json"))
            if json_files:
                f.write(f"  分析结果: {json_files[0].name}\n")
            
            # 查找图像文件
            image_files = list(Path(prediction_dir).glob("*.png"))
            f.write(f"  生成图像: {len(image_files)} 个\n")
        
        f.write("\n会话完成!\n")
    
    print(f"总结报告已保存至: {report_path}")
    return report_path


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='混凝土拉压循环损伤参数识别')
    parser.add_argument('--data', default='cyclic_data.xlsx', help='训练数据文件路径')
    parser.add_argument('--epochs', type=int, default=1500, help='训练轮数')
    parser.add_argument('--lr', type=float, default=0.001, help='学习率')
    parser.add_argument('--train-only', action='store_true', help='仅训练，不预测')
    parser.add_argument('--predict-only', action='store_true', help='仅预测，不训练')
    parser.add_argument('--model-path', help='用于预测的模型路径')
    
    args = parser.parse_args()
    
    print("混凝土拉压循环损伤参数识别系统")
    print("="*60)
    print(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"数据文件: {args.data}")
    
    # 创建会话目录
    session_dir, timestamp = create_session_directory()
    
    training_success = False
    prediction_success = False
    model_path = args.model_path
    
    try:
        # 训练阶段
        if not args.predict_only:
            training_config = {
                'num_epochs': args.epochs,
                'learning_rate': args.lr
            }
            
            training_dir, training_success = train_model(
                data_file=args.data,
                session_dir=session_dir,
                config=training_config
            )
            
            if training_success:
                # 找到训练好的模型
                model_files = list(Path(training_dir).glob("pinn_model_*.pth"))
                if model_files:
                    model_path = str(model_files[0])
        
        # 预测阶段
        if not args.train_only and (training_success or args.predict_only):
            if model_path is None:
                print("警告: 未找到可用的模型文件，跳过预测阶段")
            else:
                prediction_dir, prediction_success = predict_with_model(
                    model_path=model_path,
                    session_dir=session_dir
                )
        
        # 生成总结报告
        generate_summary_report(session_dir)
        
        print("\n" + "="*60)
        print("程序运行完成!")
        print(f"所有结果已保存在: {session_dir}")
        
        if training_success:
            print("✓ 训练阶段成功完成")
        if prediction_success:
            print("✓ 预测阶段成功完成")
        
        print("="*60)
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行过程中发生错误: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main()) 