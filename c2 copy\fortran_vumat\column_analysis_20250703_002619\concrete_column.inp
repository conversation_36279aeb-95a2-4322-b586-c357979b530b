*HEADING
混凝土柱VUMAT验证模型 - 循环荷载下的滞回曲线
** 单位: MPa, mm, s
** 圆柱体: 直径400mm, 长度800mm
**
*PREPRINT, ECHO=NO, MODEL=NO, HISTORY=NO, CONTACT=NO
**
** ----------------------------------------------------------------
** 节点定义
** ----------------------------------------------------------------
*NODE, NSET=ALL_NODES
** 底面中心节点
      1,           0.,           0.,           0.
** 底面周边节点 (8个)
    101,         200.,           0.,           0.
    102,      141.421,      141.421,           0.
    103,           0.,         200.,           0.
    104,     -141.421,      141.421,           0.
    105,        -200.,           0.,           0.
    106,     -141.421,     -141.421,           0.
    107,           0.,        -200.,           0.
    108,      141.421,     -141.421,           0.
** 顶面中心节点
      2,           0.,           0.,         800.
** 顶面周边节点 (8个)
    201,         200.,           0.,         800.
    202,      141.421,      141.421,         800.
    203,           0.,         200.,         800.
    204,     -141.421,      141.421,         800.
    205,        -200.,           0.,         800.
    206,     -141.421,     -141.421,         800.
    207,           0.,        -200.,         800.
    208,      141.421,     -141.421,         800.
** 中间层节点 (每层9个节点，共3层)
** 高度 = 200mm
    301,         200.,           0.,         200.
    302,      141.421,      141.421,         200.
    303,           0.,         200.,         200.
    304,     -141.421,      141.421,         200.
    305,        -200.,           0.,         200.
    306,     -141.421,     -141.421,         200.
    307,           0.,        -200.,         200.
    308,      141.421,     -141.421,         200.
    309,           0.,           0.,         200.
** 高度 = 400mm
    401,         200.,           0.,         400.
    402,      141.421,      141.421,         400.
    403,           0.,         200.,         400.
    404,     -141.421,      141.421,         400.
    405,        -200.,           0.,         400.
    406,     -141.421,     -141.421,         400.
    407,           0.,        -200.,         400.
    408,      141.421,     -141.421,         400.
    409,           0.,           0.,         400.
** 高度 = 600mm
    501,         200.,           0.,         600.
    502,      141.421,      141.421,         600.
    503,           0.,         200.,         600.
    504,     -141.421,      141.421,         600.
    505,        -200.,           0.,         600.
    506,     -141.421,     -141.421,         600.
    507,           0.,        -200.,         600.
    508,      141.421,     -141.421,         600.
    509,           0.,           0.,         600.
**
** ----------------------------------------------------------------
** 单元定义 - 使用C3D8R (8节点线性砖，减缩积分)
** ----------------------------------------------------------------
*ELEMENT, TYPE=C3D8R, ELSET=COLUMN
** 底层单元
    1, 1, 101, 102, 103, 309, 301, 302, 303
    2, 1, 103, 104, 105, 309, 303, 304, 305
    3, 1, 105, 106, 107, 309, 305, 306, 307
    4, 1, 107, 108, 101, 309, 307, 308, 301
** 中下层单元
   11, 309, 301, 302, 303, 409, 401, 402, 403
   12, 309, 303, 304, 305, 409, 403, 404, 405
   13, 309, 305, 306, 307, 409, 405, 406, 407
   14, 309, 307, 308, 301, 409, 407, 408, 401
** 中上层单元
   21, 409, 401, 402, 403, 509, 501, 502, 503
   22, 409, 403, 404, 405, 509, 503, 504, 505
   23, 409, 405, 406, 407, 509, 505, 506, 507
   24, 409, 407, 408, 401, 509, 507, 508, 501
** 顶层单元
   31, 509, 501, 502, 503, 2, 201, 202, 203
   32, 509, 503, 504, 505, 2, 203, 204, 205
   33, 509, 505, 506, 507, 2, 205, 206, 207
   34, 509, 507, 508, 501, 2, 207, 208, 201
**
** ----------------------------------------------------------------
** 节点集定义
** ----------------------------------------------------------------
*NSET, NSET=BOTTOM
1, 101, 102, 103, 104, 105, 106, 107, 108
**
*NSET, NSET=TOP
2, 201, 202, 203, 204, 205, 206, 207, 208
**
*NSET, NSET=TOP_CENTER
2
**
** ----------------------------------------------------------------
** 材料定义
** ----------------------------------------------------------------
*MATERIAL, NAME=CONCRETE_DAMAGE
*USER MATERIAL, CONSTANTS=9
** E0,     f_t,     f_c,     A+,      B+,      xi+,     A-,      B-,      xi-
10000.0,  3.67043, 10.0,    0.84463, 1.81372, 0.5,     2.0,     1.32925, 0.50028
*DENSITY
2.4e-9
*DEPVAR
5
** 1: Plastic strain
** 2: Tensile damage d+
** 3: Compressive damage d-  
** 4: Maximum tensile damage force r_max+
** 5: Maximum compressive damage force r_max-
**
** ----------------------------------------------------------------
** 截面定义
** ----------------------------------------------------------------
*SOLID SECTION, ELSET=COLUMN, MATERIAL=CONCRETE_DAMAGE
**
** ----------------------------------------------------------------
** 初始条件
** ----------------------------------------------------------------
*INITIAL CONDITIONS, TYPE=SOLUTION
COLUMN, 0.0, 0.0, 0.0, 3.67043, 10.0
**
** ----------------------------------------------------------------
** 边界条件
** ----------------------------------------------------------------
*BOUNDARY
BOTTOM, 1, 3
**
** ----------------------------------------------------------------
** 循环荷载定义
** ----------------------------------------------------------------
*AMPLITUDE, NAME=CYCLIC_LOAD
0.0, 0.0
0.1, 0.16
0.2, 0.0
0.3, -0.24
0.4, 0.0
0.5, 0.32
0.6, 0.0
0.7, -0.48
0.8, 0.0
0.9, 0.48
1.0, 0.0
**
** ----------------------------------------------------------------
** 分析步骤
** ----------------------------------------------------------------
*STEP, NAME=CyclicLoading, NLGEOM=YES
*DYNAMIC, EXPLICIT
, 1.0
**
*BOUNDARY, AMPLITUDE=CYCLIC_LOAD
TOP, 1, 1, 1.0
**
** ----------------------------------------------------------------
** 输出设置
** ----------------------------------------------------------------
*OUTPUT, FIELD, NUMBER INTERVAL=20
*NODE OUTPUT
U, V, A, RF
*ELEMENT OUTPUT
S, LE, SDV
**
*OUTPUT, HISTORY, FREQUENCY=20
*NODE OUTPUT, NSET=TOP_CENTER
U1, RF1
*ELEMENT OUTPUT, ELSET=COLUMN
S11, LE11, SDV1, SDV2, SDV3, SDV4, SDV5
**
*END STEP 