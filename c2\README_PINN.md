# PINN-Based Concrete Cyclic Damage Model

> Physics-Informed Neural Network (PINN) framework for simulating cyclic tension–compression behaviour of concrete.

## 1. 目录总览

```
c2/
├── train.py              # 端到端训练：构建网络、损失、日志
├── main.py               # 推理 / 评估入口
├── pinn_model_v2.py      # 网络结构及物理约束损失
├── predict_hysteresis.py # 生成滞回曲线、误差评估
├── data_processor.py     # 读取 / 归一化实验数据
├── model_utils.py        # 权重初始化、梯度检查等工具
├── results/…             # 训练日志、模型权重、图表
└── requirements.txt      # 依赖包列表（Python>=3.9, PyTorch>=2.0 等）
```



## 2. 快速开始

### 2.1 环境安装

```bash
conda create -n pinn-env python=3.10
conda activate pinn-env
pip install -r requirements.txt
```

### 2.2 数据准备

```
input/
└── cyclic_data.xlsx   # 原始实验曲线  
```

`data_processor.py` 会在首次运行时自动拆分为 `train / val / test` 数据集。

### 2.3 训练

```bash
python train.py --epochs 2000 --batch-size 512 \
  --loss-weights.phys 1.0 --loss-weights.data 0.3
```

输出：
* `results/` — 训练曲线 (`loss_history.png`, `mae.png`)
* `checkpoints/` — `best_model.pth`（验证误差最小）

### 2.4 推理与评估

```bash
python main.py --checkpoint checkpoints/best_model.pth --plot
```

将生成：
* `pred_vs_exp.png` — 模型预测与实验对比
* `metrics.txt` — MAE、RMSE 等指标

## 3. 物理约束与损失函数

损失由三部分构成：

1. `L_data` — 与实验应力–应变数据的 MSE  
2. `L_phys` — 平衡方程、能量守恒等残差  
3. `L_reg` — L2 正则，控制模型复杂度  

总损失：`L = w_data * L_data + w_phys * L_phys + w_reg * L_reg`  
详见 `pinn_model_v2.py::forward()`。

## 4. 结果示例

| 案例        | 实验峰值应力 | 预测峰值应力 | 误差   |
| ----------- | ------------ | ------------ | ------ |
| 压缩加载 #1 | 39.5 MPa     | 38.9 MPa     | –1.5 % |
| 张拉加载 #2 | –3.2 MPa     | –3.3 MPa     | +2.3 % |

完整图表位于 `results/session_xxx/`。

## 5. 常见问题

* **梯度爆炸**  
  调低 `--lr`，或在 `pinn_model_v2.py` 中启用梯度裁剪。  
* **显存不足**  
  缩小 `--batch-size`，或添加 `--mixed-precision`。

## 6. 参考文献

1. Raissi, M. *Physics-Informed Neural Networks: A Deep Learning Framework for Solving Forward and Inverse Problems Involving Nonlinear Partial Differential Equations*, JCP, 2019.
2. Lu, L. *DeepXDE: A Deep Learning Library for Solving Differential Equations*, arXiv:1907.04502. 