import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False
from datetime import datetime
import os

class TensileDamageModel:
    """混凝土拉伸损伤本构模型"""
    
    def __init__(self, E0=30000, ft=3.0, A_plus=1.0, B_plus=0.13, mu_plus=2100, a_plus=5.5):
        """
        初始化拉伸损伤模型参数
        
        参数:
        E0: 初始弹性模量 (MPa)
        ft: 单轴抗拉强度 (MPa)
        A_plus: 形状参数1，控制初始损伤演化速率
        B_plus: 形状参数2，控制损伤饱和趋势
        mu_plus: 粘性系数 (N/(s·m))
        a_plus: 非线性指数
        """
        self.E0 = E0
        self.ft = ft
        self.r0_plus = ft  # 初始阈值等于抗拉强度
        self.A_plus = A_plus
        self.B_plus = B_plus
        self.mu_plus = mu_plus
        self.a_plus = a_plus
        
    def calculate_Y_plus(self, stress):
        """计算拉伸损伤能释放率 Y+ (式15)"""
        # 简化为单轴情况
        if stress > 0:  # 拉伸应力
            return np.sqrt(self.E0 * stress**2 / self.E0)  # 简化为 |stress|
        return 0
    
    def update_r_plus_static(self, r_plus_old, Y_plus):
        """准静态加载下的阈值更新 (式30)"""
        return max(r_plus_old, Y_plus)
    
    def calculate_damage(self, r_plus):
        """计算拉伸损伤变量 d+ (式18)"""
        if r_plus <= self.r0_plus:
            return 0
        
        ratio = self.r0_plus / r_plus
        exp_term = np.exp(self.B_plus * (1 - r_plus / self.r0_plus))
        d_plus = 1 - (ratio * (1 - self.A_plus) + self.A_plus) * exp_term
        
        return max(0, min(d_plus, 0.99))  # 限制损伤在[0, 0.99]范围内
    
    def generate_tensile_curve(self, max_strain=0.001, num_points=100, loading_cycles=3):
        """生成拉伸应力-应变曲线数据"""
        
        # 生成加载路径
        strain_path = []
        
        # 单调加载到峰值
        strain_monotonic = np.linspace(0, max_strain, num_points//2)
        strain_path.extend(strain_monotonic)
        
        # 循环加卸载
        for cycle in range(loading_cycles):
            # 卸载到零应变
            strain_unload = np.linspace(max_strain, 0, num_points//4)
            strain_path.extend(strain_unload[1:])  # 避免重复点
            
            # 重新加载
            strain_reload = np.linspace(0, max_strain * (1 + 0.1 * cycle), num_points//4)
            strain_path.extend(strain_reload[1:])
            max_strain = max_strain * (1 + 0.1 * cycle)
        
        strain_array = np.array(strain_path)
        stress_array = np.zeros_like(strain_array)
        damage_array = np.zeros_like(strain_array)
        r_plus_array = np.zeros_like(strain_array)
        
        # 初始化状态变量
        r_plus = self.r0_plus
        d_plus = 0
        
        for i, strain in enumerate(strain_array):
            # 计算弹性应力
            elastic_stress = self.E0 * strain
            
            if elastic_stress > 0:  # 拉伸状态
                # 计算损伤能释放率
                Y_plus = self.calculate_Y_plus(elastic_stress)
                
                # 判断是否为加载段（应变增加）
                is_loading = (i == 0) or (strain > strain_array[i-1])
                
                if is_loading:  # 仅在加载时更新损伤
                    r_plus = self.update_r_plus_static(r_plus, Y_plus)
                    d_plus = self.calculate_damage(r_plus)
                # 卸载段：损伤不演化，保持当前损伤值
                
                # 计算实际应力
                stress = (1 - d_plus) * elastic_stress
            else:
                stress = 0
                Y_plus = 0
            
            stress_array[i] = stress
            damage_array[i] = d_plus
            r_plus_array[i] = r_plus
        
        return strain_array, stress_array, damage_array, r_plus_array

def generate_experimental_data():
    """生成多组拉伸实验数据"""
    
    # 定义不同的材料参数组合
    test_configs = [
        {'name': 'C30混凝土', 'E0': 30000, 'ft': 2.8, 'A_plus': 1.0, 'B_plus': 0.13},
        {'name': 'C40混凝土', 'E0': 32500, 'ft': 3.2, 'A_plus': 0.9, 'B_plus': 0.15},
        {'name': 'C50混凝土', 'E0': 35000, 'ft': 3.6, 'A_plus': 0.8, 'B_plus': 0.17},
        {'name': '高强混凝土', 'E0': 38000, 'ft': 4.2, 'A_plus': 0.7, 'B_plus': 0.20},
    ]
    
    all_data = []
    
    for config in test_configs:
        model = TensileDamageModel(**{k: v for k, v in config.items() if k != 'name'})
        
        # 生成不同应变幅值的试验
        strain_levels = [0.0008, 0.0012, 0.0015]
        
        for j, max_strain in enumerate(strain_levels):
            strain, stress, damage, r_plus = model.generate_tensile_curve(
                max_strain=max_strain, 
                num_points=120,
                loading_cycles=2
            )
            
            # 添加实验噪声
            noise_level = 0.02  # 2%噪声
            stress_noisy = stress * (1 + np.random.normal(0, noise_level, len(stress)))
            
            # 创建数据记录
            for k in range(len(strain)):
                all_data.append({
                    '试验编号': f"{config['name']}_试验{j+1}",
                    '材料类型': config['name'],
                    '弹性模量(MPa)': config['E0'],
                    '抗拉强度(MPa)': config['ft'],
                    'A+参数': config['A_plus'],
                    'B+参数': config['B_plus'],
                    '应变': strain[k],
                    '理论应力(MPa)': stress[k],
                    '实测应力(MPa)': stress_noisy[k],
                    '损伤变量': damage[k],
                    '阈值r+': r_plus[k],
                    '数据点序号': k+1,
                    '最大应变': max_strain
                })
    
    return pd.DataFrame(all_data)

def create_html_report(df, output_dir):
    """创建HTML报告"""
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('混凝土拉伸损伤本构关系实验数据分析', fontsize=16, fontweight='bold')
    
    # 1. 应力-应变曲线对比
    ax1 = axes[0, 0]
    materials = df['材料类型'].unique()
    colors = plt.cm.Set1(np.linspace(0, 1, len(materials)))
    
    for i, material in enumerate(materials):
        material_data = df[df['材料类型'] == material]
        test_groups = material_data['试验编号'].unique()
        
        for test in test_groups:
            test_data = material_data[material_data['试验编号'] == test]
            ax1.plot(test_data['应变'], test_data['实测应力(MPa)'], 
                    color=colors[i], alpha=0.7, linewidth=1.5,
                    label=f'{material}' if test == test_groups[0] else "")
    
    ax1.set_xlabel('应变')
    ax1.set_ylabel('应力 (MPa)')
    ax1.set_title('应力-应变曲线对比')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 损伤演化曲线
    ax2 = axes[0, 1]
    for i, material in enumerate(materials):
        material_data = df[df['材料类型'] == material]
        test_data = material_data[material_data['试验编号'] == material_data['试验编号'].iloc[0]]
        ax2.plot(test_data['应变'], test_data['损伤变量'], 
                color=colors[i], linewidth=2, label=material)
    
    ax2.set_xlabel('应变')
    ax2.set_ylabel('损伤变量 d+')
    ax2.set_title('损伤演化曲线')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 参数敏感性分析
    ax3 = axes[1, 0]
    param_data = df.groupby('材料类型')[['A+参数', 'B+参数', '抗拉强度(MPa)']].first()
    x_pos = np.arange(len(materials))
    
    ax3_twin = ax3.twinx()
    bars1 = ax3.bar(x_pos - 0.2, param_data['A+参数'], 0.4, label='A+参数', alpha=0.7)
    bars2 = ax3.bar(x_pos + 0.2, param_data['B+参数'], 0.4, label='B+参数', alpha=0.7)
    line1 = ax3_twin.plot(x_pos, param_data['抗拉强度(MPa)'], 'ro-', label='抗拉强度')
    
    ax3.set_xlabel('材料类型')
    ax3.set_ylabel('损伤参数')
    ax3_twin.set_ylabel('抗拉强度 (MPa)')
    ax3.set_title('材料参数对比')
    ax3.set_xticks(x_pos)
    ax3.set_xticklabels(materials, rotation=45)
    
    # 合并图例
    lines1, labels1 = ax3.get_legend_handles_labels()
    lines2, labels2 = ax3_twin.get_legend_handles_labels()
    ax3.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    # 4. 数据统计
    ax4 = axes[1, 1]
    stats_data = df.groupby('材料类型').agg({
        '实测应力(MPa)': ['max', 'mean'],
        '损伤变量': 'max',
        '试验编号': 'nunique'
    }).round(3)
    
    # 创建统计表格
    table_data = []
    for material in materials:
        row = [
            material,
            f"{stats_data.loc[material, ('实测应力(MPa)', 'max')]:.2f}",
            f"{stats_data.loc[material, ('实测应力(MPa)', 'mean')]:.2f}",
            f"{stats_data.loc[material, ('损伤变量', 'max')]:.3f}",
            f"{stats_data.loc[material, ('试验编号', 'nunique')]}"
        ]
        table_data.append(row)
    
    ax4.axis('tight')
    ax4.axis('off')
    table = ax4.table(cellText=table_data,
                     colLabels=['材料类型', '峰值应力', '平均应力', '最大损伤', '试验数量'],
                     cellLoc='center',
                     loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 1.5)
    ax4.set_title('数据统计摘要')
    
    plt.tight_layout()
    
    # 保存图片
    plot_path = os.path.join(output_dir, 'tensile_analysis.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    # 生成HTML内容
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>混凝土拉伸损伤本构关系实验数据报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }}
        .formula-section {{
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }}
        .formula {{
            background-color: white;
            padding: 15px;
            border-left: 4px solid #e74c3c;
            margin: 10px 0;
            font-family: 'Times New Roman', serif;
            font-size: 16px;
        }}
        .parameter-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        .parameter-table th, .parameter-table td {{
            border: 1px solid #bdc3c7;
            padding: 12px;
            text-align: center;
        }}
        .parameter-table th {{
            background-color: #3498db;
            color: white;
        }}
        .parameter-table tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}
        .chart-container {{
            text-align: center;
            margin: 30px 0;
        }}
        .chart-container img {{
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
        }}
        .download-section {{
            background-color: #d5f4e6;
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
            text-align: center;
        }}
        .download-btn {{
            display: inline-block;
            background-color: #27ae60;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px;
            font-weight: bold;
        }}
        .download-btn:hover {{
            background-color: #219a52;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        .stats-card {{
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }}
        .stats-number {{
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>混凝土拉伸损伤本构关系实验数据报告</h1>
        
        <div class="stats-grid">
            <div class="stats-card">
                <h3>数据总量</h3>
                <div class="stats-number">{len(df):,}</div>
                <p>个数据点</p>
            </div>
            <div class="stats-card">
                <h3>材料类型</h3>
                <div class="stats-number">{len(df['材料类型'].unique())}</div>
                <p>种混凝土</p>
            </div>
            <div class="stats-card">
                <h3>试验组数</h3>
                <div class="stats-number">{len(df['试验编号'].unique())}</div>
                <p>组试验</p>
            </div>
            <div class="stats-card">
                <h3>生成时间</h3>
                <div class="stats-number">{datetime.now().strftime('%Y-%m-%d')}</div>
                <p>{datetime.now().strftime('%H:%M:%S')}</p>
            </div>
        </div>

        <h2>1. 拉伸损伤理论公式</h2>
        <div class="formula-section">
            <h3>拉伸损伤能释放率（Y⁺）</h3>
            <div class="formula">
                Y⁺ = √(E₀(σ⁺ : A₀ : σ⁺))　　(式15)
            </div>
            <p><strong>物理意义：</strong>基于拉伸应力分量σ⁺的能量释放率</p>
            
            <h3>拉伸损伤阈值演化（r⁺）</h3>
            <div class="formula">
                r⁺ₙₑw = max(r⁺ₒₗd, Y⁺)　　(式30，准静态)
            </div>
            <div class="formula">
                ṙ⁺ = μ⁺⟨Y⁺/r⁺ - 1⟩^(a⁺)　　(式17，动力加载)
            </div>
            
            <h3>拉伸损伤变量（d⁺）</h3>
            <div class="formula">
                d⁺ = 1 - (r₀⁺/r⁺(1-A⁺) + A⁺)exp(B⁺(1-r⁺/r₀⁺))　　(式18)
            </div>
        </div>

        <h2>2. 模型参数说明</h2>
        <table class="parameter-table">
            <tr>
                <th>参数符号</th>
                <th>物理意义</th>
                <th>典型取值</th>
                <th>单位</th>
            </tr>
            <tr>
                <td>E₀</td>
                <td>初始弹性模量</td>
                <td>30000-38000</td>
                <td>MPa</td>
            </tr>
            <tr>
                <td>r₀⁺</td>
                <td>初始阈值（抗拉强度）</td>
                <td>2.8-4.2</td>
                <td>MPa</td>
            </tr>
            <tr>
                <td>A⁺</td>
                <td>初始损伤演化速率</td>
                <td>0.7-1.0</td>
                <td>-</td>
            </tr>
            <tr>
                <td>B⁺</td>
                <td>损伤饱和趋势</td>
                <td>0.13-0.20</td>
                <td>-</td>
            </tr>
            <tr>
                <td>μ⁺</td>
                <td>粘性系数</td>
                <td>2.1×10³</td>
                <td>N/(s·m)</td>
            </tr>
            <tr>
                <td>a⁺</td>
                <td>非线性指数</td>
                <td>5.5</td>
                <td>-</td>
            </tr>
        </table>

        <h2>3. 实验数据可视化分析</h2>
        <div class="chart-container">
            <img src="tensile_analysis.png" alt="拉伸损伤分析图表">
        </div>

        <h2>4. 数据下载</h2>
        <div class="download-section">
            <h3>📊 实验数据文件</h3>
            <p>包含完整的应力-应变数据、损伤变量、模型参数等信息</p>
            <a href="tensile_experimental_data.xlsx" class="download-btn">📥 下载Excel文件</a>
            <a href="tensile_experimental_data.csv" class="download-btn">📄 下载CSV文件</a>
        </div>

        <h2>5. 数据说明</h2>
        <ul>
            <li><strong>试验类型：</strong>单轴拉伸试验，包含循环加卸载</li>
            <li><strong>数据特点：</strong>基于统一损伤本构模型生成，含2%随机噪声模拟实测误差</li>
            <li><strong>应用范围：</strong>适用于混凝土拉伸损伤分析、本构模型验证</li>
            <li><strong>参数拟合：</strong>可用于损伤参数A⁺、B⁺的反演识别</li>
        </ul>

        <footer style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #bdc3c7; text-align: center; color: #7f8c8d;">
            <p>报告生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
            <p>基于《混凝土弹塑性损伤本构关系统一模型》理论框架</p>
        </footer>
    </div>
</body>
</html>
    """
    
    # 保存HTML文件
    html_path = os.path.join(output_dir, 'tensile_damage_report.html')
    with open(html_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return html_path

def main():
    """主函数"""
    # 创建输出目录
    output_dir = 'd:/column/F4/tensile_results'
    os.makedirs(output_dir, exist_ok=True)
    
    print("正在生成拉伸实验数据...")
    
    # 生成实验数据
    df = generate_experimental_data()
    
    # 保存Excel文件
    excel_path = os.path.join(output_dir, 'tensile_experimental_data.xlsx')
    with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
        # 主数据表
        df.to_excel(writer, sheet_name='实验数据', index=False)
        
        # 参数汇总表
        param_summary = df.groupby('材料类型')[[
            '弹性模量(MPa)', '抗拉强度(MPa)', 'A+参数', 'B+参数'
        ]].first()
        param_summary.to_excel(writer, sheet_name='材料参数')
        
        # 统计摘要
        stats_summary = df.groupby('材料类型').agg({
            '实测应力(MPa)': ['count', 'max', 'mean', 'std'],
            '损伤变量': ['max', 'mean'],
            '试验编号': 'nunique'
        }).round(4)
        stats_summary.to_excel(writer, sheet_name='统计摘要')
    
    # 保存CSV文件
    csv_path = os.path.join(output_dir, 'tensile_experimental_data.csv')
    df.to_csv(csv_path, index=False, encoding='utf-8-sig')
    
    # 创建HTML报告
    html_path = create_html_report(df, output_dir)
    
    print(f"\n✅ 数据生成完成！")
    print(f"📁 输出目录: {output_dir}")
    print(f"📊 Excel文件: {excel_path}")
    print(f"📄 CSV文件: {csv_path}")
    print(f"🌐 HTML报告: {html_path}")
    print(f"\n📈 生成数据统计:")
    print(f"   - 总数据点: {len(df):,} 个")
    print(f"   - 材料类型: {len(df['材料类型'].unique())} 种")
    print(f"   - 试验组数: {len(df['试验编号'].unique())} 组")
    
    return excel_path, html_path

if __name__ == "__main__":
    excel_path, html_path = main()