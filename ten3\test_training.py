#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from concrete_pinn_integrated import *

# 设置设备
device = setup_device()

# 创建目录
create_directories()

# 配置参数
config = {
    'E0': 30000.0,
    'ft': 3.0,
    'hidden_size': 64,
    'num_layers': 2,
    'learning_rate': 0.001,
    'weight_decay': 1e-5,
    'num_epochs': 10,  # 只训练10个epoch
    'batch_size': 1,
    'loss_weights': {
        'data': 1.0,
        'physics': 0.5,
        'damage': 0.3,
        'plastic': 0.2
    },
    'normalize': True,
    'log_interval': 1  # 每个epoch都打印
}

# 数据文件路径
data_file = r"D:\column\ten3\tension.xlsx"

print("开始测试训练...")

try:
    # 数据处理
    processor = ConcreteDataProcessor(normalize=config['normalize'])
    
    # 加载数据
    strain_data, stress_data = processor.load_data_from_excel(data_file)
    strain_clean, stress_clean = processor.clean_and_smooth_data(strain_data, stress_data)
    
    # 准备训练数据
    train_strain, train_stress = processor.prepare_training_data(strain_clean, stress_clean)
    
    # 创建数据加载器
    train_loader = processor.create_dataloaders(train_strain, train_stress, batch_size=config['batch_size'])
    
    print(f"训练数据: {len(train_strain)} 个点")
    
    # 创建模型
    model = ConcreteTensionPINN(
        E0=config['E0'],
        ft=config['ft'],
        hidden_size=config['hidden_size'],
        num_layers=config['num_layers'],
        device=device
    )
    
    # 创建训练器
    trainer = PINNTrainer(model=model, device=device, learning_rate=config['learning_rate'])
    
    # 测试几个epoch
    trainer.train(
        train_dataloader=train_loader,
        num_epochs=config['num_epochs'],
        loss_weights=config['loss_weights'],
        log_interval=config['log_interval']
    )
    
    print("测试训练完成!")
    print(f"最终参数: A+={model.A_plus.item():.4f}, B+={model.B_plus.item():.4f}, xi={model.xi.item():.4f}")
    
except Exception as e:
    print(f"训练测试失败: {e}")
    import traceback
    traceback.print_exc() 