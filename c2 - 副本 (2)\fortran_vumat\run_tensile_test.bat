@echo off
echo ===================================================
echo 混凝土单轴拉伸测试 - PINN-VUMAT验证
echo ===================================================
echo.

REM 设置Abaqus环境变量（如果需要）
REM call "C:\SIMULIA\Abaqus\Commands\abaqus_v6.env.bat"

REM 清理旧的结果文件
echo 清理旧的结果文件...
if exist tensile_test.odb del tensile_test.odb
if exist tensile_test.dat del tensile_test.dat
if exist tensile_test.com del tensile_test.com
if exist tensile_test.msg del tensile_test.msg
if exist tensile_test.sta del tensile_test.sta
if exist tensile_test.prt del tensile_test.prt
if exist tensile_test.sim del tensile_test.sim
echo 清理完成
echo.

REM 确保PINN模型参数已导出
echo 检查PINN模型参数...
if not exist material_parameters.inc (
    echo 警告: material_parameters.inc 文件不存在
    echo 将使用默认参数
) else (
    echo 已找到PINN模型参数文件
)
echo.

REM 运行分析（VUMAT将在运行时编译）
echo 运行单轴拉伸分析...
abaqus job=tensile_test user=vumat_tensile_test.f double=both interactive
if %ERRORLEVEL% NEQ 0 (
    echo 分析运行失败！
    exit /b 1
)
echo 分析完成
echo.

REM 提取结果
echo 提取分析结果...
abaqus odbreport job=tensile_test history
echo 结果提取完成
echo.

echo ===================================================
echo 单轴拉伸测试完成
echo 结果文件: tensile_test.odb
echo ===================================================