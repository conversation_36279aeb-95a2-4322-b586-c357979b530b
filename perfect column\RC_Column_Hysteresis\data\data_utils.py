# -*- coding: utf-8 -*-
"""
数据处理工具模块

提供从Excel文件中提取钢筋混凝土柱的静态参数和力-位移数据，
并进行标准化处理的功能。
"""

import os
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler


def load_data(excel_path, static_sheet="静态参数", dynamic_sheet="力和位移数据"):
    """
    从Excel文件中加载静态参数和力-位移数据
    
    参数:
        excel_path (str): Excel文件路径
        static_sheet (str): 静态参数表名称
        dynamic_sheet (str): 力-位移数据表名称
        
    返回:
        tuple: (静态参数, 位移数据, 力数据, 循环阶段, 样本ID)
    """
    # 检查文件是否存在
    if not os.path.exists(excel_path):
        raise FileNotFoundError(f"Excel文件不存在: {excel_path}")
    
    try:
        # 读取静态参数表
        static_df = pd.read_excel(excel_path, sheet_name=static_sheet)
        # 读取力-位移数据表
        dynamic_df = pd.read_excel(excel_path, sheet_name=dynamic_sheet)
        
        # 提取静态参数 - 根据参数名称提取对应的数值
        # 创建一个空的DataFrame来存储参数
        params_dict = {}
        
        # 检查静态参数表的格式
        if '参数名称' in static_df.columns and '数值' in static_df.columns:
            # 新格式：参数名称和数值分别在不同列
            for _, row in static_df.iterrows():
                param_name = row['参数名称']
                param_value = row['数值']
                if param_name == 'L/D':
                    params_dict['L/D'] = param_value
                elif param_name == 'P/fcAg':
                    params_dict['P/f_cA_g'] = param_value
                elif param_name == 'ρl':
                    params_dict['rho_l'] = param_value
                elif param_name == 'ρt':
                    params_dict['rho_t'] = param_value
                elif param_name == '混凝土强度':
                    params_dict['混凝土强度'] = param_value
            
            # 将参数字典转换为数组 - 确保只有4个静态参数，与模型期望的输入维度一致
            static_params = np.array([[params_dict.get('L/D', 0), 
                                     params_dict.get('P/f_cA_g', 0), 
                                     params_dict.get('rho_l', 0), 
                                     params_dict.get('rho_t', 0)]])
        else:
            # 旧格式：直接使用参数名称作为列名
            static_params = static_df[["L/D", "P/f_cA_g", "rho_l", "rho_t", "混凝土强度"]].values
        
        # 提取力-位移数据
        # 检查动态数据表的列名
        # 处理可能的列名变体
        displacement_cols = ['Deflection ( mm )', 'Deflection（mm）', 'Deflection(mm)', '位移 x (mm)', 'λ�� x (mm)']
        force_cols = ['Force ( KN )', 'Force（KN）', 'Force(KN)', '力 F (kN)']
        
        # 查找位移列
        displacement_col = None
        for col in displacement_cols:
            if col in dynamic_df.columns:
                displacement_col = col
                break
        
        # 查找力列
        force_col = None
        for col in force_cols:
            if col in dynamic_df.columns:
                force_col = col
                break
        
        if displacement_col is None or force_col is None:
            # 如果找不到匹配的列名，打印所有可用的列名以便调试
            available_cols = list(dynamic_df.columns)
            raise ValueError(f"找不到位移或力列。可用的列名: {available_cols}")
        
        # 提取数据
        displacement = dynamic_df[displacement_col].values
        force = dynamic_df[force_col].values
        
        # 提取循环阶段和样本ID（如果存在）
        cycle_phase = dynamic_df["循环阶段"].values if "循环阶段" in dynamic_df.columns else None
        sample_id = dynamic_df["样本ID"].values if "样本ID" in dynamic_df.columns else None
        
        return static_params, displacement, force, cycle_phase, sample_id
    
    except Exception as e:
        raise Exception(f"加载数据时出错: {str(e)}")


def normalize_data(displacement, force):
    """
    对位移和力数据进行归一化处理
    
    参数:
        displacement (numpy.ndarray): 位移数据
        force (numpy.ndarray): 力数据
        
    返回:
        tuple: (归一化位移, 归一化力, 位移最小值, 位移最大值, 力最小值, 力最大值)
    """
    # 位移归一化到[-1, 1]范围
    x_min, x_max = displacement.min(), displacement.max()
    x_norm = (displacement - x_min) / (x_max - x_min) * 2 - 1
    
    # 力归一化到[-1, 1]范围
    F_min, F_max = force.min(), force.max()
    F_norm = (force - F_min) / (F_max - F_min) * 2 - 1
    
    return x_norm, F_norm, x_min, x_max, F_min, F_max


def standardize_static_params(static_params):
    """
    对静态参数进行标准化处理
    
    参数:
        static_params (numpy.ndarray): 静态参数数组
        
    返回:
        tuple: (标准化后的静态参数, 标准化器)
    """
    scaler = StandardScaler()
    static_params_scaled = scaler.fit_transform(static_params)
    return static_params_scaled, scaler


def split_data(x_norm, F_norm, static_params_scaled, train_ratio=0.8):
    """
    按照滞回循环顺序划分训练集和测试集
    
    参数:
        x_norm (numpy.ndarray): 归一化位移数据
        F_norm (numpy.ndarray): 归一化力数据
        static_params_scaled (numpy.ndarray): 标准化静态参数
        train_ratio (float): 训练集比例
        
    返回:
        tuple: (训练集位移, 训练集力, 测试集位移, 测试集力, 训练集静态参数, 测试集静态参数)
    """
    # 计算分割点
    split_idx = int(len(x_norm) * train_ratio)
    
    # 划分训练集和测试集
    x_train, x_test = x_norm[:split_idx], x_norm[split_idx:]
    F_train, F_test = F_norm[:split_idx], F_norm[split_idx:]
    
    # 处理静态参数
    if static_params_scaled.shape[0] == 1:  # 如果只有一组静态参数
        static_params_train = static_params_scaled
        static_params_test = static_params_scaled
    else:  # 如果每个样本都有对应的静态参数
        static_params_train = static_params_scaled[:split_idx]
        static_params_test = static_params_scaled[split_idx:]
    
    return x_train, F_train, x_test, F_test, static_params_train, static_params_test


class HysteresisDataset(Dataset):
    """
    滞回曲线数据集类
    """
    def __init__(self, x_data, F_data, static_params, requires_grad=False):
        """
        初始化数据集
        
        参数:
            x_data (numpy.ndarray): 位移数据
            F_data (numpy.ndarray): 力数据
            static_params (numpy.ndarray): 静态参数
            requires_grad (bool): 是否需要梯度计算（用于物理损失）
        """
        # 转换为PyTorch张量
        self.x = torch.FloatTensor(x_data)
        self.F = torch.FloatTensor(F_data)
        
        # 处理静态参数
        if static_params.shape[0] == 1:  # 如果只有一组静态参数
            # 复制静态参数到每个样本
            self.static_params = torch.FloatTensor(np.tile(static_params, (len(x_data), 1)))
        else:  # 如果每个样本都有对应的静态参数
            self.static_params = torch.FloatTensor(static_params)
        
        # 设置是否需要梯度计算
        if requires_grad:
            self.x.requires_grad_(True)
    
    def __len__(self):
        return len(self.x)
    
    def __getitem__(self, idx):
        # 构建输入特征：[归一化位移, 静态参数]
        # 确保张量维度正确
        x_tensor = self.x[idx].view(-1)  # 将位移变为标量
        
        # 使用所有静态参数，确保与模型期望的输入维度一致
        # 模型期望输入维度为6：[归一化位移 + 5个静态参数]
        static_tensor = self.static_params[idx]
        
        # 检查静态参数维度，如果不是5维，则进行填充
        if static_tensor.size(0) < 5:
            # 创建一个5维的零张量，然后复制现有参数
            padded_static_tensor = torch.zeros(5)
            padded_static_tensor[:static_tensor.size(0)] = static_tensor
            static_tensor = padded_static_tensor
        
        # 创建输入特征向量：[位移, 5个静态参数]
        input_features = torch.zeros(6)  # 特征维度为6
        input_features[0] = x_tensor  # 第一个元素是位移
        input_features[1:] = static_tensor  # 后面5个元素是静态参数
        
        return {
            "input": input_features,  # 确保维度为6
            "output": self.F[idx]
        }


def create_data_loaders(x_train, F_train, x_test, F_test, static_params_train, static_params_test, 
                       batch_size=32, requires_grad=False):
    """
    创建训练集和测试集的数据加载器
    
    参数:
        x_train, F_train: 训练集位移和力数据
        x_test, F_test: 测试集位移和力数据
        static_params_train, static_params_test: 训练集和测试集静态参数
        batch_size (int): 批次大小
        requires_grad (bool): 是否需要梯度计算
        
    返回:
        tuple: (训练集数据加载器, 测试集数据加载器)
    """
    # 创建数据集
    train_dataset = HysteresisDataset(x_train, F_train, static_params_train, requires_grad)
    test_dataset = HysteresisDataset(x_test, F_test, static_params_test, requires_grad)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=False)  # 保持物理连续性
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, test_loader


def prepare_data(excel_path, batch_size=32, train_ratio=0.8, requires_grad=False):
    """
    完整的数据准备流程
    
    参数:
        excel_path (str): Excel文件路径
        batch_size (int): 批次大小
        train_ratio (float): 训练集比例
        requires_grad (bool): 是否需要梯度计算
        
    返回:
        dict: 包含所有数据处理结果的字典
    """
    # 加载数据
    static_params, displacement, force, cycle_phase, sample_id = load_data(excel_path)
    
    # 归一化数据
    x_norm, F_norm, x_min, x_max, F_min, F_max = normalize_data(displacement, force)
    
    # 标准化静态参数
    static_params_scaled, scaler = standardize_static_params(static_params)
    
    # 划分数据集
    x_train, F_train, x_test, F_test, static_params_train, static_params_test = \
        split_data(x_norm, F_norm, static_params_scaled, train_ratio)
    
    # 创建数据加载器
    train_loader, test_loader = create_data_loaders(
        x_train, F_train, x_test, F_test, 
        static_params_train, static_params_test,
        batch_size, requires_grad
    )
    
    # 返回所有处理结果
    return {
        "train_loader": train_loader,
        "test_loader": test_loader,
        "x_min": x_min,
        "x_max": x_max,
        "F_min": F_min,
        "F_max": F_max,
        "scaler": scaler,
        "x_norm": x_norm,
        "F_norm": F_norm,
        "static_params_scaled": static_params_scaled,
        "displacement": displacement,
        "force": force
    }