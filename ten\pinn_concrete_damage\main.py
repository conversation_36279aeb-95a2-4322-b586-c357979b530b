import os
import argparse
import torch
import numpy as np
import matplotlib.pyplot as plt

# 导入自定义模块
from utils.data_processor import TensileDataProcessor
from models.pinn_model import ConcreteDamagePINN
from train import train_fixed_model, test_fixed_model
from predict import load_trained_model, predict_with_model, evaluate_predictions, plot_prediction_results
from utils.visualization import plot_comprehensive_results


def main():
    """
    主函数：实现PINN模型的训练和预测流程
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='混凝土拉伸损伤PINN模型')
    parser.add_argument('--mode', type=str, default='train', choices=['train', 'predict', 'both'],
                        help='运行模式: train, predict, both')
    parser.add_argument('--data_path', type=str, default='../tension.xlsx',
                        help='数据文件路径')
    parser.add_argument('--model_dir', type=str, default='./saved_models',
                        help='模型保存目录')
    parser.add_argument('--results_dir', type=str, default='./results',
                        help='结果保存目录')
    parser.add_argument('--epochs', type=int, default=1000,
                        help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='批次大小')
    parser.add_argument('--sequence_length', type=int, default=5,
                        help='序列长度')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                        help='学习率')
    parser.add_argument('--hidden_dim', type=int, default=64,
                        help='LSTM隐藏层维度')
    parser.add_argument('--lstm_layers', type=int, default=2,
                        help='LSTM层数')
    parser.add_argument('--fc_layers', type=int, default=2,
                        help='全连接层数')
    parser.add_argument('--use_dynamic_loss', action='store_true',
                        help='是否使用动态加权损失')
    parser.add_argument('--early_stopping_patience', type=int, default=50,
                        help='早停耐心值')
    parser.add_argument('--perform_sensitivity', action='store_true',
                        help='是否进行敏感性分析')
    
    args = parser.parse_args()
    
    # 创建保存目录 - 转换为绝对路径避免Windows权限问题
    model_dir = os.path.abspath(args.model_dir)
    results_dir = os.path.abspath(args.results_dir)
    os.makedirs(model_dir, exist_ok=True)
    os.makedirs(results_dir, exist_ok=True)
    
    # 更新args中的路径
    args.model_dir = model_dir
    args.results_dir = results_dir
    
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 加载和预处理数据
    data_processor = TensileDataProcessor(args.data_path)
    strain_data, stress_data = data_processor.preprocess_data(normalize=True)
    
    if strain_data is None or stress_data is None:
        print("数据加载失败，请检查数据文件路径。")
        return
    
    # 训练模式
    if args.mode in ['train', 'both']:
        print("\n=== 开始训练PINN模型 ===")
        model, data_processor = train_fixed_model()
        print("=== 模型训练完成 ===")
    
    # 预测模式
    if args.mode in ['predict', 'both']:
        print("\n=== 开始预测和评估 ===")
        # 加载最佳模型
        model_path = os.path.join(args.model_dir, 'best_model.pth')
        if not os.path.exists(model_path):
            print(f"模型文件不存在: {model_path}")
            if args.mode == 'predict':
                return
        else:
            # 如果在训练模式中已经有了模型和数据处理器，直接使用
            if args.mode == 'both' and 'model' in locals() and 'data_processor' in locals():
                predictions, targets, damage_values, plastic_strain_values = test_fixed_model(model, data_processor)
            else:
                # 重新加载模型进行预测
                print("重新加载模型进行预测...")
                model, data_processor = train_fixed_model()
                predictions, targets, damage_values, plastic_strain_values = test_fixed_model(model, data_processor)
            
            # 敏感性分析功能暂时不可用（需要重新实现）
            if args.perform_sensitivity:
                print("敏感性分析功能暂时不可用，请使用训练和预测功能。")
            
            print("=== 预测和评估完成 ===")
    
    print("\n程序执行完成！")


if __name__ == '__main__':
    main()