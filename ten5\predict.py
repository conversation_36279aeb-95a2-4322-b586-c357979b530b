"""
预测脚本
使用训练好的模型和实验数据的应变量进行预测
严格按照框架要求输出对比图像
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import json
import glob
import os

from pinn_model_v2 import DamagePINNV2, PhysicsCalculatorV2, LossCalculator
from data_processor import DataProcessor
import font_config

class Predictor:
    """
    PINN预测器
    使用训练好的模型进行预测和结果分析
    """
    
    def __init__(self):
        self.model = None
        self.physics_calc = None
        self.model_info = None
        self.training_data = None
        self.results_dir = None  # 结果保存目录
        
    def load_trained_model(self, model_path=None):
        """
        加载训练好的模型
        """
        if model_path is None:
            # 自动寻找最新的训练文件夹
            training_dirs = glob.glob('results/training_*')
            if not training_dirs:
                raise FileNotFoundError("未找到训练结果文件夹")
            
            # 找到最新的训练文件夹
            latest_dir = max(training_dirs, key=os.path.getctime)
            self.results_dir = latest_dir
            
            # 在该文件夹中寻找模型文件
            model_files = glob.glob(f'{latest_dir}/pinn_model_*.pth')
            if not model_files:
                raise FileNotFoundError(f"在 {latest_dir} 中未找到模型文件")
            model_path = model_files[0]  # 每个文件夹只有一个模型文件
        else:
            # 如果指定了模型路径，从中提取results_dir
            self.results_dir = os.path.dirname(model_path)
        
        print(f"加载模型: {model_path}")
        print(f"预测结果将保存到: {self.results_dir}")
        
        # 加载模型信息
        self.model_info = torch.load(model_path, map_location='cpu')
        
        # 重建模型
        config = self.model_info['config']
        self.model = DamagePINNV2(
            input_size=1,
            hidden_size=config['hidden_size'],
            num_layers=config['num_layers'],
            output_size=3
        )
        
        # 加载模型权重
        self.model.load_state_dict(self.model_info['model_state_dict'])
        self.model.eval()
        
        # 重建物理计算器
        material_constants = self.model_info['material_constants']
        self.physics_calc = PhysicsCalculatorV2(
            E0=material_constants['E0'],
            f_t=material_constants['f_t']
        )
        
        print("模型加载成功!")
        print(f"识别的物理参数:")
        physics_params = self.model_info['physics_parameters']
        print(f"  A+ = {physics_params['A_plus']:.4f}")
        print(f"  B+ = {physics_params['B_plus']:.4f}")
        print(f"  xi = {physics_params['xi']:.4f}")
        print(f"材料常量:")
        print(f"  E0 = {material_constants['E0']:.2f} MPa")
        print(f"  f_t = {material_constants['f_t']:.2f} MPa")
        
        return True
    
    def load_experimental_data(self, excel_path="tension.xlsx"):
        """
        加载实验数据
        """
        processor = DataProcessor(excel_path)
        
        if not processor.load_experimental_data():
            raise ValueError("实验数据加载失败")
        
        self.training_data = processor.prepare_training_data()
        
        print(f"实验数据加载完成，数据点数: {self.training_data['sequence_length']}")
        return processor
    
    def predict_with_experimental_strain(self):
        """
        使用实验数据的应变量进行预测
        严格按照框架要求的增量式输入
        """
        if self.model is None or self.training_data is None:
            raise ValueError("请先加载模型和实验数据")
        
        print("开始预测...")
        
        with torch.no_grad():
            # 获取实验数据
            strain_increment_input = self.training_data['strain_increment_input']
            strain_total_exp = self.training_data['strain_total_exp']
            stress_exp = self.training_data['stress_exp']
            strain_increment = self.training_data['strain_increment']
            
            # 神经网络预测
            sigma_pred, d_pred, ep_pred = self.model(strain_increment_input)
            sigma_pred = sigma_pred.squeeze().numpy()
            d_pred = d_pred.squeeze().numpy()
            ep_pred = ep_pred.squeeze().numpy()
            
            # 物理约束计算（用于验证）
            physics_params = self.model_info['physics_parameters']
            A_plus = torch.tensor(physics_params['A_plus'])
            B_plus = torch.tensor(physics_params['B_plus'])
            xi = torch.tensor(physics_params['xi'])
            
            d_phy, ep_phy = self.physics_calc.calculate_physics_constraints(
                strain_increment, A_plus, B_plus, xi
            )
            d_phy = d_phy.numpy()
            ep_phy = ep_phy.numpy()
            
            # 根据本构关系计算理论应力
            material_constants = self.model_info['material_constants']
            E0 = material_constants['E0']
            sigma_theory = (1 - d_pred) * E0 * (strain_total_exp.numpy() - ep_pred)
            
            # 整理预测结果
            prediction_results = {
                'strain_exp': strain_total_exp.numpy(),
                'stress_exp': stress_exp.numpy(),
                'stress_pred': sigma_pred,
                'stress_theory': sigma_theory,
                'damage_pred': d_pred,
                'damage_phy': d_phy,
                'plastic_strain_pred': ep_pred,
                'plastic_strain_phy': ep_phy,
                'physics_parameters': physics_params,
                'material_constants': material_constants
            }
        
        print("预测完成!")
        return prediction_results
    
    def plot_comprehensive_results(self, results, save_prefix="prediction"):
        """
        绘制综合预测结果
        按照框架要求输出三类图像：应力应变对比、损伤变量更新、塑性应变更新
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建综合图像 - 包含框架要求的所有内容
        fig = plt.figure(figsize=(16, 12))
        
        # 1. 应力应变曲线对比（左上）
        ax1 = plt.subplot(2, 3, (1, 2))
        ax1.plot(results['strain_exp'], results['stress_exp'], 'b-', 
                linewidth=3, label='实验数据', alpha=0.8)
        ax1.plot(results['strain_exp'], results['stress_pred'], 'r--', 
                linewidth=2, label='神经网络预测', alpha=0.9)
        ax1.plot(results['strain_exp'], results['stress_theory'], 'g:', 
                linewidth=2, label='本构关系计算', alpha=0.7)
        ax1.set_xlabel('应变')
        ax1.set_ylabel('应力 (MPa)')
        ax1.set_title('应力-应变曲线对比', fontsize=14, fontweight='bold')
        ax1.legend(loc='best')
        ax1.grid(True, alpha=0.3)
        
        # 2. 损伤变量随应变的更新（右上）
        ax2 = plt.subplot(2, 3, 3)
        ax2.plot(results['strain_exp'], results['damage_pred'], 'r-', 
                linewidth=2, label='神经网络预测', alpha=0.9)
        ax2.plot(results['strain_exp'], results['damage_phy'], 'g--', 
                linewidth=2, label='物理计算', alpha=0.7)
        ax2.set_xlabel('应变')
        ax2.set_ylabel('损伤变量')
        ax2.set_title('损伤变量演化', fontsize=14, fontweight='bold')
        ax2.legend(loc='best')
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, 1)
        
        # 3. 塑性应变随应变的更新（左下）
        ax3 = plt.subplot(2, 3, 4)
        ax3.plot(results['strain_exp'], results['plastic_strain_pred'], 'r-', 
                linewidth=2, label='神经网络预测', alpha=0.9)
        ax3.plot(results['strain_exp'], results['plastic_strain_phy'], 'g--', 
                linewidth=2, label='物理计算', alpha=0.7)
        ax3.set_xlabel('应变')
        ax3.set_ylabel('塑性应变')
        ax3.set_title('塑性应变演化', fontsize=14, fontweight='bold')
        ax3.legend(loc='best')
        ax3.grid(True, alpha=0.3)
        
        # 4. 预测误差分析（中下）
        ax4 = plt.subplot(2, 3, 5)
        stress_error = np.abs(results['stress_pred'] - results['stress_exp'])
        ax4.plot(results['strain_exp'], stress_error, 'k-', linewidth=2)
        ax4.set_xlabel('应变')
        ax4.set_ylabel('应力预测误差 (MPa)')
        ax4.set_title('应力预测误差', fontsize=14, fontweight='bold')
        ax4.grid(True, alpha=0.3)
        
        # 5. 参数信息（右下）
        ax5 = plt.subplot(2, 3, 6)
        ax5.axis('off')
        
        # 显示识别的参数
        params_text = f"""识别的物理参数:
A+ = {results['physics_parameters']['A_plus']:.4f}
B+ = {results['physics_parameters']['B_plus']:.4f}
xi = {results['physics_parameters']['xi']:.4f}

材料常量:
E0 = {results['material_constants']['E0']:.1f} MPa
f_t = {results['material_constants']['f_t']:.2f} MPa

 预测精度指标:
 应力RMSE = {np.sqrt(np.mean((results['stress_pred'] - results['stress_exp'])**2)):.3f} MPa
 应力$R^2$ = {self.calculate_r_squared(results['stress_exp'], results['stress_pred']):.4f}
 最大应力误差 = {np.max(np.abs(results['stress_pred'] - results['stress_exp'])):.3f} MPa"""
        
        ax5.text(0.05, 0.95, params_text, transform=ax5.transAxes, 
                fontsize=11, verticalalignment='top', 
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.5))
        
        plt.suptitle('混凝土损伤参数识别PINN预测结果', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # 保存综合结果图
        comprehensive_path = f'{self.results_dir}/{save_prefix}_comprehensive_{timestamp}.png'
        plt.savefig(comprehensive_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"综合预测结果已保存至: {comprehensive_path}")
        
        # 单独绘制应力应变对比图（高质量版本）
        self.plot_stress_strain_comparison(results, timestamp)
        
        # 单独绘制损伤和塑性应变演化图
        self.plot_damage_plastic_evolution(results, timestamp)
        
    def plot_stress_strain_comparison(self, results, timestamp):
        """
        单独绘制高质量的应力应变对比图
        """
        plt.figure(figsize=(10, 8))
        
        plt.plot(results['strain_exp'], results['stress_exp'], 'b-', 
                linewidth=3, label='实验数据', alpha=0.8, marker='o', markersize=3, markevery=10)
        plt.plot(results['strain_exp'], results['stress_pred'], 'r--', 
                linewidth=2.5, label='PINN预测', alpha=0.9)
        plt.plot(results['strain_exp'], results['stress_theory'], 'g:', 
                linewidth=2, label='本构关系', alpha=0.7)
        
        plt.xlabel('应变', fontsize=14)
        plt.ylabel('应力 (MPa)', fontsize=14)
        plt.title('应力-应变曲线对比', fontsize=16, fontweight='bold')
        plt.legend(fontsize=12, loc='best')
        plt.grid(True, alpha=0.3)
        
        # 添加统计信息
        rmse = np.sqrt(np.mean((results['stress_pred'] - results['stress_exp'])**2))
        r2 = self.calculate_r_squared(results['stress_exp'], results['stress_pred'])
        plt.text(0.05, 0.95, f'RMSE = {rmse:.3f} MPa\n$R^2$ = {r2:.4f}', 
                transform=plt.gca().transAxes, fontsize=12,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
        
        plt.tight_layout()
        save_path = f'{self.results_dir}/stress_strain_comparison_{timestamp}.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"应力应变对比图已保存至: {save_path}")
    
    def plot_damage_plastic_evolution(self, results, timestamp):
        """
        绘制损伤和塑性应变演化图
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 损伤演化
        ax1.plot(results['strain_exp'], results['damage_pred'], 'r-', 
                linewidth=2.5, label='神经网络预测', alpha=0.9)
        ax1.plot(results['strain_exp'], results['damage_phy'], 'g--', 
                linewidth=2, label='物理计算', alpha=0.7)
        ax1.set_xlabel('应变', fontsize=12)
        ax1.set_ylabel('损伤变量', fontsize=12)
        ax1.set_title('损伤变量演化', fontsize=14, fontweight='bold')
        ax1.legend(fontsize=11)
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 1)
        
        # 塑性应变演化
        ax2.plot(results['strain_exp'], results['plastic_strain_pred'], 'r-', 
                linewidth=2.5, label='神经网络预测', alpha=0.9)
        ax2.plot(results['strain_exp'], results['plastic_strain_phy'], 'g--', 
                linewidth=2, label='物理计算', alpha=0.7)
        ax2.set_xlabel('应变', fontsize=12)
        ax2.set_ylabel('塑性应变', fontsize=12)
        ax2.set_title('塑性应变演化', fontsize=14, fontweight='bold')
        ax2.legend(fontsize=11)
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        save_path = f'{self.results_dir}/damage_plastic_evolution_{timestamp}.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"损伤塑性演化图已保存至: {save_path}")
    
    def calculate_r_squared(self, y_true, y_pred):
        """
        计算R²决定系数
        """
        ss_res = np.sum((y_true - y_pred) ** 2)
        ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
        return 1 - (ss_res / ss_tot)
    
    def save_prediction_results(self, results, timestamp):
        """
        保存预测结果到文件
        """
        # 保存数值结果
        results_data = {
            'strain_exp': results['strain_exp'].tolist(),
            'stress_exp': results['stress_exp'].tolist(),
            'stress_pred': results['stress_pred'].tolist(),
            'damage_pred': results['damage_pred'].tolist(),
            'plastic_strain_pred': results['plastic_strain_pred'].tolist(),
            'physics_parameters': results['physics_parameters'],
            'material_constants': results['material_constants'],
            'accuracy_metrics': {
                'stress_rmse': float(np.sqrt(np.mean((results['stress_pred'] - results['stress_exp'])**2))),
                'stress_r2': float(self.calculate_r_squared(results['stress_exp'], results['stress_pred'])),
                'max_stress_error': float(np.max(np.abs(results['stress_pred'] - results['stress_exp'])))
            },
            'timestamp': timestamp
        }
        
        results_path = f'{self.results_dir}/prediction_results_{timestamp}.json'
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, indent=2, ensure_ascii=False)
        
        print(f"预测结果已保存至: {results_path}")
    
    def run_prediction(self, model_path=None, excel_path="tension.xlsx"):
        """
        运行完整的预测流程
        """
        print("开始PINN预测流程")
        print("=" * 60)
        
        # 加载模型
        self.load_trained_model(model_path)
        
        # 加载实验数据
        self.load_experimental_data(excel_path)
        
        # 进行预测
        results = self.predict_with_experimental_strain()
        
        # 绘制结果图像
        self.plot_comprehensive_results(results)
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.save_prediction_results(results, timestamp)
        
        print("=" * 60)
        print("预测流程完成!")
        
        # 打印精度总结
        rmse = np.sqrt(np.mean((results['stress_pred'] - results['stress_exp'])**2))
        r2 = self.calculate_r_squared(results['stress_exp'], results['stress_pred'])
        max_error = np.max(np.abs(results['stress_pred'] - results['stress_exp']))
        
        print(f"\n预测精度总结:")
        print(f"  应力RMSE: {rmse:.3f} MPa")
        print(f"  应力R²: {r2:.4f}")
        print(f"  最大应力误差: {max_error:.3f} MPa")
        
        return results


def main():
    """
    主预测函数
    """
    print("混凝土损伤参数识别PINN - 预测模块")
    
    predictor = Predictor()
    results = predictor.run_prediction()
    
    return results


if __name__ == "__main__":
    main() 