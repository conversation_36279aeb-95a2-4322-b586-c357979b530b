import traceback
import sys

try:
    import os
    import torch
    import numpy as np
    import matplotlib.pyplot as plt
    import pandas as pd
    import argparse
    import time
    from column_hysteresis_model import (
        load_excel_data, 
        preprocess_data, 
        ColumnHysteresisPINN, 
        plot_hysteresis_curve,
        physics_informed_loss
    )
    
    # 执行主程序，使用UTF-8编码打开文件
    exec(open('d:/column/advanced_column_training.py', encoding='utf-8').read())
    
except Exception as e:
    print('\n\n错误详情:')
    print('=' * 50)
    print(f'错误类型: {type(e).__name__}')
    print(f'错误信息: {str(e)}')
    print('\n完整堆栈跟踪:')
    traceback.print_exc(file=sys.stdout)
    print('=' * 50)