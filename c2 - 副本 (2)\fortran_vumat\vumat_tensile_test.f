C=====================================================================
C     VUMAT for Concrete Tensile Test
C     Based on PINN-identified parameters
C     Simplified version for single-element tensile test
C=====================================================================
      SUBROUTINE VUMAT(
C     Read only variables -
     1  NBLOCK, NDIR, NSHR, NSTATEV, NFIELDV, NPROPS, LANNEAL,
     2  STEPTIME, TOTALTIME, DT, CMNAME, COORDMP, CHARLENGTH,
     3  PROPS, DENSITY, STRAININC, RELSPININC,
     4  TEMPOLD, STRETCHOLD, DEFGRADOLD, FIELDOLD,
     5  STRESSOLD, STATEOLD, ENERINTERNOLD, ENERINELASOLD,
     6  TEMPNEW, STRETCHNEW, DEFGRADNEW, FIELDNEW,
C     Write only variables -
     7  STRESSNEW, STATENEW, ENERINTERNNEW, ENERINELASNEW )
C
      INCLUDE 'VABA_PARAM.INC'
C
      DIMENSION PROPS(NPROPS), DENSITY(NBLOCK), COORDMP(NBLOCK,*),
     1  CHARLENGTH(NBLOCK), STRAININC(NBLOCK,NDIR+NSHR),
     2  RELSPININC(NBLOCK,NSHR), TEMPOLD(NBLOCK),
     3  STRETCHOLD(NBLOCK,NDIR+NSHR),
     4  DEFGRADOLD(NBLOCK,NDIR+NSHR+NSHR),
     5  FIELDOLD(NBLOCK,NFIELDV), STRESSOLD(NBLOCK,NDIR+NSHR),
     6  STATEOLD(NBLOCK,NSTATEV), ENERINTERNOLD(NBLOCK),
     7  ENERINELASOLD(NBLOCK), TEMPNEW(NBLOCK),
     8  STRETCHNEW(NBLOCK,NDIR+NSHR),
     9  DEFGRADNEW(NBLOCK,NDIR+NSHR+NSHR),
     1  FIELDNEW(NBLOCK,NFIELDV), STRESSNEW(NBLOCK,NDIR+NSHR),
     2  STATENEW(NBLOCK,NSTATEV), ENERINTERNNEW(NBLOCK),
     3  ENERINELASNEW(NBLOCK)
C
      CHARACTER*80 CMNAME
C
C     Local variables and parameters
      PARAMETER (ZERO=0.D0, ONE=1.D0, TWO=2.D0, THREE=3.D0)
      PARAMETER (HALF=0.5D0, THIRD=1.D0/3.D0)
      PARAMETER (D_MAX=0.99D0)
      PARAMETER (TOL=1.D-10)
C
C     Declare all real variables explicitly
      REAL*8 E0, FT, FC, A_PLUS, B_PLUS, XI_PLUS
      REAL*8 A_MINUS, B_MINUS, XI_MINUS
      REAL*8 EP_OLD, D_PLUS_OLD, D_MINUS_OLD
      REAL*8 R_MAX_PLUS_OLD, R_MAX_MINUS_OLD
      REAL*8 D_PLUS_NEW, D_MINUS_NEW, EP_NEW
      REAL*8 R_MAX_PLUS_NEW, R_MAX_MINUS_NEW
      REAL*8 STRAIN_INC, STRAIN_TOTAL, DELTA_EP
      REAL*8 STRAIN_ELASTIC, Y_PLUS, Y_MINUS
      REAL*8 RATIO, EXP_TERM, TEMP_CALC
      REAL*8 D_EFFECTIVE, STRESS_TRIAL
C
C     Include PINN-identified parameters if available
C     Uncomment the following line when using PINN parameters
C     INCLUDE 'material_parameters.inc'
C
C     Material parameters from PROPS
C     PROPS(1) = E0 (Initial elastic modulus)
C     PROPS(2) = f_t (Tensile strength)
C     PROPS(3) = f_c (Compressive strength)
C     PROPS(4) = A_plus (Tensile damage parameter A)
C     PROPS(5) = B_plus (Tensile damage parameter B)
C     PROPS(6) = xi_plus (Tensile plastic parameter)
C     PROPS(7) = A_minus (Compressive damage parameter A)
C     PROPS(8) = B_minus (Compressive damage parameter B)
C     PROPS(9) = xi_minus (Compressive plastic parameter)
C
C     State variables
C     STATEV(1) = Accumulated plastic strain
C     STATEV(2) = d_plus (Tensile damage)
C     STATEV(3) = d_minus (Compressive damage)
C     STATEV(4) = r_max_plus (Maximum tensile damage force)
C     STATEV(5) = r_max_minus (Maximum compressive damage force)
C
C     Declare integer variables explicitly to override implicit
      INTEGER K, I
C
C     Extract material parameters
      IF (NPROPS .GE. 9) THEN
        E0 = PROPS(1)
        FT = PROPS(2)
        FC = PROPS(3)
        A_PLUS = PROPS(4)
        B_PLUS = PROPS(5)
        XI_PLUS = PROPS(6)
        A_MINUS = PROPS(7)
        B_MINUS = PROPS(8)
        XI_MINUS = PROPS(9)
      ELSE
C       Use default values if PROPS not provided
        E0 = 10000.0D0
        FT = 3.67043D0
        FC = 10.0D0
        A_PLUS = 0.84463D0
        B_PLUS = 1.81372D0
        XI_PLUS = 0.5D0
        A_MINUS = 2.0D0
        B_MINUS = 1.32925D0
        XI_MINUS = 0.50028D0
      ENDIF
C
C     Loop over all material points in the block
      DO 100 K = 1, NBLOCK
C
C       Get old state variables
        EP_OLD = STATEOLD(K,1)
        D_PLUS_OLD = STATEOLD(K,2)
        D_MINUS_OLD = STATEOLD(K,3)
        R_MAX_PLUS_OLD = STATEOLD(K,4)
        R_MAX_MINUS_OLD = STATEOLD(K,5)
C
C       Initialize new state variables with old values
        D_PLUS_NEW = D_PLUS_OLD
        D_MINUS_NEW = D_MINUS_OLD
        EP_NEW = EP_OLD
        R_MAX_PLUS_NEW = R_MAX_PLUS_OLD
        R_MAX_MINUS_NEW = R_MAX_MINUS_OLD
C
C       For single-axis tensile test, focus on the axial component (Z-direction)
        STRAIN_INC = STRAININC(K,3)
        STRAIN_TOTAL = STRETCHNEW(K,3) - ONE
C
C       Update plastic strain based on strain increment direction
        IF (STRAIN_INC .GT. ZERO) THEN
C         Tensile increment
          DELTA_EP = XI_PLUS * STRAIN_INC
        ELSE
C         Compressive increment
          DELTA_EP = -XI_MINUS * ABS(STRAIN_INC)
        ENDIF
        EP_NEW = EP_OLD + DELTA_EP
C
C       Calculate effective elastic strain
        STRAIN_ELASTIC = STRAIN_TOTAL - EP_NEW
C
C       Calculate damage driving forces
        IF (STRAIN_ELASTIC .GE. ZERO) THEN
C         Tensile state
          Y_PLUS = E0 * STRAIN_ELASTIC
          Y_MINUS = ZERO
        ELSE
C         Compressive state
          Y_PLUS = ZERO
          Y_MINUS = E0 * ABS(STRAIN_ELASTIC)
        ENDIF
C
C       Update tensile damage
        IF (Y_PLUS .GT. R_MAX_PLUS_OLD) THEN
          R_MAX_PLUS_NEW = Y_PLUS
          IF (R_MAX_PLUS_NEW .GT. FT) THEN
            RATIO = R_MAX_PLUS_NEW / FT
            EXP_TERM = EXP(B_PLUS * (ONE - RATIO))
            TEMP_CALC = (ONE - A_PLUS) + A_PLUS * EXP_TERM
            D_PLUS_NEW = ONE - (ONE/RATIO) * TEMP_CALC
            D_PLUS_NEW = MIN(D_PLUS_NEW, D_MAX)
            D_PLUS_NEW = MAX(D_PLUS_NEW, ZERO)
          ENDIF
        ENDIF
C
C       Update compressive damage
        IF (Y_MINUS .GT. R_MAX_MINUS_OLD) THEN
          R_MAX_MINUS_NEW = Y_MINUS
          IF (R_MAX_MINUS_NEW .GT. FC) THEN
            RATIO = R_MAX_MINUS_NEW / FC
            EXP_TERM = EXP(B_MINUS * (ONE - RATIO))
            TEMP_CALC = (ONE - A_MINUS) + A_MINUS * EXP_TERM
            D_MINUS_NEW = ONE - (ONE/RATIO) * TEMP_CALC
            D_MINUS_NEW = MIN(D_MINUS_NEW, D_MAX)
            D_MINUS_NEW = MAX(D_MINUS_NEW, ZERO)
          ENDIF
        ENDIF
C
C       Calculate stress based on damage state
        IF (STRAIN_ELASTIC .GE. ZERO) THEN
C         Tensile stress
          D_EFFECTIVE = D_PLUS_NEW
        ELSE
C         Compressive stress
          D_EFFECTIVE = D_MINUS_NEW
        ENDIF
C
        STRESS_TRIAL = (ONE - D_EFFECTIVE) * E0 * STRAIN_ELASTIC
C
C       Update stress (for uniaxial test, focus on axial component)
        STRESSNEW(K,1) = ZERO
        STRESSNEW(K,2) = ZERO
        STRESSNEW(K,3) = STRESS_TRIAL
C       Zero out shear stress components
        DO 50 I = 4, NDIR+NSHR
          STRESSNEW(K,I) = ZERO
   50   CONTINUE
C
C       Update state variables
        STATENEW(K,1) = EP_NEW
        STATENEW(K,2) = D_PLUS_NEW
        STATENEW(K,3) = D_MINUS_NEW
        STATENEW(K,4) = R_MAX_PLUS_NEW
        STATENEW(K,5) = R_MAX_MINUS_NEW
C
C       Update internal energy (simplified)
        ENERINTERNNEW(K) = ENERINTERNOLD(K) +
     &    HALF * (STRESSOLD(K,3) + STRESSNEW(K,3)) * STRAIN_INC
C
  100 CONTINUE
C
      RETURN
      END 