#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Abaqus环境中的损伤数据提取脚本
需要在Abaqus Python环境中运行: abaqus python extract_damage_data.py
Author: AI Assistant
Date: 2025-07-08
"""

from abaqus import *
from abaqusConstants import *
import odbAccess
import numpy as np
import csv
import os
import sys

def extract_damage_data_from_odb(odb_path, output_dir):
    """从ODB文件提取详细的损伤变量数据"""
    
    print(f"正在读取ODB文件: {odb_path}")
    
    try:
        # 打开ODB文件
        odb = odbAccess.openOdb(odb_path)
        
        # 获取步骤
        step_name = 'ApplyDisplacement'
        if step_name not in odb.steps:
            print(f"错误: 未找到步骤 '{step_name}'")
            odb.close()
            return None
        
        step = odb.steps[step_name]
        frames = step.frames
        n_frames = len(frames)
        
        print(f"找到 {n_frames} 个分析帧")
        
        # 初始化数据列表
        results_data = []
        
        # 常量定义
        LENGTH = 100.0  # mm，柱子高度
        AREA = 100.0    # mm^2，截面积
        
        # 遍历每一帧
        for i, frame in enumerate(frames):
            frame_data = {}
            frame_data['Frame'] = i
            frame_data['Time'] = frame.frameValue
            
            try:
                # 1. 获取位移数据计算应变
                if 'U' in frame.fieldOutputs:
                    u_field = frame.fieldOutputs['U']
                    # 获取顶部节点的位移
                    top_displacement = u_field.getSubset(region=odb.rootAssembly.nodeSets['SET_TOP'])
                    u3_values = [v.data[2] for v in top_displacement.values]
                    u3_avg = sum(u3_values) / len(u3_values)
                    strain = u3_avg / LENGTH
                    frame_data['Displacement'] = u3_avg
                    frame_data['Strain'] = strain
                else:
                    frame_data['Displacement'] = 0.0
                    frame_data['Strain'] = 0.0
                
                # 2. 获取应力数据
                if 'S' in frame.fieldOutputs:
                    stress_field = frame.fieldOutputs['S']
                    element_stress = stress_field.getSubset(region=odb.rootAssembly.elementSets['COLUMN-1.E_COLUMN'])
                    s33_values = [v.data[2] for v in element_stress.values]  # S33是轴向应力
                    s33_avg = sum(s33_values) / len(s33_values)
                    frame_data['Stress_S33'] = s33_avg
                    
                    # 也提取其他应力分量
                    s11_values = [v.data[0] for v in element_stress.values]
                    s22_values = [v.data[1] for v in element_stress.values]
                    frame_data['Stress_S11'] = sum(s11_values) / len(s11_values)
                    frame_data['Stress_S22'] = sum(s22_values) / len(s22_values)
                else:
                    frame_data['Stress_S33'] = 0.0
                    frame_data['Stress_S11'] = 0.0
                    frame_data['Stress_S22'] = 0.0
                
                # 3. 获取状态变量（损伤变量）
                if 'SDV' in frame.fieldOutputs:
                    sdv_field = frame.fieldOutputs['SDV']
                    element_sdv = sdv_field.getSubset(region=odb.rootAssembly.elementSets['COLUMN-1.E_COLUMN'])
                    
                    # 根据VUMAT定义提取状态变量
                    # SDV1: 受拉损伤 (DAMAGE_PLUS)
                    # SDV2: 受压损伤 (DAMAGE_MINUS)
                    # SDV3: 受拉损伤驱动力历史最大值 (R_MAX_PLUS)
                    # SDV4: 受压损伤驱动力历史最大值 (R_MAX_MINUS)
                    # SDV5: 塑性应变 (EP)
                    
                    # 获取第一个积分点的数据（单元素情况）
                    if len(element_sdv.values) > 0:
                        sdv_values = element_sdv.values[0].data
                        
                        if len(sdv_values) >= 5:
                            frame_data['Damage_Plus'] = sdv_values[0]    # SDV1
                            frame_data['Damage_Minus'] = sdv_values[1]   # SDV2
                            frame_data['R_Max_Plus'] = sdv_values[2]     # SDV3
                            frame_data['R_Max_Minus'] = sdv_values[3]    # SDV4
                            frame_data['Plastic_Strain'] = sdv_values[4] # SDV5
                        else:
                            # 如果状态变量不足，设为0
                            frame_data['Damage_Plus'] = 0.0
                            frame_data['Damage_Minus'] = 0.0
                            frame_data['R_Max_Plus'] = 3.67  # ft
                            frame_data['R_Max_Minus'] = 10.0  # fc
                            frame_data['Plastic_Strain'] = 0.0
                    else:
                        frame_data['Damage_Plus'] = 0.0
                        frame_data['Damage_Minus'] = 0.0
                        frame_data['R_Max_Plus'] = 3.67
                        frame_data['R_Max_Minus'] = 10.0
                        frame_data['Plastic_Strain'] = 0.0
                else:
                    # 没有状态变量输出
                    frame_data['Damage_Plus'] = 0.0
                    frame_data['Damage_Minus'] = 0.0
                    frame_data['R_Max_Plus'] = 3.67
                    frame_data['R_Max_Minus'] = 10.0
                    frame_data['Plastic_Strain'] = 0.0
                
                # 4. 计算有效弹性应变
                elastic_strain = frame_data['Strain'] - frame_data['Plastic_Strain']
                frame_data['Elastic_Strain'] = elastic_strain
                
                # 5. 计算损伤驱动力
                E0 = 10000.0  # MPa
                Y_plus = E0 * max(0.0, elastic_strain)
                Y_minus = E0 * max(0.0, -elastic_strain)
                frame_data['Y_Plus'] = Y_plus
                frame_data['Y_Minus'] = Y_minus
                
                results_data.append(frame_data)
                
            except Exception as e:
                print(f"警告: 处理第{i}帧时出错: {e}")
                continue
        
        odb.close()
        
        # 保存详细数据到CSV
        if results_data:
            csv_path = os.path.join(output_dir, 'detailed_damage_results.csv')
            
            # 获取所有字段名
            fieldnames = results_data[0].keys()
            
            with open(csv_path, 'w', newline='') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(results_data)
            
            print(f"详细损伤数据已保存到: {csv_path}")
            
            # 打印统计信息
            print_damage_statistics(results_data)
            
            return results_data
        else:
            print("错误: 未提取到任何数据")
            return None
            
    except Exception as e:
        print(f"错误: 读取ODB文件失败 - {e}")
        return None

def print_damage_statistics(data):
    """打印损伤统计信息"""
    if not data:
        return
    
    print("\n=== 损伤变量统计信息 ===")
    
    # 提取数据
    strains = [d['Strain'] for d in data]
    stresses = [d['Stress_S33'] for d in data]
    damage_plus = [d['Damage_Plus'] for d in data]
    damage_minus = [d['Damage_Minus'] for d in data]
    plastic_strains = [d['Plastic_Strain'] for d in data]
    
    print(f"数据点数: {len(data)}")
    print(f"应变范围: [{min(strains):.6f}, {max(strains):.6f}]")
    print(f"应力范围: [{min(stresses):.2f}, {max(stresses):.2f}] MPa")
    print(f"最大受拉损伤: {max(damage_plus):.4f}")
    print(f"最大受压损伤: {max(damage_minus):.4f}")
    print(f"最大塑性应变: {max(plastic_strains):.6f}")
    
    # 找到损伤开始的点
    damage_start_idx = next((i for i, d in enumerate(damage_plus) if d > 0.001), None)
    if damage_start_idx:
        print(f"损伤开始应变: {strains[damage_start_idx]:.6f}")
        print(f"损伤开始应力: {stresses[damage_start_idx]:.2f} MPa")

def create_damage_plots(data_file, output_dir):
    """创建损伤分析图表的Python脚本"""
    
    plot_script = '''
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 读取数据
df = pd.read_csv('{data_file}')

# 创建子图
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
fig.suptitle('VUMAT损伤变量详细分析', fontsize=16)

# 1. 应力-应变曲线
ax1 = axes[0, 0]
ax1.plot(df['Strain'], df['Stress_S33'], 'b-', linewidth=2)
ax1.set_xlabel('应变')
ax1.set_ylabel('应力 (MPa)')
ax1.set_title('应力-应变曲线')
ax1.grid(True, alpha=0.3)

# 2. 受拉损伤演化
ax2 = axes[0, 1]
ax2.plot(df['Strain'], df['Damage_Plus'], 'r-', linewidth=2)
ax2.set_xlabel('应变')
ax2.set_ylabel('受拉损伤变量')
ax2.set_title('受拉损伤演化')
ax2.grid(True, alpha=0.3)
ax2.set_ylim(0, 1)

# 3. 塑性应变演化
ax3 = axes[0, 2]
ax3.plot(df['Strain'], df['Plastic_Strain'], 'g-', linewidth=2)
ax3.set_xlabel('应变')
ax3.set_ylabel('塑性应变')
ax3.set_title('塑性应变演化')
ax3.grid(True, alpha=0.3)

# 4. 损伤驱动力演化
ax4 = axes[1, 0]
ax4.plot(df['Strain'], df['Y_Plus'], 'orange', linewidth=2, label='Y+')
ax4.plot(df['Strain'], df['R_Max_Plus'], 'red', linewidth=2, label='R_max+')
ax4.set_xlabel('应变')
ax4.set_ylabel('损伤驱动力 (MPa)')
ax4.set_title('受拉损伤驱动力')
ax4.grid(True, alpha=0.3)
ax4.legend()

# 5. 弹性应变 vs 总应变
ax5 = axes[1, 1]
ax5.plot(df['Strain'], df['Elastic_Strain'], 'purple', linewidth=2, label='弹性应变')
ax5.plot(df['Strain'], df['Strain'], 'k--', linewidth=1, label='总应变')
ax5.set_xlabel('总应变')
ax5.set_ylabel('应变')
ax5.set_title('弹性应变 vs 总应变')
ax5.grid(True, alpha=0.3)
ax5.legend()

# 6. 损伤-应力关系
ax6 = axes[1, 2]
ax6.plot(df['Damage_Plus'], df['Stress_S33'], 'brown', linewidth=2)
ax6.set_xlabel('受拉损伤变量')
ax6.set_ylabel('应力 (MPa)')
ax6.set_title('损伤-应力关系')
ax6.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('{output_dir}/detailed_damage_analysis.png', dpi=300, bbox_inches='tight')
print("详细损伤分析图已保存")
plt.show()
'''.format(data_file=data_file.replace('\\', '/'), output_dir=output_dir.replace('\\', '/'))
    
    plot_script_path = os.path.join(output_dir, 'plot_damage_analysis.py')
    with open(plot_script_path, 'w') as f:
        f.write(plot_script)
    
    print(f"绘图脚本已创建: {plot_script_path}")
    print("运行以下命令生成损伤分析图:")
    print(f"python {plot_script_path}")

def main():
    """主函数"""
    print("=== Abaqus损伤数据提取工具 ===")
    
    # 获取当前目录
    current_dir = os.getcwd()
    print(f"当前工作目录: {current_dir}")
    
    # 查找ODB文件
    odb_files = [f for f in os.listdir('.') if f.endswith('.odb')]
    
    if not odb_files:
        print("错误: 当前目录下未找到ODB文件")
        return
    
    # 使用第一个找到的ODB文件
    odb_path = odb_files[0]
    print(f"使用ODB文件: {odb_path}")
    
    # 创建输出目录
    output_dir = 'damage_analysis_output'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 提取损伤数据
    results = extract_damage_data_from_odb(odb_path, output_dir)
    
    if results:
        # 创建绘图脚本
        csv_path = os.path.join(output_dir, 'detailed_damage_results.csv')
        create_damage_plots(csv_path, output_dir)
        
        print(f"\n✓ 损伤数据提取完成!")
        print(f"结果保存在: {output_dir}")
        
    else:
        print("损伤数据提取失败!")

if __name__ == "__main__":
    main() 