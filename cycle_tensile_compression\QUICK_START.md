# 快速开始指南

## 🚀 快速运行

### 1. 使用演示数据快速体验

```bash
# 创建演示数据并运行完整流程
python run_example.py
# 选择 1 创建演示数据
# 选择 2 运行完整示例
```

### 2. 使用真实数据

```bash
# 将您的数据文件命名为 cyclic_data.xlsx，包含 strain 和 stress 两列
python main.py --data cyclic_data.xlsx --epochs 1500
```

## 📁 结果查看

每次运行后，所有结果都保存在 `results/session_YYYYMMDD_HHMMSS/` 文件夹中：

```
results/session_20231201_143022/
├── training/                    # 训练结果
│   ├── pinn_model_*.pth        # 训练好的模型
│   ├── training_history.png    # 训练历史图
│   └── identified_parameters_*.json  # 识别的参数
├── prediction/                  # 预测结果
│   ├── prediction_results_*.xlsx     # 预测数据
│   └── cyclic_analysis_*.png         # 分析图像
└── session_summary.txt         # 总结报告
```

## 🎯 核心功能

- ✅ **双向损伤识别**: 区分拉伸和压缩损伤参数
- ✅ **自动文件管理**: 每次运行结果独立保存
- ✅ **完整分析**: 滞回曲线、能量耗散、刚度退化
- ✅ **灵活运行**: 支持仅训练或仅预测模式

## 📊 输出分析

### 训练阶段
- 损失函数收敛曲线
- 物理参数演化过程
- 实验数据质量检查

### 预测阶段
- 完整滞回曲线
- 骨架曲线对比
- 损伤演化分析
- 能量耗散统计
- 刚度退化趋势

## ⚙️ 常用命令

```bash
# 完整运行（推荐）
python main.py --data your_data.xlsx

# 仅训练
python main.py --train-only --data your_data.xlsx --epochs 2000

# 仅预测
python main.py --predict-only --model-path results/session_xxx/training/pinn_model_xxx.pth

# 快速测试（100轮训练）
python main.py --data your_data.xlsx --epochs 100

# 调整学习率
python main.py --data your_data.xlsx --lr 0.0005
```

## 💡 使用技巧

1. **数据准备**: 确保数据包含完整的拉压循环
2. **训练监控**: 观察损失函数是否收敛
3. **参数验证**: 检查识别的物理参数是否合理
4. **结果对比**: 对比预测的滞回曲线与实验数据
5. **损伤验证**: 确保应变足够大以触发损伤演化

## 🔬 损伤计算验证

如果发现损伤值为0，可以运行测试脚本验证：

```bash
python test_damage_calculation.py
```

该脚本会：
- 测试不同加载条件下的损伤演化
- 生成损伤演化图像
- 验证参数敏感性
- 确保损伤公式正确实现

## 🔧 故障排除

### 常见问题解决

- **训练不收敛**: 调整学习率或增加训练轮数
- **内存不足**: 减小批次大小或数据长度
- **参数不合理**: 检查数据质量和初始参数设置

### 字体显示问题解决

如果遇到中文字体显示异常（方框、乱码等），请运行字体修复工具：

```bash
python fix_font_display.py
```

选择对应的操作：
1. **诊断字体问题** - 查看系统字体状态
2. **自动修复字体** - 一键修复字体配置
3. **查看安装指南** - 获取字体安装帮助

### 手动修复字体

#### Windows系统
```bash
# 检查系统字体
dir C:\Windows\Fonts\*hei*
dir C:\Windows\Fonts\*ya*

# 如果没有中文字体，下载并安装
# SimHei.ttf (黑体) 或 msyh.ttc (微软雅黑)
```

#### macOS系统
字体通常已预装，如有问题请运行修复工具。

#### Linux系统
```bash
# 安装中文字体
sudo apt-get install fonts-noto-cjk
sudo apt-get install fonts-wqy-zenhei

# 刷新字体缓存
fc-cache -f -v
```

需要更多帮助？查看 `README.md` 获取详细说明。 