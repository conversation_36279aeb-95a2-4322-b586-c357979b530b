# -*- coding: utf-8 -*-
"""
可视化工具模块

提供绘制滞回曲线、训练历史和其他可视化功能的工具函数。
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from matplotlib.ticker import MaxNLocator
import torch

# 导入字体配置
from utils.font_config import configure_chinese_font

# 配置中文字体
configure_chinese_font()


def plot_hysteresis_curve(displacement, force_exp, force_pred=None, title="滞回曲线", 
                         save_path=None, show=True, figsize=(10, 8)):
    """
    绘制滞回曲线
    
    参数:
        displacement (numpy.ndarray): 位移数据
        force_exp (numpy.ndarray): 实验力数据
        force_pred (numpy.ndarray, optional): 预测力数据
        title (str): 图表标题
        save_path (str, optional): 保存路径
        show (bool): 是否显示图表
        figsize (tuple): 图表尺寸
    """
    plt.figure(figsize=figsize)
    
    # 绘制实验曲线
    plt.plot(displacement, force_exp, 'b-', label="实验数据", linewidth=2)
    
    # 如果有预测数据，绘制预测曲线
    if force_pred is not None:
        plt.plot(displacement, force_pred, 'r--', label="预测数据", linewidth=2)
    
    plt.xlabel("位移 (mm)", fontsize=12)
    plt.ylabel("力 (kN)", fontsize=12)
    plt.title(title, fontsize=14)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend(fontsize=12)
    
    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    # 显示图表
    if show:
        plt.show()
    else:
        plt.close()


def plot_hysteresis_comparison(displacement, force_exp, force_pred, 
                              save_path=None, show=True, figsize=(16, 8)):
    """
    绘制滞回曲线对比图
    
    参数:
        displacement (numpy.ndarray): 位移数据
        force_exp (numpy.ndarray): 实验力数据
        force_pred (numpy.ndarray): 预测力数据
        save_path (str, optional): 保存路径
        show (bool): 是否显示图表
        figsize (tuple): 图表尺寸
    """
    fig = plt.figure(figsize=figsize)
    gs = gridspec.GridSpec(1, 2, width_ratios=[1, 1])
    
    # 左侧：实验曲线
    ax1 = plt.subplot(gs[0])
    ax1.plot(displacement, force_exp, 'b-', linewidth=2)
    ax1.set_xlabel("位移 (mm)", fontsize=12)
    ax1.set_ylabel("力 (kN)", fontsize=12)
    ax1.set_title("实验滞回曲线", fontsize=14)
    ax1.grid(True, linestyle='--', alpha=0.7)
    
    # 右侧：预测曲线
    ax2 = plt.subplot(gs[1])
    ax2.plot(displacement, force_pred, 'r-', linewidth=2)
    ax2.set_xlabel("位移 (mm)", fontsize=12)
    ax2.set_ylabel("力 (kN)", fontsize=12)
    ax2.set_title("预测滞回曲线", fontsize=14)
    ax2.grid(True, linestyle='--', alpha=0.7)
    
    # 确保两个子图的坐标轴范围一致
    y_min = min(force_exp.min(), force_pred.min())
    y_max = max(force_exp.max(), force_pred.max())
    ax1.set_ylim(y_min, y_max)
    ax2.set_ylim(y_min, y_max)
    
    plt.tight_layout()
    
    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    # 显示图表
    if show:
        plt.show()
    else:
        plt.close()


def plot_hysteresis_overlay(displacement, force_exp, force_pred, 
                           save_path=None, show=True, figsize=(10, 8)):
    """
    绘制滞回曲线叠加图
    
    参数:
        displacement (numpy.ndarray): 位移数据
        force_exp (numpy.ndarray): 实验力数据
        force_pred (numpy.ndarray): 预测力数据
        save_path (str, optional): 保存路径
        show (bool): 是否显示图表
        figsize (tuple): 图表尺寸
    """
    plt.figure(figsize=figsize)
    
    # 绘制实验曲线和预测曲线
    plt.plot(displacement, force_exp, 'b-', label="实验数据", linewidth=2, alpha=0.7)
    plt.plot(displacement, force_pred, 'r--', label="预测数据", linewidth=2, alpha=0.7)
    
    plt.xlabel("位移 (mm)", fontsize=12)
    plt.ylabel("力 (kN)", fontsize=12)
    plt.title("滞回曲线对比", fontsize=14)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend(fontsize=12)
    
    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    # 显示图表
    if show:
        plt.show()
    else:
        plt.close()


def plot_bouc_wen_params(params, save_path=None, show=True, figsize=(12, 8)):
    """
    绘制Bouc-Wen参数可视化图
    
    参数:
        params (dict): Bouc-Wen参数字典
        save_path (str, optional): 保存路径
        show (bool): 是否显示图表
        figsize (tuple): 图表尺寸
    """
    plt.figure(figsize=figsize)
    
    # 提取参数
    param_names = list(params.keys())
    param_values = list(params.values())
    
    # 创建条形图
    bars = plt.bar(param_names, param_values, color='skyblue', edgecolor='navy')
    
    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height,
                f'{height:.4f}', ha='center', va='bottom', fontsize=10)
    
    plt.xlabel("参数名称", fontsize=12)
    plt.ylabel("参数值", fontsize=12)
    plt.title("Bouc-Wen模型参数", fontsize=14)
    plt.grid(True, axis='y', linestyle='--', alpha=0.7)
    
    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    # 显示图表
    if show:
        plt.show()
    else:
        plt.close()


def plot_error_distribution(force_exp, force_pred, bins=30, 
                           save_path=None, show=True, figsize=(10, 8)):
    """
    绘制预测误差分布直方图
    
    参数:
        force_exp (numpy.ndarray): 实验力数据
        force_pred (numpy.ndarray): 预测力数据
        bins (int): 直方图箱数
        save_path (str, optional): 保存路径
        show (bool): 是否显示图表
        figsize (tuple): 图表尺寸
    """
    # 计算误差
    errors = force_pred - force_exp
    
    plt.figure(figsize=figsize)
    
    # 绘制误差直方图
    plt.hist(errors, bins=bins, color='skyblue', edgecolor='navy', alpha=0.7)
    
    # 添加垂直线表示均值
    mean_error = np.mean(errors)
    plt.axvline(mean_error, color='red', linestyle='dashed', linewidth=2, 
                label=f'均值: {mean_error:.2f}')
    
    # 添加垂直线表示零误差
    plt.axvline(0, color='green', linestyle='solid', linewidth=2, 
                label='零误差')
    
    plt.xlabel("预测误差 (kN)", fontsize=12)
    plt.ylabel("频数", fontsize=12)
    plt.title("预测误差分布", fontsize=14)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend(fontsize=12)
    
    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    # 显示图表
    if show:
        plt.show()
    else:
        plt.close()


def plot_energy_dissipation(displacement, force_exp, force_pred, 
                           save_path=None, show=True, figsize=(10, 8)):
    """
    绘制能量耗散对比图
    
    参数:
        displacement (numpy.ndarray): 位移数据
        force_exp (numpy.ndarray): 实验力数据
        force_pred (numpy.ndarray): 预测力数据
        save_path (str, optional): 保存路径
        show (bool): 是否显示图表
        figsize (tuple): 图表尺寸
    """
    # 计算能量耗散（滞回环面积）
    def calculate_energy(disp, force):
        # 使用梯形法则计算滞回曲线的能量耗散
        # 滞回曲线的能量是闭合曲线围成的面积
        # 对于滞回曲线，我们需要计算绝对面积总和
        
        # 确保数据是numpy数组
        disp = np.array(disp)
        force = np.array(force)
        
        # 使用更精确的方法计算滞回曲线的能量耗散
        # 滞回曲线的能量应该是曲线围成的面积
        
        # 方法1：直接计算完整曲线的面积
        # 注意：对于滞回曲线，直接使用trapz可能会得到接近于零的结果，因为正负区域会相互抵消
        # 所以我们需要分段计算，然后取绝对值之和
        
        # 找出位移方向变化的点，这些点通常是滞回循环的转折点
        disp_diff = np.diff(disp)
        # 使用位移导数的符号变化来检测转折点
        sign_changes = np.where(np.diff(np.sign(disp_diff)))[0] + 1
        
        # 如果没有检测到转折点，尝试使用位移的极值点
        if len(sign_changes) == 0:
            # 寻找局部极大值和极小值点
            from scipy.signal import find_peaks
            peaks, _ = find_peaks(disp)
            valleys, _ = find_peaks(-disp)
            sign_changes = np.sort(np.concatenate([peaks, valleys]))
        
        # 如果仍然没有检测到转折点，使用固定间隔分段
        if len(sign_changes) == 0:
            # 每100个点分一段
            segment_size = 100
            sign_changes = np.arange(segment_size, len(disp), segment_size)
        
        # 添加起点和终点
        segments = np.concatenate(([0], sign_changes, [len(disp)-1]))
        segments = np.unique(segments)  # 确保没有重复点
        
        # 计算总能量
        total_energy = 0.0
        
        # 对每个段落计算能量
        for i in range(len(segments)-1):
            start_idx = segments[i]
            end_idx = segments[i+1] + 1  # 包含结束点
            
            segment_disp = disp[start_idx:end_idx]
            segment_force = force[start_idx:end_idx]
            
            # 计算这个段落的能量（使用梯形法则）
            segment_energy = np.abs(np.trapz(segment_force, segment_disp))
            total_energy += segment_energy
        
        # 如果计算出的能量仍然很小，尝试使用另一种方法
        if total_energy < 1000:
            # 方法2：计算整个曲线的面积，不分段
            # 这种方法适用于某些特殊情况
            area = np.trapz(force, disp)
            if np.abs(area) > total_energy:
                total_energy = np.abs(area)
        
        # 如果能量仍然很小，可能是数据问题，使用力的绝对值和位移的乘积作为估计
        if total_energy < 1000:
            # 方法3：使用力的绝对值和位移的乘积作为能量的粗略估计
            force_abs_mean = np.mean(np.abs(force))
            disp_range = np.max(disp) - np.min(disp)
            estimated_energy = force_abs_mean * disp_range
            # 如果估计值比计算值大得多，使用估计值
            if estimated_energy > 10 * total_energy:
                total_energy = estimated_energy
        
        return total_energy  # 返回总能量
    
    energy_exp = calculate_energy(displacement, force_exp)
    energy_pred = calculate_energy(displacement, force_pred)
    
    plt.figure(figsize=figsize)
    
    # 创建条形图
    bars = plt.bar(['实验数据', '预测数据'], [energy_exp, energy_pred], 
                  color=['blue', 'red'], alpha=0.7, width=0.4)
    
    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height,
                f'{height:.2f}', ha='center', va='bottom', fontsize=12)
    
    # 计算误差百分比
    error_percent = (energy_pred - energy_exp) / energy_exp * 100
    plt.title(f"能量耗散对比 (误差: {error_percent:.2f}%)", fontsize=14)
    
    plt.ylabel("能量耗散 (kN·mm)", fontsize=12)
    plt.grid(True, axis='y', linestyle='--', alpha=0.7)
    
    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    # 显示图表
    if show:
        plt.show()
    else:
        plt.close()