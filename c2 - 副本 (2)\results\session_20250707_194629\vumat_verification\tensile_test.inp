** ==========================================================
** INP Template for Single Element Uniaxial Tensile Test with VUMAT
** ==========================================================
*HEADING
Single Element Uniaxial Tensile Test with VUMAT
**
** PART DEFINITION: 10x10x100mm Column
*PART, NAME=COLUMN
*NODE
  1,   0.0,   0.0,   0.0
  2,  10.0,   0.0,   0.0
  3,  10.0,  10.0,   0.0
  4,   0.0,  10.0,   0.0
  5,   0.0,   0.0, 100.0
  6,  10.0,   0.0, 100.0
  7,  10.0,  10.0, 100.0
  8,   0.0,  10.0, 100.0
*ELEMENT, TYPE=C3D8R, ELSET=E_COLUMN
  1, 1, 2, 3, 4, 5, 6, 7, 8
*NSET, NSET=N_BOTTOM
  1, 2, 3, 4
*NSET, NSET=N_TOP
  5, 6, 7, 8
*SOLID SECTION, ELSET=E_COLUMN, MATERIAL=CONCRETE_VUMAT
*END PART
**
** ASSEMBLY
*ASSEMBLY, NAME=ASSEMBLY
*INSTANCE, NAME=COLUMN-1, PART=COLUMN
*END INSTANCE
*NSET, NSET=SET_BOTTOM, INSTANCE=COLUMN-1
N_BOTTOM
*NSET, NSET=SET_TOP, INSTANCE=COLUMN-1
N_TOP
*END ASSEMBLY
**
** MATERIAL DEFINITION
*Material, name=CONCRETE_VUMAT
*Density
2.4e-9
*Depvar
5
*User Material, constants=10
10000.0, 3.67, 10.0, 0.85, 1.69, 0.5, 2.0, 1.23, 0.5, 0.18
**
** AMPLITUDE for smooth loading
*AMPLITUDE, NAME=AMP-LINEAR, DEFINITION=TABULAR
0.0, 0.0,
1.0, 1.0
**
** STEP DEFINITION
*STEP, NAME=ApplyDisplacement, NLGEOM=NO
*DYNAMIC, EXPLICIT
, 1.0
**
** BOUNDARY CONDITIONS
*BOUNDARY
SET_BOTTOM, ENCASTRE
SET_TOP, 3, 3, 2.0, AMP=AMP-LINEAR
**
** OUTPUT REQUESTS
*OUTPUT, FIELD
*ELEMENT OUTPUT, ELSET=COLUMN-1.E_COLUMN
S, E, SDV
*NODE OUTPUT
U, RF
*OUTPUT, HISTORY, FREQUENCY=1
*NODE OUTPUT, NSET=SET_TOP
U3
*NODE OUTPUT, NSET=SET_BOTTOM
RF3
*END STEP