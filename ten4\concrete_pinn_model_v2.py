import torch
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ConcretePINN(nn.Module):
    """
    改进的混凝土拉伸损伤曲线PINN模型
    """
    def __init__(self, input_size=1, hidden_size=128, num_layers=3, output_size=3):
        super(ConcretePINN, self).__init__()
        
        # 更深的LSTM网络
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, 
                           batch_first=True, dropout=0.1)
        
        # 多层全连接网络
        self.fc_layers = nn.Sequential(
            nn.Linear(hidden_size, hidden_size//2),
            nn.Tanh(),
            nn.Linear(hidden_size//2, output_size)
        )
        
    def forward(self, strain_seq):
        lstm_out, _ = self.lstm(strain_seq)
        output = self.fc_layers(lstm_out)
        
        # 改进的输出约束
        sigma_hat = output[:, :, 0:1]  # 应力 - 无约束
        d_hat = torch.sigmoid(output[:, :, 1:2])  # 损伤变量 [0,1]
        ep_hat = torch.relu(output[:, :, 2:3]) * 0.01  # 塑性应变 - 限制范围
        
        return sigma_hat, d_hat, ep_hat

class ImprovedPINNTrainer:
    """
    改进的PINN训练器
    """
    def __init__(self, model, E0=30000.0, ft=3.0, device='cpu'):
        self.model = model.to(device)
        self.device = device
        
        # 物理参数
        self.E0 = E0
        self.ft = ft
        self.r0 = ft
        
        # 可训练物理参数 - 改进初始化
        self.A_plus = nn.Parameter(torch.tensor(0.7, device=device))
        self.B_plus = nn.Parameter(torch.tensor(5.0, device=device))
        self.xi = nn.Parameter(torch.tensor(0.01, device=device))
        
        # 数据标准化器
        self.strain_scaler = MinMaxScaler()
        self.stress_scaler = MinMaxScaler()
        
        # 改进的损失权重
        self.lambda_data = 10.0      # 增加数据拟合权重
        self.lambda_stress = 1.0     # 本构关系权重
        self.lambda_damage = 0.1     # 降低损伤约束权重
        self.lambda_plastic = 0.01   # 降低塑性约束权重
        
    def normalize_data(self, strain, stress):
        """数据标准化"""
        strain_norm = self.strain_scaler.fit_transform(strain.reshape(-1, 1)).flatten()
        stress_norm = self.stress_scaler.fit_transform(stress.reshape(-1, 1)).flatten()
        return strain_norm, stress_norm
    
    def denormalize_stress(self, stress_norm):
        """应力反标准化"""
        return self.stress_scaler.inverse_transform(stress_norm.reshape(-1, 1)).flatten()
    
    def compute_physics_loss(self, strain_seq, sigma_hat, d_hat, ep_hat):
        """
        改进的物理约束计算
        """
        batch_size, seq_len = strain_seq.shape[0], strain_seq.shape[1]
        device = strain_seq.device
        
        # 反标准化应变用于物理计算
        strain_real = torch.tensor(
            self.strain_scaler.inverse_transform(strain_seq.cpu().numpy().reshape(-1, 1))
        ).reshape(batch_size, seq_len, 1).to(device)
        
        # 初始化物理状态
        r_max = torch.full((batch_size,), self.ft, device=device)
        eps_p = torch.zeros((batch_size,), device=device)
        
        physics_loss = 0.0
        stress_loss = 0.0
        
        for i in range(seq_len):
            # 塑性应变增量
            if i > 0:
                deps = strain_real[:, i, 0] - strain_real[:, i-1, 0]
                deps = torch.clamp(deps, min=0)  # 只考虑拉伸
                eps_p = eps_p + self.xi * deps
            
            # 损伤驱动力
            Y = self.E0 * (strain_real[:, i, 0] - eps_p)
            Y = torch.clamp(Y, min=0)
            
            # 更新损伤阈值
            r_max = torch.max(r_max, Y)
            
            # 损伤演化 - 简化的指数衰减模型
            if torch.any(r_max > self.r0):
                d_theory = self.A_plus * (1 - torch.exp(-self.B_plus * (r_max - self.r0) / self.r0))
                d_theory = torch.clamp(d_theory, 0.0, 0.99)
            else:
                d_theory = torch.zeros_like(r_max)
            
            # 本构关系
            sigma_theory = self.E0 * (1 - d_theory) * (strain_real[:, i, 0] - eps_p)
            sigma_theory = torch.clamp(sigma_theory, min=0)
            
            # 标准化理论应力用于损失计算
            sigma_theory_norm = torch.tensor(
                self.stress_scaler.transform(sigma_theory.cpu().numpy().reshape(-1, 1))
            ).flatten().to(device)
            
            # 物理约束损失
            physics_loss += torch.mean((d_hat[:, i, 0] - d_theory)**2)
            stress_loss += torch.mean((sigma_hat[:, i, 0] - sigma_theory_norm)**2)
        
        return physics_loss / seq_len, stress_loss / seq_len
    
    def train(self, strain_exp, stress_exp, num_epochs=3000, lr=0.001):
        """训练模型"""
        
        # 数据预处理
        strain_norm, stress_norm = self.normalize_data(strain_exp, stress_exp)
        
        # 转换为张量
        strain_tensor = torch.tensor(strain_norm, dtype=torch.float32, device=self.device)
        stress_tensor = torch.tensor(stress_norm, dtype=torch.float32, device=self.device)
        
        strain_tensor = strain_tensor.unsqueeze(0).unsqueeze(-1)
        stress_tensor = stress_tensor.unsqueeze(0)
        
        # 优化器 - 使用不同学习率
        model_params = list(self.model.parameters())
        physics_params = [self.A_plus, self.B_plus, self.xi]
        
        optimizer = optim.Adam([
            {'params': model_params, 'lr': lr},
            {'params': physics_params, 'lr': lr * 0.1}  # 物理参数用较小学习率
        ])
        
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=200, factor=0.8)
        
        loss_history = []
        print("开始训练...")
        
        self.model.train()
        best_loss = float('inf')
        
        for epoch in range(num_epochs):
            optimizer.zero_grad()
            
            # 前向传播
            sigma_hat, d_hat, ep_hat = self.model(strain_tensor)
            
            # 数据拟合损失
            loss_data = torch.mean((sigma_hat.squeeze(-1) - stress_tensor)**2)
            
            # 物理约束损失
            physics_loss, stress_loss = self.compute_physics_loss(
                strain_tensor, sigma_hat, d_hat, ep_hat
            )
            
            # 总损失
            total_loss = (self.lambda_data * loss_data + 
                         self.lambda_stress * stress_loss + 
                         self.lambda_damage * physics_loss)
            
            # 反向传播
            total_loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            optimizer.step()
            scheduler.step(total_loss)
            
            # 约束参数
            with torch.no_grad():
                self.A_plus.data = torch.clamp(self.A_plus.data, 0.1, 1.0)
                self.B_plus.data = torch.clamp(self.B_plus.data, 0.1, 20.0)
                self.xi.data = torch.clamp(self.xi.data, 0.0, 0.1)
            
            loss_history.append(total_loss.item())
            
            # 保存最佳模型
            if total_loss.item() < best_loss:
                best_loss = total_loss.item()
                torch.save(self.model.state_dict(), 'best_model.pth')
            
            if epoch % 300 == 0:
                print(f"Epoch {epoch:4d}, Loss: {total_loss.item():.6f}")
                print(f"  Data: {loss_data.item():.6f}, Physics: {physics_loss.item():.6f}, Stress: {stress_loss.item():.6f}")
                print(f"  A+: {self.A_plus.item():.4f}, B+: {self.B_plus.item():.4f}, xi: {self.xi.item():.6f}")
                print(f"  LR: {optimizer.param_groups[0]['lr']:.6f}")
                
        # 加载最佳模型
        self.model.load_state_dict(torch.load('best_model.pth'))
        print("训练完成！")
        return loss_history
    
    def predict(self, strain_exp):
        """预测"""
        self.model.eval()
        with torch.no_grad():
            # 使用训练时的标准化
            strain_norm = self.strain_scaler.transform(strain_exp.reshape(-1, 1)).flatten()
            strain_tensor = torch.tensor(strain_norm, dtype=torch.float32, device=self.device)
            strain_tensor = strain_tensor.unsqueeze(0).unsqueeze(-1)
            
            sigma_pred, d_pred, ep_pred = self.model(strain_tensor)
            
            # 反标准化应力
            sigma_pred_norm = sigma_pred.squeeze().cpu().numpy()
            sigma_pred_real = self.denormalize_stress(sigma_pred_norm)
            
            d_pred = d_pred.squeeze().cpu().numpy()
            ep_pred = ep_pred.squeeze().cpu().numpy()
            
            return sigma_pred_real, d_pred, ep_pred

def improved_plot_results(strain_exp, stress_exp, stress_pred, damage_pred):
    """改进的绘图函数"""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 应力-应变曲线对比
    ax1.plot(strain_exp, stress_exp, 'ro-', label='Experimental Data', 
             markersize=3, linewidth=1.5, alpha=0.7)
    ax1.plot(strain_exp, stress_pred, 'b-', label='PINN Prediction', 
             linewidth=2)
    ax1.set_xlabel('Strain')
    ax1.set_ylabel('Stress (MPa)')
    ax1.set_title('Stress-Strain Curve Comparison')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 损伤-应变曲线
    ax2.plot(strain_exp, damage_pred, 'g-', label='Damage Evolution', linewidth=2)
    ax2.set_xlabel('Strain')
    ax2.set_ylabel('Damage Variable')
    ax2.set_title('Damage-Strain Curve')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 误差分析
    error = stress_exp - stress_pred
    ax3.plot(strain_exp, error, 'r-', linewidth=1)
    ax3.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax3.set_xlabel('Strain')
    ax3.set_ylabel('Error (MPa)')
    ax3.set_title('Prediction Error')
    ax3.grid(True, alpha=0.3)
    
    # 相关性图
    ax4.scatter(stress_exp, stress_pred, alpha=0.6, s=20)
    min_val = min(stress_exp.min(), stress_pred.min())
    max_val = max(stress_exp.max(), stress_pred.max())
    ax4.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
    ax4.set_xlabel('Experimental Stress (MPa)')
    ax4.set_ylabel('Predicted Stress (MPa)')
    ax4.set_title('Prediction vs Experiment')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('improved_pinn_results.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Device: {device}")
    
    # 加载数据
    try:
        df = pd.read_excel('tension.xlsx')
        strain_exp = df['strain'].values
        stress_exp = df['stress'].values
        print(f"Data loaded: {len(strain_exp)} points")
        print(f"Strain range: {strain_exp.min():.6f} to {strain_exp.max():.6f}")
        print(f"Stress range: {stress_exp.min():.2f} to {stress_exp.max():.2f} MPa")
    except Exception as e:
        print(f"Error loading data: {e}")
        return
    
    # 创建改进模型
    model = ConcretePINN(input_size=1, hidden_size=128, num_layers=3, output_size=3)
    trainer = ImprovedPINNTrainer(model, E0=30000.0, ft=3.0, device=device)
    
    # 训练
    loss_history = trainer.train(strain_exp, stress_exp, num_epochs=3000, lr=0.001)
    
    # 预测
    print("\nPredicting...")
    stress_pred, damage_pred, plastic_pred = trainer.predict(strain_exp)
    
    # 性能评估
    mse = np.mean((stress_exp - stress_pred)**2)
    mae = np.mean(np.abs(stress_exp - stress_pred))
    r2 = 1 - np.sum((stress_exp - stress_pred)**2) / np.sum((stress_exp - np.mean(stress_exp))**2)
    
    print(f"\nPerformance Metrics:")
    print(f"MSE: {mse:.6f}")
    print(f"MAE: {mae:.6f}")
    print(f"R²: {r2:.6f}")
    print(f"Final Parameters:")
    print(f"A+ = {trainer.A_plus.item():.4f}")
    print(f"B+ = {trainer.B_plus.item():.4f}")
    print(f"xi = {trainer.xi.item():.6f}")
    
    # 绘制结果
    improved_plot_results(strain_exp, stress_exp, stress_pred, damage_pred)
    
    # 保存结果
    results = np.column_stack((strain_exp, stress_exp, stress_pred, damage_pred, plastic_pred))
    np.savetxt('improved_results.txt', results, 
               header='strain_exp stress_exp stress_pred damage_pred plastic_pred', 
               fmt='%.8f')
    
    # 保存模型
    torch.save({
        'model_state_dict': model.state_dict(),
        'strain_scaler': trainer.strain_scaler,
        'stress_scaler': trainer.stress_scaler,
        'A_plus': trainer.A_plus.item(),
        'B_plus': trainer.B_plus.item(),
        'xi': trainer.xi.item(),
        'E0': trainer.E0,
        'ft': trainer.ft
    }, 'improved_pinn_model.pth')
    
    print("Model and results saved!")

if __name__ == "__main__":
    main() 