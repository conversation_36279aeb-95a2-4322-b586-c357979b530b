# 混凝土拉伸损伤PINN模型（修复版）

## 项目简介

基于物理信息神经网络(PINN)的混凝土拉伸损伤建模项目。本项目已经过优化和修复，提供更稳定和准确的训练结果。

本项目实现了一个基于物理信息神经网络（Physics-Informed Neural Network, PINN）的混凝土拉伸损伤识别模型。该模型结合了深度学习和损伤力学理论，能够从拉伸试验数据中识别混凝土的损伤演化和塑性变形参数。

## 理论基础

本模型基于连续损伤力学理论，主要考虑以下物理方程：

1. **损伤变量定义**：描述材料微观裂缝和缺陷的演化
2. **应力-应变关系**：考虑损伤影响的本构方程
3. **损伤能释放率**：驱动损伤演化的热力学力
4. **损伤阈值**：控制损伤演化的临界条件
5. **塑性应变演化**：描述不可恢复变形的累积

## 模型架构

本项目的PINN模型架构包括：

1. **LSTM网络**：捕捉应变序列的时间依赖性
2. **全连接层**：映射到应力、损伤变量和塑性应变
3. **物理约束**：通过损失函数引入物理方程约束
4. **可训练材料参数**：包括初始弹性模量、损伤参数和塑性参数

## 项目结构

```
pinn_concrete_damage/
├── models/                  # 模型定义
│   ├── __init__.py
│   └── pinn_model.py        # PINN模型核心实现
├── utils/                   # 工具函数
│   ├── __init__.py
│   ├── data_processor.py    # 数据处理
│   └── visualization.py     # 可视化工具
├── main.py                  # 主程序入口
├── train.py                 # 训练模块
├── predict.py               # 预测和评估模块
├── requirements.txt         # 依赖包列表
└── README.md                # 项目说明
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 训练模型

```bash
python main.py --mode train --data_path ../tension.xlsx --model_dir ./saved_models --epochs 1000 --use_dynamic_loss
```

### 预测和评估

```bash
python main.py --mode predict --data_path ../tension.xlsx --model_dir ./saved_models --results_dir ./results
```

### 训练和预测一体化

```bash
python main.py --mode both --data_path ../tension.xlsx --model_dir ./saved_models --results_dir ./results --epochs 1000 --use_dynamic_loss
```

### 进行敏感性分析

```bash
python main.py --mode predict --data_path ../tension.xlsx --model_dir ./saved_models --results_dir ./results --perform_sensitivity
```

## 参数说明

- `--mode`: 运行模式，可选 'train', 'predict', 'both'
- `--data_path`: 数据文件路径
- `--model_dir`: 模型保存目录
- `--results_dir`: 结果保存目录
- `--epochs`: 训练轮数
- `--batch_size`: 批次大小
- `--sequence_length`: 序列长度
- `--learning_rate`: 学习率
- `--hidden_dim`: LSTM隐藏层维度
- `--lstm_layers`: LSTM层数
- `--fc_layers`: 全连接层数
- `--use_dynamic_loss`: 是否使用动态加权损失
- `--early_stopping_patience`: 早停耐心值
- `--perform_sensitivity`: 是否进行敏感性分析

## 结果分析

训练完成后，模型将生成以下结果：

1. **应力-应变曲线**：对比真实应力和预测应力
2. **损伤演化曲线**：显示损伤变量随应变的变化
3. **塑性应变曲线**：显示塑性应变随应变的变化
4. **预测vs真实散点图**：评估预测精度
5. **误差分布直方图**：分析预测误差
6. **损伤-应力关系**：探索损伤与应力的相互作用
7. **材料参数变化**：训练过程中材料参数的演化
8. **敏感性分析**：参数对模型输出的影响

## 模型优势

1. **物理一致性**：通过物理约束确保结果符合损伤力学理论
2. **参数识别**：能够识别难以直接测量的材料参数
3. **动态加权**：自适应调整不同物理约束的重要性
4. **序列建模**：捕捉应变历史对当前状态的影响

## 注意事项

1. 数据文件应包含至少两列：第一列为应变数据，第二列为应力数据
2. 训练前建议对数据进行标准化处理
3. 对于不同的混凝土材料，可能需要调整初始材料参数
4. 序列长度参数对模型性能有重要影响，建议根据数据特点调整