# 钢筋混凝土柱滞回曲线拟合与预测使用说明

## 项目简介
本项目基于物理信息神经网络(PINN)实现钢筋混凝土柱在循环荷载下的滞回曲线拟合与预测。通过融合Bouc-Wen物理模型与深度学习方法，实现对柱构件滞回行为的高精度建模。

## 环境配置

### 依赖安装
在项目根目录下执行以下命令安装所需依赖：
```bash
pip install -r requirements.txt
```

### 数据准备
确保Excel数据文件`column.xlsx`位于`perfect column`文件夹下，该文件应包含以下内容：
- Sheet1：静态参数表（L/D、P/f_cA_g、rho_l、rho_t、混凝土强度）
- Sheet2：力-位移数据表（位移、力、循环阶段、样本ID等）

## 使用方法

### 完整流程（训练+预测）
```bash
python main.py --mode all
```

### 仅训练模型
```bash
python main.py --mode train
```

### 仅进行预测
```bash
python main.py --mode predict
```

### 自定义参数
可以通过命令行参数自定义训练和预测过程：
```bash
python main.py --mode all --batch_size 64 --epochs 2000 --hidden_dim 150 --hidden_layers 5 --learning_rate 5e-4 --lambda_physics 0.8 --use_lbfgs
```

## 参数说明
- `--mode`：运行模式，可选值为`train`（仅训练）、`predict`（仅预测）、`all`（训练+预测）
- `--batch_size`：批次大小
- `--epochs`：训练轮数
- `--hidden_dim`：隐藏层维度
- `--hidden_layers`：隐藏层数量
- `--learning_rate`：学习率
- `--lambda_data`：数据匹配损失权重
- `--lambda_physics`：物理残差损失权重
- `--patience`：早停耐心值
- `--l2_reg`：L2正则化系数
- `--use_lbfgs`：是否使用L-BFGS优化器进行精细调参
- `--model_path`：模型文件路径（仅在预测模式下使用）

## 输出结果
所有结果将保存在`results`文件夹下，包括：
- `models/`：保存训练的模型文件
- `figures/`：保存生成的图表
- `metrics/`：保存评估指标

## 主要图表说明
- `hysteresis_curve_overlay.png`：滞回曲线叠加对比图
- `hysteresis_curve_comparison.png`：实验与预测滞回曲线并排对比
- `error_distribution.png`：预测误差分布直方图
- `energy_dissipation.png`：能量耗散对比图
- `bouc_wen_params.png`：Bouc-Wen模型参数可视化
- `training_history.png`：训练过程损失曲线