# -*- coding: utf-8 -*-
"""
数据处理模块

处理循环加卸载试验数据，提取应力应变关系用于PINN训练
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from font_config import configure_chinese_font

class DataProcessor:
    def __init__(self, excel_path):
        """
        初始化数据处理器
        
        Args:
            excel_path: Excel文件路径
        """
        self.excel_path = excel_path
        self.raw_data = None
        self.processed_data = None
        
        # 配置中文字体
        configure_chinese_font()
    
    def load_data(self):
        """
        加载Excel数据
        """
        try:
            # 尝试读取Excel文件的第一个工作表
            self.raw_data = pd.read_excel(self.excel_path, sheet_name=0)
            print(f"数据加载成功，数据形状: {self.raw_data.shape}")
            print(f"列名: {list(self.raw_data.columns)}")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def preprocess_data(self):
        """
        预处理数据：分离加载和卸载段，计算应变和应力
        """
        if self.raw_data is None:
            print("请先加载数据")
            return False
        
        # 假设数据包含位移和力的列
        # 根据实际数据结构调整列名
        columns = self.raw_data.columns.tolist()
        
        # 自动识别可能的位移和力列
        displacement_col = None
        force_col = None
        
        for col in columns:
            col_lower = str(col).lower()
            if any(keyword in col_lower for keyword in ['位移', 'displacement', 'disp', '变形']):
                displacement_col = col
            elif any(keyword in col_lower for keyword in ['力', 'force', '载荷', 'load']):
                force_col = col
        
        if displacement_col is None or force_col is None:
            # 如果无法自动识别，使用前两列
            displacement_col = columns[0]
            force_col = columns[1]
            print(f"自动选择列: 位移={displacement_col}, 力={force_col}")
        
        # 提取位移和力数据
        displacement = self.raw_data[displacement_col].values
        force = self.raw_data[force_col].values
        
        # 计算应变和应力（需要根据试件尺寸调整）
        # 这里使用标准化的值，实际应用中需要根据试件几何参数计算
        gauge_length = 100.0  # mm，标距长度
        cross_section_area = 100.0  # mm²，截面积
        
        strain = displacement / gauge_length  # 应变
        stress = force / cross_section_area   # 应力 (MPa)
        
        # 识别加载和卸载段
        loading_segments, unloading_segments = self._identify_cycles(strain, stress)
        
        # 存储处理后的数据
        self.processed_data = {
            'strain': strain,
            'stress': stress,
            'displacement': displacement,
            'force': force,
            'loading_segments': loading_segments,
            'unloading_segments': unloading_segments
        }
        
        print(f"数据预处理完成，识别到 {len(loading_segments)} 个加载段")
        return True
    
    def _identify_cycles(self, strain, stress):
        """
        识别循环加载和卸载段
        
        Args:
            strain: 应变数组
            stress: 应力数组
            
        Returns:
            loading_segments: 加载段列表
            unloading_segments: 卸载段列表
        """
        loading_segments = []
        unloading_segments = []
        
        # 计算应变变化率
        strain_diff = np.diff(strain)
        
        # 识别转折点
        current_segment = []
        is_loading = strain_diff[0] > 0
        
        for i in range(len(strain_diff)):
            current_segment.append(i)
            
            # 检查是否发生方向改变
            if i < len(strain_diff) - 1:
                next_is_loading = strain_diff[i + 1] > 0
                
                if is_loading != next_is_loading:
                    # 方向改变，结束当前段
                    current_segment.append(i + 1)  # 包含转折点
                    
                    if is_loading:
                        loading_segments.append(current_segment.copy())
                    else:
                        unloading_segments.append(current_segment.copy())
                    
                    current_segment = [i + 1]
                    is_loading = next_is_loading
        
        # 处理最后一段
        if current_segment:
            current_segment.append(len(strain) - 1)
            if is_loading:
                loading_segments.append(current_segment)
            else:
                unloading_segments.append(current_segment)
        
        return loading_segments, unloading_segments
    
    def plot_raw_data(self, save_path=None):
        """
        绘制原始数据
        """
        if self.processed_data is None:
            print("请先预处理数据")
            return
        
        plt.figure(figsize=(12, 8))
        
        strain = self.processed_data['strain']
        stress = self.processed_data['stress']
        
        plt.subplot(2, 2, 1)
        plt.plot(strain, stress, 'b-', linewidth=1)
        plt.xlabel('应变')
        plt.ylabel('应力 (MPa)')
        plt.title('应力-应变曲线')
        plt.grid(True)
        
        plt.subplot(2, 2, 2)
        plt.plot(range(len(strain)), strain, 'g-', linewidth=1)
        plt.xlabel('数据点')
        plt.ylabel('应变')
        plt.title('应变历程')
        plt.grid(True)
        
        plt.subplot(2, 2, 3)
        plt.plot(range(len(stress)), stress, 'r-', linewidth=1)
        plt.xlabel('数据点')
        plt.ylabel('应力 (MPa)')
        plt.title('应力历程')
        plt.grid(True)
        
        plt.subplot(2, 2, 4)
        # 绘制循环识别结果
        loading_segments = self.processed_data['loading_segments']
        unloading_segments = self.processed_data['unloading_segments']
        
        for i, segment in enumerate(loading_segments):
            plt.plot(strain[segment], stress[segment], 'b-', alpha=0.7, label='加载' if i == 0 else "")
        
        for i, segment in enumerate(unloading_segments):
            plt.plot(strain[segment], stress[segment], 'r-', alpha=0.7, label='卸载' if i == 0 else "")
        
        plt.xlabel('应变')
        plt.ylabel('应力 (MPa)')
        plt.title('循环识别结果')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图像已保存至: {save_path}")
        
        plt.show()
    
    def get_training_data(self):
        """
        获取用于PINN训练的数据
        
        Returns:
            dict: 包含训练数据的字典
        """
        if self.processed_data is None:
            print("请先预处理数据")
            return None
        
        strain = self.processed_data['strain']
        stress = self.processed_data['stress']
        
        # 归一化数据
        strain_normalized = (strain - np.min(strain)) / (np.max(strain) - np.min(strain))
        stress_normalized = (stress - np.min(stress)) / (np.max(stress) - np.min(stress))
        
        return {
            'strain': strain,
            'stress': stress,
            'strain_normalized': strain_normalized,
            'stress_normalized': stress_normalized,
            'strain_min': np.min(strain),
            'strain_max': np.max(strain),
            'stress_min': np.min(stress),
            'stress_max': np.max(stress)
        }

# 测试代码
if __name__ == "__main__":
    # 创建数据处理器
    processor = DataProcessor('data.xlsx')
    
    # 加载和预处理数据
    if processor.load_data():
        if processor.preprocess_data():
            # 绘制数据
            processor.plot_raw_data('raw_data_analysis.png')
            
            # 获取训练数据
            training_data = processor.get_training_data()
            if training_data:
                print("训练数据准备完成")
                print(f"应变范围: {training_data['strain_min']:.6f} ~ {training_data['strain_max']:.6f}")
                print(f"应力范围: {training_data['stress_min']:.2f} ~ {training_data['stress_max']:.2f} MPa")