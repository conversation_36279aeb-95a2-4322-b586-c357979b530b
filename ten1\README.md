# 混凝土拉伸损伤曲线PINN学习系统

## 项目简介

本项目实现了一个基于物理信息神经网络（Physics-Informed Neural Networks, PINN）的混凝土拉伸损伤曲线学习系统。该系统能够从实验数据中学习混凝土的损伤演化规律，识别材料参数，并预测任意应变状态下的应力、损伤变量和塑性应变。

## 核心特性

- **物理约束集成**：将混凝土损伤力学的本构关系直接嵌入神经网络训练过程
- **参数识别**：自动识别损伤演化参数（A+, B+）和塑性应变发展系数（ξ）
- **状态预测**：输入应变即可输出对应的应力、损伤变量和塑性应变
- **历史依赖性**：使用LSTM网络捕捉加载历史对材料状态的影响
- **可视化分析**：提供完整的训练过程和结果可视化

## 理论基础

### 物理方程

1. **应力-应变关系**：
   ```
   σ = E₀(1-d)(ε-εᵖ)
   ```

2. **损伤驱动力**：
   ```
   Y = E₀(ε-εᵖ)
   ```

3. **损伤阈值演化**：
   ```
   r(t) = max(r_old, Y(t))
   ```

4. **损伤演化规律**：
   ```
   d = 1 - (r₀/r)[(1-A⁺) + A⁺exp(B⁺(1-r/r₀))]
   ```

5. **塑性应变累积**：
   ```
   εᵖ(t) = εᵖ(t-1) + ξΔε
   ```

### 网络架构

- **输入层**：应变序列 ε(t)
- **LSTM层**：捕捉历史依赖性（3层，隐藏维度128）
- **全连接层**：映射到物理量
- **输出层**：应力σ、损伤变量d、塑性应变εᵖ

### 损失函数

总损失函数包含四个部分：

```
L_total = λ₁L_data + λ₂L_stress + λ₃L_damage + λ₄L_plastic
```

- **L_data**：数据拟合损失（实验应力vs预测应力）
- **L_stress**：应力本构关系损失
- **L_damage**：损伤演化物理约束损失
- **L_plastic**：塑性应变累积物理约束损失

## 安装和使用

### 环境要求

- Python 3.7+
- PyTorch 1.9+
- 其他依赖见 `requirements.txt`

### 安装步骤

1. 克隆或下载项目文件
2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

### 数据准备

将实验数据保存为Excel文件（`tension.xlsx`），包含以下列：
- 应变数据（第一列或名为"应变"的列）
- 应力数据（第二列或名为"应力"的列）

### 运行训练

```bash
python concrete_tension_pinn.py
```

### 主要功能

#### 1. 数据加载
```python
# 从Excel文件加载数据
strain_data, stress_data = trainer.load_data_from_excel("tension.xlsx")
```

#### 2. 模型训练
```python
# 创建模型和训练器
model = ConcreteTensionPINN()
trainer = ConcreteTensionTrainer(model)

# 训练模型
trainer.train(strain_data, stress_data, epochs=1000)
```

#### 3. 结果预测
```python
# 预测新的应变状态
predictions = trainer.predict(new_strain_data)
```

#### 4. 结果可视化
```python
# 绘制训练结果
trainer.plot_results(strain_data, stress_data)
```

## 输出结果

### 训练过程输出

训练过程中会显示：
- 各项损失函数值
- 材料参数的实时更新
- 学习率调整信息

### 最终结果

1. **识别的材料参数**：
   - A⁺：损伤演化形状参数
   - B⁺：损伤演化速率参数
   - ξ：塑性应变发展系数

2. **可视化图表**：
   - 应力-应变曲线对比
   - 损伤演化曲线
   - 塑性应变演化
   - 训练损失历史
   - 参数演化历史
   - 损伤阈值演化

3. **保存文件**：
   - `concrete_tension_pinn_model.pth`：训练好的模型
   - `pinn_results.png`：结果可视化图

## 模型参数说明

### 网络参数
- `input_size`：输入维度（默认1，应变）
- `hidden_size`：LSTM隐藏层维度（默认128）
- `num_layers`：LSTM层数（默认3）
- `output_size`：输出维度（默认3：应力、损伤、塑性应变）

### 物理参数
- `E0`：初始弹性模量（默认30000 MPa）
- `ft`：单轴抗拉强度（默认3.0 MPa）
- `A_plus`：损伤演化形状参数（可训练）
- `B_plus`：损伤演化速率参数（可训练）
- `xi`：塑性应变发展系数（可训练）

### 训练参数
- `learning_rate`：学习率（默认0.001）
- `epochs`：训练轮数（默认1000）
- `batch_size`：批次大小（默认1，单条曲线）

### 损失权重
- `lambda_data`：数据拟合损失权重（默认1.0）
- `lambda_stress`：应力约束损失权重（默认0.8）
- `lambda_damage`：损伤约束损失权重（默认0.5）
- `lambda_plastic`：塑性约束损失权重（默认0.5）

## 自定义使用

### 修改材料参数

在模型初始化时修改固定参数：
```python
model = ConcreteTensionPINN()
model.E0 = torch.tensor(35000.0)  # 修改弹性模量
model.ft = torch.tensor(3.5)      # 修改抗拉强度
```

### 调整损失权重

在训练器中修改权重：
```python
trainer.lambda_data = 1.0
trainer.lambda_stress = 1.0
trainer.lambda_damage = 0.8
trainer.lambda_plastic = 0.6
```

### 使用预训练模型

```python
# 加载预训练模型
checkpoint = torch.load("concrete_tension_pinn_model.pth")
model.load_state_dict(checkpoint['model_state_dict'])

# 进行预测
predictions = trainer.predict(new_strain_data)
```

## 注意事项

1. **数据质量**：确保实验数据的质量和完整性
2. **参数初值**：可根据材料特性调整初始参数值
3. **训练时间**：根据数据复杂度调整训练轮数
4. **收敛性**：监控损失函数确保模型收敛
5. **物理合理性**：验证识别参数的物理合理性

## 扩展功能

### 多曲线训练

可以扩展为同时训练多条实验曲线：
```python
# 准备多条曲线数据
strain_sequences = [strain1, strain2, strain3]
stress_sequences = [stress1, stress2, stress3]

# 批量训练
for strain, stress in zip(strain_sequences, stress_sequences):
    trainer.train(strain, stress)
```

### 参数敏感性分析

```python
# 分析参数对结果的影响
for A_plus in [0.3, 0.5, 0.7]:
    model.A_plus = torch.tensor(A_plus)
    predictions = trainer.predict(strain_data)
    # 分析结果差异
```

## 故障排除

### 常见问题

1. **训练不收敛**：
   - 降低学习率
   - 调整损失权重
   - 增加训练轮数

2. **预测结果不合理**：
   - 检查数据质量
   - 验证物理参数设置
   - 调整网络架构

3. **内存不足**：
   - 减少序列长度
   - 降低网络规模
   - 使用CPU训练

### 调试技巧

1. 监控各项损失的变化趋势
2. 检查参数的演化是否合理
3. 验证物理约束的满足程度
4. 对比预测结果与实验数据

## 参考文献

1. 混凝土损伤力学理论
2. 物理信息神经网络方法
3. 深度学习在材料建模中的应用

## 联系方式

如有问题或建议，请联系项目开发者。

---

**版本**：1.0  
**更新日期**：2024年  
**许可证**：MIT License