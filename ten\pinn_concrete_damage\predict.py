#!/usr/bin/env python
# -*- coding: utf-8 -*-

import torch
import numpy as np
import matplotlib.pyplot as plt
import os
from sklearn.metrics import mean_squared_error, r2_score

# 导入自定义模块
from utils.data_processor import TensileDataProcessor, create_dataloader
from models.pinn_model import ConcreteDamagePINN
from utils.font_config import setup_chinese_font

# 设置中文字体
setup_chinese_font()

def load_trained_model(model_path, device):
    """
    加载训练好的模型
    
    参数:
        model_path: 模型文件路径
        device: 计算设备
        
    返回:
        加载的模型和训练信息
    """
    # 检查模型文件是否存在
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")
    
    # 加载模型检查点
    checkpoint = torch.load(model_path, map_location=device)
    
    # 提取材料参数
    material_params = checkpoint.get('material_params', {})
    
    # 初始化模型
    model = ConcreteDamagePINN(
        input_dim=1,
        hidden_dim=32,
        lstm_layers=1,
        fc_layers=2,
        dropout=0.1
    ).to(device)
    
    # 加载模型状态
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 设置为评估模式
    model.eval()
    
    return model, checkpoint

def predict_with_model(model, data_loader, device):
    """
    使用模型进行预测
    
    参数:
        model: 训练好的模型
        data_loader: 数据加载器
        device: 计算设备
        
    返回:
        预测结果字典
    """
    model.eval()
    predictions = []
    targets = []
    
    with torch.no_grad():
        for strain_seq, stress_true in data_loader:
            strain_seq = strain_seq.to(device)
            stress_true = stress_true.to(device)
            
            # 重置模型状态
            model.reset_state(strain_seq.size(0))
            
            # 前向传播
            stress_pred, damage_pred, plastic_strain_pred = model.forward(strain_seq, return_components=True)
            
            predictions.append(stress_pred.cpu().numpy())
            targets.append(stress_true.cpu().numpy())
    
    # 合并结果
    predictions = np.concatenate(predictions, axis=0)
    targets = np.concatenate(targets, axis=0)
    
    return {
        'predictions': predictions,
        'targets': targets
    }

def evaluate_predictions(predictions, targets):
    """
    评估预测结果
    
    参数:
        predictions: 预测值
        targets: 真实值
        
    返回:
        评估指标字典
    """
    # 计算评估指标
    mse = mean_squared_error(targets, predictions)
    r2 = r2_score(targets, predictions)
    mae = np.mean(np.abs(targets - predictions))
    
    return {
        'mse': mse,
        'r2': r2,
        'mae': mae
    }

def plot_prediction_results(predictions, targets, strain_data, save_dir):
    """
    绘制预测结果
    
    参数:
        predictions: 预测值
        targets: 真实值
        strain_data: 应变数据
        save_dir: 保存目录
    """
    os.makedirs(save_dir, exist_ok=True)
    
    plt.figure(figsize=(15, 10))
    
    # 应力-应变曲线对比
    plt.subplot(2, 3, 1)
    plt.plot(strain_data[:len(targets)], targets, 'b-', label='真实应力', linewidth=2)
    plt.plot(strain_data[:len(predictions)], predictions, 'r--', label='预测应力', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('应力 (MPa)')
    plt.title('应力-应变曲线对比')
    plt.legend()
    plt.grid(True)
    
    # 预测vs真实散点图
    plt.subplot(2, 3, 2)
    plt.scatter(targets, predictions, alpha=0.6)
    min_val = min(targets.min(), predictions.min())
    max_val = max(targets.max(), predictions.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'k--', linewidth=2)
    plt.xlabel('真实应力 (MPa)')
    plt.ylabel('预测应力 (MPa)')
    plt.title('预测vs真实应力')
    plt.grid(True)
    
    # 误差分布
    plt.subplot(2, 3, 3)
    errors = predictions - targets
    plt.hist(errors.flatten(), bins=30, alpha=0.7, color='blue')
    plt.xlabel('预测误差 (MPa)')
    plt.ylabel('频数')
    plt.title('预测误差分布')
    plt.grid(True)
    
    # 误差随应变变化
    plt.subplot(2, 3, 4)
    plt.plot(strain_data[:len(errors)], errors, 'g-', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('预测误差 (MPa)')
    plt.title('误差随应变变化')
    plt.grid(True)
    
    # 相对误差
    plt.subplot(2, 3, 5)
    relative_errors = np.abs(errors) / (np.abs(targets) + 1e-6) * 100
    plt.plot(strain_data[:len(relative_errors)], relative_errors, 'm-', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('相对误差 (%)')
    plt.title('相对误差随应变变化')
    plt.grid(True)
    
    # 累积误差
    plt.subplot(2, 3, 6)
    cumulative_error = np.cumsum(np.abs(errors))
    plt.plot(strain_data[:len(cumulative_error)], cumulative_error, 'c-', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('累积绝对误差 (MPa)')
    plt.title('累积误差')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'prediction_results.png'), dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == '__main__':
    # 示例使用
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 加载模型
    model_path = './saved_models/best_model.pth'
    model, checkpoint = load_trained_model(model_path, device)
    
    # 加载数据
    data_path = '../tension.xlsx'
    processor = TensileDataProcessor(data_path)
    strain_data, stress_data = processor.preprocess_data(normalize=True)
    
    # 创建数据加载器
    data_loader = create_dataloader(
        strain_data, stress_data, 
        batch_size=16, 
        sequence_length=3, 
        shuffle=False
    )
    
    # 预测
    results = predict_with_model(model, data_loader, device)
    
    # 评估
    metrics = evaluate_predictions(results['predictions'], results['targets'])
    print(f"评估指标: MSE={metrics['mse']:.6f}, R²={metrics['r2']:.6f}, MAE={metrics['mae']:.6f}")
    
    # 绘制结果
    plot_prediction_results(
        results['predictions'], 
        results['targets'], 
        strain_data, 
        './results'
    )