# 混凝土弹塑性损伤本构关系统一模型公式总结

<!-- 本文档总结了混凝土弹塑性损伤本构模型的主要公式与数值实现方法，适用于有限元分析与理论推导。 -->

## 1. 张量及牵张量的正负分解
- **张量分解公式：**
  $$
  X = X^+ + X^- \tag{1}
  $$
  $$
  X^\pm = P_X^\pm : X, \quad X^\pm = Q_X^\pm : X \tag{2-3}
  $$
  - **投影算子：**
    $$
    P_X^\pm = \sum H(X^{(n)}) \mathbf{p}_X^{(n)} \otimes \mathbf{p}_X^{(n)}, \quad Q_X^\pm = P_X^\pm + \text{高阶修正项}
    $$
    - $H(\cdot)$：Heaviside函数，$\mathbf{p}_X^{(n)}$：特征向量。

---

## 2. 弹塑性损伤本构关系
### 2.1 本构关系表达式
- **总应力张量（含阻尼应力）：**
  $$
  \sigma_{\text{tot}} = (1-d^+)\sigma^+ + (1-d^-)\sigma^- + \sigma_{\text{vis}} \tag{12}
  $$
  - $d^\pm$：拉/压损伤变量，$\sigma_{\text{vis}}$：粘弹性-损伤阻尼应力。

- **有效应力与塑性应变关系：**
  $$
  \sigma = C_0 : (\varepsilon - \varepsilon^p) \tag{10}
  $$
  $$
  \dot{\varepsilon}^p = \xi^\pm E_0 \sigma \quad (\text{简化塑性演化}) \tag{13}
  $$

### 2.2 损伤演化法则
- **损伤能释放率：**
  $$
  Y^+ = \sqrt{E_0 (\sigma^+ : A_0 : \sigma^+)}, \quad Y^- = \alpha I_1 + \sqrt{3 J_2} \tag{15}
  $$
  - $I_1$：有效应力第一不变量，$J_2$：偏应力第二不变量，$\alpha = 0.12$。

- **损伤阈值演化（应变率相关）：**
  $$
  \dot{r}^\pm = \mu^\pm \left\langle \frac{Y^\pm}{r^\pm} - 1 \right\rangle^{a^\pm} \tag{17}
  $$
  - $\mu^+ = 2.1 \times 10^3 \, \text{N/(s·m)}$，$\mu^- = 6.0 \times 10^{10} \, \text{N/(s·m)}$，$a^\pm = 5.5/4.5$。

- **损伤变量表达式：**
  $$
  d^\pm = 1 - \left( \frac{r_0^\pm}{r^\pm}(1-A^\pm) + A^\pm \right) \exp\left(B^\pm\left(1 - \frac{r^\pm}{r_0^\pm}\right)\right) \tag{18-19}
  $$
  - $A^\pm, B^\pm$：试验标定参数（如 $A^+ = 1.0, B^+ = 0.13$）。

---

## 3. 数值实现算法
### 3.1 应力更新算法
- **后退欧拉法更新有效应力：**
  $$
  \sigma = \zeta \sigma^{\text{trial}}, \quad \sigma^{\text{trial}} = \sigma_n + C_0 : \Delta \varepsilon \tag{29}
  $$
  $$
  \zeta = 1 - E_0 \frac{\xi^\pm H(d^\pm)}{\|\sigma^{\text{trial}}\|} \quad (\text{塑性修正系数})
  $$

### 3.2 一致性切线刚度
- **算法一致性切线模量：**
  $$
  \frac{\mathrm{d}\sigma_{\text{tot}}}{\mathrm{d}\varepsilon} = (I - \omega - R_{\text{tot}}) : C^{\text{alg}} + (I - \omega_{\text{vis}}) : C^{\text{alg}}_{\text{vis}} \tag{41}
  $$
  - $\omega$：损伤退化张量，$R_{\text{tot}}$：损伤演化贡献项。

---

## 4. 参数与验证
- **材料参数示例：**
  - $f_0^+ = 2.41 \, \text{MPa}$，$f_0^- = 10.0 \, \text{MPa}$
  - $\xi^\pm = 0.2$，阻尼系数 $\beta = 0.03$

- **KOYNA大坝验证：**
  - 动力分析中，模型无需人为提高抗拉强度，直接通过本构关系捕捉应变率效应（图1-3对比验证）。

---

## 公式总结
| 公式编号 | 主要内容           | 关键变量/参数                  |
| -------- | ------------------ | ------------------------------ |
| (1-3)    | 张量分解与投影算子 | $X^\pm, P_X^\pm, Q_X^\pm$      |
| (12)     | 总应力表达式       | $d^\pm, \text{vis}$            |
| (15)     | 损伤能释放率       | $Y^\pm, I_1, J_2$              |
| (17)     | 损伤阈值演化       | $\mu^\pm, a^\pm$               |
| (18-19)  | 损伤变量计算       | $A^\pm, B^\pm, r_0^\pm$        |
| (29)     | 应力更新算法       | $\zeta, \sigma^{\text{trial}}$ |
| (41)     | 一致性切线刚度     | $\omega, R_{\text{tot}}$       |


# 损伤阈值设置条件与更新规则

## 损伤阈值触发条件
<!-- 损伤加载条件由能释放率与阈值的关系决定 -->
根据文中公式（16）的损伤准则，损伤加载条件为：
$$
G^\pm(Y^\pm, r^\pm) = Y^\pm - r^\pm \leq 0
$$
当满足 $Y^\pm \geq r^\pm$ 时，触发损伤阈值更新。

---

## 损伤阈值更新规则
### 1. **应变率无关情况（准静态加载）**
- **阈值更新公式：**
  $$
  r^\pm_{\text{new}} = \max\left(r^\pm_{\text{old}}, Y^\pm\right) \tag{30}
  $$
  - 直接取历史最大值，反映不可逆损伤演化。

### 2. **应变率相关情况（动力加载）**
- **粘性规则化演化方程（式17）：**
  $$
  \dot{r}^\pm = \mu^\pm \left\langle \frac{Y^\pm}{r^\pm} - 1 \right\rangle^{a^\pm}
  $$
  - **触发条件：**当 $\frac{Y^\pm}{r^\pm} - 1 > 0$ 时，$\dot{r}^\pm > 0$，阈值逐步增大。
  - **参数说明：**
    - $\mu^\pm$：控制阈值更新速率的粘性系数（如 $\mu^+ = 2.1 \times 10^3 \, \text{N/(s·m)}$）。
    - $a^\pm$：非线性指数（如 $a^+ = 5.5, a^- = 4.5$）。

---

## 数值实现步骤（用于曲线拟合或有限元分析）
1. **计算当前损伤能释放率：**
   $$
   Y^\pm = \begin{cases}
   \sqrt{E_0 (\sigma^\pm : A_0 : \sigma^\pm)} & (\text{受拉}) \\
   \alpha I_1 + \sqrt{3 J_2} & (\text{受压})
   \end{cases} \tag{15}
   $$
2. **判断是否触发阈值更新：**
   $$
   \text{if } Y^\pm \geq r^\pm_{\text{current}} \Rightarrow \text{更新 } r^\pm
   $$
3. **按加载类型更新阈值：**
   - **准静态：**$r^\pm_{\text{new}} = \max(r^\pm_{\text{current}}, Y^\pm)$
   - **动力加载：**通过数值积分求解式17的微分方程（如后退欧拉法）：
     $$
     r^\pm_{n+1} = r^\pm_n + \Delta t \cdot \mu^\pm \left\langle \frac{Y^\pm_n}{r^\pm_n} - 1 \right\rangle^{a^\pm}
     $$
4. **更新损伤变量：**
   $$
   d^\pm = 1 - \left( \frac{r_0^\pm}{r^\pm}(1 - A^\pm) + A^\pm \right) \exp\left(B^\pm\left(1 - \frac{r^\pm}{r_0^\pm}\right)\right) \tag{18-19}
   $$

---

## 应用示例（曲线拟合）
- **输入数据：**试验测得的应力-应变曲线（含不同应变率）。
- **拟合流程：**
  1. 初始化 $r^\pm = r_0^\pm$（初始阈值）。
  2. 遍历应变增量，计算每一步的 $Y^\pm$。
  3. 根据 $Y^\pm \geq r^\pm$ 判断是否更新阈值：
     - 若更新，按上述规则修正 $r^\pm$。
  4. 代入式（18-19）计算 $d^\pm$，进而得到损伤修正后的应力值。
  5. 调整 $\mu^\pm, a^\pm, A^\pm, B^\pm$ 等参数，使模型预测与试验数据匹配。

---

## 关键公式总结
| 条件/规则        | 公式/表达式                                                                                                                    | 应用场景     |
| ---------------- | ------------------------------------------------------------------------------------------------------------------------------ | ------------ |
| 损伤加载触发条件 | $Y^\pm \geq r^\pm$                                                                                                             | 所有加载情况 |
| 准静态阈值更新   | $r^\pm_{\text{new}} = \max(r^\pm_{\text{old}}, Y^\pm)$                                                                         | 静力分析     |
| 动力阈值更新     | $\dot{r}^\pm = \mu^\pm \left\langle \frac{Y^\pm}{r^\pm} - 1 \right\rangle^{a^\pm}$                                             | 动力分析     |
| 损伤变量计算     | $d^\pm = 1 - \left( \frac{r_0^\pm}{r^\pm}(1-A^\pm) + A^\pm \right) \exp\left(B^\pm\left(1-\frac{r^\pm}{r_0^\pm}\right)\right)$ | 损伤演化     |