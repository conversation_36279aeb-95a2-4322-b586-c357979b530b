"""
修正版的单轴反复受拉滞回曲线预测脚本
处理模型在循环加载预测中的问题
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import json
import glob
import os
import pandas as pd

from pinn_model_v2 import DamagePINNV2, PhysicsCalculatorV2
import font_config


class CyclicTensilePredictorFixed:
    """
    修正版的单轴反复受拉滞回曲线预测器
    """
    
    def __init__(self):
        self.model = None
        self.physics_calc = None
        self.model_info = None
        self.results_dir = None
        
    def load_model(self, model_path=None):
        """
        加载训练好的模型
        """
        if model_path is None:
            # 自动寻找最新的训练文件夹
            training_dirs = glob.glob('results/training_*')
            if not training_dirs:
                raise FileNotFoundError("未找到训练结果文件夹")
            
            latest_dir = max(training_dirs, key=os.path.getctime)
            self.results_dir = latest_dir
            
            model_files = glob.glob(f'{latest_dir}/pinn_model_*.pth')
            if not model_files:
                raise FileNotFoundError(f"在 {latest_dir} 中未找到模型文件")
            model_path = model_files[0]
        else:
            self.results_dir = os.path.dirname(model_path)
        
        print(f"加载模型: {model_path}")
        
        # 加载模型信息
        self.model_info = torch.load(model_path, map_location='cpu')
        
        # 重建模型
        config = self.model_info['config']
        self.model = DamagePINNV2(
            input_size=1,
            hidden_size=config['hidden_size'],
            num_layers=config['num_layers'],
            output_size=3
        )
        
        # 加载模型权重
        self.model.load_state_dict(self.model_info['model_state_dict'])
        self.model.eval()
        
        # 重建物理计算器
        material_constants = self.model_info['material_constants']
        self.physics_calc = PhysicsCalculatorV2(
            E0=material_constants['E0'],
            f_t=material_constants['f_t']
        )
        
        print("模型加载成功!")
        print(f"  材料参数: E0={material_constants['E0']} MPa, f_t={material_constants['f_t']} MPa")
        print(f"  损伤参数: A+={self.model_info['physics_parameters']['A_plus']:.4f}, "
              f"B+={self.model_info['physics_parameters']['B_plus']:.4f}, "
              f"xi={self.model_info['physics_parameters']['xi']:.4f}")
        
        return True
    
    def generate_cyclic_loading_path(self, loading_scheme, n_points_per_segment=100):
        """
        生成循环加载路径
        
        Args:
            loading_scheme: 加载方案，格式为 [(target_strain, n_cycles), ...]
            n_points_per_segment: 每个加载/卸载段的点数
            
        Returns:
            strain_path: 完整的应变路径
            loading_info: 加载信息
        """
        strain_path = []
        loading_info = {
            'segments': [],
            'cycles': []
        }
        
        current_position = 0.0
        
        for target_strain, n_cycles in loading_scheme:
            for cycle in range(n_cycles):
                # 记录循环信息
                cycle_start_idx = len(strain_path)
                
                # 加载段：从当前位置加载到目标应变
                loading_segment = np.linspace(current_position, target_strain, n_points_per_segment)
                if len(strain_path) > 0 and current_position == 0:
                    loading_segment = loading_segment[1:]  # 避免重复零点
                
                strain_path.extend(loading_segment)
                loading_info['segments'].append({
                    'type': 'loading',
                    'start_idx': cycle_start_idx,
                    'end_idx': len(strain_path) - 1,
                    'start_strain': current_position,
                    'end_strain': target_strain
                })
                
                # 卸载段：从目标应变卸载到零
                unloading_segment = np.linspace(target_strain, 0.0, n_points_per_segment)[1:]
                strain_path.extend(unloading_segment)
                
                loading_info['segments'].append({
                    'type': 'unloading',
                    'start_idx': len(strain_path) - len(unloading_segment),
                    'end_idx': len(strain_path) - 1,
                    'start_strain': target_strain,
                    'end_strain': 0.0
                })
                
                loading_info['cycles'].append({
                    'cycle_number': len(loading_info['cycles']) + 1,
                    'target_strain': target_strain,
                    'start_idx': cycle_start_idx,
                    'end_idx': len(strain_path) - 1
                })
                
                current_position = 0.0
        
        return np.array(strain_path), loading_info
    
    def generate_realistic_cyclic_loading_path(self, loading_scheme, n_points_per_segment=100):
        """
        生成真实的循环加载路径（考虑残余应变累积）
        需要先运行一次简化预测来估算每个循环的残余应变
        """
        # 先用简化方法估算残余应变
        xi_base = self.model_info['physics_parameters']['xi']
        E0 = self.model_info['material_constants']['E0']
        f_t = self.model_info['material_constants']['f_t']
        A_plus = self.model_info['physics_parameters']['A_plus']
        B_plus = self.model_info['physics_parameters']['B_plus']
        
        strain_path = []
        loading_info = {
            'segments': [],
            'cycles': []
        }
        
        current_position = 0.0  # 当前应变位置
        accumulated_plastic_strain = 0.0  # 累积塑性应变
        accumulated_damage = 0.0  # 累积损伤
        r_max = f_t  # 损伤阈值
        
        for target_strain, n_cycles in loading_scheme:
            for cycle in range(n_cycles):
                cycle_start_idx = len(strain_path)
                
                # 加载段：从当前位置加载到目标应变
                loading_segment = np.linspace(current_position, target_strain, n_points_per_segment)
                if len(strain_path) > 0:
                    loading_segment = loading_segment[1:]  # 避免重复点
                
                # 估算本次加载的塑性应变增量
                # 使用平均损伤来估算
                strain_increment_total = target_strain - current_position
                
                # 估算加载过程中的损伤演化
                elastic_strain_peak = target_strain - accumulated_plastic_strain
                Y_peak = E0 * abs(elastic_strain_peak)
                if Y_peak > r_max:
                    r_max = Y_peak
                    term1 = f_t / r_max * (1 - A_plus)
                    term2 = A_plus * np.exp(B_plus * (1 - r_max / f_t))
                    new_damage = 1 - (term1 + term2)
                    new_damage = np.clip(new_damage, 0.0, 1.0)
                else:
                    new_damage = accumulated_damage
                
                # 使用平均损伤估算塑性应变增量
                avg_damage = (accumulated_damage + new_damage) / 2
                xi_effective = xi_base * (1 + avg_damage * 2.0)
                plastic_strain_increment = xi_effective * strain_increment_total
                accumulated_plastic_strain += plastic_strain_increment
                accumulated_damage = new_damage
                
                # 卸载终点 = 当前累积的塑性应变（残余应变）
                unload_end_strain = accumulated_plastic_strain
                
                strain_path.extend(loading_segment)
                loading_info['segments'].append({
                    'type': 'loading',
                    'start_idx': cycle_start_idx,
                    'end_idx': len(strain_path) - 1,
                    'start_strain': current_position,
                    'end_strain': target_strain
                })
                
                # 卸载段：从目标应变卸载到残余应变
                unloading_segment = np.linspace(target_strain, unload_end_strain, n_points_per_segment)[1:]
                strain_path.extend(unloading_segment)
                
                loading_info['segments'].append({
                    'type': 'unloading',
                    'start_idx': len(strain_path) - len(unloading_segment),
                    'end_idx': len(strain_path) - 1,
                    'start_strain': target_strain,
                    'end_strain': unload_end_strain
                })
                
                loading_info['cycles'].append({
                    'cycle_number': len(loading_info['cycles']) + 1,
                    'target_strain': target_strain,
                    'start_idx': cycle_start_idx,
                    'end_idx': len(strain_path) - 1,
                    'estimated_residual_strain': unload_end_strain
                })
                
                # 更新当前位置为卸载终点
                current_position = unload_end_strain
        
        return np.array(strain_path), loading_info
    
    def predict_response_fixed(self, strain_path, loading_info):
        """
        修正版的响应预测 - 改进版
        正确处理残余应变累积
        """
        # 材料参数
        E0 = self.model_info['material_constants']['E0']
        f_t = self.model_info['material_constants']['f_t']
        A_plus = self.model_info['physics_parameters']['A_plus']
        B_plus = self.model_info['physics_parameters']['B_plus']
        xi_base = self.model_info['physics_parameters']['xi']
        
        # 初始化结果数组
        n_points = len(strain_path)
        stress = np.zeros(n_points)
        damage = np.zeros(n_points)
        plastic_strain = np.zeros(n_points)
        
        # 使用基于物理的简化模型进行预测
        r_max = f_t  # 损伤阈值
        
        for i in range(1, n_points):
            # 当前应变和应变增量
            strain = strain_path[i]
            strain_increment = strain_path[i] - strain_path[i-1]
            
            # 弹性应变
            elastic_strain = strain - plastic_strain[i-1]
            
            # 计算当前的损伤驱动力
            Y = E0 * abs(elastic_strain)
            
            # 更新损伤（只在加载时）
            if strain_increment > 0 and Y > r_max:
                r_max = Y
                # 损伤演化
                term1 = f_t / r_max * (1 - A_plus)
                term2 = A_plus * np.exp(B_plus * (1 - r_max / f_t))
                damage[i] = 1 - (term1 + term2)
                damage[i] = np.clip(damage[i], 0.0, 1.0)
            else:
                damage[i] = damage[i-1]
            
            # 更新塑性应变（只在加载时）
            if strain_increment > 0:
                # 动态xi，考虑损伤影响
                xi_effective = xi_base * (1 + damage[i] * 2.0)
                plastic_strain[i] = plastic_strain[i-1] + xi_effective * strain_increment
            else:
                plastic_strain[i] = plastic_strain[i-1]
            
            # 计算应力
            stress[i] = (1 - damage[i]) * E0 * elastic_strain
            
            # 确保卸载时应力不为负
            if strain_increment < 0:
                stress[i] = max(0, stress[i])
        
        results = {
            'strain': strain_path,
            'stress': stress,
            'damage': damage,
            'plastic_strain': plastic_strain,
            'elastic_strain': strain_path - plastic_strain,
            'strain_increment': np.diff(strain_path, prepend=0.0)
        }
        
        # 计算割线模量
        results['secant_modulus'] = np.where(
            strain_path != 0,
            stress / strain_path,
            E0
        )
        
        return results
    
    def analyze_hysteresis_loops(self, results, loading_info):
        """
        分析滞回曲线特性
        """
        analysis = {
            'cycles': [],
            'overall': {}
        }
        
        # 分析每个循环
        for cycle_info in loading_info['cycles']:
            start_idx = cycle_info['start_idx']
            end_idx = cycle_info['end_idx']
            
            cycle_strain = results['strain'][start_idx:end_idx+1]
            cycle_stress = results['stress'][start_idx:end_idx+1]
            cycle_damage = results['damage'][start_idx:end_idx+1]
            
            # 找到峰值点（应力最大的点）
            peak_idx = np.argmax(cycle_stress)
            peak_strain = cycle_strain[peak_idx]
            peak_stress = cycle_stress[peak_idx]
            
            # 计算耗散能量（循环面积）
            if len(cycle_strain) > 3:
                energy_dissipated = np.abs(np.trapz(cycle_stress, cycle_strain))
            else:
                energy_dissipated = 0.0
            
            # 计算残余应变（循环结束时的塑性应变）
            residual_strain = results['plastic_strain'][end_idx]
            
            # 计算刚度退化（使用峰值点的割线刚度）
            if peak_strain > 0:
                secant_stiffness = peak_stress / peak_strain
            else:
                secant_stiffness = self.model_info['material_constants']['E0']
            
            stiffness_degradation = 1 - secant_stiffness / self.model_info['material_constants']['E0']
            
            cycle_analysis = {
                'cycle_number': cycle_info['cycle_number'],
                'target_strain': cycle_info['target_strain'],
                'peak_strain': float(peak_strain),
                'peak_stress': float(peak_stress),
                'peak_damage': float(cycle_damage[peak_idx]),
                'energy_dissipated': float(energy_dissipated),
                'residual_strain': float(residual_strain),
                'secant_stiffness': float(secant_stiffness),
                'stiffness_degradation': float(stiffness_degradation)
            }
            
            analysis['cycles'].append(cycle_analysis)
        
        # 整体分析
        analysis['overall'] = {
            'max_stress': float(np.max(results['stress'])),
            'max_damage': float(np.max(results['damage'])),
            'final_plastic_strain': float(results['plastic_strain'][-1]),
            'total_energy_dissipated': sum(c['energy_dissipated'] for c in analysis['cycles'])
        }
        
        return analysis
    
    def plot_results(self, results, loading_info, analysis, save_dir=None):
        """
        绘制完整的结果图
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if save_dir is None:
            save_dir = self.results_dir
        
        # 创建主图
        fig = plt.figure(figsize=(20, 16))
        
        # 1. 滞回曲线（占据左侧两行）
        ax1 = plt.subplot(3, 3, (1, 4))
        ax1.plot(results['strain'] * 1000, results['stress'], 'b-', linewidth=2.5)
        ax1.set_xlabel('应变 (千分比)', fontsize=14)
        ax1.set_ylabel('应力 (MPa)', fontsize=14)
        ax1.set_title('应力-应变滞回曲线', fontsize=16, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        ax1.set_xlim(-0.1, max(results['strain']) * 1000 * 1.1)
        
        # 标注峰值点
        for cycle in analysis['cycles']:
            ax1.plot(cycle['peak_strain'] * 1000, cycle['peak_stress'], 'ro', markersize=8)
            ax1.annotate(f'C{cycle["cycle_number"]}', 
                        xy=(cycle['peak_strain'] * 1000, cycle['peak_stress']),
                        xytext=(5, 5), textcoords='offset points', fontsize=10)
        
        # 2. 应力时程曲线
        ax2 = plt.subplot(3, 3, 2)
        time_points = np.arange(len(results['stress']))
        ax2.plot(time_points, results['stress'], 'b-', linewidth=1.5)
        ax2.set_xlabel('时间步', fontsize=12)
        ax2.set_ylabel('应力 (MPa)', fontsize=12)
        ax2.set_title('应力时程曲线', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)
        
        # 3. 损伤演化
        ax3 = plt.subplot(3, 3, 3)
        ax3.plot(results['strain'] * 1000, results['damage'], 'r-', linewidth=2)
        ax3.set_xlabel('应变 (千分比)', fontsize=12)
        ax3.set_ylabel('损伤变量 D', fontsize=12)
        ax3.set_title('损伤演化曲线', fontsize=14, fontweight='bold')
        ax3.grid(True, alpha=0.3)
        ax3.set_ylim(0, min(1.0, max(results['damage']) * 1.1))
        
        # 4. 塑性应变演化
        ax4 = plt.subplot(3, 3, 5)
        ax4.plot(time_points, results['plastic_strain'] * 1000, 'g-', linewidth=2, label='塑性应变')
        ax4.plot(time_points, results['elastic_strain'] * 1000, 'b--', linewidth=1.5, 
                label='弹性应变', alpha=0.7)
        ax4.set_xlabel('时间步', fontsize=12)
        ax4.set_ylabel('应变 (千分比)', fontsize=12)
        ax4.set_title('应变分量演化', fontsize=14, fontweight='bold')
        ax4.legend(fontsize=11)
        ax4.grid(True, alpha=0.3)
        
        # 5. 割线模量退化
        ax5 = plt.subplot(3, 3, 6)
        ax5.plot(time_points, results['secant_modulus'], 'k-', linewidth=2)
        ax5.axhline(y=self.model_info['material_constants']['E0'], 
                   color='r', linestyle='--', label='初始模量', alpha=0.7)
        ax5.set_xlabel('时间步', fontsize=12)
        ax5.set_ylabel('割线模量 (MPa)', fontsize=12)
        ax5.set_title('刚度退化曲线', fontsize=14, fontweight='bold')
        ax5.legend(fontsize=11)
        ax5.grid(True, alpha=0.3)
        
        # 6. 循环特性分析（柱状图）
        ax6 = plt.subplot(3, 3, 7)
        cycle_numbers = [c['cycle_number'] for c in analysis['cycles']]
        peak_stresses = [c['peak_stress'] for c in analysis['cycles']]
        
        bars = ax6.bar(cycle_numbers, peak_stresses, color='skyblue', edgecolor='navy', linewidth=1.5)
        ax6.set_xlabel('循环次数', fontsize=12)
        ax6.set_ylabel('峰值应力 (MPa)', fontsize=12)
        ax6.set_title('峰值应力演化', fontsize=14, fontweight='bold')
        ax6.grid(True, alpha=0.3, axis='y')
        ax6.set_ylim(0, max(peak_stresses) * 1.2)
        
        # 在柱上标注数值
        for bar, stress in zip(bars, peak_stresses):
            height = bar.get_height()
            ax6.text(bar.get_x() + bar.get_width()/2., height,
                    f'{stress:.2f}', ha='center', va='bottom', fontsize=10)
        
        # 7. 能量耗散
        ax7 = plt.subplot(3, 3, 8)
        energy_dissipated = [c['energy_dissipated'] for c in analysis['cycles']]
        
        bars = ax7.bar(cycle_numbers, energy_dissipated, color='orange', edgecolor='red', linewidth=1.5)
        ax7.set_xlabel('循环次数', fontsize=12)
        ax7.set_ylabel('耗散能量', fontsize=12)
        ax7.set_title('循环能量耗散', fontsize=14, fontweight='bold')
        ax7.grid(True, alpha=0.3, axis='y')
        
        # 在柱上标注数值
        for bar, energy in zip(bars, energy_dissipated):
            height = bar.get_height()
            ax7.text(bar.get_x() + bar.get_width()/2., height,
                    f'{energy:.6f}', ha='center', va='bottom', fontsize=9)
        
        # 8. 残余应变累积
        ax8 = plt.subplot(3, 3, 9)
        residual_strains = [c['residual_strain'] * 1000 for c in analysis['cycles']]
        
        ax8.plot(cycle_numbers, residual_strains, 'go-', linewidth=2, markersize=8)
        ax8.set_xlabel('循环次数', fontsize=12)
        ax8.set_ylabel('残余应变 (千分比)', fontsize=12)
        ax8.set_title('残余应变累积', fontsize=14, fontweight='bold')
        ax8.grid(True, alpha=0.3)
        
        # 在点上标注数值
        for x, y in zip(cycle_numbers, residual_strains):
            ax8.annotate(f'{y:.4f}', xy=(x, y), xytext=(0, 10), 
                        textcoords='offset points', ha='center', fontsize=10)
        
        # 添加总标题
        plt.suptitle('单轴反复受拉滞回曲线预测分析（修正版）', fontsize=18, fontweight='bold')
        plt.tight_layout()
        
        # 保存图像
        plot_path = os.path.join(save_dir, f'cyclic_tensile_analysis_fixed_{timestamp}.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"分析图已保存至: {plot_path}")
        return plot_path
    
    def save_results(self, results, loading_info, analysis, save_dir=None):
        """
        保存预测结果和分析数据
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if save_dir is None:
            save_dir = self.results_dir
        
        # 1. 保存时程数据
        df_time_history = pd.DataFrame({
            'time_step': np.arange(len(results['strain'])),
            'strain': results['strain'],
            'stress': results['stress'],
            'damage': results['damage'],
            'plastic_strain': results['plastic_strain'],
            'elastic_strain': results['elastic_strain'],
            'secant_modulus': results['secant_modulus']
        })
        
        csv_path = os.path.join(save_dir, f'cyclic_tensile_time_history_fixed_{timestamp}.csv')
        df_time_history.to_csv(csv_path, index=False)
        print(f"时程数据已保存至: {csv_path}")
        
        # 2. 保存循环分析数据
        df_cycles = pd.DataFrame(analysis['cycles'])
        cycles_csv_path = os.path.join(save_dir, f'cyclic_tensile_cycles_analysis_fixed_{timestamp}.csv')
        df_cycles.to_csv(cycles_csv_path, index=False)
        print(f"循环分析数据已保存至: {cycles_csv_path}")
        
        # 3. 保存完整分析报告
        report = {
            'model_info': {
                'material_constants': self.model_info['material_constants'],
                'physics_parameters': self.model_info['physics_parameters']
            },
            'loading_scheme': loading_info,
            'analysis': analysis,
            'timestamp': timestamp,
            'method': 'fixed_physics_based'
        }
        
        json_path = os.path.join(save_dir, f'cyclic_tensile_report_fixed_{timestamp}.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=4, ensure_ascii=False)
        print(f"分析报告已保存至: {json_path}")
        
        return {
            'time_history': csv_path,
            'cycles_analysis': cycles_csv_path,
            'report': json_path
        }


def generate_loading_data(scheme_type='standard'):
    """
    生成不同类型的加载数据
    """
    if scheme_type == 'standard':
        # 标准递增加载（根据图片中的模式）
        loading_scheme = [
            (0.0001, 2),   # 0.1‰应变，2个循环
            (0.0002, 2),   # 0.2‰应变，2个循环
            (0.0003, 2),   # 0.3‰应变，2个循环
            (0.0004, 2),   # 0.4‰应变，2个循环
            (0.0005, 2),   # 0.5‰应变，2个循环
            (0.0006, 2),   # 0.6‰应变，2个循环
        ]
    
    elif scheme_type == 'simple':
        # 简单加载方案
        loading_scheme = [
            (0.0002, 3),   # 0.2‰应变，3个循环
            (0.0004, 3),   # 0.4‰应变，3个循环
        ]
    
    elif scheme_type == 'single':
        # 单一幅值
        loading_scheme = [
            (0.0003, 5),   # 0.3‰应变，5个循环
        ]
    
    elif scheme_type == 'large':
        # 大幅值加载
        loading_scheme = [
            (0.0005, 2),   # 0.5‰应变，2个循环
            (0.0010, 2),   # 1.0‰应变，2个循环
            (0.0015, 2),   # 1.5‰应变，2个循环
        ]
    
    else:
        raise ValueError(f"未知的加载方案类型: {scheme_type}")
    
    return loading_scheme


def main():
    """
    主函数
    """
    print("=" * 80)
    print("单轴反复受拉滞回曲线预测（修正版）")
    print("=" * 80)
    
    # 创建预测器
    predictor = CyclicTensilePredictorFixed()
    
    # 加载模型
    try:
        predictor.load_model()
    except Exception as e:
        print(f"模型加载失败: {e}")
        return
    
    # 直接使用标准加载方案
    scheme_type = 'standard'
    
    # 生成加载方案
    loading_scheme = generate_loading_data(scheme_type)
    
    print(f"\n使用 {scheme_type} 加载方案:")
    for strain, cycles in loading_scheme:
        print(f"  - 最大应变: {strain:.4f} ({strain*1000:.1f}千分比), 循环次数: {cycles}")
    
    # 生成加载路径（使用真实的残余应变累积方法）
    print("\n生成真实的加载路径（考虑残余应变累积）...")
    strain_path, loading_info = predictor.generate_realistic_cyclic_loading_path(loading_scheme)
    print(f"加载路径生成完成，总点数: {len(strain_path)}")
    
    # 使用修正的预测方法
    print("\n使用修正的物理模型预测响应...")
    results = predictor.predict_response_fixed(strain_path, loading_info)
    print("预测完成")
    
    # 分析滞回曲线
    print("\n分析滞回曲线特性...")
    analysis = predictor.analyze_hysteresis_loops(results, loading_info)
    
    # 打印分析结果
    print("\n循环特性分析:")
    print("-" * 80)
    print(f"{'循环':<6} {'目标应变(千分比)':<15} {'峰值应力(MPa)':<14} {'峰值损伤':<10} {'耗散能量':<12} {'残余应变(千分比)':<15}")
    print("-" * 80)
    
    for cycle in analysis['cycles']:
        print(f"{cycle['cycle_number']:<6} "
              f"{cycle['target_strain']*1000:<15.1f} "
              f"{cycle['peak_stress']:<14.3f} "
              f"{cycle['peak_damage']:<10.4f} "
              f"{cycle['energy_dissipated']:<12.6f} "
              f"{cycle['residual_strain']*1000:<15.4f}")
    
    print("-" * 80)
    print(f"\n整体统计:")
    print(f"  最大应力: {analysis['overall']['max_stress']:.3f} MPa")
    print(f"  最大损伤: {analysis['overall']['max_damage']:.4f}")
    print(f"  最终塑性应变: {analysis['overall']['final_plastic_strain']*1000:.4f}千分比")
    print(f"  总耗散能量: {analysis['overall']['total_energy_dissipated']:.6f}")
    
    # 绘制结果
    print("\n绘制分析图...")
    predictor.plot_results(results, loading_info, analysis)
    
    # 保存结果
    print("\n保存结果数据...")
    saved_files = predictor.save_results(results, loading_info, analysis)
    
    print("\n" + "=" * 80)
    print("单轴反复受拉滞回曲线预测完成!")
    print("=" * 80)


if __name__ == "__main__":
    main() 