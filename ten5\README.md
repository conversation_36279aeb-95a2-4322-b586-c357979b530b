# 混凝土损伤参数识别PINN框架

## 项目概述

本项目实现了一个基于**物理信息神经网络(PINN)**的混凝土损伤参数识别框架，严格按照增量式输入的方式，从单条混凝土单轴拉伸试验的应力-应变曲线中，**端到端地识别**材料的损伤本构参数。

## 核心特点

- **增量式输入**: 采用应变增量序列作为输入，符合数值积分思想
- **物理约束**: 集成完整的混凝土损伤本构模型
- **GRU架构**: 使用循环神经网络处理序列数据，自然建模路径依赖性
- **端到端识别**: 自动识别材料参数A+、B+、xi
- **多损失函数**: 数据拟合、本构自洽、损伤物理、塑性物理四项损失

## 识别参数

### 待识别物理参数
- **A+**: 控制损伤演化曲线形状的材料参数
- **B+**: 控制损伤演化速率的材料参数  
- **xi**: 控制塑性应变发展的比例系数

### 已知物理常量
- **E0**: 初始弹性模量
- **f_t**: 单轴抗拉强度

## 文件结构

```
ten5/
├── main.py              # 主程序入口
├── train.py             # 训练脚本
├── predict.py           # 预测脚本
├── pinn_model.py        # PINN模型定义（原版）
├── pinn_model_v2.py     # PINN模型定义（改进版）
├── data_processor.py    # 数据处理器
├── font_config.py       # 字体配置
├── tension.xlsx         # 实验数据（示例）
├── requirements.txt     # 依赖包列表
├── README.md           # 项目说明
├── 改进版模型说明.md    # 改进版模型详细说明
├── 滞回曲线预测分析.md  # 滞回曲线功能说明
├── 模型输入输出详解.md  # 模型输入输出详细解释
├── 滞回曲线预测问题分析.md # 预测问题分析
├── 真实滞回曲线预测说明.md  # 真实滞回曲线说明
├── 单轴反复受拉预测指南.md  # 使用指南
├── predict_cyclic_tensile.py # 原始循环加载预测
├── predict_cyclic_tensile_fixed.py # 修正版预测
├── predict_cyclic_tensile_realistic.py # 真实版预测
├── diagnose_prediction.py # 诊断工具
├── generate_cyclic_input_data.py # 数据生成工具
└── results/            # 结果输出目录
```

## 安装说明

1. 安装Python依赖包：
```bash
pip install -r requirements.txt
```

2. 准备实验数据：
   - Excel文件必须包含`strain`和`stress`两列
   - 数据应为单轴拉伸试验结果
   - 应变值应单调递增

## 使用方法

### 1. 完整流程（训练+预测）
```bash
python main.py --data tension.xlsx --epochs 2000
```

### 2. 仅训练
```bash
python main.py --mode train --data tension.xlsx --epochs 2000 --lr 0.001
```

### 3. 仅预测
```bash
python main.py --mode predict --data tension.xlsx --model results/pinn_model_xxx.pth
```

### 4. 直接运行训练
```bash
python train.py
```

### 5. 直接运行预测
```bash
python predict.py
```

### 6. 生成单轴反复受拉滞回曲线

#### 生成输入数据
```bash
python generate_cyclic_input_data.py
```

#### 运行预测（三个版本）
```bash
# 原始版本（神经网络直接预测）
python predict_cyclic_tensile.py

# 修正版本（基于物理模型，卸载到0应变）
python predict_cyclic_tensile_fixed.py

# 真实版本（基于物理模型，正确处理残余应变）
python predict_cyclic_tensile_realistic.py
```

**推荐使用真实版本**，它能正确模拟：
- 卸载后的残余应变
- 损伤演化和塑性累积
- 符合物理规律的滞回环
- 能量耗散计算

## 输出结果

训练和预测完成后，所有结果将保存到`results/`目录，包括：

### 图像文件
1. **应力应变对比图**: 实验数据vs预测数据
2. **损伤变量演化图**: 损伤随应变的变化
3. **塑性应变演化图**: 塑性应变随应变的变化
4. **训练历史图**: 损失函数和参数演化
5. **综合结果图**: 包含所有对比信息

### 数据文件
1. **训练好的模型**: `pinn_model_timestamp.pth`
2. **识别参数**: `identified_parameters_timestamp.json`
3. **预测结果**: `prediction_results_timestamp.json`

## 物理模型

框架实现的增量式物理模型：

### 应变累积
```
ε_i = ε_{i-1} + Δε_i
```

### 塑性应变演化
```
Δε^p_i = xi × max(0, Δε_i)
ε^p_i = ε^p_{i-1} + Δε^p_i
```

### 本构关系
```
σ_i = (1 - d_i) × E0 × (ε_i - ε^p_i)
```

### 损伤演化
```
当 Y_i > r_{i-1} 时：
  r_i = Y_i
  d_i = 1 - (f_t/r_i) × [(1-A+) + A+ × exp[B+(1-r_i/f_t)]]
否则：
  r_i = r_{i-1}, d_i = d_{i-1}
```

## 技术架构

- **神经网络**: 2层GRU + 全连接层
- **优化器**: Adam优化器
- **损失函数**: 多项物理约束损失
- **激活函数**: Sigmoid(损伤), ReLU(塑性应变)
- **正则化**: Dropout + 梯度裁剪

## 注意事项

1. **数据格式**: Excel文件必须包含'strain'和'stress'列
2. **数据质量**: 确保数据完整，无缺失值
3. **训练时间**: 建议训练2000轮以上获得稳定结果
4. **中文字体**: 程序会自动配置中文字体，确保图像正确显示

## 示例结果

成功运行后，您将获得：
- 识别的物理参数(A+, B+, xi)
- 高精度的应力预测(典型R²>0.98)
- 完整的损伤和塑性应变演化曲线
- 详细的训练历史和参数收敛过程

## 联系方式

如有问题，请检查：
1. 数据格式是否正确
2. 依赖包是否完整安装
3. 训练轮数是否足够
4. 输入参数是否合理 