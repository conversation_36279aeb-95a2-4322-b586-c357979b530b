import numpy as np
from scipy.linalg import eigh

class ConcreteElastoPlasticDamage:
    def __init__(self, params):
        # 材料参数初始化
        self.E0 = params['E0']      # 初始弹性模量 (MPa)
        self.v0 = params['v0']      # 泊松比
        self.ft = params['ft']      # 单轴抗拉强度 (MPa)
        self.fc = params['fc']      # 单轴抗压强度 (MPa)
        self.A_plus = params['A_plus']  # 受拉损伤参数
        self.B_plus = params['B_plus']
        self.A_minus = params['A_minus'] # 受压损伤参数
        self.B_minus = params['B_minus']
        self.alpha_E_plus = params['alpha_E_plus']  # 塑性硬化参数
        self.alpha_E_minus = params['alpha_E_minus']
        
        # 计算Lamé常数
        self.mu0 = self.E0 / (2 * (1 + self.v0))  # 剪切模量
        self.lam0 = self.E0 * self.v0 / ((1 + self.v0) * (1 - 2 * self.v0))  # Lamé第一参数
        self.K0 = self.E0 / (3 * (1 - 2 * self.v0))  # 体积模量
        
        # 固定参数 (根据论文设定)
        self.alpha = 0.20       # 剪胀系数
        self.beta_f = 1.16      # 双轴/单轴抗压强度比
        self.alpha_plastic = (self.beta_f - 1) / (2 * self.beta_f - 1)  # 塑性参数
        
        # 初始化状态变量
        self.reset_state()

    def reset_state(self):
        """重置材料状态变量"""
        # 应变张量 (3x3)
        self.epsilon = np.zeros((3, 3))
        # 塑性应变张量 (3x3)
        self.epsilon_p = np.zeros((3, 3))
        # 硬化参数
        self.k_plus = 0.0   # 受拉硬化
        self.k_minus = 0.0  # 受压硬化
        # 损伤变量
        self.d_plus = 0.0   # 受拉损伤
        self.d_minus = 0.0  # 受压损伤
        # 损伤阈值
        self.r_plus = self.ft  # 初始受拉阈值
        self.r_minus = 0.3 * self.fc  # 初始受压阈值 (0.3~0.5fc)
        # 名义应力和有效应力
        self.sigma = np.zeros((3, 3))
        self.sigma_eff = np.zeros((3, 3))

    def elastic_stiffness(self):
        """计算弹性刚度张量 (4阶)"""
        C_elas = np.zeros((3, 3, 3, 3))
        for i in range(3):
            for j in range(3):
                for k in range(3):
                    for l in range(3):
                        term1 = self.lam0 * (1 if i == j and k == l else 0)
                        term2 = self.mu0 * ((1 if i == k and j == l else 0) + 
                                           (1 if i == l and j == k else 0))
                        C_elas[i, j, k, l] = term1 + term2
        return C_elas

    def stress_update(self, d_epsilon):
        """
        应力更新算法 (三步骤)
        d_epsilon: 应变增量 (3x3张量)
        """
        # 步骤1: 弹性预测
        self.epsilon += d_epsilon
        epsilon_e = self.epsilon - self.epsilon_p
        C0 = self.elastic_stiffness()
        self.sigma_eff = np.tensordot(C0, epsilon_e, axes=([2, 3], [0, 1]))
        
        # 步骤2: 塑性修正
        if self.check_yield():
            self.plastic_correction()
        
        # 步骤3: 损伤修正
        self.damage_correction()

    def check_yield(self):
        """检查屈服条件 (简化示例)"""
        # 实际实现需根据论文中的屈服函数
        sig_max = np.max(np.linalg.eigvalsh(self.sigma_eff))
        return sig_max > self.ft

    def plastic_correction(self):
        """塑性修正 (回映算法)"""
        # 1. 谱分解
        eigvals, eigvecs = eigh(self.sigma_eff)
        eigvals_trial = eigvals.copy()
        
        # 2. 迭代求解塑性流动因子 (简化示例)
        Delta_lambda = 0.01  # 实际需迭代求解
        c1 = 1 - Delta_lambda * (2 * self.mu0) / np.linalg.norm(eigvals_trial)
        c2 = Delta_lambda * (np.mean(eigvals_trial) * self.mu0 / np.linalg.norm(eigvals_trial) - 3 * self.alpha * self.K0)
        
        # 3. 更新特征值
        eigvals = c1 * eigvals_trial + c2
        
        # 4. 重建有效应力张量
        self.sigma_eff = np.dot(eigvecs, np.dot(np.diag(eigvals), eigvecs.T))
        
        # 5. 更新塑性应变和硬化参数 (简化)
        # 实际需根据塑性流动法则

    def damage_correction(self):
        """损伤修正"""
        # 1. 计算损伤能释放率
        Y_plus, Y_minus = self.calculate_damage_energy()
        
        # 2. 更新损伤阈值
        self.r_plus = max(self.r_plus, Y_plus)
        self.r_minus = max(self.r_minus, Y_minus)
        
        # 3. 更新损伤变量
        if Y_plus > self.r_plus:
            self.d_plus = self.calculate_d_plus(Y_plus)
        if Y_minus > self.r_minus:
            self.d_minus = self.calculate_d_minus(Y_minus)
        
        # 4. 计算名义应力
        self.calculate_nominal_stress()

    def calculate_damage_energy(self):
        """计算损伤能释放率 (简化示例)"""
        # 实际需根据论文公式(5)
        sig_vals = np.linalg.eigvalsh(self.sigma_eff)
        Y_plus = np.max(sig_vals)  # 最大主应力
        Y_minus = np.sqrt(np.mean(sig_vals**2))  # RMS应力
        return Y_plus, Y_minus

    def calculate_d_plus(self, Y_plus):
        """计算受拉损伤变量 (公式39a)"""
        term1 = (1 - self.A_plus) * (self.ft / Y_plus)
        term2 = np.exp(self.B_plus * (1 - Y_plus / self.ft))
        return 1 - term1 * term2 - self.A_plus

    def calculate_d_minus(self, Y_minus):
        """计算受压损伤变量 (公式39b)"""
        term1 = (1 - self.A_minus) * (0.3 * self.fc / Y_minus)
        term2 = self.A_minus * np.exp(self.B_minus * (1 - Y_minus / (0.3 * self.fc)))
        return 1 - term1 - term2

    def calculate_nominal_stress(self):
        """计算名义应力 (公式8)"""
        # 1. 应力谱分解
        eigvals, eigvecs = eigh(self.sigma_eff)
        sigma_plus = np.zeros_like(self.sigma_eff)
        sigma_minus = np.zeros_like(self.sigma_eff)
        
        for i in range(3):
            if eigvals[i] > 0:
                sigma_plus += eigvals[i] * np.outer(eigvecs[:, i], eigvecs[:, i])
            else:
                sigma_minus += eigvals[i] * np.outer(eigvecs[:, i], eigvecs[:, i])
        
        # 2. 应用损伤变量
        self.sigma = (1 - self.d_plus) * sigma_plus + (1 - self.d_minus) * sigma_minus

    def consistent_tangent(self):
        """计算一致性切线模量 (公式22)"""
        # 简化实现 - 实际需完整实现论文第3节
        C_eff = self.elastic_stiffness()  # 有效弹性刚度
        C_tang = (1 - self.d_plus) * C_eff  # 简化处理
        return C_tang


有限元程序框架 (伪代码)
class NonlinearFEA:
    def __init__(self, mesh, material):
        self.mesh = mesh          # 有限元网格
        self.material = material  # 材料模型
        self.K_global = None      # 全局刚度矩阵
        self.f_int = None         # 内力向量
        self.f_ext = None         # 外力向量

    def assemble_stiffness(self):
        """组装刚度矩阵"""
        for element in self.mesh.elements:
            # 计算单元刚度
            ke = self.element_stiffness(element)
            # 组装到全局矩阵

    def element_stiffness(self, element):
        """计算单元刚度矩阵"""
        ke = np.zeros((8, 8))  # 四节点平面应力单元
        for gp in element.integration_points:
            # 获取应变-位移矩阵B
            B = self.calculate_B_matrix(gp)
            # 获取材料切线刚度
            D = self.material.consistent_tangent()
            # 计算单元刚度贡献
            ke += gp.weight * np.dot(B.T, np.dot(D, B))
        return ke

    def newton_raphson(self, load_steps):
        """牛顿-拉夫森迭代求解"""
        for step in range(load_steps):
            # 施加荷载增量
            self.apply_load(step)
            
            # 迭代求解
            for iter in range(max_iters):
                # 组装刚度矩阵
                self.assemble_stiffness()
                
                # 计算残差
                residual = self.f_ext - self.f_int
                
                # 检查收敛
                if np.linalg.norm(residual) < tolerance:
                    break
                
                # 求解位移增量
                du = np.linalg.solve(self.K_global, residual)
                
                # 更新位移
                self.update_displacements(du)
                
                # 更新材料状态
                self.update_material_states()

    def update_material_states(self):
        """更新所有积分点的材料状态"""
        for element in self.mesh.elements:
            for gp in element.integration_points:
                # 获取应变增量
                d_epsilon = self.calculate_strain_increment(gp)
                # 更新材料状态
                gp.material.stress_update(d_epsilon)


关键算法说明
1. 应力更新三步骤：

弹性预测：计算试应力 $\sigma_{n+1}^{\text{trial}}$

塑性修正：使用谱分解回映算法
Python
# 谱分解
eigvals, eigvecs = eigh(sigma_trial)
# 更新特征值 
eigvals_updated = c1 * eigvals + c2  
# 重建应力张量
sigma_eff = np.matmul(np.matmul(eigvecs, np.diag(eigvals_updated)), eigvecs.transpose())

损伤修正：计算损伤能释放率并更新损伤变量

2. 一致性切线模量：

根据论文公式(22)计算：$\frac{d\sigma}{d\varepsilon} = (I - \omega - R) : C^{\text{alg}}$

包含损伤退化张量 $\omega$ 和损伤演化张量 $R$

3. 非线性求解：

使用Newton-Raphson迭代

位移控制加载（更易收敛）


参数标定示例
# 混凝土材料参数 (示例值)
concrete_params = {
    'E0': 30000,    # 弹性模量 (MPa)
    'v0': 0.2,       # 泊松比
    'ft': 3.0,       # 抗拉强度 (MPa)
    'fc': 30.0,      # 抗压强度 (MPa)
    'A_plus': 0.0,   # 受拉损伤参数
    'B_plus': 0.5,
    'A_minus': 1.0,  # 受压损伤参数
    'B_minus': 0.2,
    'alpha_E_plus': 19.0,  # 塑性硬化参数
    'alpha_E_minus': 2.0
}

# 初始化材料模型
concrete = ConcreteElastoPlasticDamage(concrete_params)

# 应变增量示例 (单轴受拉)
d_epsilon = np.array([
    [0.001, 0.0, 0.0],
    [0.0, -0.0003, 0.0],
    [0.0, 0.0, 0.0]
])

# 应力更新
concrete.stress_update(d_epsilon)

print("名义应力 (MPa):")
print(concrete.sigma)
print("损伤变量: d+ =", concrete.d_plus, "d- =", concrete.d_minus)

此程序实现了论文核心算法，包括：

基于能量的损伤本构框架

三阶段应力更新（弹性预测-塑性修正-损伤修正）

谱分解回映算法

损伤能释放率计算

Newton-Raphson非线性求解框架

实际应用需完善：

完整实现屈服函数和塑性流动法则

完善谱分解回映的迭代算法

完整实现一致性切线模量

添加反复荷载路径处理