import numpy as np
import torch
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from utils.data_generator import generate_training_data

def evaluate_model(model, test_data, device):
    """增强版评估PINN模型性能，添加更多评估指标
    
    Args:
        model: PINN模型
        test_data: 测试数据
        device: 计算设备
        
    Returns:
        metrics: 包含各种评估指标的字典
    """
    # 打印测试结果的函数
    def print_test_results(damage_name, metrics):
        print(f"{damage_name}测试结果:")
        for metric_name, metric_value in metrics.items():
            if isinstance(metric_value, dict):
                print(f"  {metric_name}:")
                for sub_name, sub_value in metric_value.items():
                    if isinstance(sub_value, (int, float)) and not isinstance(sub_value, bool):
                        print(f"    {sub_name}: {sub_value:.6f}")
                    else:
                        print(f"    {sub_name}: {sub_value}")
            elif isinstance(metric_value, (int, float)) and not isinstance(metric_value, bool):
                print(f"  {metric_name}: {metric_value:.6f}")
            else:
                print(f"  {metric_name}: {metric_value}")
    # 准备数据
    coords = torch.tensor(test_data['coords'], dtype=torch.float32).to(device)
    coords.requires_grad_(True)  # 确保坐标张量有梯度，用于应变和应力计算
    u_true = test_data['displacement']
    damage_true = test_data['damage'].flatten()
    
    # 模型预测
    model.eval()
    with torch.enable_grad():  # 确保在评估时启用梯度计算
        u_pred, stress_pred, damage_pred = model(coords)
        u_pred = u_pred.detach().cpu().numpy()
        stress_pred = stress_pred.detach().cpu().numpy()
        damage_pred = damage_pred.detach().cpu().numpy().flatten()
    
    # 计算位移场误差
    u_mse = mean_squared_error(u_true, u_pred)
    u_mae = mean_absolute_error(u_true, u_pred)
    u_r2 = r2_score(u_true.reshape(-1), u_pred.reshape(-1))
    u_rel_error = compute_relative_error(u_true, u_pred)  # 相对误差
    
    # 计算各分量的误差
    u_component_mse = {
        'u_x': mean_squared_error(u_true[:, 0], u_pred[:, 0]),
        'u_y': mean_squared_error(u_true[:, 1], u_pred[:, 1]),
        'u_z': mean_squared_error(u_true[:, 2], u_pred[:, 2])
    }
    
    # 计算损伤识别精度
    damage_mse = mean_squared_error(damage_true, damage_pred)
    damage_mae = mean_absolute_error(damage_true, damage_pred)
    damage_r2 = r2_score(damage_true, damage_pred)
    
    # 计算损伤识别的二分类指标（假设阈值为0.2）
    threshold = 0.2
    damage_true_binary = (damage_true > threshold).astype(int)
    damage_pred_binary = (damage_pred > threshold).astype(int)
    
    # 计算准确率
    damage_accuracy = np.mean(damage_true_binary == damage_pred_binary)
    
    # 计算损伤交并比(IoU)
    damage_localization = evaluate_damage_localization(damage_true, damage_pred, threshold)
    
    # 计算应力重构信噪比
    # 由于我们没有真实应力场，这里使用一个简化的方法估计
    # 在实际应用中，应该使用有限元分析结果作为参考
    try:
        # 重新计算一次，获取应力场
        coords_with_grad = torch.tensor(test_data['coords'], dtype=torch.float32, requires_grad=True).to(device)
        with torch.enable_grad():
            u_pred_tensor, stress_pred_tensor, _ = model(coords_with_grad)
            
            # 使用物理模型计算参考应力场
            # 这里假设损伤为0时的应力场作为参考
            # 获取损伤预测张量
            _, _, damage_pred_tensor = model(coords_with_grad)
            zero_damage = torch.zeros_like(damage_pred_tensor)
            # 确保coords_with_grad有梯度
            if not coords_with_grad.requires_grad:
                coords_with_grad.requires_grad_(True)
            stress_ref = model.compute_stress(coords_with_grad, u_pred_tensor, zero_damage)
            
            # 计算应力误差
            stress_err = stress_pred_tensor - stress_ref
            
            # 计算信噪比 SNR = 20*log10(||stress_ref||/||stress_err||)
            stress_ref_norm = torch.norm(stress_ref)
            stress_err_norm = torch.norm(stress_err)
            stress_snr = 20 * torch.log10(stress_ref_norm / (stress_err_norm + 1e-10))
            stress_snr = stress_snr.item()
    except Exception as e:
        print(f"计算应力重构信噪比时出错: {e}")
        stress_snr = float('nan')  # 使用NaN表示计算失败
    
    # 计算物理残差
    try:
        # 确保coords有梯度
        coords_for_residual = coords.clone()
        coords_for_residual.requires_grad_(True)
        # 再次检查梯度状态
        if not coords_for_residual.requires_grad:
            raise RuntimeError("Failed to set requires_grad=True on coords_for_residual")
        with torch.enable_grad():
            physics_residual = model.compute_pde_residual(coords_for_residual).detach().item()
    except Exception as e:
        print(f"计算物理残差时出错: {e}")
        physics_residual = float('nan')  # 使用NaN表示计算失败
    
    # 计算噪声鲁棒性指标
    # 添加不同级别的噪声，测试模型性能衰减
    noise_levels = [0.05, 0.1, 0.2]  # 5%, 10%, 20%
    noise_robustness = {}
    
    try:
        for noise_level in noise_levels:
            # 生成带噪声的测试数据
            noisy_test_data = generate_training_data(
                is_test=True,
                noise_level=noise_level,
                damage_mode='multi',  # 使用多点损伤模式进行测试
                stage=3  # 高级阶段
            )
            
            # 在带噪声的数据上评估模型
            coords_noisy = torch.tensor(noisy_test_data['coords'], dtype=torch.float32).to(device)
            u_true_noisy = noisy_test_data['displacement']
            
            with torch.no_grad():
                u_pred_noisy, _, damage_pred_noisy = model(coords_noisy)
                u_pred_noisy = u_pred_noisy.detach().cpu().numpy()
                damage_pred_noisy = damage_pred_noisy.detach().cpu().numpy().flatten()
            
            # 计算噪声下的位移误差
            u_mse_noisy = mean_squared_error(u_true_noisy, u_pred_noisy)
            
            # 计算噪声下的损伤识别精度
            damage_true_noisy = noisy_test_data['damage'].flatten()
            damage_mse_noisy = mean_squared_error(damage_true_noisy, damage_pred_noisy)
            
            # 计算性能衰减率
            u_degradation = (u_mse_noisy - u_mse) / u_mse
            damage_degradation = (damage_mse_noisy - damage_mse) / damage_mse
            
            noise_robustness[f'noise_{int(noise_level*100)}pct'] = {
                'u_mse': float(u_mse_noisy),
                'damage_mse': float(damage_mse_noisy),
                'u_degradation': float(u_degradation),
                'damage_degradation': float(damage_degradation)
            }
    except Exception as e:
        print(f"计算噪声鲁棒性时出错: {e}")
        noise_robustness = {'error': str(e)}
        # 确保noise_robustness是一个可以被正确格式化的字典
        for noise_level in noise_levels:
            noise_key = f'noise_{int(noise_level*100)}pct'
            if noise_key not in noise_robustness:
                noise_robustness[noise_key] = {
                    'u_mse': float('nan'),
                    'damage_mse': float('nan'),
                    'u_degradation': float('nan'),
                    'damage_degradation': float('nan')
                }
    
    # 汇总所有指标
    metrics = {
        # 位移场指标
        'displacement_mse': float(u_mse),
        'displacement_mae': float(u_mae),
        'displacement_r2': float(u_r2),
        'displacement_rel_error': float(u_rel_error),
        'displacement_component_mse': u_component_mse,
        
        # 损伤识别指标
        'damage_mse': float(damage_mse),
        'damage_mae': float(damage_mae),
        'damage_r2': float(damage_r2),
        'damage_accuracy': float(damage_accuracy),
        'damage_localization': damage_localization,
        
        # 应力重构信噪比
        'stress_snr': float(stress_snr),
        
        # 物理残差
        'physics_residual': float(physics_residual),
        
        # 噪声鲁棒性 - 将字典转换为字符串表示，避免格式化问题
        'noise_robustness': str(noise_robustness)
    }
    
    return metrics


def compute_relative_error(true, pred):
    """计算相对误差
    
    Args:
        true: 真实值
        pred: 预测值
        
    Returns:
        relative_error: 相对误差
    """
    return np.mean(np.abs(true - pred) / (np.abs(true) + 1e-8))


def evaluate_damage_localization(damage_true, damage_pred, threshold=0.2):
    """评估损伤定位性能
    
    Args:
        damage_true: 真实损伤场
        damage_pred: 预测损伤场
        threshold: 损伤阈值
        
    Returns:
        metrics: 包含损伤定位评估指标的字典
    """
    # 二值化损伤场
    damage_true_binary = (damage_true > threshold).astype(int)
    damage_pred_binary = (damage_pred > threshold).astype(int)
    
    # 计算真阳性、假阳性、真阴性、假阴性
    tp = np.sum((damage_true_binary == 1) & (damage_pred_binary == 1))
    fp = np.sum((damage_true_binary == 0) & (damage_pred_binary == 1))
    tn = np.sum((damage_true_binary == 0) & (damage_pred_binary == 0))
    fn = np.sum((damage_true_binary == 1) & (damage_pred_binary == 0))
    
    # 计算精确率、召回率、F1分数
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    accuracy = (tp + tn) / (tp + tn + fp + fn)
    
    # 计算IoU (Intersection over Union)
    iou = tp / (tp + fp + fn) if (tp + fp + fn) > 0 else 0
    
    return {
        'precision': float(precision),
        'recall': float(recall),
        'f1_score': float(f1),
        'accuracy': float(accuracy),
        'iou': float(iou)
    }