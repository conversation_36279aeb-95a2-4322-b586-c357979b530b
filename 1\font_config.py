import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import platform

def configure_chinese_font():
    """
    配置matplotlib使用中文字体
    """
    # 检测操作系统
    system = platform.system()
    
    # 设置中文字体
    if system == 'Windows':
        # Windows系统优先使用微软雅黑，其次是SimHei(黑体)
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'Arial']
    elif system == 'Darwin':  # macOS
        plt.rcParams['font.sans-serif'] = ['PingFang SC', 'Heiti SC', 'STHeiti', 'Arial']
    else:  # Linux等其他系统
        plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'Droid Sans Fallback', 'Arial']
    
    # 解决负号显示问题
    plt.rcParams['axes.unicode_minus'] = False
    
    # 设置DPI以获得更清晰的图像
    plt.rcParams['figure.dpi'] = 300
    
    # 设置全局字体大小
    plt.rcParams['font.size'] = 12
    plt.rcParams['axes.titlesize'] = 14
    plt.rcParams['axes.labelsize'] = 12
    
    print(f"已配置中文字体: {plt.rcParams['font.sans-serif'][0]}")

# 当直接运行此文件时，测试字体配置
if __name__ == "__main__":
    configure_chinese_font()
    
    # 创建一个测试图表
    plt.figure(figsize=(10, 6))
    plt.plot([1, 2, 3, 4], [1, 4, 9, 16], 'ro-')
    plt.title('中文标题测试')
    plt.xlabel('横坐标 (x)')
    plt.ylabel('纵坐标 (y)')
    plt.grid(True)
    
    # 保存测试图表
    test_image_path = os.path.join(os.path.dirname(__file__), 'font_test.png')
    plt.savefig(test_image_path)
    print(f"测试图表已保存至: {test_image_path}")
    
    # 显示图表
    plt.show()