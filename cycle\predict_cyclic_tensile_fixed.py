"""
修正版的单轴反复受拉滞回曲线预测脚本
处理模型在循环加载预测中的问题
现在支持受拉受压的全滞回曲线预测
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import json
import glob
import os
import pandas as pd

from pinn_model_v2 import DamagePINNV2, PhysicsCalculatorV2
import font_config # 确保字体配置已导入


class CyclicTensilePredictorFixed: # 保持原文件名，但功能已扩展
    """
    修正版的单轴反复受拉滞回曲线预测器
    现在支持受拉受压的全滞回曲线预测
    """
    
    def __init__(self):
        self.model = None
        self.physics_calc = None # 在预测时不直接使用PhysicsCalculator的增量式计算，而是其逻辑
        self.model_info = None
        self.results_dir = None
        
    def load_model(self, model_path=None):
        """
        加载训练好的模型，现在加载所有受拉受压参数
        """
        if model_path is None:
            # 自动寻找最新的训练文件夹
            training_dirs = glob.glob('results/training_*')
            if not training_dirs:
                raise FileNotFoundError("未找到训练结果文件夹")
            
            latest_dir = max(training_dirs, key=os.path.getctime)
            self.results_dir = latest_dir
            
            model_files = glob.glob(f'{latest_dir}/pinn_model_*.pth')
            if not model_files:
                raise FileNotFoundError(f"在 {latest_dir} 中未找到模型文件")
            model_path = model_files[0]
        else:
            self.results_dir = os.path.dirname(model_path)
        
        print(f"加载模型: {model_path}")
        
        # 加载模型信息
        self.model_info = torch.load(model_path, map_location='cpu')
        
        # 重建模型
        config = self.model_info['config']
        self.model = DamagePINNV2(
            input_size=1,
            hidden_size=config['hidden_size'],
            num_layers=config['num_layers'],
            output_size=3
        )
        
        # 加载模型权重
        self.model.load_state_dict(self.model_info['model_state_dict'])
        self.model.eval() # 设置为评估模式
        
        # 获取材料常数和识别出的物理参数
        self.E0 = self.model_info['material_constants']['E0']
        self.f_t = self.model_info['material_constants']['f_t']
        self.f_c = self.model_info['material_constants']['f_c'] # 新增 f_c
        
        self.A_plus = self.model_info['physics_parameters']['A_plus']
        self.B_plus = self.model_info['physics_parameters']['B_plus']
        self.xi_plus = self.model_info['physics_parameters']['xi_plus'] # 新增
        
        self.A_minus = self.model_info['physics_parameters']['A_minus'] # 新增
        self.B_minus = self.model_info['physics_parameters']['B_minus'] # 新增
        self.xi_minus = self.model_info['physics_parameters']['xi_minus'] # 新增
        
        print("模型加载成功!")
        print(f"  材料参数: E0={self.E0:.2f} MPa, f_t={self.f_t:.2f} MPa, f_c={self.f_c:.2f} MPa")
        print(f"  损伤参数 (拉伸): A+={self.A_plus:.4f}, B+={self.B_plus:.4f}, xi+={self.xi_plus:.4f}")
        print(f"  损伤参数 (压缩): A-={self.A_minus:.4f}, B-={self.B_minus:.4f}, xi-={self.xi_minus:.4f}")
        
        return True
    
    def generate_cyclic_loading_path(self, loading_scheme, n_points_per_segment=100):
        """
        生成循环加载路径（基础版，不考虑真实残余应变，仅用于应变控制）
        用于生成预测的应变输入路径
        
        Args:
            loading_scheme: 加载方案，格式为 [(target_strain, n_cycles), ...]
                            target_strain 可以是正（拉伸）或负（压缩）
            n_points_per_segment: 每个加载/卸载段的点数
            
        Returns:
            strain_path: 完整的应变路径 numpy array
            loading_info: 加载信息 (段落和循环)
        """
        strain_path = []
        loading_info = {
            'segments': [],
            'cycles': []
        }
        
        current_position = 0.0 # 当前应变位置
        
        for target_strain, n_cycles in loading_scheme:
            for cycle in range(n_cycles):
                cycle_start_idx = len(strain_path)
                
                # 加载段：从当前位置加载到目标应变
                loading_segment = np.linspace(current_position, target_strain, n_points_per_segment)
                if len(strain_path) > 0: # 避免重复起始点，除非是第一个点从0开始
                    loading_segment = loading_segment[1:]
                
                strain_path.extend(loading_segment)
                loading_info['segments'].append({
                    'type': 'loading',
                    'start_idx': cycle_start_idx,
                    'end_idx': len(strain_path) - 1,
                    'start_strain': current_position,
                    'end_strain': target_strain
                })
                
                # 卸载段：从目标应变卸载回零点（或原点）
                # 这里假设卸载回0，如果需要考虑残余应变，则需要更复杂的预测模型
                unloading_segment = np.linspace(target_strain, 0.0, n_points_per_segment)[1:]
                strain_path.extend(unloading_segment)
                
                loading_info['segments'].append({
                    'type': 'unloading',
                    'start_idx': len(strain_path) - len(unloading_segment),
                    'end_idx': len(strain_path) - 1,
                    'start_strain': target_strain,
                    'end_strain': 0.0
                })
                
                loading_info['cycles'].append({
                    'cycle_number': len(loading_info['cycles']) + 1,
                    'target_strain': target_strain,
                    'start_idx': cycle_start_idx,
                    'end_idx': len(strain_path) - 1
                })
                
                current_position = 0.0 # 卸载回零
        
        return np.array(strain_path), loading_info
    
    def generate_realistic_cyclic_loading_path(self, loading_scheme, n_points_per_segment=100):
        """
        生成真实的循环加载路径（考虑残余应变累积）
        根据当前模型识别的xi_plus和xi_minus估算残余应变
        """
        strain_path = []
        loading_info = {
            'segments': [],
            'cycles': []
        }
        
        current_position = 0.0  # 当前应变位置
        accumulated_plastic_strain = 0.0  # 累积塑性应变
        
        # 为了估算，使用简化的损伤累积逻辑 (不用于最终预测，仅用于路径生成)
        # 实际损伤演化在 predict_response_full 中处理
        current_d_plus = 0.0
        current_d_minus = 0.0
        r_max_plus_est = self.f_t
        r_max_minus_est = self.f_c # 估算时用fc作为初始阈值

        for target_strain, n_cycles in loading_scheme:
            for cycle in range(n_cycles):
                cycle_start_idx = len(strain_path)
                
                # 加载段：从当前位置加载到目标应变
                loading_segment = np.linspace(current_position, target_strain, n_points_per_segment)
                if len(strain_path) > 0:
                    loading_segment = loading_segment[1:]  # 避免重复点
                
                # 估算本次加载的塑性应变增量和残余应变
                delta_strain_load = target_strain - current_position
                
                # 估算加载过程中的损伤和塑性 (简化处理，仅用于路径生成)
                # 这部分逻辑需要与PhysicsCalculatorV2中的逻辑保持一致，用于路径估算
                
                # 估算累积塑性应变
                estimated_delta_ep = 0.0
                if delta_strain_load > 0: # 拉伸加载
                    # 简化：假设拉伸塑性主要发生在受拉峰值附近
                    estimated_delta_ep = self.xi_plus * delta_strain_load 
                elif delta_strain_load < 0: # 压缩加载
                    # 简化：假设压缩塑性主要发生在受压峰值附近
                    estimated_delta_ep = self.xi_minus * delta_strain_load # xi_minus为正，delta_strain_load为负，所以delta_ep为负
                
                accumulated_plastic_strain += estimated_delta_ep
                
                # 卸载终点 = 当前累积的塑性应变（残余应变）
                unload_end_strain = accumulated_plastic_strain
                
                strain_path.extend(loading_segment)
                loading_info['segments'].append({
                    'type': 'loading',
                    'start_idx': cycle_start_idx,
                    'end_idx': len(strain_path) - 1,
                    'start_strain': current_position,
                    'end_strain': target_strain
                })
                
                # 卸载段：从目标应变卸载到残余应变
                unloading_segment = np.linspace(target_strain, unload_end_strain, n_points_per_segment)[1:]
                strain_path.extend(unloading_segment)
                
                loading_info['segments'].append({
                    'type': 'unloading',
                    'start_idx': len(strain_path) - len(unloading_segment),
                    'end_idx': len(strain_path) - 1,
                    'start_strain': target_strain,
                    'end_strain': unload_end_strain
                })
                
                loading_info['cycles'].append({
                    'cycle_number': len(loading_info['cycles']) + 1,
                    'target_strain': target_strain,
                    'start_idx': cycle_start_idx,
                    'end_idx': len(strain_path) - 1,
                    'estimated_residual_strain': unload_end_strain
                })
                
                # 更新当前位置为卸载终点
                current_position = unload_end_strain
        
        return np.array(strain_path), loading_info
    
    def predict_response_full(self, strain_path): # 预测全滞回曲线
        """
        修正版的响应预测 - 全滞回曲线
        使用识别出的拉压损伤和塑性参数，进行物理演化
        """
        n_points = len(strain_path)
        
        # 初始化结果数组
        stress = np.zeros(n_points)
        damage_plus = np.zeros(n_points)   # 拉伸损伤
        damage_minus = np.zeros(n_points)  # 压缩损伤
        plastic_strain = np.zeros(n_points) # 累积塑性应变
        elastic_strain = np.zeros(n_points) # 弹性应变

        # 初始化物理状态变量
        current_epsilon_total = 0.0
        current_ep_phy = 0.0
        current_d_plus_phy = 0.0
        current_d_minus_phy = 0.0
        
        # 历史最大损伤阈值
        r_max_plus_phy = self.f_t
        r_max_minus_phy = self.f_c # 初始压缩阈值，可根据文献调整为 (1-alpha)*f_c

        # 遍历应变路径
        for i in range(n_points):
            delta_epsilon = strain_path[i] - (0.0 if i == 0 else strain_path[i-1])
            
            # 1. 更新总应变
            current_epsilon_total += delta_epsilon
            
            # 2. 计算当前有效应变 (弹性应变)
            current_elastic_strain = current_epsilon_total - current_ep_phy
            
            # 3. 损伤演化逻辑 (受拉和受压独立判断)
            # 拉伸损伤演化
            if current_elastic_strain > 1e-10: # 处于拉伸状态
                Y_plus_current = self.E0 * current_elastic_strain
                if Y_plus_current > r_max_plus_phy: # 突破历史最大拉伸损伤阈值
                    r_max_plus_phy = Y_plus_current
                    term1_plus = self.f_t / r_max_plus_phy * (1 - self.A_plus)
                    term2_plus = self.A_plus * np.exp(self.B_plus * (1 - r_max_plus_phy / self.f_t))
                    current_d_plus_phy = 1 - (term1_plus + term2_plus)
                    current_d_plus_phy = np.clip(current_d_plus_phy, 0.0, 1.0)
                # 否则 current_d_plus_phy 保持不变 (卸载或未达到新阈值)
            # 压缩损伤演化
            elif current_elastic_strain < -1e-10: # 处于压缩状态
                Y_minus_current = self.E0 * abs(current_elastic_strain)
                if Y_minus_current > r_max_minus_phy: # 突破历史最大压缩损伤阈值
                    r_max_minus_phy = Y_minus_current
                    term1_minus = self.f_c / r_max_minus_phy * (1 - self.A_minus)
                    term2_minus = self.A_minus * np.exp(self.B_minus * (1 - r_max_minus_phy / self.f_c))
                    current_d_minus_phy = 1 - (term1_minus + term2_minus)
                    current_d_minus_phy = np.clip(current_d_minus_phy, 0.0, 1.0)
                # 否则 current_d_minus_phy 保持不变

            # 4. 塑性应变演化
            delta_ep = 0.0
            if delta_epsilon > 0: # 拉伸加载
                delta_ep = self.xi_plus * delta_epsilon
            elif delta_epsilon < 0: # 压缩加载
                delta_ep = self.xi_minus * delta_epsilon # xi_minus通常为正，delta_epsilon为负，导致delta_ep为负
            
            current_ep_phy += delta_ep # 累积塑性应变
            
            # 5. 计算应力
            # 根据当前有效应变的正负选择对应的损伤变量
            current_d_effective = 0.0
            if current_elastic_strain >= 0: # 拉伸区，使用拉伸损伤
                current_d_effective = current_d_plus_phy
            else: # 压缩区，使用压缩损伤
                current_d_effective = current_d_minus_phy

            # 最终应力
            current_stress = (1 - current_d_effective) * self.E0 * current_elastic_strain
            
            # 6. 存储结果
            stress[i] = current_stress
            damage_plus[i] = current_d_plus_phy
            damage_minus[i] = current_d_minus_phy
            plastic_strain[i] = current_ep_phy
            elastic_strain[i] = current_elastic_strain

        results = {
            'strain': strain_path,
            'stress': stress,
            'damage_plus': damage_plus,   # 新增
            'damage_minus': damage_minus, # 新增
            'plastic_strain': plastic_strain,
            'elastic_strain': elastic_strain,
            'strain_increment': np.diff(strain_path, prepend=0.0)
        }
        
        # 计算割线模量
        results['secant_modulus'] = np.where(
            results['strain'] != 0,
            results['stress'] / results['strain'],
            self.E0 # 应变为0时，模量为初始模量
        )
        
        return results
    
    def analyze_hysteresis_loops(self, results, loading_info):
        """
        分析滞回曲线特性
        (此函数可根据需要进一步扩展，以处理拉压循环的特定指标)
        """
        analysis = {
            'cycles': [],
            'overall': {}
        }
        
        # 分析每个循环
        for cycle_info in loading_info['cycles']:
            start_idx = cycle_info['start_idx']
            end_idx = cycle_info['end_idx']
            
            cycle_strain = results['strain'][start_idx:end_idx+1]
            cycle_stress = results['stress'][start_idx:end_idx+1]
            cycle_damage_plus = results['damage_plus'][start_idx:end_idx+1] # 新增
            cycle_damage_minus = results['damage_minus'][start_idx:end_idx+1] # 新增
            
            # 找到峰值点（应力绝对值最大的点）
            peak_abs_idx = np.argmax(np.abs(cycle_stress))
            peak_strain = cycle_strain[peak_abs_idx]
            peak_stress = cycle_stress[peak_abs_idx]
            
            # 找到循环结束时的残余应变
            residual_strain = results['plastic_strain'][end_idx]
            
            # 耗散能量（循环面积）
            if len(cycle_strain) > 3:
                energy_dissipated = np.abs(np.trapz(cycle_stress, cycle_strain))
            else:
                energy_dissipated = 0.0
            
            cycle_analysis = {
                'cycle_number': cycle_info['cycle_number'],
                'target_strain': cycle_info['target_strain'],
                'peak_strain': float(peak_strain),
                'peak_stress': float(peak_stress),
                'peak_damage_plus': float(cycle_damage_plus[peak_abs_idx]), # 峰值拉伸损伤
                'peak_damage_minus': float(cycle_damage_minus[peak_abs_idx]), # 峰值压缩损伤
                'energy_dissipated': float(energy_dissipated),
                'residual_strain': float(residual_strain),
                # 刚度退化计算可能需要更复杂，例如峰值拉伸刚度或压缩刚度
            }
            
            analysis['cycles'].append(cycle_analysis)
        
        # 整体分析
        analysis['overall'] = {
            'max_stress_abs': float(np.max(np.abs(results['stress']))), # 最大应力绝对值
            'max_stress_tensile': float(np.max(results['stress'][results['stress']>=0])), # 最大拉应力
            'min_stress_compressive': float(np.min(results['stress'][results['stress']<0])) if np.any(results['stress']<0) else 0.0, # 最小压应力
            'max_damage_plus': float(np.max(results['damage_plus'])),
            'max_damage_minus': float(np.max(results['damage_minus'])),
            'final_plastic_strain': float(results['plastic_strain'][-1]),
            'total_energy_dissipated': sum(c['energy_dissipated'] for c in analysis['cycles'])
        }
        
        return analysis
    
    def plot_results(self, results, loading_info, analysis, save_dir=None):
        """
        绘制完整的结果图
        现在包含拉压滞回曲线和独立的损伤演化图
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if save_dir is None:
            save_dir = self.results_dir
        
        # 创建主图
        fig = plt.figure(figsize=(20, 16))
        
        # 1. 应力-应变滞回曲线 (占据左侧两行)
        ax1 = plt.subplot(3, 3, (1, 4))
        ax1.plot(results['strain'] * 1000, results['stress'], 'b-', linewidth=2.5, label='PINN预测')
        # 如果有实验数据，在此添加 plot experiment_data
        # ax1.plot(exp_strain * 1000, exp_stress, 'r--', linewidth=1.5, label='实验数据', alpha=0.7)
        ax1.set_xlabel('应变 (千分比)', fontsize=14)
        ax1.set_ylabel('应力 (MPa)', fontsize=14)
        ax1.set_title('应力-应变滞回曲线', fontsize=16, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        ax1.legend(fontsize=12)
        # 根据数据动态设置 x 和 y 轴范围，确保显示完整滞回环
        min_strain_val = np.min(results['strain']) * 1000
        max_strain_val = np.max(results['strain']) * 1000
        min_stress_val = np.min(results['stress'])
        max_stress_val = np.max(results['stress'])
        ax1.set_xlim(min_strain_val * 1.1, max_strain_val * 1.1)
        ax1.set_ylim(min_stress_val * 1.1, max_stress_val * 1.1)
        
        # 2. 应力时程曲线
        ax2 = plt.subplot(3, 3, 2)
        time_points = np.arange(len(results['stress']))
        ax2.plot(time_points, results['stress'], 'b-', linewidth=1.5)
        ax2.set_xlabel('时间步', fontsize=12)
        ax2.set_ylabel('应力 (MPa)', fontsize=12)
        ax2.set_title('应力时程曲线', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)
        
        # 3. 损伤演化 (拉伸和压缩损伤分开绘制)
        ax3 = plt.subplot(3, 3, 3)
        ax3.plot(results['strain'] * 1000, results['damage_plus'], 'r-', linewidth=2, label='拉伸损伤 $D^+$')
        ax3.plot(results['strain'] * 1000, results['damage_minus'], 'b--', linewidth=1.5, label='压缩损伤 $D^-$', alpha=0.7)
        ax3.set_xlabel('应变 (千分比)', fontsize=12)
        ax3.set_ylabel('损伤变量 D', fontsize=12)
        ax3.set_title('损伤演化曲线', fontsize=14, fontweight='bold')
        ax3.grid(True, alpha=0.3)
        ax3.set_ylim(0, 1.0) # 损伤变量范围 [0, 1]
        ax3.legend(fontsize=11)
        
        # 4. 塑性应变演化
        ax4 = plt.subplot(3, 3, 5)
        ax4.plot(time_points, results['plastic_strain'] * 1000, 'g-', linewidth=2, label='累积塑性应变')
        ax4.plot(time_points, results['elastic_strain'] * 1000, 'b--', linewidth=1.5, 
                label='弹性应变', alpha=0.7)
        ax4.set_xlabel('时间步', fontsize=12)
        ax4.set_ylabel('应变 (千分比)', fontsize=12)
        ax4.set_title('应变分量演化', fontsize=14, fontweight='bold')
        ax4.legend(fontsize=11)
        ax4.grid(True, alpha=0.3)
        
        # 5. 割线模量退化
        ax5 = plt.subplot(3, 3, 6)
        ax5.plot(time_points, results['secant_modulus'], 'k-', linewidth=2)
        ax5.axhline(y=self.E0, color='r', linestyle='--', label='初始模量', alpha=0.7)
        ax5.set_xlabel('时间步', fontsize=12)
        ax5.set_ylabel('割线模量 (MPa)', fontsize=12)
        ax5.set_title('刚度退化曲线', fontsize=14, fontweight='bold')
        ax5.legend(fontsize=11)
        ax5.grid(True, alpha=0.3)
        
        # 6. 循环峰值应力演化
        ax6 = plt.subplot(3, 3, 7)
        cycle_numbers = [c['cycle_number'] for c in analysis['cycles']]
        peak_stresses = [c['peak_stress'] for c in analysis['cycles']] # 峰值应力绝对值
        
        bars = ax6.bar(cycle_numbers, peak_stresses, color='skyblue', edgecolor='navy', linewidth=1.5)
        ax6.set_xlabel('循环次数', fontsize=12)
        ax6.set_ylabel('峰值应力 (MPa)', fontsize=12)
        ax6.set_title('峰值应力演化', fontsize=14, fontweight='bold')
        ax6.grid(True, alpha=0.3, axis='y')
        ax6.set_ylim(0, max(peak_stresses) * 1.2)
        
        for bar, stress_val in zip(bars, peak_stresses):
            height = bar.get_height()
            ax6.text(bar.get_x() + bar.get_width()/2., height,
                    f'{stress_val:.2f}', ha='center', va='bottom', fontsize=10)
        
        # 7. 能量耗散
        ax7 = plt.subplot(3, 3, 8)
        energy_dissipated = [c['energy_dissipated'] for c in analysis['cycles']]
        
        bars = ax7.bar(cycle_numbers, energy_dissipated, color='orange', edgecolor='red', linewidth=1.5)
        ax7.set_xlabel('循环次数', fontsize=12)
        ax7.set_ylabel('耗散能量', fontsize=12)
        ax7.set_title('循环能量耗散', fontsize=14, fontweight='bold')
        ax7.grid(True, alpha=0.3, axis='y')
        
        for bar, energy in zip(bars, energy_dissipated):
            height = bar.get_height()
            ax7.text(bar.get_x() + bar.get_width()/2., height,
                    f'{energy:.6f}', ha='center', va='bottom', fontsize=9)
        
        # 8. 残余应变累积
        ax8 = plt.subplot(3, 3, 9)
        residual_strains = [c['residual_strain'] * 1000 for c in analysis['cycles']]
        
        ax8.plot(cycle_numbers, residual_strains, 'go-', linewidth=2, markersize=8)
        ax8.set_xlabel('循环次数', fontsize=12)
        ax8.set_ylabel('残余应变 (千分比)', fontsize=12)
        ax8.set_title('残余应变累积', fontsize=14, fontweight='bold')
        ax8.grid(True, alpha=0.3)
        
        for x, y in zip(cycle_numbers, residual_strains):
            ax8.annotate(f'{y:.4f}', xy=(x, y), xytext=(0, 10), 
                        textcoords='offset points', ha='center', fontsize=10)
        
        plt.suptitle('单轴反复拉压滞回曲线预测分析', fontsize=18, fontweight='bold') # 更改总标题
        plt.tight_layout(rect=[0, 0.03, 1, 0.96]) # 调整布局以适应总标题
        
        plot_path = os.path.join(save_dir, f'cyclic_tensile_analysis_full_{timestamp}.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"分析图已保存至: {plot_path}")
        return plot_path
    
    def save_results(self, results, loading_info, analysis, save_dir=None):
        """
        保存预测结果和分析数据
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if save_dir is None:
            save_dir = self.results_dir
        
        # 1. 保存时程数据
        df_time_history = pd.DataFrame({
            'time_step': np.arange(len(results['strain'])),
            'strain': results['strain'],
            'stress': results['stress'],
            'damage_plus': results['damage_plus'],   # 新增
            'damage_minus': results['damage_minus'], # 新增
            'plastic_strain': results['plastic_strain'],
            'elastic_strain': results['elastic_strain'],
            'secant_modulus': results['secant_modulus']
        })
        
        csv_path = os.path.join(save_dir, f'cyclic_tensile_time_history_full_{timestamp}.csv')
        df_time_history.to_csv(csv_path, index=False)
        print(f"时程数据已保存至: {csv_path}")
        
        # 2. 保存循环分析数据
        df_cycles = pd.DataFrame(analysis['cycles'])
        cycles_csv_path = os.path.join(save_dir, f'cyclic_tensile_cycles_analysis_full_{timestamp}.csv')
        df_cycles.to_csv(cycles_csv_path, index=False)
        print(f"循环分析数据已保存至: {cycles_csv_path}")
        
        # 3. 保存完整分析报告
        report = {
            'model_info': {
                'material_constants': {
                    'E0': self.E0, 'f_t': self.f_t, 'f_c': self.f_c
                },
                'physics_parameters': {
                    'A_plus': self.A_plus, 'B_plus': self.B_plus, 'xi_plus': self.xi_plus,
                    'A_minus': self.A_minus, 'B_minus': self.B_minus, 'xi_minus': self.xi_minus
                }
            },
            'loading_scheme': loading_info,
            'analysis': analysis,
            'timestamp': timestamp,
            'method': 'full_physics_based'
        }
        
        json_path = os.path.join(save_dir, f'cyclic_tensile_report_full_{timestamp}.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=4, ensure_ascii=False)
        print(f"分析报告已保存至: {json_path}")
        
        return {
            'time_history': csv_path,
            'cycles_analysis': cycles_csv_path,
            'report': json_path
        }


def generate_loading_data(scheme_type='standard_tension_compression'):
    """
    生成不同类型的循环加载数据，现在包含拉压循环
    """
    if scheme_type == 'standard_tension_compression':
        # 标准递增加载（先拉后压，或拉压交替）
        loading_scheme = [
            (0.0001, 1),   # 0.1‰应变，1个拉伸循环
            (-0.0001, 1),  # 0.1‰应变，1个压缩循环
            (0.0002, 1),   # 0.2‰应变，1个拉伸循环
            (-0.0002, 1),  # 0.2‰应变，1个压缩循环
            (0.0003, 1),   # 0.3‰应变，1个拉伸循环
            (-0.0003, 1),  # 0.3‰应变，1个压缩循环
        ]
    elif scheme_type == 'large_tension_compression':
        loading_scheme = [
            (0.0005, 2),
            (-0.0005, 2),
            (0.0010, 2),
            (-0.0010, 2),
        ]
    # 你可以根据需要添加更多加载方案
    
    else: # 保持原有的拉伸方案
        # 标准递增加载
        loading_scheme = [
            (0.0001, 2),
            (0.0002, 2),
            (0.0003, 2),
            (0.0004, 2),
            (0.0005, 2),
            (0.0006, 2),
        ]
    
    return loading_scheme


def main():
    """
    主函数
    """
    print("=" * 80)
    print("单轴反复拉压滞回曲线预测 (全功能版)")
    print("=" * 80)
    
    # 创建预测器
    predictor = CyclicTensilePredictorFixed()
    
    # 加载模型
    try:
        predictor.load_model()
    except Exception as e:
        print(f"模型加载失败: {e}")
        return
    
    # 直接使用包含拉压的加载方案
    scheme_type = 'standard_tension_compression'
    
    # 生成加载方案
    loading_scheme = generate_loading_data(scheme_type)
    
    print(f"\n使用 {scheme_type} 加载方案:")
    for strain, cycles in loading_scheme:
        print(f"  - 目标应变: {strain:.4f} ({strain*1000:.1f}千分比), 循环次数: {cycles}")
    
    # 生成加载路径（使用真实的残余应变累积方法）
    print("\n生成真实的加载路径（考虑残余应变累积）...")
    strain_path, loading_info = predictor.generate_realistic_cyclic_loading_path(loading_scheme)
    print(f"加载路径生成完成，总点数: {len(strain_path)}")
    
    # 使用修正的预测方法
    print("\n使用物理模型预测全滞回响应...")
    results = predictor.predict_response_full(strain_path) # 调用新的预测函数
    print("预测完成")
    
    # 分析滞回曲线
    print("\n分析滞回曲线特性...")
    analysis = predictor.analyze_hysteresis_loops(results, loading_info)
    
    # 打印分析结果
    print("\n循环特性分析:")
    print("-" * 80)
    print(f"{'循环':<6} {'目标应变(‰)':<15} {'峰值应力(MPa)':<14} {'峰值D+':<10} {'峰值D-':<10} {'耗散能量':<12} {'残余应变(‰)':<15}")
    print("-" * 80)
    
    for cycle in analysis['cycles']:
        print(f"{cycle['cycle_number']:<6} "
              f"{cycle['target_strain']*1000:<15.1f} "
              f"{cycle['peak_stress']:<14.3f} "
              f"{cycle['peak_damage_plus']:<10.4f} " # 打印拉伸损伤
              f"{cycle['peak_damage_minus']:<10.4f} " # 打印压缩损伤
              f"{cycle['energy_dissipated']:<12.6f} "
              f"{cycle['residual_strain']*1000:<15.4f}")
    
    print("-" * 80)
    print(f"\n整体统计:")
    print(f"  最大应力绝对值: {analysis['overall']['max_stress_abs']:.3f} MPa")
    print(f"  最大拉应力: {analysis['overall']['max_stress_tensile']:.3f} MPa")
    print(f"  最小压应力: {analysis['overall']['min_stress_compressive']:.3f} MPa")
    print(f"  最大拉伸损伤: {analysis['overall']['max_damage_plus']:.4f}")
    print(f"  最大压缩损伤: {analysis['overall']['max_damage_minus']:.4f}")
    print(f"  最终塑性应变: {analysis['overall']['final_plastic_strain']*1000:.4f}‰")
    print(f"  总耗散能量: {analysis['overall']['total_energy_dissipated']:.6f}")
    
    # 绘制结果
    print("\n绘制分析图...")
    predictor.plot_results(results, loading_info, analysis)
    
    # 保存结果
    print("\n保存结果数据...")
    saved_files = predictor.save_results(results, loading_info, analysis)
    
    print("\n" + "=" * 80)
    print("单轴反复拉压滞回曲线预测完成!")
    print("=" * 80)


if __name__ == "__main__":
    main()
