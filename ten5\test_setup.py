"""
环境设置测试脚本
验证数据处理和字体配置是否正常工作
"""

import sys
import os
from pathlib import Path

def test_imports():
    """
    测试所有必要的包是否可以正常导入
    """
    print("测试导入包...")
    
    try:
        import torch
        print(f"  ✓ PyTorch {torch.__version__}")
    except ImportError:
        print("  ✗ PyTorch 导入失败")
        return False
    
    try:
        import numpy as np
        print(f"  ✓ NumPy {np.__version__}")
    except ImportError:
        print("  ✗ NumPy 导入失败")
        return False
    
    try:
        import pandas as pd
        print(f"  ✓ Pandas {pd.__version__}")
    except ImportError:
        print("  ✗ Pandas 导入失败")
        return False
    
    try:
        import matplotlib.pyplot as plt
        print(f"  ✓ Matplotlib {plt.matplotlib.__version__}")
    except ImportError:
        print("  ✗ Matplotlib 导入失败")
        return False
    
    try:
        import openpyxl
        print(f"  ✓ OpenPyXL {openpyxl.__version__}")
    except ImportError:
        print("  ✗ OpenPyXL 导入失败")
        return False
    
    return True

def test_font_config():
    """
    测试中文字体配置
    """
    print("\n测试中文字体配置...")
    
    try:
        import font_config
        print("  ✓ 字体配置模块导入成功")
        
        # 运行字体测试，保存到results目录
        if font_config.test_chinese_display('results/font_test.png'):
            print("  ✓ 中文字体测试通过")
            return True
        else:
            print("  ✗ 中文字体测试失败")
            return False
    except Exception as e:
        print(f"  ✗ 字体配置失败: {e}")
        return False

def test_data_processor():
    """
    测试数据处理器
    """
    print("\n测试数据处理器...")
    
    # 检查数据文件是否存在
    if not os.path.exists("tension.xlsx"):
        print("  ✗ 实验数据文件 tension.xlsx 不存在")
        return False
    
    try:
        from data_processor import test_data_processor
        
        if test_data_processor():
            print("  ✓ 数据处理器测试通过")
            return True
        else:
            print("  ✗ 数据处理器测试失败")
            return False
    except Exception as e:
        print(f"  ✗ 数据处理器测试失败: {e}")
        return False

def test_pinn_model():
    """
    测试PINN模型
    """
    print("\n测试PINN模型...")
    
    try:
        import torch
        from pinn_model import DamagePINN, PhysicsCalculator, LossCalculator
        
        # 创建测试模型
        model = DamagePINN(input_size=1, hidden_size=32, num_layers=1, output_size=3)
        print("  ✓ PINN模型创建成功")
        
        # 测试前向传播
        test_input = torch.randn(1, 10, 1)  # batch_size=1, seq_len=10, input_size=1
        with torch.no_grad():
            sigma, d, ep = model(test_input)
        print("  ✓ 模型前向传播测试通过")
        
        # 测试物理计算器
        physics_calc = PhysicsCalculator()
        test_strain_increment = torch.tensor([0.0001, 0.0001, 0.0001])
        A_plus = torch.tensor(0.5)
        B_plus = torch.tensor(1.0)
        xi = torch.tensor(0.1)
        
        d_phy, ep_phy = physics_calc.calculate_physics_constraints(
            test_strain_increment, A_plus, B_plus, xi
        )
        print("  ✓ 物理计算器测试通过")
        
        # 测试损失计算器
        loss_calc = LossCalculator()
        print("  ✓ 损失计算器创建成功")
        
        return True
        
    except Exception as e:
        print(f"  ✗ PINN模型测试失败: {e}")
        return False

def test_directory_structure():
    """
    测试目录结构
    """
    print("\n检查目录结构...")
    
    # 确保results目录存在
    Path('results').mkdir(exist_ok=True)
    print("  ✓ results目录已创建")
    
    # 检查必要文件
    required_files = [
        'main.py',
        'train.py', 
        'predict.py',
        'pinn_model.py',
        'data_processor.py',
        'font_config.py',
        'requirements.txt',
        'README.md'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✓ {file}")
        else:
            print(f"  ✗ {file} 缺失")
            missing_files.append(file)
    
    return len(missing_files) == 0

def main():
    """
    运行所有测试
    """
    print("="*60)
    print("混凝土损伤参数识别PINN框架 - 环境测试")
    print("="*60)
    
    tests = [
        ("导入测试", test_imports),
        ("目录结构", test_directory_structure),
        ("字体配置", test_font_config),
        ("数据处理", test_data_processor),
        ("PINN模型", test_pinn_model)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✓ {test_name} 通过")
                passed += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "="*60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！环境配置正确，可以开始使用框架")
        print("\n建议运行命令:")
        print("  python main.py --data tension.xlsx --epochs 100  # 快速测试")
        print("  python main.py --data tension.xlsx --epochs 2000 # 完整训练")
    else:
        print("⚠️  部分测试失败，请检查环境配置")
        print("\n建议操作:")
        print("  1. pip install -r requirements.txt")
        print("  2. 确保 tension.xlsx 文件存在且格式正确")
        print("  3. 检查Python版本 (建议>=3.7)")
    
    print("="*60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 