import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import os
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split

# 导入中文字体配置
try:
    from font_config import configure_chinese_font
    # 配置中文字体
    configure_chinese_font()
    print("已配置中文字体支持")
except ImportError:
    print("警告: 未找到font_config模块，中文显示可能不正确")
    # 尝试直接配置字体
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'Arial']
    plt.rcParams['axes.unicode_minus'] = False

# 设置环境变量，避免某些Windows系统上的OMP错误
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 设备配置
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

#====================== 数据处理函数 ======================
def load_excel_data(excel_path, static_sheet_name="基础参数", dynamic_sheet_name="力和位移数据"):
    """从Excel文件中加载数据"""
    try:
        # 读取静态参数表
        static_df = pd.read_excel(excel_path, sheet_name=static_sheet_name)

        # 提取有用的静态参数
        static_params = {}
        for _, row in static_df.iterrows():
            if len(row) >= 3:  # 确保行至少有类别、属性名和值三列
                param_category = row.iloc[0] if not pd.isna(row.iloc[0]) else "未分类"
                param_name = row.iloc[1] if not pd.isna(row.iloc[1]) else ""
                param_value = row.iloc[2] if not pd.isna(row.iloc[2]) else None

                # 尝试将字符串转换为数值
                if param_value is not None and isinstance(param_value, str):
                    try:
                        if '.' in param_value:
                            param_value = float(param_value)
                        else:
                            param_value = int(param_value)
                    except (ValueError, TypeError):
                        pass  # 保持原始值（可能是字符串）

                if param_name:  # 只添加有名称的参数
                    key = f"{param_category}_{param_name}" if param_category != "未分类" else param_name
                    static_params[key] = param_value

        # 读取动态力-位移数据
        dynamic_data = pd.read_excel(excel_path, sheet_name=dynamic_sheet_name)

        # 确保数据列名正确
        if len(dynamic_data.columns) >= 2:
            # 重命名列以确保一致性
            dynamic_data.columns = ["Force", "Deflection"] + list(dynamic_data.columns[2:])

            # 删除可能的NaN行
            dynamic_data = dynamic_data.dropna(subset=["Force", "Deflection"])

            print(f"成功加载数据: {len(dynamic_data)}行力-位移数据")
            return static_params, dynamic_data
        else:
            raise ValueError("力-位移数据表格式不正确，至少需要两列数据")

    except Exception as e:
        print(f"加载数据时出错: {str(e)}")
        raise

def preprocess_data(static_params, dynamic_data):
    """简化的数据预处理函数，专注于捕捉滞回特性"""
    # 1. 提取数值型静态参数
    numeric_params = {}
    for key, value in static_params.items():
        if isinstance(value, (int, float)) and not isinstance(value, bool) and not pd.isna(value):
            numeric_params[key] = value

    print(f"提取了{len(numeric_params)}个数值型静态参数")

    # 2. 静态参数归一化
    static_scaler = MinMaxScaler()
    static_values = np.array(list(numeric_params.values())).reshape(-1, 1)
    normalized_static = static_scaler.fit_transform(static_values).flatten()

    # 3. 提取力和位移数据
    force_data = dynamic_data["Force"].values
    disp_data = dynamic_data["Deflection"].values

    # 4. 检测位移方向变化点（关键特征点）
    direction_changes = []
    for i in range(1, len(disp_data)-1):
        if (disp_data[i-1] < disp_data[i] and disp_data[i] > disp_data[i+1]) or \
           (disp_data[i-1] > disp_data[i] and disp_data[i] < disp_data[i+1]):
            direction_changes.append(i)
    
    print(f"检测到{len(direction_changes)}个位移方向变化点")

    # 5. 力和位移数据归一化
    force_scaler = MinMaxScaler()
    disp_scaler = MinMaxScaler()

    normalized_force = force_scaler.fit_transform(force_data.reshape(-1, 1)).flatten()
    normalized_disp = disp_scaler.fit_transform(disp_data.reshape(-1, 1)).flatten()

    # 6. 计算位移增量和历史信息
    disp_increment = np.zeros_like(normalized_disp)
    disp_increment[1:] = normalized_disp[1:] - normalized_disp[:-1]
    
    # 7. 计算累积位移（用于捕捉加载历史）
    loading_history = np.zeros_like(normalized_disp)
    for i in range(1, len(normalized_disp)):
        # 累积位移变化的绝对值
        loading_history[i] = loading_history[i-1] + abs(disp_increment[i])
    
    # 归一化累积位移
    loading_history = loading_history / (np.max(loading_history) + 1e-8)

    # 8. 计算位移方向（加载为1，卸载为-1）
    loading_direction = np.zeros_like(normalized_disp)
    for i in range(1, len(normalized_disp)):
        if disp_increment[i] > 0:
            loading_direction[i] = 1.0  # 加载
        elif disp_increment[i] < 0:
            loading_direction[i] = -1.0  # 卸载
        else:
            loading_direction[i] = loading_direction[i-1]  # 保持前一状态

    # 9. 构建输入特征
    # 特征包括：静态参数 + 当前位移 + 位移增量 + 加载方向 + 累积位移历史
    feature_count = len(normalized_static) + 4
    X = np.zeros((len(normalized_disp), feature_count))

    for i in range(len(normalized_disp)):
        X[i, :len(normalized_static)] = normalized_static
        X[i, len(normalized_static)] = normalized_disp[i]      # 当前位移
        X[i, len(normalized_static) + 1] = disp_increment[i]   # 位移增量
        X[i, len(normalized_static) + 2] = loading_direction[i]  # 加载方向
        X[i, len(normalized_static) + 3] = loading_history[i]  # 累积位移历史

    # 10. 构建输出标签（力）
    y = normalized_force

    # 11. 转换为PyTorch张量
    X_tensor = torch.tensor(X, dtype=torch.float32, device=device)
    y_tensor = torch.tensor(y, dtype=torch.float32, device=device).view(-1, 1)

    # 12. 保存缩放器和原始数据，用于后续反归一化和可视化
    scalers = {
        'static': static_scaler,
        'force': force_scaler,
        'disp': disp_scaler
    }

    original_data = {
        'force': force_data,
        'disp': disp_data,
        'direction_changes': direction_changes
    }

    return X_tensor, y_tensor, scalers, original_data

#====================== 模型定义 ======================
class EnhancedHysteresisBlock(nn.Module):
    """增强的滞回特性捕捉模块，使用更复杂的非线性表达和交叉特征"""
    def __init__(self, input_dim, hidden_dim):
        super().__init__()
        # 使用适度的网络深度和宽度，避免过拟合
        self.direction_encoder = nn.Sequential(
            nn.Linear(2, hidden_dim),  # 位移和方向
            nn.Tanh(),  # 使用Tanh激活函数，更适合捕捉周期性和对称性
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.Tanh()
        )
        
        self.history_encoder = nn.Sequential(
            nn.Linear(2, hidden_dim),  # 位移增量和累积历史
            nn.Tanh(),  # 使用Tanh激活函数
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.Tanh()
        )
        
        # 添加交叉特征处理 - 使用简化的注意力机制
        self.cross_attention = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.Tanh(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim),
            nn.Tanh()
        )
        
        # 添加残差连接 - 使用简单的映射
        self.residual_layer = nn.Sequential(
            nn.Linear(4, hidden_dim // 2),
            nn.Tanh(),
            nn.Linear(hidden_dim // 2, hidden_dim)
        )  # 原始特征到隐藏维度的映射
        
        # 添加门控机制 - 控制残差信息的流动
        self.gate = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.Sigmoid()
        )
        
    def forward(self, x, disp_idx, inc_idx, dir_idx, hist_idx):
        # 提取关键特征
        displacement = x[:, disp_idx:disp_idx+1]
        direction = x[:, dir_idx:dir_idx+1]
        increment = x[:, inc_idx:inc_idx+1]
        history = x[:, hist_idx:hist_idx+1]
        
        # 原始特征组合，用于残差连接
        raw_features = torch.cat([displacement, direction, increment, history], dim=1)
        
        # 编码方向信息 - 确保批次维度正确
        dir_input = torch.cat([displacement, direction], dim=1)
        batch_size = dir_input.size(0)
        dir_features = self.direction_encoder(dir_input)
        
        # 编码历史信息 - 确保批次维度正确
        hist_input = torch.cat([increment, history], dim=1)
        hist_features = self.history_encoder(hist_input)
        
        # 组合特征
        combined = torch.cat([dir_features, hist_features], dim=1)
        
        # 应用交叉注意力机制增强特征交互
        enhanced = self.cross_attention(combined)
        
        # 添加残差连接，增强梯度流动
        residual = self.residual_layer(raw_features)
        
        # 计算门控值 - 控制残差信息的流动
        gate_input = torch.cat([enhanced, residual], dim=1)
        gate_value = self.gate(gate_input)
        
        # 应用门控机制组合主路径和残差路径
        output = enhanced * gate_value + residual * (1 - gate_value)
        
        return output

class SimplifiedColumnHysteresisModel(nn.Module):
    """增强版柱子滞回曲线模型，专注于捕捉滞回特性和非线性表达"""
    def __init__(self, input_dim, static_feature_count, hidden_dim=128):
        super().__init__()
        
        # 简化静态特征编码器 - 移除BatchNorm，使用Tanh激活函数
        self.static_encoder = nn.Sequential(
            nn.Linear(static_feature_count, hidden_dim),
            nn.Tanh(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.Tanh()
        )
        
        # 滞回特性捕捉模块 - 使用增强版模块
        self.hysteresis_block = EnhancedHysteresisBlock(
            input_dim - static_feature_count, 
            hidden_dim
        )
        
        # 简化主干网络 - 适度的网络深度和宽度
        self.backbone = nn.Sequential(
            nn.Linear(hidden_dim + hidden_dim // 2, hidden_dim * 2),
            nn.Tanh(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim * 2, hidden_dim * 2),
            nn.Tanh(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.Tanh(),
            nn.Dropout(0.1)
        )
        
        # 简化幅值增强层
        self.amplitude_enhancer = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.Tanh(),
            nn.Linear(hidden_dim // 2, 1),
            nn.Tanh()  # 使用Tanh确保输出在合理范围内
        )
        
        # 简化输出层
        self.output_layer = nn.Linear(hidden_dim, 1)
        
        # 增大缩放因子初始值，使模型能产生更大的输出幅值
        self.scaling_factor = nn.Parameter(torch.tensor([5.0]), requires_grad=True)  # 可学习的缩放因子
        
    def forward(self, x, static_feature_count):
        # 分离静态特征和动态特征
        static_features = x[:, :static_feature_count]
        
        # 计算特征索引
        disp_idx = static_feature_count
        inc_idx = static_feature_count + 1
        dir_idx = static_feature_count + 2
        hist_idx = static_feature_count + 3
        
        # 编码静态特征
        static_encoded = self.static_encoder(static_features)
        
        # 捕捉滞回特性
        hysteresis_features = self.hysteresis_block(x, disp_idx, inc_idx, dir_idx, hist_idx)
        
        # 组合所有特征
        combined_features = torch.cat([static_encoded, hysteresis_features], dim=1)
        
        # 通过主干网络
        backbone_output = self.backbone(combined_features)
        
        # 生成基础预测
        base_output = self.output_layer(backbone_output)
        
        # 生成幅值增强因子
        amplitude_factor = self.amplitude_enhancer(backbone_output)
        
        # 提取位移和方向信息用于滞回特性增强
        displacement = x[:, disp_idx:disp_idx+1]
        direction = x[:, dir_idx:dir_idx+1]
        
        # 简化的动态缩放 - 根据加载方向调整输出
        # 加载时放大输出，卸载时减小输出
        dir_factor = torch.where(direction > 0, 1.5, 0.8)
        
        # 应用幅值增强和缩放因子
        enhanced_output = base_output * (1.0 + amplitude_factor * self.scaling_factor * dir_factor)
        
        return enhanced_output

#====================== 损失函数 ======================
def enhanced_hysteresis_loss(model, x_batch, y_batch, static_feature_count,
                         lambda_data=1.0, lambda_hysteresis=2.5, lambda_smoothness=0.1, lambda_energy=2.0):
    """增强的滞回特性损失函数，专注于捕捉滞回曲线的关键特性和能量耗散"""
    # 数据拟合损失 - 使用Huber损失代替MSE，对异常值更鲁棒
    y_pred = model(x_batch, static_feature_count)
    data_loss = F.huber_loss(y_pred, y_batch, delta=0.1)  # 使用小delta值增强对小误差的敏感度
    
    # 滞回特性损失 - 确保加载和卸载路径有足够差异
    hysteresis_loss = torch.tensor(0.0, device=device)
    energy_loss = torch.tensor(0.0, device=device)
    
    # 提取位移和方向信息
    disp_idx = static_feature_count
    inc_idx = static_feature_count + 1
    dir_idx = static_feature_count + 2
    hist_idx = static_feature_count + 3
    
    displacement = x_batch[:, disp_idx]
    direction = x_batch[:, dir_idx]
    increment = x_batch[:, inc_idx]
    history = x_batch[:, hist_idx]
    
    # 找到加载和卸载点
    loading_mask = (direction > 0).float()
    unloading_mask = (direction < 0).float()
    
    if torch.sum(loading_mask) > 0 and torch.sum(unloading_mask) > 0:
        # 计算加载和卸载点的平均力值和标准差
        loading_forces = y_pred.squeeze() * loading_mask
        unloading_forces = y_pred.squeeze() * unloading_mask
        
        avg_loading_force = torch.sum(loading_forces) / (torch.sum(loading_mask) + 1e-8)
        avg_unloading_force = torch.sum(unloading_forces) / (torch.sum(unloading_mask) + 1e-8)
        
        # 计算真实加载和卸载力值
        true_loading_forces = y_batch.squeeze() * loading_mask
        true_unloading_forces = y_batch.squeeze() * unloading_mask
        
        true_avg_loading_force = torch.sum(true_loading_forces) / (torch.sum(loading_mask) + 1e-8)
        true_avg_unloading_force = torch.sum(true_unloading_forces) / (torch.sum(unloading_mask) + 1e-8)
        
        # 计算真实力值差异
        true_force_diff = torch.abs(true_avg_loading_force - true_avg_unloading_force)
        
        # 鼓励预测的力值差异接近或大于真实差异
        pred_force_diff = torch.abs(avg_loading_force - avg_unloading_force)
        force_diff_ratio = true_force_diff / (pred_force_diff + 1e-8)
        
        # 使用更强的惩罚函数，鼓励更宽的滞回环
        force_diff_loss = torch.exp(-pred_force_diff * 10.0) + torch.relu(1.2 - pred_force_diff/true_force_diff) * 2.0
        hysteresis_loss = hysteresis_loss + force_diff_loss
        
        # 计算位移相似点的力值差异 - 使用更精细的相似度计算
        disp_diffs = torch.abs(displacement.unsqueeze(1) - displacement.unsqueeze(0))
        
        # 使用自适应高斯核函数计算相似度
        disp_range = torch.max(displacement) - torch.min(displacement)
        sigma = 0.02 * disp_range  # 自适应相似度敏感度
        similar_points = torch.exp(-(disp_diffs**2) / (2 * sigma**2))
        
        # 找到位移相似但方向不同的点对
        direction_diff = torch.abs(direction.unsqueeze(1) - direction.unsqueeze(0))
        different_direction = (direction_diff > 1.0).float()  # 方向不同的点对
        
        # 位移相似但方向不同的点对
        hysteresis_pairs = similar_points * different_direction
        
        if torch.sum(hysteresis_pairs) > 0:
            # 计算这些点对的力值差异
            pred_force_diffs = torch.abs(y_pred.squeeze().unsqueeze(1) - y_pred.squeeze())
            true_force_diffs = torch.abs(y_batch.squeeze().unsqueeze(1) - y_batch.squeeze())
            
            # 计算位移幅值相关的理想滞回宽度
            max_disp = torch.max(torch.abs(displacement))
            disp_ratio = torch.abs(displacement) / (max_disp + 1e-8)
            disp_ratio_matrix = disp_ratio.unsqueeze(1) + disp_ratio.unsqueeze(0)
            
            # 理想的滞回宽度与位移幅值和累积历史成正比
            ideal_width_factor = 0.6  # 增大理想的滞回宽度系数
            ideal_force_diff = ideal_width_factor * disp_ratio_matrix * max_disp
            
            # 计算真实滞回宽度与预测宽度的比率
            width_ratio = true_force_diffs / (pred_force_diffs + 1e-8)
            width_ratio = torch.clamp(width_ratio, 0.1, 10.0)  # 限制比率范围
            
            # 鼓励预测宽度接近或大于真实宽度
            width_penalty = torch.exp(-pred_force_diffs * 5.0) + torch.relu(1.0 - pred_force_diffs/(true_force_diffs + 1e-8)) * 3.0
            
            # 应用滞回对的权重
            pair_losses = width_penalty * hysteresis_pairs
            hysteresis_loss = hysteresis_loss + torch.sum(pair_losses) / (torch.sum(hysteresis_pairs) + 1e-8)
        
        # 添加能量耗散损失 - 鼓励更大的滞回环面积和更准确的能量耗散特性
        # 近似计算滞回环面积
        if x_batch.size(0) > 2:
            # 按照加载/卸载方向分组计算
            loading_indices = torch.where(loading_mask > 0)[0]
            unloading_indices = torch.where(unloading_mask > 0)[0]
            
            if len(loading_indices) > 0 and len(unloading_indices) > 0:
                # 计算加载曲线下的面积
                loading_disp = displacement[loading_indices]
                loading_pred = y_pred.squeeze()[loading_indices]
                loading_true = y_batch.squeeze()[loading_indices]
                
                # 计算卸载曲线下的面积
                unloading_disp = displacement[unloading_indices]
                unloading_pred = y_pred.squeeze()[unloading_indices]
                unloading_true = y_batch.squeeze()[unloading_indices]
                
                # 计算真实滞回环面积的近似值
                if len(loading_disp) > 1 and len(unloading_disp) > 1:
                    # 使用梯形法则近似积分
                    true_loading_area = torch.trapz(loading_true, loading_disp)
                    true_unloading_area = torch.trapz(unloading_true, unloading_disp)
                    true_energy = torch.abs(true_loading_area - true_unloading_area)
                    
                    # 计算预测滞回环面积
                    pred_loading_area = torch.trapz(loading_pred, loading_disp)
                    pred_unloading_area = torch.trapz(unloading_pred, unloading_disp)
                    pred_energy = torch.abs(pred_loading_area - pred_unloading_area)
                    
                    # 计算能量耗散比率
                    energy_ratio = pred_energy / (true_energy + 1e-8)
                    
                    # 使用更强的惩罚函数，鼓励更大的能量耗散
                    # 当预测能量小于真实能量时，给予更大的惩罚
                    energy_loss = torch.exp(-pred_energy * 5.0) + torch.relu(1.2 - energy_ratio) * 3.0
                    
                    # 添加曲线形状相似度损失
                    # 计算加载曲线的形状相似度
                    if len(loading_disp) > 2:
                        loading_pred_norm = (loading_pred - torch.min(loading_pred)) / (torch.max(loading_pred) - torch.min(loading_pred) + 1e-8)
                        loading_true_norm = (loading_true - torch.min(loading_true)) / (torch.max(loading_true) - torch.min(loading_true) + 1e-8)
                        loading_shape_loss = F.mse_loss(loading_pred_norm, loading_true_norm)
                        energy_loss = energy_loss + loading_shape_loss * 2.0
                    
                    # 计算卸载曲线的形状相似度
                    if len(unloading_disp) > 2:
                        unloading_pred_norm = (unloading_pred - torch.min(unloading_pred)) / (torch.max(unloading_pred) - torch.min(unloading_pred) + 1e-8)
                        unloading_true_norm = (unloading_true - torch.min(unloading_true)) / (torch.max(unloading_true) - torch.min(unloading_true) + 1e-8)
                        unloading_shape_loss = F.mse_loss(unloading_pred_norm, unloading_true_norm)
                        energy_loss = energy_loss + unloading_shape_loss * 2.0
    
    # 平滑度损失 - 确保预测曲线平滑但保留关键特征
    smoothness_loss = torch.tensor(0.0, device=device)
    if x_batch.size(0) > 2:
        # 计算预测力的二阶差分
        second_diff = y_pred[2:] - 2 * y_pred[1:-1] + y_pred[:-2]
        
        # 计算位移的二阶差分，用于识别关键转折点
        disp_second_diff = displacement[2:] - 2 * displacement[1:-1] + displacement[:-2]
        
        # 在非转折点处更强调平滑性
        is_turning_point = (torch.abs(disp_second_diff) > 0.01).float()
        smoothness_weights = 1.0 - is_turning_point * 0.8  # 在转折点处降低平滑度要求
        
        weighted_smoothness = torch.abs(second_diff) * smoothness_weights
        smoothness_loss = torch.mean(weighted_smoothness)
    
    # 总损失 - 增加滞回特性和能量损失的权重
    total_loss = lambda_data * data_loss + lambda_hysteresis * hysteresis_loss + \
                lambda_smoothness * smoothness_loss + lambda_energy * energy_loss
    
    # 返回损失字典
    loss_dict = {
        'data': data_loss.item(),
        'hysteresis': hysteresis_loss.item(),
        'smoothness': smoothness_loss.item(),
        'energy': energy_loss.item(),
        'total': total_loss.item()
    }
    
    return total_loss, loss_dict

#====================== 训练函数 ======================
def train_enhanced_model(model, X, y, static_feature_count, epochs=300, batch_size=16, lr=5e-4, patience=30,
                      lambda_data=1.0, lambda_hysteresis=1.5, lambda_smoothness=0.2, lambda_energy=0.8):
    """训练增强版滞回曲线模型，使用优化的训练策略"""
    import time
    start_time = time.time()
    
    # 数据集划分 - 使用更多数据用于训练
    X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.1, random_state=42)
    
    # 创建数据加载器 - 减小批次大小以增加更新频率
    train_dataset = TensorDataset(X_train, y_train)
    val_dataset = TensorDataset(X_val, y_val)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size)
    
    # 优化器 - 使用AdamW优化器，更好的权重衰减
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-4)
    
    # 学习率调度器 - 使用余弦退火调度
    scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=20, T_mult=2, eta_min=1e-5
    )
    
    # 初始化早停计数器和最佳验证损失
    early_stop_counter = 0
    best_val_loss = float('inf')
    
    # 初始化训练历史记录
    history = {
        'train_loss': [], 'val_loss': [],
        'train_data': [], 'val_data': [],
        'train_hysteresis': [], 'val_hysteresis': [],
        'train_smoothness': [], 'val_smoothness': [],
        'train_energy': [], 'val_energy': []
    }
    
    print(f"开始训练增强版滞回曲线模型，总共{epochs}轮...")
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_losses = []
        train_loss_components = {'data': 0, 'hysteresis': 0, 'smoothness': 0, 'energy': 0, 'total': 0}
        
        for X_batch, y_batch in train_loader:
            # 前向传播和损失计算
            loss, loss_dict = enhanced_hysteresis_loss(
                model, X_batch, y_batch, static_feature_count,
                lambda_data, lambda_hysteresis, lambda_smoothness, lambda_energy
            )
            
            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪，防止梯度爆炸
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            # 记录损失
            train_losses.append(loss.item())
            for k, v in loss_dict.items():
                train_loss_components[k] += v
        
        # 计算平均训练损失
        avg_train_loss = sum(train_losses) / len(train_losses)
        for k in train_loss_components:
            train_loss_components[k] /= len(train_loader)
        
        # 验证阶段
        model.eval()
        val_losses = []
        val_loss_components = {'data': 0, 'hysteresis': 0, 'smoothness': 0, 'energy': 0, 'total': 0}
        
        with torch.no_grad():
            for X_batch, y_batch in val_loader:
                # 前向传播和损失计算
                loss, loss_dict = enhanced_hysteresis_loss(
                    model, X_batch, y_batch, static_feature_count,
                    lambda_data, lambda_hysteresis, lambda_smoothness, lambda_energy
                )
                
                # 记录损失
                val_losses.append(loss.item())
                for k, v in loss_dict.items():
                    val_loss_components[k] += v
        
        # 计算平均验证损失
        avg_val_loss = sum(val_losses) / len(val_losses)
        for k in val_loss_components:
            val_loss_components[k] /= len(val_loader)
        
        # 更新学习率调度器
        scheduler.step()
        
        # 记录训练历史
        history['train_loss'].append(avg_train_loss)
        history['val_loss'].append(avg_val_loss)
        history['train_data'].append(train_loss_components['data'])
        history['val_data'].append(val_loss_components['data'])
        history['train_hysteresis'].append(train_loss_components['hysteresis'])
        history['val_hysteresis'].append(val_loss_components['hysteresis'])
        history['train_smoothness'].append(train_loss_components['smoothness'])
        history['val_smoothness'].append(val_loss_components['smoothness'])
        history['train_energy'].append(train_loss_components['energy'])
        history['val_energy'].append(val_loss_components['energy'])
        
        # 打印训练进度
        if (epoch + 1) % 10 == 0 or epoch == 0:
            print(f"Epoch {epoch+1}/{epochs} - "
                  f"Train Loss: {avg_train_loss:.4f} (Data: {train_loss_components['data']:.4f}, "
                  f"Hyst: {train_loss_components['hysteresis']:.4f}, "
                  f"Smooth: {train_loss_components['smoothness']:.4f}, "
                  f"Energy: {train_loss_components['energy']:.4f}) | "
                  f"Val Loss: {avg_val_loss:.4f} (Data: {val_loss_components['data']:.4f}, "
                  f"Hyst: {val_loss_components['hysteresis']:.4f}, "
                  f"Smooth: {val_loss_components['smoothness']:.4f}, "
                  f"Energy: {val_loss_components['energy']:.4f})")
        
        # 早停检查
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            early_stop_counter = 0
            # 保存最佳模型
            torch.save(model.state_dict(), 'd:\\column\\best_simplified_model.pth')
            print(f"Epoch {epoch+1}: 保存新的最佳模型，验证损失: {best_val_loss:.4f}")
        else:
            early_stop_counter += 1
            if early_stop_counter >= patience:
                print(f"早停触发！{patience}轮内验证损失未改善。")
                break
    
    # 加载最佳模型
    model.load_state_dict(torch.load('d:\\column\\best_simplified_model.pth'))
    
    # 计算训练时间
    training_time = time.time() - start_time
    print(f"训练完成！总用时: {training_time:.2f}秒 ({training_time/60:.2f}分钟)")
    
    return model, history

#====================== 可视化函数 ======================
def plot_training_history(history):
    """绘制训练历史曲线，包含能量损失"""
    plt.figure(figsize=(15, 12))
    
    # 总损失
    plt.subplot(3, 2, 1)
    plt.plot(history['train_loss'], label='训练总损失')
    plt.plot(history['val_loss'], label='验证总损失')
    plt.title('总损失')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)
    
    # 数据拟合损失
    plt.subplot(3, 2, 2)
    plt.plot(history['train_data'], label='训练数据损失')
    plt.plot(history['val_data'], label='验证数据损失')
    plt.title('数据拟合损失')
    plt.xlabel('Epoch')
    plt.ylabel('MSE Loss')
    plt.legend()
    plt.grid(True)
    
    # 滞回特性损失
    plt.subplot(3, 2, 3)
    plt.plot(history['train_hysteresis'], label='训练滞回损失')
    plt.plot(history['val_hysteresis'], label='验证滞回损失')
    plt.title('滞回特性损失')
    plt.xlabel('Epoch')
    plt.ylabel('Hysteresis Loss')
    plt.legend()
    plt.grid(True)
    
    # 平滑度损失
    plt.subplot(3, 2, 4)
    plt.plot(history['train_smoothness'], label='训练平滑损失')
    plt.plot(history['val_smoothness'], label='验证平滑损失')
    plt.title('平滑度损失')
    plt.xlabel('Epoch')
    plt.ylabel('Smoothness Loss')
    plt.legend()
    plt.grid(True)
    
    # 能量损失
    plt.subplot(3, 2, 5)
    plt.plot(history['train_energy'], label='训练能量损失')
    plt.plot(history['val_energy'], label='验证能量损失')
    plt.title('能量耗散损失')
    plt.xlabel('Epoch')
    plt.ylabel('Energy Loss')
    plt.legend()
    plt.grid(True)
    
    # 学习率变化（如果有记录）
    if 'learning_rate' in history:
        plt.subplot(3, 2, 6)
        plt.plot(history['learning_rate'], label='学习率')
        plt.title('学习率变化')
        plt.xlabel('Epoch')
        plt.ylabel('Learning Rate')
        plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('d:\\column\\simplified_training_history.png', dpi=300, bbox_inches='tight')
    plt.show()


def plot_hysteresis_curve(model, X, y, static_feature_count, force_scaler, disp_scaler, original_data=None, title="简化模型滞回曲线对比"):
    """绘制滞回曲线对比图"""
    model.eval()
    
    with torch.no_grad():
        # 预测力值
        y_pred = model(X, static_feature_count)
        
        # 如果有原始数据，直接使用
        if original_data is not None and 'force' in original_data and 'disp' in original_data:
            disp = original_data['disp']
            force_true = original_data['force']
            
            # 反归一化预测力
            force_pred = force_scaler.inverse_transform(y_pred.cpu().numpy().reshape(-1, 1)).flatten()
            
            # 提取方向变化点
            direction_changes = original_data.get('direction_changes', [])
        else:
            # 提取位移特征并反归一化
            disp_idx = static_feature_count
            disp_feature = X[:, disp_idx].cpu().numpy().reshape(-1, 1)
            disp = disp_scaler.inverse_transform(disp_feature).flatten()
            
            # 反归一化力值
            force_true = force_scaler.inverse_transform(y.cpu().numpy().reshape(-1, 1)).flatten()
            force_pred = force_scaler.inverse_transform(y_pred.cpu().numpy().reshape(-1, 1)).flatten()
            
            # 计算方向变化点
            direction_changes = []
            for i in range(1, len(disp)-1):
                if (disp[i-1] < disp[i] and disp[i] > disp[i+1]) or \
                   (disp[i-1] > disp[i] and disp[i] < disp[i+1]):
                    direction_changes.append(i)
        
        # 创建两个子图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
        
        # 第一个子图：真实曲线和预测曲线对比
        ax1.plot(disp, force_true, 'b-', label='实验测量', linewidth=2)
        ax1.plot(disp, force_pred, 'r--', label='模型预测', linewidth=2)
        
        # 标记方向变化点
        if len(direction_changes) > 0:
            ax1.scatter(disp[direction_changes], force_true[direction_changes], 
                       c='blue', marker='o', s=80, label='实验转折点')
            ax1.scatter(disp[direction_changes], force_pred[direction_changes], 
                       c='red', marker='x', s=80, label='预测转折点')
        
        ax1.set_xlabel('位移 (mm)', fontsize=12)
        ax1.set_ylabel('力 (kN)', fontsize=12)
        ax1.set_title('滞回曲线对比', fontsize=14)
        ax1.grid(True)
        ax1.legend(fontsize=12)
        
        # 第二个子图：误差分析
        error = force_pred - force_true
        ax2.plot(disp, error, 'g-', linewidth=1.5)
        ax2.axhline(y=0, color='k', linestyle='--', alpha=0.7)
        
        # 标记误差较大的区域
        error_threshold = np.std(error) * 2  # 使用2倍标准差作为阈值
        large_error_indices = np.where(np.abs(error) > error_threshold)[0]
        if len(large_error_indices) > 0:
            ax2.scatter(disp[large_error_indices], error[large_error_indices], 
                       c='red', marker='x', s=50, label='大误差点')
        
        ax2.set_xlabel('位移 (mm)', fontsize=12)
        ax2.set_ylabel('预测误差 (kN)', fontsize=12)
        ax2.set_title('预测误差分析', fontsize=14)
        ax2.grid(True)
        if len(large_error_indices) > 0:
            ax2.legend(fontsize=10)
        
        # 设置整体标题
        fig.suptitle(title, fontsize=16)
        plt.tight_layout(rect=[0, 0, 1, 0.95])
        
        # 保存图像
        plt.savefig('d:\\column\\simplified_hysteresis_curve_comparison.png', dpi=300, bbox_inches='tight')
        
        # 计算预测误差
        mae = np.mean(np.abs(force_true - force_pred))
        rmse = np.sqrt(np.mean((force_true - force_pred)**2))
        max_error = np.max(np.abs(force_true - force_pred))
        
        # 计算滞回特性指标 - 能量耗散
        # 使用梯形法则计算滞回环面积（能量耗散）
        true_energy = 0
        pred_energy = 0
        
        # 按照加载-卸载循环分段计算能量
        if len(direction_changes) >= 2:
            for i in range(len(direction_changes) - 1):
                start_idx = direction_changes[i]
                end_idx = direction_changes[i + 1]
                
                # 计算真实曲线的能量耗散
                segment_disp = disp[start_idx:end_idx+1]
                segment_force_true = force_true[start_idx:end_idx+1]
                true_segment_energy = np.trapz(segment_force_true, segment_disp)
                true_energy += abs(true_segment_energy)
                
                # 计算预测曲线的能量耗散
                segment_force_pred = force_pred[start_idx:end_idx+1]
                pred_segment_energy = np.trapz(segment_force_pred, segment_disp)
                pred_energy += abs(pred_segment_energy)
        
        # 计算能量耗散误差
        energy_error = abs(true_energy - pred_energy) / (true_energy + 1e-6) * 100  # 百分比误差
        
        print(f"预测误差统计:")
        print(f"平均绝对误差 (MAE): {mae:.4f} kN")
        print(f"均方根误差 (RMSE): {rmse:.4f} kN")
        print(f"最大误差: {max_error:.4f} kN")
        print(f"能量耗散误差: {energy_error:.2f}%")
        print(f"真实能量耗散: {true_energy:.2f} kN·mm")
        print(f"预测能量耗散: {pred_energy:.2f} kN·mm")
        
        plt.show()
        
        return {
            'mae': mae,
            'rmse': rmse,
            'max_error': max_error,
            'energy_error': energy_error,
            'true_energy': true_energy,
            'pred_energy': pred_energy
        }

def plot_combined_hysteresis_curve(model, X, y, static_feature_count, force_scaler, disp_scaler, original_data=None):
    """绘制叠加的滞回曲线对比图"""
    model.eval()
    
    with torch.no_grad():
        # 预测力值
        y_pred = model(X, static_feature_count)
        
        # 如果有原始数据，直接使用
        if original_data is not None and 'force' in original_data and 'disp' in original_data:
            disp = original_data['disp']
            force_true = original_data['force']
            
            # 反归一化预测力
            force_pred = force_scaler.inverse_transform(y_pred.cpu().numpy().reshape(-1, 1)).flatten()
            
            # 提取方向变化点
            direction_changes = original_data.get('direction_changes', [])
        else:
            # 提取位移特征并反归一化
            disp_idx = static_feature_count
            disp_feature = X[:, disp_idx].cpu().numpy().reshape(-1, 1)
            disp = disp_scaler.inverse_transform(disp_feature).flatten()
            
            # 反归一化力值
            force_true = force_scaler.inverse_transform(y.cpu().numpy().reshape(-1, 1)).flatten()
            force_pred = force_scaler.inverse_transform(y_pred.cpu().numpy().reshape(-1, 1)).flatten()
            
            # 计算方向变化点
            direction_changes = []
            for i in range(1, len(disp)-1):
                if (disp[i-1] < disp[i] and disp[i] > disp[i+1]) or \
                   (disp[i-1] > disp[i] and disp[i] < disp[i+1]):
                    direction_changes.append(i)
        
        # 创建图形
        plt.figure(figsize=(12, 10))
        
        # 绘制真实曲线和预测曲线
        plt.plot(disp, force_true, 'b-', label='实验测量', linewidth=2)
        plt.plot(disp, force_pred, 'r--', label='简化模型预测', linewidth=2)
        
        # 标记方向变化点
        if len(direction_changes) > 0:
            plt.scatter(disp[direction_changes], force_true[direction_changes], 
                       c='blue', marker='o', s=60, label='实验转折点')
            plt.scatter(disp[direction_changes], force_pred[direction_changes], 
                       c='red', marker='x', s=60, label='预测转折点')
        
        plt.xlabel('位移 (mm)', fontsize=14)
        plt.ylabel('力 (kN)', fontsize=14)
        plt.title('简化模型滞回曲线对比', fontsize=16)
        plt.grid(True)
        plt.legend(fontsize=12)
        
        # 保存图像
        plt.savefig('d:\\column\\simplified_hysteresis_curve_overlay.png', dpi=300, bbox_inches='tight')
        plt.show()