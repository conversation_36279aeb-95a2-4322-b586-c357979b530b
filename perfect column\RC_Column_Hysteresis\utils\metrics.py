# -*- coding: utf-8 -*-
"""
评估指标模块

提供计算模型预测性能的各种评估指标。
"""

import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score


def calculate_mse(y_true, y_pred):
    """
    计算均方误差 (Mean Squared Error)
    
    参数:
        y_true (numpy.ndarray): 真实值
        y_pred (numpy.ndarray): 预测值
        
    返回:
        float: MSE值
    """
    return mean_squared_error(y_true, y_pred)


def calculate_rmse(y_true, y_pred):
    """
    计算均方根误差 (Root Mean Squared Error)
    
    参数:
        y_true (numpy.ndarray): 真实值
        y_pred (numpy.ndarray): 预测值
        
    返回:
        float: RMSE值
    """
    return np.sqrt(mean_squared_error(y_true, y_pred))


def calculate_mae(y_true, y_pred):
    """
    计算平均绝对误差 (Mean Absolute Error)
    
    参数:
        y_true (numpy.ndarray): 真实值
        y_pred (numpy.ndarray): 预测值
        
    返回:
        float: MAE值
    """
    return mean_absolute_error(y_true, y_pred)


def calculate_mape(y_true, y_pred, epsilon=1e-10):
    """
    计算平均绝对百分比误差 (Mean Absolute Percentage Error)
    
    参数:
        y_true (numpy.ndarray): 真实值
        y_pred (numpy.ndarray): 预测值
        epsilon (float): 小值，防止除零错误
        
    返回:
        float: MAPE值（百分比）
    """
    # 避免除以零
    mask = np.abs(y_true) > epsilon
    return 100.0 * np.mean(np.abs((y_true[mask] - y_pred[mask]) / (y_true[mask] + epsilon)))


def calculate_r2(y_true, y_pred):
    """
    计算决定系数 (R²)
    
    参数:
        y_true (numpy.ndarray): 真实值
        y_pred (numpy.ndarray): 预测值
        
    返回:
        float: R²值
    """
    return r2_score(y_true, y_pred)


def calculate_energy_error(displacement, force_true, force_pred):
    """
    计算能量耗散误差（滞回环面积误差）
    
    参数:
        displacement (numpy.ndarray): 位移数据
        force_true (numpy.ndarray): 真实力数据
        force_pred (numpy.ndarray): 预测力数据
        
    返回:
        tuple: (能量误差百分比, 真实能量, 预测能量)
    """
    # 计算能量耗散（滞回环面积）
    def calculate_energy(disp, force):
        # 使用梯形法则计算滞回曲线的能量耗散
        # 滞回曲线的能量是闭合曲线围成的面积
        # 对于滞回曲线，我们需要计算绝对面积总和
        
        # 确保数据是numpy数组
        disp = np.array(disp)
        force = np.array(force)
        
        # 使用更精确的方法计算滞回曲线的能量耗散
        # 滞回曲线的能量应该是曲线围成的面积
        
        # 方法1：直接计算完整曲线的面积
        # 注意：对于滞回曲线，直接使用trapz可能会得到接近于零的结果，因为正负区域会相互抵消
        # 所以我们需要分段计算，然后取绝对值之和
        
        # 找出位移方向变化的点，这些点通常是滞回循环的转折点
        disp_diff = np.diff(disp)
        # 使用位移导数的符号变化来检测转折点
        sign_changes = np.where(np.diff(np.sign(disp_diff)))[0] + 1
        
        # 如果没有检测到转折点，尝试使用位移的极值点
        if len(sign_changes) == 0:
            # 寻找局部极大值和极小值点
            from scipy.signal import find_peaks
            peaks, _ = find_peaks(disp)
            valleys, _ = find_peaks(-disp)
            sign_changes = np.sort(np.concatenate([peaks, valleys]))
        
        # 如果仍然没有检测到转折点，使用固定间隔分段
        if len(sign_changes) == 0:
            # 每100个点分一段
            segment_size = 100
            sign_changes = np.arange(segment_size, len(disp), segment_size)
        
        # 添加起点和终点
        segments = np.concatenate(([0], sign_changes, [len(disp)-1]))
        segments = np.unique(segments)  # 确保没有重复点
        
        # 计算总能量
        total_energy = 0.0
        
        # 对每个段落计算能量
        for i in range(len(segments)-1):
            start_idx = segments[i]
            end_idx = segments[i+1] + 1  # 包含结束点
            
            segment_disp = disp[start_idx:end_idx]
            segment_force = force[start_idx:end_idx]
            
            # 计算这个段落的能量（使用梯形法则）
            segment_energy = np.abs(np.trapz(segment_force, segment_disp))
            total_energy += segment_energy
        
        # 如果计算出的能量仍然很小，尝试使用另一种方法
        if total_energy < 1000:
            # 方法2：计算整个曲线的面积，不分段
            # 这种方法适用于某些特殊情况
            area = np.trapz(force, disp)
            if np.abs(area) > total_energy:
                total_energy = np.abs(area)
        
        # 如果能量仍然很小，可能是数据问题，使用力的绝对值和位移的乘积作为估计
        if total_energy < 1000:
            # 方法3：使用力的绝对值和位移的乘积作为能量的粗略估计
            force_abs_mean = np.mean(np.abs(force))
            disp_range = np.max(disp) - np.min(disp)
            estimated_energy = force_abs_mean * disp_range
            # 如果估计值比计算值大得多，使用估计值
            if estimated_energy > 10 * total_energy:
                total_energy = estimated_energy
        
        return total_energy
    
    energy_true = calculate_energy(displacement, force_true)
    energy_pred = calculate_energy(displacement, force_pred)
    
    # 确保能量值不为零，避免除零错误
    if energy_true < 1e-6:
        energy_true = 1e-6
    if energy_pred < 1e-6:
        energy_pred = 1e-6
    
    # 计算误差百分比
    error_percent = (energy_pred - energy_true) / energy_true * 100
    
    return error_percent, energy_true, energy_pred


def calculate_all_metrics(y_true, y_pred, displacement=None):
    """
    计算所有评估指标
    
    参数:
        y_true (numpy.ndarray): 真实值
        y_pred (numpy.ndarray): 预测值
        displacement (numpy.ndarray, optional): 位移数据，用于计算能量误差
        
    返回:
        dict: 包含所有评估指标的字典
    """
    metrics = {
        "MSE": calculate_mse(y_true, y_pred),
        "RMSE": calculate_rmse(y_true, y_pred),
        "MAE": calculate_mae(y_true, y_pred),
        "MAPE": calculate_mape(y_true, y_pred),
        "R2": calculate_r2(y_true, y_pred)
    }
    
    # 如果提供了位移数据，计算能量误差
    if displacement is not None:
        error_percent, energy_true, energy_pred = calculate_energy_error(displacement, y_true, y_pred)
        metrics["Energy_Error_Percent"] = error_percent
        metrics["Energy_True"] = energy_true
        metrics["Energy_Pred"] = energy_pred
    
    return metrics


def print_metrics(metrics):
    """
    打印评估指标
    
    参数:
        metrics (dict): 评估指标字典
    """
    print("\n性能评估指标:")
    print("-" * 40)
    print(f"均方误差 (MSE): {metrics['MSE']:.6f}")
    print(f"均方根误差 (RMSE): {metrics['RMSE']:.6f}")
    print(f"平均绝对误差 (MAE): {metrics['MAE']:.6f}")
    print(f"平均绝对百分比误差 (MAPE): {metrics['MAPE']:.2f}%")
    print(f"决定系数 (R²): {metrics['R2']:.6f}")
    
    if "Energy_Error_Percent" in metrics:
        print(f"能量耗散误差: {metrics['Energy_Error_Percent']:.2f}%")
        print(f"真实能量: {metrics['Energy_True']:.2f}")
        print(f"预测能量: {metrics['Energy_Pred']:.2f}")
    print("-" * 40)