# 单轴反复受拉滞回曲线预测指南

## 概述

本指南详细说明如何使用训练好的PINN模型预测单轴反复受拉的滞回曲线。系统设计为应变驱动模式，输入应变路径，输出应力响应及相关物理量。

## 工作流程

```
1. 生成输入数据 → 2. 加载模型 → 3. 预测响应 → 4. 分析结果
```

## 第一步：生成输入数据

### 使用数据生成脚本

运行 `generate_cyclic_input_data.py` 生成标准化的输入数据：

```bash
python generate_cyclic_input_data.py
```

脚本提供4种预定义的加载方案：

1. **标准递增加载** (standard)
   - 0.1‰ × 2循环 → 0.2‰ × 2循环 → ... → 0.6‰ × 2循环
   - 适用于材料性能的全面评估

2. **简单加载** (simple)
   - 0.2‰ × 3循环 → 0.4‰ × 3循环
   - 适用于快速测试

3. **单一幅值** (single)
   - 0.3‰ × 5循环
   - 适用于研究特定应变水平下的行为

4. **大幅值加载** (large)
   - 0.5‰ × 2循环 → 1.0‰ × 2循环 → 1.5‰ × 2循环
   - 适用于研究大变形行为

### 自定义输入数据格式

如果需要自定义加载路径，可以直接创建包含应变序列的数组：

```python
import numpy as np

# 方法1：手动创建应变路径
strain_path = []
# 第一个循环：0 → 0.0002 → 0
strain_path.extend(np.linspace(0, 0.0002, 100))
strain_path.extend(np.linspace(0.0002, 0, 100)[1:])
# 第二个循环：0 → 0.0004 → 0
strain_path.extend(np.linspace(0, 0.0004, 100)[1:])
strain_path.extend(np.linspace(0.0004, 0, 100)[1:])

strain_path = np.array(strain_path)
```

### 输入数据要求

- **数据类型**: 一维numpy数组
- **单位**: 应变值（无量纲），通常在0到0.002范围内
- **采样密度**: 建议每个加载/卸载段至少50个点，推荐100个点
- **起始点**: 必须从0开始

## 第二步：使用模型预测

### 运行预测脚本

使用 `predict_cyclic_tensile.py` 进行预测：

```bash
python predict_cyclic_tensile.py
```

### 编程方式调用

```python
from predict_cyclic_tensile import CyclicTensilePredictor

# 创建预测器
predictor = CyclicTensilePredictor()

# 加载模型（自动寻找最新的训练模型）
predictor.load_model()

# 生成加载路径
loading_scheme = [(0.0002, 2), (0.0004, 2), (0.0006, 2)]
strain_path, loading_info = predictor.generate_cyclic_loading_path(loading_scheme)

# 预测响应
results = predictor.predict_response(strain_path)

# 分析结果
analysis = predictor.analyze_hysteresis_loops(results, loading_info)

# 绘图和保存
predictor.plot_results(results, loading_info, analysis)
predictor.save_results(results, loading_info, analysis)
```

## 第三步：理解输出结果

### 主要输出数据

1. **时程数据** (`cyclic_tensile_time_history_*.csv`)
   - `time_step`: 时间步索引
   - `strain`: 总应变
   - `stress`: 应力响应 (MPa)
   - `damage`: 损伤变量 D (0-1)
   - `plastic_strain`: 塑性应变
   - `elastic_strain`: 弹性应变
   - `secant_modulus`: 割线模量 (MPa)

2. **循环分析数据** (`cyclic_tensile_cycles_analysis_*.csv`)
   - 每个循环的峰值应力、峰值损伤
   - 能量耗散
   - 残余应变
   - 刚度退化率

3. **综合报告** (`cyclic_tensile_report_*.json`)
   - 模型参数
   - 加载方案
   - 完整分析结果

### 可视化输出

生成的分析图包含9个子图：

1. **应力-应变滞回曲线**: 展示完整的滞回行为
2. **应力时程曲线**: 应力随时间的变化
3. **损伤演化曲线**: 损伤随应变的累积
4. **应变分量演化**: 塑性应变和弹性应变的分离
5. **割线模量退化**: 刚度随时间的退化
6. **峰值应力演化**: 各循环峰值应力的变化
7. **循环能量耗散**: 每个循环的耗散能量
8. **残余应变累积**: 塑性变形的累积

## 模型物理意义

### 输入处理
- **原始输入**: 总应变序列 ε(t)
- **模型输入**: 应变增量 Δε(t) = ε(t) - ε(t-1)
- **物理意义**: 增量形式符合路径依赖的本构关系

### 输出解释

1. **应力 σ**
   - 单位：MPa
   - 物理意义：材料的应力响应
   - 特征：随损伤累积而降低

2. **损伤变量 D**
   - 范围：0（无损伤）到 1（完全损伤）
   - 物理意义：材料劣化程度
   - 特征：单调递增，不可恢复

3. **塑性应变系数 ξ**
   - 范围：0 到 0.1
   - 物理意义：塑性流动的强度
   - 特征：受损伤影响，ξ_effective = ξ × (1 + 2D)

4. **塑性应变 εp**
   - 计算：εp_increment = ξ × max(0, Δε)
   - 物理意义：不可恢复的永久变形
   - 特征：仅在拉伸时增长

## 使用建议

### 1. 加载方案设计
- 从小应变开始，逐步增大
- 每个应变水平至少2个循环，以观察稳定行为
- 避免过大的应变增量跳跃

### 2. 数据点密度
- 标准测试：每段100点
- 快速预览：每段50点
- 高精度分析：每段200点

### 3. 结果验证
- 检查损伤单调性（必须单调递增）
- 验证应力-应变曲线的光滑性
- 确认残余应变的合理性

## 常见问题

### Q1: 为什么不能输入应力预测应变？
**A**: 模型基于应变驱动的增量理论，内部状态变量（损伤、塑性应变）依赖于完整的应变历史。应力驱动需要迭代求解，当前模型架构不支持。

### Q2: 最大可预测的应变是多少？
**A**: 建议不超过0.002（2‰），超出训练范围可能导致不准确的预测。

### Q3: 如何处理压缩？
**A**: 当前模型仅考虑拉伸损伤。压缩时假设损伤不发展，塑性应变不增长。

### Q4: 预测速度如何？
**A**: 对于2000个数据点，预测时间通常小于1秒（不含绘图）。

## 扩展应用

### 1. 批量预测
```python
# 批量处理多个加载方案
schemes = [
    [(0.0001, 5)],  # 小应变多循环
    [(0.0003, 3)],  # 中等应变
    [(0.0005, 2)],  # 大应变
]

for i, scheme in enumerate(schemes):
    strain_path, loading_info = predictor.generate_cyclic_loading_path(scheme)
    results = predictor.predict_response(strain_path)
    # 保存结果...
```

### 2. 参数研究
通过修改加载方案，可以研究：
- 应变幅值对损伤演化的影响
- 循环次数对残余应变的影响
- 加载速率效应（通过改变点密度模拟）

### 3. 与实验对比
将实验应变数据导入，使用模型预测，与实验应力对比：
```python
# 读取实验数据
exp_strain = pd.read_excel('experiment.xlsx')['strain'].values
# 预测
results = predictor.predict_response(exp_strain)
# 对比...
```

## 参考文献

1. 吴建营, 李杰. 混凝土弹塑性损伤本构模型研究
2. 相关PINN理论文献

## 联系方式

如有问题或建议，请参考项目README文件。 