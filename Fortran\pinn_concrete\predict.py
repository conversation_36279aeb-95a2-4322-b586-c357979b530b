import os
import torch
import matplotlib.pyplot as plt
import numpy as np
import argparse

# 导入自定义模块
from data_processor import DataProcessor
from pinn_model import PINN, PINNTrainer
from visualization import Visualizer

# 导入中文字体配置
from font_config import configure_chinese_font

def predict_from_model(model_path, data_path=None, strain_values=None, L0=100.0, A=10000.0, 
                      E=30000.0, eps0=0.0001, k=100.0, save_dir='./results'):
    """
    使用训练好的模型进行预测
    
    参数:
    model_path: 模型检查点路径
    data_path: 数据文件路径，用于加载归一化参数
    strain_values: 自定义应变值列表，如果为None则使用数据文件中的应变
    L0: 试件初始长度(mm)
    A: 横截面积(mm²)
    E: 弹性模量(MPa)
    eps0: 损伤起始应变阈值
    k: 损伤演化参数
    save_dir: 结果保存目录
    """
    # 创建结果目录
    os.makedirs(save_dir, exist_ok=True)
    
    # 配置中文字体
    configure_chinese_font()
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载数据处理器
    if data_path:
        data_processor = DataProcessor(data_path, L0=L0, A=A)
        data_processor.load_data()
        data_processor.normalize_data()
    else:
        print("未提供数据文件，将使用自定义应变值")
        if strain_values is None:
            strain_values = np.linspace(0, 0.01, 100)  # 默认应变范围
        data_processor = None
    
    # 创建模型
    model = PINN(hidden_layers=1, hidden_units=10)
    
    # 加载模型参数
    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    print(f"模型已从 {model_path} 加载")
    
    # 创建训练器（仅用于预测）
    trainer = PINNTrainer(model, E=E, eps0=eps0, k=k, device=device)
    
    # 进行预测
    if data_processor:
        # 使用数据文件中的应变
        strain_orig = data_processor.strain
        strain_norm = (strain_orig - data_processor.strain_mean) / data_processor.strain_std
        strain_tensor = torch.FloatTensor(strain_norm.reshape(-1, 1))
        
        # 预测
        pred_stress_norm, pred_damage = trainer.predict(strain_tensor)
        
        # 反归一化
        pred_stress = pred_stress_norm.flatten() * data_processor.stress_std + data_processor.stress_mean
        
        # 可视化
        visualizer = Visualizer(data_processor, trainer)
        visualizer.plot_all(save_dir=save_dir)
        
        # 保存预测结果
        results = np.column_stack((strain_orig, pred_stress, pred_damage.flatten()))
        np.savetxt(os.path.join(save_dir, 'prediction_results.csv'), 
                  results, 
                  delimiter=',', 
                  header='strain,stress,damage', 
                  comments='')
    else:
        # 使用自定义应变值
        strain_tensor = torch.FloatTensor(strain_values.reshape(-1, 1))
        
        # 预测
        pred_stress, pred_damage = trainer.predict(strain_tensor)
        
        # 绘制预测结果
        plt.figure(figsize=(12, 8))
        
        # 应力-应变曲线
        plt.subplot(2, 1, 1)
        plt.plot(strain_values, pred_stress, 'r-', linewidth=2)
        plt.xlabel('应变 ε')
        plt.ylabel('应力 σ (MPa)')
        plt.title('预测应力-应变曲线')
        plt.grid(True)
        
        # 损伤-应变曲线
        plt.subplot(2, 1, 2)
        plt.plot(strain_values, pred_damage, 'b-', linewidth=2)
        plt.xlabel('应变 ε')
        plt.ylabel('损伤变量 d')
        plt.title('预测损伤-应变曲线')
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'custom_prediction.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 保存预测结果
        results = np.column_stack((strain_values, pred_stress.flatten(), pred_damage.flatten()))
        np.savetxt(os.path.join(save_dir, 'custom_prediction_results.csv'), 
                  results, 
                  delimiter=',', 
                  header='strain,stress,damage', 
                  comments='')
    
    print(f"预测结果已保存至 {os.path.abspath(save_dir)}")
    return results

def predict_custom_strain(model_path, strain_values, save_dir='results'):
    """预测自定义应变下的应力和损伤"""
    # 配置中文字体
    configure_chinese_font()
    
    if data_processor:
        # 使用数据文件中的应变
        strain_orig = data_processor.strain
        strain_norm = (strain_orig - data_processor.strain_mean) / data_processor.strain_std
        strain_tensor = torch.FloatTensor(strain_norm.reshape(-1, 1))
        
        # 预测
        pred_stress_norm, pred_damage = trainer.predict(strain_tensor)
        
        # 反归一化
        pred_stress = pred_stress_norm.flatten() * data_processor.stress_std + data_processor.stress_mean
        
        # 可视化
        visualizer = Visualizer(data_processor, trainer)
        visualizer.plot_all(save_dir=save_dir)
        
        # 保存预测结果
        results = np.column_stack((strain_orig, pred_stress, pred_damage.flatten()))
        np.savetxt(os.path.join(save_dir, 'prediction_results.csv'), 
                  results, 
                  delimiter=',', 
                  header='strain,stress,damage', 
                  comments='')
    else:
        # 使用自定义应变值
        strain_tensor = torch.FloatTensor(strain_values.reshape(-1, 1))
        
        # 预测
        pred_stress, pred_damage = trainer.predict(strain_tensor)
        
        # 绘制预测结果
        plt.figure(figsize=(12, 8))
        
        # 应力-应变曲线
        plt.subplot(2, 1, 1)
        plt.plot(strain_values, pred_stress, 'r-', linewidth=2)
        plt.xlabel('应变 ε')
        plt.ylabel('应力 σ (MPa)')
        plt.title('预测应力-应变曲线')
        plt.grid(True)
        
        # 损伤-应变曲线
        plt.subplot(2, 1, 2)
        plt.plot(strain_values, pred_damage, 'b-', linewidth=2)
        plt.xlabel('应变 ε')
        plt.ylabel('损伤变量 d')
        plt.title('预测损伤-应变曲线')
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'custom_prediction.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 保存预测结果
        results = np.column_stack((strain_values, pred_stress.flatten(), pred_damage.flatten()))
        np.savetxt(os.path.join(save_dir, 'custom_prediction_results.csv'), 
                  results, 
                  delimiter=',', 
                  header='strain,stress,damage', 
                  comments='')
    
    print(f"预测结果已保存至 {os.path.abspath(save_dir)}")
    return results

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='PINN混凝土单轴本构预测')
    parser.add_argument('--model', type=str, default='./checkpoints/pinn_model.pth', help='模型检查点路径')
    parser.add_argument('--data', type=str, default='../data.xlsx', help='数据文件路径')
    parser.add_argument('--custom', action='store_true', help='使用自定义应变范围')
    parser.add_argument('--min_strain', type=float, default=0.0, help='最小应变值')
    parser.add_argument('--max_strain', type=float, default=0.01, help='最大应变值')
    parser.add_argument('--num_points', type=int, default=100, help='应变点数')
    parser.add_argument('--E', type=float, default=30000.0, help='弹性模量(MPa)')
    parser.add_argument('--eps0', type=float, default=0.0001, help='损伤起始应变阈值')
    parser.add_argument('--k', type=float, default=100.0, help='损伤演化参数')
    parser.add_argument('--output', type=str, default='./results', help='结果保存目录')
    
    args = parser.parse_args()
    
    # 设置自定义应变范围
    if args.custom:
        strain_values = np.linspace(args.min_strain, args.max_strain, args.num_points)
        predict_from_model(
            model_path=args.model,
            data_path=None,
            strain_values=strain_values,
            E=args.E,
            eps0=args.eps0,
            k=args.k,
            save_dir=args.output
        )
    else:
        predict_from_model(
            model_path=args.model,
            data_path=args.data,
            E=args.E,
            eps0=args.eps0,
            k=args.k,
            save_dir=args.output
        )

if __name__ == "__main__":
    main()