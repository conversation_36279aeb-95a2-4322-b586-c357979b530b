import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm

from model.pinn import PINN
from utils.data_generator import generate_training_data
from utils.visualization import plot_results
from utils.evaluation import evaluate_model


def main():
    # 设置随机种子以确保结果可复现
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 设置PyTorch梯度检查点
    torch.autograd.set_detect_anomaly(True)  # 启用异常检测
    
    # 模型参数 - 增加隐藏层维度以提高模型容量
    input_dim = 3  # 空间坐标 (x, y, z)
    hidden_dim = 512  # 增加隐藏层维度
    output_dim = 4  # 位移场 (u, v, w) 和损伤变量 θ
    
    # 物理参数
    E0 = 210e9  # 杨氏模量 (Pa)
    nu = 0.3    # 泊松比
    
    # 创建PINN模型
    model = PINN(input_dim, hidden_dim, output_dim, E0, nu).to(device)
    
    # 生成初始训练数据 - 使用单点损伤模式
    train_data = generate_training_data(damage_mode='single', stage=0)
    print("初始训练数据生成完成，开始训练...")
    
    # 训练模型 - 使用增强版训练策略
    train_model(model, train_data, device, epochs=3000)  # 增加训练轮数
    
    # 评估模型 - 使用多种损伤模式进行测试
    print("\n开始评估模型性能...")
    # 单点损伤测试
    test_data_single = generate_training_data(is_test=True, damage_mode='single')
    metrics_single = evaluate_model(model, test_data_single, device)
    print(f"单点损伤测试结果:\n{metrics_single}")
    
    # 多点损伤测试
    test_data_multi = generate_training_data(is_test=True, damage_mode='multi')
    metrics_multi = evaluate_model(model, test_data_multi, device)
    print(f"多点损伤测试结果:\n{metrics_multi}")
    
    # 随机损伤测试
    test_data_random = generate_training_data(is_test=True, damage_mode='random')
    metrics_random = evaluate_model(model, test_data_random, device)
    print(f"随机损伤测试结果:\n{metrics_random}")
    
    # 使用多点损伤数据进行可视化
    print("\n生成可视化结果...")
    test_data = test_data_multi  # 使用多点损伤数据进行可视化
    metrics = evaluate_model(model, test_data, device)
    print(f"Evaluation metrics: {metrics}")
    
    # 可视化结果
    plot_results(model, test_data)
    

def train_model(model, train_data, device, epochs=2000):
    """增强版四阶段课程学习训练策略实现"""
    # 增加训练轮数以确保充分学习
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)  # 提高初始学习率以加速收敛
    
    # 使用更复杂的学习率调度器 - 先预热，再余弦衰减
    warmup_epochs = 100
    cosine_epochs = epochs - warmup_epochs
    scheduler = torch.optim.lr_scheduler.SequentialLR(
        optimizer,
        schedulers=[
            torch.optim.lr_scheduler.LinearLR(optimizer, start_factor=0.1, end_factor=1.0, total_iters=warmup_epochs),
            torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=cosine_epochs, eta_min=1e-6)
        ],
        milestones=[warmup_epochs]
    )
    
    # 损失权重初始化
    alpha = 1.0  # 数据项权重
    beta = 0.05  # 物理项权重 - 初始值更小，逐步增加
    gamma = 0.1  # 损伤正则项权重
    
    # 定义训练阶段
    stages = [
        {"epoch": 0, "name": "预训练阶段 - 单点局部损伤", "beta": 0.05, "gamma": 0.1, "damage_mode": "single", "noise": 0.01, "stage": 0},
        {"epoch": 200, "name": "物理增强阶段 - 多区域复杂损伤", "beta": 0.5, "gamma": 0.2, "damage_mode": "multi", "noise": 0.03, "stage": 1},
        {"epoch": 600, "name": "全域物理增强阶段 - 随机损伤", "beta": 1.0, "gamma": 0.5, "damage_mode": "random", "noise": 0.05, "stage": 2},
        {"epoch": 1200, "name": "强化物理约束阶段 - 高噪声鲁棒性", "beta": 2.0, "gamma": 1.0, "damage_mode": "random", "noise": 0.1, "stage": 3}
    ]
    
    # 当前阶段索引
    current_stage_idx = 0
    current_stage = stages[current_stage_idx]
    
    # 训练循环
    for epoch in tqdm(range(epochs)):
        # 检查是否需要进入下一阶段
        for i, stage in enumerate(stages):
            if epoch == stage["epoch"] and i >= current_stage_idx:
                current_stage = stage
                current_stage_idx = i
                print(f"进入阶段{i+1}: {stage['name']}")
                print(f"物理权重: {stage['beta']}, 损伤权重: {stage['gamma']}, 噪声水平: {stage['noise']}")
                
                # 根据当前阶段生成新的训练数据
                train_data = generate_training_data(
                    is_test=False,
                    noise_level=stage["noise"],
                    damage_mode=stage["damage_mode"],
                    stage=stage["stage"]
                )
                break
        
        # 设置当前阶段的权重
        beta = current_stage["beta"]
        gamma = current_stage["gamma"]
        
        # 动态调整数据权重 - 基于训练进度
        progress = min(1.0, epoch / (epochs * 0.8))  # 训练完成80%时达到最大值
        alpha = 1.0 + 0.5 * progress  # 随着训练进行，逐步增加数据项权重
        
        # 前向传播
        model.train()
        
        # 动态批量大小 - 随着训练进行逐步增加
        base_batch_size = 256
        batch_size = min(512, base_batch_size + int(progress * 256))  # 从256逐步增加到512
        
        # 从训练数据中采样 - 使用聚焦采样策略
        if current_stage_idx >= 1:  # 第二阶段开始使用聚焦采样
            # 先随机采样一批点
            idx_random = np.random.choice(len(train_data['coords']), batch_size // 2)
            
            # 然后在损伤区域附近重点采样
            damage_values = train_data['damage'].flatten()
            damage_probs = damage_values / (np.sum(damage_values) + 1e-10)  # 归一化为概率
            idx_focused = np.random.choice(
                len(train_data['coords']), 
                batch_size // 2, 
                p=damage_probs,
                replace=True
            )
            
            # 合并两种采样
            idx = np.concatenate([idx_random, idx_focused])
        else:
            # 第一阶段使用普通随机采样
            idx = np.random.choice(len(train_data['coords']), batch_size)
        
        coords = torch.tensor(train_data['coords'][idx], dtype=torch.float32).to(device)
        u_true = torch.tensor(train_data['displacement'][idx], dtype=torch.float32).to(device)
        
        # 确保坐标有梯度
        coords.requires_grad_(True)
        
        # 计算预测值和损失
        u_pred, stress_pred, damage_pred = model(coords)
        
        # 计算数据损失 - 使用加权MSE，更关注大位移区域
        displacement_weights = 1.0 + torch.norm(u_true, dim=1, keepdim=True)  # 大位移区域权重更高
        data_loss = torch.mean(displacement_weights * (u_pred - u_true)**2)
        
        # 计算物理损失 - 平衡方程残差
        physics_loss = model.compute_pde_residual(coords)
        
        # 计算损伤正则项 - 传入坐标和损伤预测值以计算梯度惩罚
        damage_loss = model.compute_damage_regularization(coords, damage_pred)
        
        # 总损失 - 使用动态权重
        total_loss = alpha * data_loss + beta * physics_loss + gamma * damage_loss
        
        # 反向传播和优化
        optimizer.zero_grad()
        total_loss.backward()
        
        # 梯度裁剪，防止梯度爆炸
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        # 更新学习率
        scheduler.step()
        
        # 每50个epoch打印一次损失
        if epoch % 50 == 0:
            print(f"Epoch {epoch}/{epochs}, Loss: {total_loss.item():.6f}, "
                  f"Data: {data_loss.item():.6f}, Physics: {physics_loss.item():.6f}, "
                  f"Damage: {damage_loss.item():.6f}, "
                  f"LR: {scheduler.get_last_lr()[0]:.6f}")
            
            # 每200个epoch保存一次中间模型
            if epoch % 200 == 0 and epoch > 0:
                save_dir = 'checkpoints'
                if not os.path.exists(save_dir):
                    os.makedirs(save_dir)
                checkpoint_path = os.path.join(save_dir, f'pinn_model_epoch_{epoch}.pth')
                torch.save(model.state_dict(), checkpoint_path)
                print(f"中间模型已保存至 {checkpoint_path}")
    
    # 保存最终模型权重
    save_dir = 'checkpoints'
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    torch.save(model.state_dict(), os.path.join(save_dir, 'pinn_model.pth'))
    print("训练完成! 模型已保存至", os.path.join(save_dir, 'pinn_model.pth'))


if __name__ == "__main__":
    main()