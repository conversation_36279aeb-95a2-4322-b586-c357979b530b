"""
测试塑性应变计算
验证修改后的塑性应变是否能正确累积
"""

import numpy as np
import matplotlib.pyplot as plt
from font_config import setup_font

def test_plastic_strain_calculation():
    """测试塑性应变计算逻辑"""
    print("测试塑性应变计算...")
    
    # 模拟参数
    E0 = 30000.0  # MPa
    xi_plus = 0.1
    xi_minus = 0.2
    
    # 创建一个简单的循环加载路径
    strain_path = []
    # 第一个循环
    strain_path.extend(np.linspace(0, 0.002, 50))      # 拉伸到0.002
    strain_path.extend(np.linspace(0.002, -0.003, 100)) # 压缩到-0.003
    strain_path.extend(np.linspace(-0.003, 0, 75))      # 回到0
    
    strain_path = np.array(strain_path)
    n_steps = len(strain_path)
    
    # 初始化
    plastic_strain = np.zeros(n_steps)
    elastic_strain = np.zeros(n_steps)
    stress = np.zeros(n_steps)
    
    # 计算
    for i in range(1, n_steps):
        strain_increment = strain_path[i] - strain_path[i-1]
        
        # 塑性应变增量计算（新逻辑）
        if strain_increment > 0:
            # 拉伸增量
            delta_ep = xi_plus * strain_increment
        else:
            # 压缩增量
            delta_ep = xi_minus * abs(strain_increment)
            delta_ep = -delta_ep  # 压缩塑性应变为负
        
        plastic_strain[i] = plastic_strain[i-1] + delta_ep
        elastic_strain[i] = strain_path[i] - plastic_strain[i]
        stress[i] = E0 * elastic_strain[i] * 0.9  # 简化计算，考虑损伤影响
    
    # 打印关键结果
    print(f"\n关键结果:")
    print(f"最大拉伸应变: {np.max(strain_path):.6f}")
    print(f"最大压缩应变: {np.min(strain_path):.6f}")
    print(f"累积塑性应变: {plastic_strain[-1]:.6f}")
    print(f"最终弹性应变: {elastic_strain[-1]:.6f}")
    print(f"最终总应变: {strain_path[-1]:.6f}")
    
    # 绘图
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 1. 应力-应变曲线
    ax1 = axes[0, 0]
    ax1.plot(strain_path, stress, 'b-', linewidth=2)
    ax1.set_xlabel('应变')
    ax1.set_ylabel('应力 (MPa)')
    ax1.set_title('应力-应变滞回曲线')
    ax1.grid(True, alpha=0.3)
    ax1.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax1.axvline(x=0, color='k', linestyle='--', alpha=0.5)
    
    # 2. 塑性应变演化
    ax2 = axes[0, 1]
    time_steps = np.arange(n_steps)
    ax2.plot(time_steps, plastic_strain, 'r-', linewidth=2, label='塑性应变')
    ax2.plot(time_steps, strain_path, 'b--', linewidth=1, label='总应变')
    ax2.set_xlabel('时间步')
    ax2.set_ylabel('应变')
    ax2.set_title('塑性应变演化')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 弹性应变 vs 总应变
    ax3 = axes[1, 0]
    ax3.plot(strain_path, elastic_strain, 'g-', linewidth=2)
    ax3.set_xlabel('总应变')
    ax3.set_ylabel('弹性应变')
    ax3.set_title('弹性应变 vs 总应变')
    ax3.grid(True, alpha=0.3)
    ax3.plot([-0.004, 0.003], [-0.004, 0.003], 'k--', alpha=0.5, label='1:1线')
    ax3.legend()
    
    # 4. 应变增量
    ax4 = axes[1, 1]
    strain_increments = np.diff(strain_path, prepend=0)
    ax4.plot(time_steps, strain_increments, 'k-', linewidth=1)
    ax4.set_xlabel('时间步')
    ax4.set_ylabel('应变增量')
    ax4.set_title('应变增量历史')
    ax4.grid(True, alpha=0.3)
    ax4.axhline(y=0, color='r', linestyle='--', alpha=0.5)
    
    plt.tight_layout()
    plt.savefig('test_plastic_strain.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return plastic_strain[-1]

def compare_old_new_logic():
    """比较新旧塑性应变计算逻辑"""
    print("\n比较新旧塑性应变计算逻辑...")
    
    # 测试参数
    xi_plus = 0.1
    xi_minus = 0.2
    strain_increments = [0.001, -0.002, 0.0015, -0.001]
    
    print("\n应变增量序列:", strain_increments)
    print(f"塑性参数: ξ+ = {xi_plus}, ξ- = {xi_minus}")
    
    # 旧逻辑
    plastic_old = 0
    print("\n旧逻辑计算:")
    for inc in strain_increments:
        if inc > 0:
            delta_ep = xi_plus * max(0, inc)
        else:
            delta_ep = xi_minus * inc
        plastic_old += delta_ep
        print(f"  Δε = {inc:6.4f}, Δεp = {delta_ep:6.4f}, εp_total = {plastic_old:6.4f}")
    
    # 新逻辑
    plastic_new = 0
    print("\n新逻辑计算:")
    for inc in strain_increments:
        if inc > 0:
            delta_ep = xi_plus * inc
        else:
            delta_ep = xi_minus * abs(inc)
            delta_ep = -delta_ep
        plastic_new += delta_ep
        print(f"  Δε = {inc:6.4f}, Δεp = {delta_ep:6.4f}, εp_total = {plastic_new:6.4f}")
    
    print(f"\n最终塑性应变:")
    print(f"  旧逻辑: {plastic_old:.6f}")
    print(f"  新逻辑: {plastic_new:.6f}")

if __name__ == "__main__":
    # 测试塑性应变计算
    final_plastic = test_plastic_strain_calculation()
    
    # 比较新旧逻辑
    compare_old_new_logic()
    
    print(f"\n✓ 测试完成！最终塑性应变: {final_plastic:.6f}") 