# 单轴反复受拉残余应变累积改进说明

## 问题描述

原始版本中，每个循环都是从0应变开始加载，然后卸载回0应变。这不符合实际情况，因为：

1. **塑性变形不可恢复**：每次加载产生的塑性应变会保留
2. **残余应变逐次累积**：每个循环应该在上一个循环的残余应变基础上继续

## 改进方案

在 `predict_cyclic_tensile_fixed.py` 中添加了新方法 `generate_realistic_cyclic_loading_path`：

### 1. 核心逻辑

```python
# 初始化
current_position = 0.0  # 当前应变位置
accumulated_plastic_strain = 0.0  # 累积塑性应变

for target_strain, n_cycles in loading_scheme:
    for cycle in range(n_cycles):
        # 加载：从当前位置 → 目标应变
        loading_segment = np.linspace(current_position, target_strain, n_points)
        
        # 计算本次加载产生的塑性应变增量
        plastic_strain_increment = xi_effective * strain_increment_total
        accumulated_plastic_strain += plastic_strain_increment
        
        # 卸载终点 = 累积的塑性应变（残余应变）
        unload_end_strain = accumulated_plastic_strain
        
        # 卸载：从目标应变 → 残余应变
        unloading_segment = np.linspace(target_strain, unload_end_strain, n_points)
        
        # 更新当前位置为卸载终点
        current_position = unload_end_strain
```

### 2. 关键改进点

1. **累积效应**：
   - 塑性应变持续累积：`accumulated_plastic_strain += plastic_strain_increment`
   - 损伤也持续累积，不会重置

2. **加载起点**：
   - 第1个循环：从0开始
   - 后续循环：从上一个循环的残余应变开始

3. **卸载终点**：
   - 不再卸载到0
   - 卸载到当前的累积塑性应变位置

### 3. 物理意义

这种改进更符合混凝土材料的实际行为：

1. **路径依赖性**：每个循环的行为依赖于之前的加载历史
2. **损伤累积**：损伤随着循环次数增加而累积
3. **刚度退化**：由于损伤累积，材料刚度逐渐降低
4. **残余变形增长**：残余应变随循环次数单调增加

### 4. 预期结果

改进后的预测应该显示：

1. **滞回环不回到原点**：每个循环结束时有残余应变
2. **残余应变递增**：
   - 第1循环：约0.0005‰
   - 第6循环：约0.01-0.02‰
   - 第12循环：约0.05-0.10‰

3. **滞回环形状变化**：
   - 初期：饱满的滞回环
   - 后期：由于损伤累积，滞回环变窄

### 5. 实现细节

为了准确估算残余应变，代码中使用了简化的物理模型：

```python
# 估算损伤演化
elastic_strain_peak = target_strain - accumulated_plastic_strain
Y_peak = E0 * abs(elastic_strain_peak)
if Y_peak > r_max:
    # 更新损伤
    new_damage = 1 - (term1 + term2)

# 估算塑性应变增量
avg_damage = (accumulated_damage + new_damage) / 2
xi_effective = xi_base * (1 + avg_damage * 2.0)
plastic_strain_increment = xi_effective * strain_increment_total
```

### 6. 使用方法

直接运行改进后的脚本：

```bash
python predict_cyclic_tensile_fixed.py
```

脚本会自动使用 `generate_realistic_cyclic_loading_path` 方法生成考虑残余应变累积的加载路径。

### 7. 验证方法

可以通过以下方式验证改进效果：

1. 查看生成的滞回曲线图，确认循环不回到原点
2. 检查残余应变累积图，确认单调递增
3. 对比实验数据，验证残余应变量级是否合理

## 总结

这个改进使预测结果更加符合混凝土材料在循环加载下的真实行为，特别是正确反映了残余应变的累积效应。 