import numpy as np
import matplotlib.pyplot as plt
import torch
import os

# 导入中文字体配置
from font_config import configure_chinese_font

class Visualizer:
    def __init__(self, data_processor, trainer):
        """
        初始化可视化器
        
        参数:
        data_processor: 数据处理器实例
        trainer: PINN训练器实例
        """
        self.data_processor = data_processor
        self.trainer = trainer
        
        # 配置中文字体
        configure_chinese_font()
    
    def plot_stress_strain_comparison(self, save_path=None):
        """
        绘制预测应力-应变曲线与实验曲线对比
        
        参数:
        save_path: 保存图像的路径，如果为None则显示图像
        """
        # 获取原始数据
        strain_orig = self.data_processor.strain
        stress_orig = self.data_processor.stress
        
        # 创建用于预测的归一化应变数据
        strain_norm = (strain_orig - self.data_processor.strain_mean) / self.data_processor.strain_std
        strain_norm_tensor = torch.FloatTensor(strain_norm.reshape(-1, 1))
        
        # 预测应力和损伤
        pred_stress_norm, _ = self.trainer.predict(strain_norm_tensor)
        
        # 反归一化预测应力
        pred_stress = pred_stress_norm.flatten() * self.data_processor.stress_std + self.data_processor.stress_mean
        
        # 绘制对比图
        plt.figure(figsize=(10, 6))
        plt.plot(strain_orig, stress_orig, 'b-', linewidth=2, label='实验数据')
        plt.plot(strain_orig, pred_stress, 'r--', linewidth=2, label='PINN预测')
        plt.xlabel('应变 ε')
        plt.ylabel('应力 σ (MPa)')
        plt.title('混凝土单轴应力-应变曲线对比')
        plt.legend()
        plt.grid(True)
        
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"应力-应变对比图已保存至 {save_path}")
        else:
            plt.show()
        
        return True
    
    def plot_damage_evolution(self, save_path=None):
        """
        绘制损伤变量随应变演化曲线
        
        参数:
        save_path: 保存图像的路径，如果为None则显示图像
        """
        # 获取原始应变数据
        strain_orig = self.data_processor.strain
        
        # 创建用于预测的归一化应变数据
        strain_norm = (strain_orig - self.data_processor.strain_mean) / self.data_processor.strain_std
        strain_norm_tensor = torch.FloatTensor(strain_norm.reshape(-1, 1))
        
        # 预测损伤
        _, pred_damage = self.trainer.predict(strain_norm_tensor)
        pred_damage = pred_damage.flatten()
        
        # 计算理论损伤演化
        theoretical_damage = 1 - np.exp(-self.trainer.k * np.maximum(strain_orig - self.trainer.eps0, 0))
        
        # 绘制损伤演化曲线
        plt.figure(figsize=(10, 6))
        plt.plot(strain_orig, pred_damage, 'r-', linewidth=2, label='PINN预测损伤')
        plt.plot(strain_orig, theoretical_damage, 'g--', linewidth=2, label='理论损伤演化')
        plt.xlabel('应变 ε')
        plt.ylabel('损伤变量 d')
        plt.title('混凝土损伤演化曲线')
        plt.legend()
        plt.grid(True)
        
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"损伤演化曲线已保存至 {save_path}")
        else:
            plt.show()
        
        return True
    
    def plot_constitutive_verification(self, save_path=None):
        """
        验证本构关系 σ = (1-d)Eε
        
        参数:
        save_path: 保存图像的路径，如果为None则显示图像
        """
        # 获取原始数据
        strain_orig = self.data_processor.strain
        stress_orig = self.data_processor.stress
        
        # 创建用于预测的归一化应变数据
        strain_norm = (strain_orig - self.data_processor.strain_mean) / self.data_processor.strain_std
        strain_norm_tensor = torch.FloatTensor(strain_norm.reshape(-1, 1))
        
        # 预测应力和损伤
        pred_stress_norm, pred_damage = self.trainer.predict(strain_norm_tensor)
        
        # 反归一化预测应力
        pred_stress = pred_stress_norm.flatten() * self.data_processor.stress_std + self.data_processor.stress_mean
        pred_damage = pred_damage.flatten()
        
        # 计算本构应力
        constitutive_stress = (1 - pred_damage) * self.trainer.E * strain_orig
        
        # 绘制对比图
        plt.figure(figsize=(10, 6))
        plt.plot(strain_orig, stress_orig, 'b-', linewidth=2, label='实验数据')
        plt.plot(strain_orig, pred_stress, 'r--', linewidth=2, label='PINN预测应力')
        plt.plot(strain_orig, constitutive_stress, 'g-.', linewidth=2, label='本构计算应力')
        plt.xlabel('应变 ε')
        plt.ylabel('应力 σ (MPa)')
        plt.title('本构关系验证')
        plt.legend()
        plt.grid(True)
        
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"本构关系验证图已保存至 {save_path}")
        else:
            plt.show()
        
        return True
    
    def plot_hysteresis_curve(self, save_path=None):
        """
        绘制滞回曲线 - 专门用于评估模型对滞回特性的捕捉能力
        
        参数:
        save_path: 保存图像的路径，如果为None则显示图像
        """
        # 获取原始数据
        strain_orig = self.data_processor.strain
        stress_orig = self.data_processor.stress
        
        # 创建用于预测的归一化应变数据
        strain_norm = (strain_orig - self.data_processor.strain_mean) / self.data_processor.strain_std
        strain_norm_tensor = torch.FloatTensor(strain_norm.reshape(-1, 1))
        
        # 预测应力和损伤
        pred_stress_norm, _ = self.trainer.predict(strain_norm_tensor)
        
        # 反归一化预测应力
        pred_stress = pred_stress_norm.flatten() * self.data_processor.stress_std + self.data_processor.stress_mean
        
        # 绘制滞回曲线
        plt.figure(figsize=(12, 8))
        
        # 使用不同颜色标记加载和卸载阶段
        if len(strain_orig) > 1:
            # 计算应变增量
            strain_increments = np.zeros_like(strain_orig)
            strain_increments[1:] = strain_orig[1:] - strain_orig[:-1]
            
            # 确定加载和卸载阶段
            loading_mask = (strain_increments > 0)
            unloading_mask = (strain_increments < 0)
            
            # 绘制实验数据
            plt.scatter(strain_orig[loading_mask], stress_orig[loading_mask], 
                       c='blue', marker='o', s=30, label='实验数据-加载')
            plt.scatter(strain_orig[unloading_mask], stress_orig[unloading_mask], 
                       c='green', marker='s', s=30, label='实验数据-卸载')
            
            # 绘制预测数据
            plt.scatter(strain_orig[loading_mask], pred_stress[loading_mask], 
                       c='red', marker='^', s=30, label='PINN预测-加载')
            plt.scatter(strain_orig[unloading_mask], pred_stress[unloading_mask], 
                       c='purple', marker='v', s=30, label='PINN预测-卸载')
            
            # 连接点以显示路径
            plt.plot(strain_orig, stress_orig, 'b-', alpha=0.5)
            plt.plot(strain_orig, pred_stress, 'r--', alpha=0.5)
        else:
            # 如果数据点太少，简单绘制
            plt.plot(strain_orig, stress_orig, 'bo-', label='实验数据')
            plt.plot(strain_orig, pred_stress, 'r^--', label='PINN预测')
        
        plt.xlabel('应变 ε')
        plt.ylabel('应力 σ (MPa)')
        plt.title('混凝土滞回曲线对比')
        plt.legend()
        plt.grid(True)
        
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"滞回曲线对比图已保存至 {save_path}")
        else:
            plt.show()
        
        return True
    
    def plot_all(self, save_dir='results'):
        """
        绘制所有可视化图表
        
        参数:
        save_dir: 结果保存目录
        """
        os.makedirs(save_dir, exist_ok=True)
        
        # 绘制应力-应变对比图
        self.plot_stress_strain_comparison(save_path=os.path.join(save_dir, 'stress_strain_comparison.png'))
        
        # 绘制滞回曲线对比图
        self.plot_hysteresis_curve(save_path=os.path.join(save_dir, 'hysteresis_curve_comparison.png'))
        
        # 绘制损伤演化曲线
        self.plot_damage_evolution(save_path=os.path.join(save_dir, 'damage_evolution.png'))
        
        # 绘制本构关系验证图
        self.plot_constitutive_verification(save_path=os.path.join(save_dir, 'constitutive_verification.png'))
        
        # 绘制训练历史
        self.trainer.plot_history(save_path=os.path.join(save_dir, 'training_history.png'))
        
        return True