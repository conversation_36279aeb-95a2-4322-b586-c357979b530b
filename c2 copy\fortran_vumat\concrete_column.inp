*HEADING
混凝土柱VUMAT验证模型 - 循环荷载下的滞回曲线
** 单位: MPa, mm, s
** 正方形截面柱体: 200mm×200mm截面, 长度800mm
**
*PREPRINT, ECHO=NO, MODEL=NO, HISTORY=NO, CONTACT=NO
**
** ----------------------------------------------------------------
** 节点定义 - 正方形截面 200mm×200mm
** ----------------------------------------------------------------
*NODE, NSET=ALL_NODES
** 底面节点 (Z=0) - 3×3网格
      1,        -100.,        -100.,           0.
      2,           0.,        -100.,           0.
      3,         100.,        -100.,           0.
      4,        -100.,           0.,           0.
      5,           0.,           0.,           0.
      6,         100.,           0.,           0.
      7,        -100.,         100.,           0.
      8,           0.,         100.,           0.
      9,         100.,         100.,           0.
**
** 中间层1节点 (Z=200) - 3×3网格
     11,        -100.,        -100.,         200.
     12,           0.,        -100.,         200.
     13,         100.,        -100.,         200.
     14,        -100.,           0.,         200.
     15,           0.,           0.,         200.
     16,         100.,           0.,         200.
     17,        -100.,         100.,         200.
     18,           0.,         100.,         200.
     19,         100.,         100.,         200.
**
** 中间层2节点 (Z=400) - 3×3网格
     21,        -100.,        -100.,         400.
     22,           0.,        -100.,         400.
     23,         100.,        -100.,         400.
     24,        -100.,           0.,         400.
     25,           0.,           0.,         400.
     26,         100.,           0.,         400.
     27,        -100.,         100.,         400.
     28,           0.,         100.,         400.
     29,         100.,         100.,         400.
**
** 中间层3节点 (Z=600) - 3×3网格
     31,        -100.,        -100.,         600.
     32,           0.,        -100.,         600.
     33,         100.,        -100.,         600.
     34,        -100.,           0.,         600.
     35,           0.,           0.,         600.
     36,         100.,           0.,         600.
     37,        -100.,         100.,         600.
     38,           0.,         100.,         600.
     39,         100.,         100.,         600.
**
** 顶面节点 (Z=800) - 3×3网格
     41,        -100.,        -100.,         800.
     42,           0.,        -100.,         800.
     43,         100.,        -100.,         800.
     44,        -100.,           0.,         800.
     45,           0.,           0.,         800.
     46,         100.,           0.,         800.
     47,        -100.,         100.,         800.
     48,           0.,         100.,         800.
     49,         100.,         100.,         800.
**
** ----------------------------------------------------------------
** 单元定义 - 使用C3D8R (8节点线性砖，减缩积分)
** ----------------------------------------------------------------
*ELEMENT, TYPE=C3D8R, ELSET=COLUMN
** 底层单元 (Z: 0-200)
    1,  1,  2, 12, 11,  4,  5, 15, 14
    2,  2,  3, 13, 12,  5,  6, 16, 15
    3,  4,  5, 15, 14,  7,  8, 18, 17
    4,  5,  6, 16, 15,  8,  9, 19, 18
**
** 第二层单元 (Z: 200-400)
   11, 11, 12, 22, 21, 14, 15, 25, 24
   12, 12, 13, 23, 22, 15, 16, 26, 25
   13, 14, 15, 25, 24, 17, 18, 28, 27
   14, 15, 16, 26, 25, 18, 19, 29, 28
**
** 第三层单元 (Z: 400-600)
   21, 21, 22, 32, 31, 24, 25, 35, 34
   22, 22, 23, 33, 32, 25, 26, 36, 35
   23, 24, 25, 35, 34, 27, 28, 38, 37
   24, 25, 26, 36, 35, 28, 29, 39, 38
**
** 顶层单元 (Z: 600-800)
   31, 31, 32, 42, 41, 34, 35, 45, 44
   32, 32, 33, 43, 42, 35, 36, 46, 45
   33, 34, 35, 45, 44, 37, 38, 48, 47
   34, 35, 36, 46, 45, 38, 39, 49, 48
**
** ----------------------------------------------------------------
** 节点集定义
** ----------------------------------------------------------------
*NSET, NSET=BOTTOM
1, 2, 3, 4, 5, 6, 7, 8, 9
**
*NSET, NSET=TOP
41, 42, 43, 44, 45, 46, 47, 48, 49
**
*NSET, NSET=TOP_CENTER
45
**
** ----------------------------------------------------------------
** 材料定义
** ----------------------------------------------------------------
*MATERIAL, NAME=CONCRETE_DAMAGE
*USER MATERIAL, CONSTANTS=9
** E0,     f_t,     f_c,     A+,      B+,      xi+,     A-,      B-,      xi-
10000.0,  3.67043, 10.0,    0.84463, 1.81372, 0.5,     2.0,     1.32925, 0.50028
*DENSITY
2.4e-9
*DEPVAR
5
** 1: Plastic strain
** 2: Tensile damage d+
** 3: Compressive damage d-  
** 4: Maximum tensile damage force r_max+
** 5: Maximum compressive damage force r_max-
**
** ----------------------------------------------------------------
** 截面定义
** ----------------------------------------------------------------
*SOLID SECTION, ELSET=COLUMN, MATERIAL=CONCRETE_DAMAGE
**
** ----------------------------------------------------------------
** 初始条件
** ----------------------------------------------------------------
*INITIAL CONDITIONS, TYPE=SOLUTION
COLUMN, 0.0, 0.0, 0.0, 3.67043, 10.0
**
** ----------------------------------------------------------------
** 边界条件
** ----------------------------------------------------------------
*BOUNDARY
BOTTOM, 1, 3
**
** ----------------------------------------------------------------
** 循环荷载定义
** ----------------------------------------------------------------
*AMPLITUDE, NAME=CYCLIC_LOAD
0.0, 0.0
0.1, 0.16
0.2, 0.0
0.3, -0.24
0.4, 0.0
0.5, 0.32
0.6, 0.0
0.7, -0.48
0.8, 0.0
0.9, 0.48
1.0, 0.0
**
** ----------------------------------------------------------------
** 分析步骤
** ----------------------------------------------------------------
*STEP, NAME=CyclicLoading, NLGEOM=YES
*DYNAMIC, EXPLICIT
, 1.0
**
*BOUNDARY, AMPLITUDE=CYCLIC_LOAD
TOP, 1, 1, 1.0
**
** ----------------------------------------------------------------
** 输出设置
** ----------------------------------------------------------------
*OUTPUT, FIELD, NUMBER INTERVAL=20
*NODE OUTPUT
U, V, A, RF
*ELEMENT OUTPUT
S, LE, SDV
**
*OUTPUT, HISTORY, FREQUENCY=20
*NODE OUTPUT, NSET=TOP_CENTER
U1, RF1
*ELEMENT OUTPUT, ELSET=COLUMN
S11, LE11, SDV1, SDV2, SDV3, SDV4, SDV5
**
*END STEP 