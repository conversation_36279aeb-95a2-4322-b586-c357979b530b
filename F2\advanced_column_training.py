import os
import torch
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import argparse
import time
from column_hysteresis_model import (
    load_excel_data,
    preprocess_data,
    ColumnHysteresisPINN,
    plot_hysteresis_curve,
    physics_informed_loss
)

# 设置环境变量，避免某些Windows系统上的OMP错误
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 设备配置
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

# 导入中文字体配置
try:
    from font_config import configure_chinese_font
    # 配置中文字体
    configure_chinese_font()
    print("已配置中文字体支持")
except ImportError:
    print("警告: 未找到font_config模块，中文显示可能不正确")
    # 尝试直接配置字体
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'Arial']
    plt.rcParams['axes.unicode_minus'] = False

# 高级训练函数，优化物理约束权重
def advanced_train_model(model, X, y, disp_idx, epochs=2000, batch_size=32, lr=1e-4, patience=30,
                       lambda_closure=0.1, lambda_energy=0.05, lambda_smoothness=0.02, lambda_hysteresis=0.12):
    """
    高级训练模型函数，优化物理约束权重

    参数:
        model: 神经网络模型
        X: 输入特征
        y: 目标输出
        disp_idx: 位移特征在输入中的索引
        epochs: 训练轮数
        batch_size: 批次大小
        lr: 学习率
        patience: 早停耐心值
        lambda_closure: 闭合约束权重
        lambda_energy: 能量守恒约束权重
        lambda_smoothness: 平滑约束权重
        lambda_hysteresis: 滞回特性约束权重

    返回:
        model: 训练后的模型
        history: 训练历史记录
    """
    from torch.utils.data import DataLoader, TensorDataset
    from sklearn.model_selection import train_test_split

    # 数据集划分
    X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.15, random_state=42)

    # 创建数据加载器
    train_dataset = TensorDataset(X_train, y_train)
    val_dataset = TensorDataset(X_val, y_val)

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size)

    # 优化器
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-5)

    # 学习率调度器 - 使用余弦退火调度器
    scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=50, T_mult=2, eta_min=1e-6
    )

    # 训练历史记录
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_mse': [],
        'val_mse': [],
        'train_closure': [],
        'val_closure': [],
        'train_energy': [],
        'val_energy': [],
        'train_smoothness': [],
        'val_smoothness': [],
        'train_hysteresis': [],
        'val_hysteresis': []
    }

    # 早停机制
    best_val_loss = float('inf')
    patience_counter = 0

    # 记录训练开始时间
    start_time = time.time()

    # 训练循环
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_losses = []
        train_mse_losses = []
        train_closure_losses = []
        train_energy_losses = []
        train_smoothness_losses = []
        train_hysteresis_losses = []

        for X_batch, y_batch in train_loader:
            optimizer.zero_grad()

            # 自定义物理信息损失函数
            y_pred = model(X_batch)
            mse_loss = torch.nn.functional.mse_loss(y_pred, y_batch)

            # 滞回闭合约束
            if X_batch.size(0) > 1:
                # 找到位移接近的点，计算力的差异
                disp_diffs = torch.abs(X_batch[:, disp_idx].unsqueeze(1) - X_batch[:, disp_idx].unsqueeze(0))
                close_points = (disp_diffs < 0.01).float()

                # 计算这些点的力差异
                force_diffs = torch.abs(y_pred - y_pred.transpose(0, 1))
                closure_loss = torch.mean(close_points * force_diffs)
            else:
                closure_loss = torch.tensor(0.0, device=device)

            # 能量守恒约束
            if X_batch.size(0) > 1:
                # 计算预测力做功
                disp_increments = X_batch[1:, disp_idx] - X_batch[:-1, disp_idx]
                pred_work = torch.sum(y_pred[:-1] * disp_increments.unsqueeze(1))

                # 计算真实力做功
                true_work = torch.sum(y_batch[:-1] * disp_increments.unsqueeze(1))

                # 能量误差
                energy_loss = torch.abs(pred_work - true_work) / (torch.abs(true_work) + 1e-6)
            else:
                energy_loss = torch.tensor(0.0, device=device)

            # 平滑约束
            if X_batch.size(0) > 2:
                # 平滑约束
                if X_batch.size(0) > 2:
                    second_diff = y_pred[2:] - 2 * y_pred[1:-1] + y_pred[:-2]
                    smoothness_loss = torch.mean(torch.abs(second_diff))
                else:
                    smoothness_loss = torch.tensor(0.0, device=device)

                # 滞回特性约束 - 确保加载和卸载路径有足够的差异
                hysteresis_loss = torch.tensor(0.0, device=device)
                if X_batch.size(0) > 3:
                    # 计算位移增量的符号变化
                    disp_increments = X_batch[1:, disp_idx] - X_batch[:-1, disp_idx]
                    sign_changes = torch.sign(disp_increments[1:]) != torch.sign(disp_increments[:-1])

                    if torch.any(sign_changes):
                        # 找到位移方向变化的点
                        change_indices = torch.where(sign_changes)[0] + 1

                        # 对于每个变化点，计算前后路径的差异
                        path_diffs = []
                        for idx in change_indices:
                            if idx > 1 and idx < X_batch.size(0) - 2:
                                # 获取前后的小段路径
                                before_path = y_pred[idx-2:idx+1]
                                after_path = y_pred[idx:idx+3]

                                # 计算路径差异 - 鼓励有足够的滞回差异
                                path_diff = torch.mean(torch.abs(before_path - after_path))
                                path_diffs.append(path_diff)

                        if path_diffs:
                            # 鼓励更大的滞回差异 - 使用负值，因为我们希望最大化差异
                            hysteresis_loss = -torch.mean(torch.stack(path_diffs))

                # 总损失
                total_loss = mse_loss + lambda_closure * closure_loss + lambda_energy * energy_loss + lambda_smoothness * smoothness_loss + lambda_hysteresis * hysteresis_loss

            # 反向传播
            total_loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

            # 参数更新
            optimizer.step()

            # 记录损失
            loss_dict = {
                'mse': mse_loss.item(),
                'closure': closure_loss.item(),
                'energy': energy_loss.item(),
                'smoothness': smoothness_loss.item(),
                'hysteresis': hysteresis_loss.item(),
                'total': total_loss.item()
            }

            train_losses.append(loss_dict['total'])
            train_mse_losses.append(loss_dict['mse'])
            train_closure_losses.append(loss_dict['closure'])
            train_energy_losses.append(loss_dict['energy'])
            train_smoothness_losses.append(loss_dict['smoothness'])
            train_hysteresis_losses.append(loss_dict['hysteresis'])

        # 更新学习率
        scheduler.step()

        # 验证阶段
        model.eval()
        val_losses = []
        val_mse_losses = []
        val_closure_losses = []
        val_energy_losses = []
        val_smoothness_losses = []
        val_hysteresis_losses = []

        with torch.no_grad():
            for X_batch, y_batch in val_loader:
                # 计算预测
                y_pred = model(X_batch)
                mse_loss = torch.nn.functional.mse_loss(y_pred, y_batch)

                # 滞回闭合约束
                if X_batch.size(0) > 1:
                    disp_diffs = torch.abs(X_batch[:, disp_idx].unsqueeze(1) - X_batch[:, disp_idx].unsqueeze(0))
                    close_points = (disp_diffs < 0.01).float()
                    force_diffs = torch.abs(y_pred - y_pred.transpose(0, 1))
                    closure_loss = torch.mean(close_points * force_diffs)
                else:
                    closure_loss = torch.tensor(0.0, device=device)

                # 能量守恒约束
                if X_batch.size(0) > 1:
                    disp_increments = X_batch[1:, disp_idx] - X_batch[:-1, disp_idx]
                    pred_work = torch.sum(y_pred[:-1] * disp_increments.unsqueeze(1))
                    true_work = torch.sum(y_batch[:-1] * disp_increments.unsqueeze(1))
                    energy_loss = torch.abs(pred_work - true_work) / (torch.abs(true_work) + 1e-6)
                else:
                    energy_loss = torch.tensor(0.0, device=device)

                # 平滑约束
                if X_batch.size(0) > 2:
                    second_diff = y_pred[2:] - 2 * y_pred[1:-1] + y_pred[:-2]
                    smoothness_loss = torch.mean(torch.abs(second_diff))
                else:
                    smoothness_loss = torch.tensor(0.0, device=device)

                # 滞回特性约束 - 确保加载和卸载路径有足够的差异
                hysteresis_loss = torch.tensor(0.0, device=device)
                if X_batch.size(0) > 3:
                    # 计算位移增量的符号变化
                    disp_increments = X_batch[1:, disp_idx] - X_batch[:-1, disp_idx]
                    sign_changes = torch.sign(disp_increments[1:]) != torch.sign(disp_increments[:-1])

                    if torch.any(sign_changes):
                        # 找到位移方向变化的点
                        change_indices = torch.where(sign_changes)[0] + 1

                        # 对于每个变化点，计算前后路径的差异
                        path_diffs = []
                        for idx in change_indices:
                            if idx > 1 and idx < X_batch.size(0) - 2:
                                # 获取前后的小段路径
                                before_path = y_pred[idx-2:idx+1]
                                after_path = y_pred[idx:idx+3]

                                # 计算路径差异 - 鼓励有足够的滞回差异
                                path_diff = torch.mean(torch.abs(before_path - after_path))
                                path_diffs.append(path_diff)

                        if path_diffs:
                            # 鼓励更大的滞回差异 - 使用负值，因为我们希望最大化差异
                            hysteresis_loss = -torch.mean(torch.stack(path_diffs))

                # 总损失
                total_loss = mse_loss + lambda_closure * closure_loss + lambda_energy * energy_loss + lambda_smoothness * smoothness_loss + lambda_hysteresis * hysteresis_loss

                # 记录损失
                # 记录损失
                val_losses.append(total_loss.item())
                val_mse_losses.append(mse_loss.item())
                val_closure_losses.append(closure_loss.item())
                val_energy_losses.append(energy_loss.item())
                val_smoothness_losses.append(smoothness_loss.item())
                val_hysteresis_losses.append(hysteresis_loss.item())

        # 计算平均损失
        avg_train_loss = np.mean(train_losses)
        avg_val_loss = np.mean(val_losses)

        # 更新历史记录
        history['train_loss'].append(avg_train_loss)
        history['val_loss'].append(avg_val_loss)
        history['train_mse'].append(np.mean(train_mse_losses))
        history['val_mse'].append(np.mean(val_mse_losses))
        history['train_closure'].append(np.mean(train_closure_losses))
        history['val_closure'].append(np.mean(val_closure_losses))
        history['train_energy'].append(np.mean(train_energy_losses))
        history['val_energy'].append(np.mean(val_energy_losses))
        history['train_smoothness'].append(np.mean(train_smoothness_losses))
        history['val_smoothness'].append(np.mean(val_smoothness_losses))
        history['train_hysteresis'].append(np.mean(train_hysteresis_losses))
        history['val_hysteresis'].append(np.mean(val_hysteresis_losses))

        # 打印训练信息
        if epoch % 10 == 0 or epoch == epochs - 1:
            elapsed_time = time.time() - start_time
            print(f"Epoch {epoch}/{epochs} - "
                  f"Train Loss: {avg_train_loss:.4f}, "
                  f"Val Loss: {avg_val_loss:.4f}, "
                  f"LR: {optimizer.param_groups[0]['lr']:.6f}, "
                  f"Time: {elapsed_time:.1f}s")

        # 早停检查
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            patience_counter = 0
            # 保存最佳模型
            torch.save(model.state_dict(), 'd:\\column\\advanced_column_hysteresis_model.pth')
            print(f"Epoch {epoch}: 保存最佳模型，验证损失: {best_val_loss:.6f}")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f"Early stopping at epoch {epoch}")
                break

    # 加载最佳模型
    model.load_state_dict(torch.load('d:\\column\\advanced_column_hysteresis_model.pth'))

    # 计算总训练时间
    total_time = time.time() - start_time
    print(f"总训练时间: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")

    return model, history

def save_training_history(history, save_path):
    """保存训练历史曲线"""
    plt.figure(figsize=(15, 12))

    # 绘制总损失
    plt.subplot(3, 2, 1)
    plt.plot(history['train_loss'], label='训练损失')
    plt.plot(history['val_loss'], label='验证损失')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('总损失')
    plt.legend()
    plt.grid(True)

    # 绘制MSE损失
    plt.subplot(3, 2, 2)
    plt.plot(history['train_mse'], label='训练MSE')
    plt.plot(history['val_mse'], label='验证MSE')
    plt.xlabel('Epoch')
    plt.ylabel('MSE')
    plt.title('MSE损失')
    plt.legend()
    plt.grid(True)

    # 绘制闭合约束损失
    plt.subplot(3, 2, 3)
    plt.plot(history['train_closure'], label='训练闭合约束')
    plt.plot(history['val_closure'], label='验证闭合约束')
    plt.xlabel('Epoch')
    plt.ylabel('Closure Loss')
    plt.title('闭合约束损失')
    plt.legend()
    plt.grid(True)

    # 绘制能量约束损失
    plt.subplot(3, 2, 4)
    plt.plot(history['train_energy'], label='训练能量约束')
    plt.plot(history['val_energy'], label='验证能量约束')
    plt.xlabel('Epoch')
    plt.ylabel('Energy Loss')
    plt.title('能量约束损失')
    plt.legend()
    plt.grid(True)

    # 绘制平滑约束损失
    plt.subplot(3, 2, 5)
    plt.plot(history['train_smoothness'], label='训练平滑约束')
    plt.plot(history['val_smoothness'], label='验证平滑约束')
    plt.xlabel('Epoch')
    plt.ylabel('Smoothness Loss')
    plt.title('平滑约束损失')
    plt.legend()
    plt.grid(True)

    # 绘制滞回特性约束损失
    plt.subplot(3, 2, 6)
    plt.plot(history['train_hysteresis'], label='训练滞回特性约束')
    plt.plot(history['val_hysteresis'], label='验证滞回特性约束')
    plt.xlabel('Epoch')
    plt.ylabel('Hysteresis Loss')
    plt.title('滞回特性约束损失')
    plt.legend()
    plt.grid(True)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"训练历史已保存至: {save_path}")

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='柱子滞回曲线PINN模型高级训练')
    parser.add_argument('--excel_path', type=str, default=os.path.join(os.path.dirname(__file__), "column1.xlsx"),
                        help='Excel数据文件路径')
    parser.add_argument('--epochs', type=int, default=2000,
                        help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='批次大小')
    parser.add_argument('--lr', type=float, default=2e-4,
                        help='学习率')
    parser.add_argument('--patience', type=int, default=50,
                        help='早停耐心值')
    parser.add_argument('--lambda_closure', type=float, default=0.18,
                        help='闭合约束权重')
    parser.add_argument('--lambda_energy', type=float, default=0.1,
                        help='能量守恒约束权重')
    parser.add_argument('--lambda_smoothness', type=float, default=0.04,
                        help='平滑约束权重')
    parser.add_argument('--lambda_hysteresis', type=float, default=0.12,
                        help='滞回特性约束权重')
    parser.add_argument('--save_dir', type=str, default=os.path.dirname(__file__),
                        help='保存模型和图表的目录')

    args = parser.parse_args()

    # 确保保存目录存在
    os.makedirs(args.save_dir, exist_ok=True)

    # 1. 加载数据
    print("加载数据...")
    try:
        static_params, dynamic_data = load_excel_data(args.excel_path)
        print(f"成功加载Excel数据: {args.excel_path}")

        # 打印一些基本信息
        print("\n柱子基本信息:")
        important_params = [
            '混凝土强度', '直径', '配筋率', '轴向载荷', '长度'
        ]
        for key, value in static_params.items():
            for param in important_params:
                if param in key:
                    print(f"  {key}: {value}")

        print(f"\n力-位移数据: {len(dynamic_data)}行")
        print(dynamic_data.head())

    except Exception as e:
        print(f"加载数据失败: {str(e)}")
        return

    # 2. 数据预处理
    print("\n数据预处理...")
    X, y, scalers, original_data = preprocess_data(static_params, dynamic_data)

    # 3. 创建模型
    print("\n创建模型...")
    input_dim = X.shape[1]
    disp_idx = input_dim - 3  # 假设位移特征在输入的倒数第三个位置

    # 修改隐藏层维度，确保维度匹配
    # 使用完全一致的隐藏层维度，避免跳跃连接和全局连接的维度不匹配问题
    model = ColumnHysteresisPINN(input_dim, hidden_dims=[128, 128, 128, 128, 128]).to(device)
    print(f"模型输入维度: {input_dim}")
    print(f"模型结构: 隐藏层维度 = [128, 128, 128, 128, 128]")

    # 4. 训练模型
    print(f"\n开始高级训练模型，共{args.epochs}轮...")
    print(f"物理约束权重: 闭合约束={args.lambda_closure}, 能量约束={args.lambda_energy}, 平滑约束={args.lambda_smoothness}")

    model, history = advanced_train_model(
        model, X, y, disp_idx,
        epochs=args.epochs,
        batch_size=args.batch_size,
        lr=args.lr,
        patience=args.patience,
        lambda_closure=args.lambda_closure,
        lambda_energy=args.lambda_energy,
        lambda_smoothness=args.lambda_smoothness
    )

    # 保存训练历史
    save_training_history(history, os.path.join(args.save_dir, 'advanced_training_history.png'))

    # 保存模型
    model_save_path = os.path.join(args.save_dir, 'advanced_column_hysteresis_model.pth')
    torch.save(model.state_dict(), model_save_path)
    print(f"模型已保存至: {model_save_path}")

    # 5. 可视化结果
    print("\n可视化滞回曲线...")
    metrics = plot_hysteresis_curve(
        model, X, y, disp_idx,
        scalers['force'], scalers['disp'],
        original_data,
        title=f"柱子滞回曲线 - 高级训练 - {os.path.basename(args.excel_path)}"
    )

    # 6. 打印评估指标
    print("\n模型评估指标:")
    print(f"平均绝对误差 (MAE): {metrics['mae']:.4f} kN")
    print(f"均方根误差 (RMSE): {metrics['rmse']:.4f} kN")
    print(f"最大误差: {metrics['max_error']:.4f} kN")

    print("\n高级训练处理完成!")

if __name__ == "__main__":
    main()