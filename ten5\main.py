"""
混凝土损伤参数识别PINN框架 - 主程序
严格按照框架要求实现完整流程
"""

import argparse
import sys
import os
from pathlib import Path

def main():
    """
    主程序入口
    """
    parser = argparse.ArgumentParser(description='混凝土损伤参数识别PINN框架')
    parser.add_argument('--mode', choices=['train', 'predict', 'both'], default='both',
                      help='运行模式: train(仅训练), predict(仅预测), both(训练+预测)')
    parser.add_argument('--data', default='tension.xlsx',
                      help='实验数据Excel文件路径')
    parser.add_argument('--epochs', type=int, default=2000,
                      help='训练轮数')
    parser.add_argument('--lr', type=float, default=0.001,
                      help='学习率')
    parser.add_argument('--model', default=None,
                      help='预测时使用的模型文件路径（可选）')
    
    args = parser.parse_args()
    
    print("=" * 80)
    print("混凝土损伤参数识别PINN框架")
    print("基于增量式输入的端到端参数识别")
    print("=" * 80)
    
    # 检查数据文件是否存在
    if not os.path.exists(args.data):
        print(f"错误: 数据文件 {args.data} 不存在!")
        print("请确保Excel文件包含'strain'和'stress'两列数据")
        sys.exit(1)
    
    # 确保results目录存在
    Path('results').mkdir(exist_ok=True)
    
    # 初始化变量
    results_dir = None
    trainer = None
    predictor = None
    
    try:
        if args.mode in ['train', 'both']:
            print(f"\n{'='*20} 开始训练阶段 {'='*20}")
            
            # 导入训练模块
            from train import Trainer
            
            # 创建训练配置
            config = {
                'num_epochs': args.epochs,
                'learning_rate': args.lr,
                'print_interval': max(50, args.epochs // 40),
                'save_interval': max(100, args.epochs // 20)
            }
            
            # 创建训练器并运行训练
            trainer = Trainer(config)
            trainer.load_data(args.data)
            trainer.initialize_model()
            trainer.train()
            
            print(f"{'='*20} 训练阶段完成 {'='*20}")
            
            # 保存结果目录信息，供预测阶段使用
            results_dir = trainer.results_dir
        
        if args.mode in ['predict', 'both']:
            print(f"\n{'='*20} 开始预测阶段 {'='*20}")
            
            # 导入预测模块
            from predict import Predictor
            
            # 创建预测器并运行预测
            predictor = Predictor()
            results = predictor.run_prediction(args.model, args.data)
            
            print(f"{'='*20} 预测阶段完成 {'='*20}")
        
        # 确定最终的结果目录
        if args.mode == 'both':
            final_results_dir = results_dir
        elif args.mode == 'predict':
            final_results_dir = predictor.results_dir if 'predictor' in locals() else 'results/最新训练结果'
        else:  # train only
            final_results_dir = trainer.results_dir if 'trainer' in locals() else 'results/最新训练结果'
        
        print(f"\n{'='*80}")
        print("程序执行完成!")
        print(f"所有结果文件已保存到: {final_results_dir}")
        print("包括:")
        print("  - 实验数据图像")
        print("  - 训练历史图像")
        print("  - 应力应变对比图")
        print("  - 损伤变量演化图")
        print("  - 塑性应变演化图")
        print("  - 识别的物理参数")
        print("  - 训练好的模型文件")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n错误: {e}")
        print("程序执行失败，请检查输入数据和参数设置")
        sys.exit(1)


if __name__ == "__main__":
    main() 