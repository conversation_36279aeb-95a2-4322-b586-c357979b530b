import os
import torch
import numpy as np
import matplotlib.pyplot as plt
import time
from model.pinn import PINN
from utils.visualization import plot_damage_contour

# 使用当前时间作为随机种子，确保每次结果不同
current_time = int(time.time())
torch.manual_seed(current_time)
np.random.seed(current_time)
print(f"使用随机种子: {current_time}")

# 设置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

# 模型参数
input_dim = 3  # 空间坐标 (x, y, z)
hidden_dim = 512  # 隐藏层维度
output_dim = 4  # 位移场 (u, v, w) 和损伤变量 θ

# 物理参数
E0 = 210e9  # 杨氏模量 (Pa)
nu = 0.3    # 泊松比

# 创建PINN模型
model = PINN(input_dim, hidden_dim, output_dim, E0, nu).to(device)

# 加载预训练模型权重
model_path = os.path.join('checkpoints', 'pinn_model.pth')
if os.path.exists(model_path):
    model.load_state_dict(torch.load(model_path, map_location=device))
    print(f"成功加载预训练模型: {model_path}")
else:
    print(f"警告: 预训练模型 {model_path} 不存在，使用随机初始化的模型")

# 生成随机损伤可视化结果
print("生成随机损伤可视化结果...")
# 每次使用不同的随机扰动参数
random_factor = np.random.uniform(0.05, 0.2)
plot_damage_contour(model, resolution=50, threshold=0.2, random_factor=random_factor)
print(f"损伤等值线图已自动保存到contour_plots文件夹 (随机因子: {random_factor:.4f})")