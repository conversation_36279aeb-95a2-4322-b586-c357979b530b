import os
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler

class TensileDataProcessor:
    """
    处理混凝土拉伸试验数据的类
    """
    def __init__(self, data_path, sheet_name=0):
        """
        初始化数据处理器
        
        参数:
            data_path: Excel数据文件路径
            sheet_name: Excel表格名称或索引
        """
        self.data_path = data_path
        self.sheet_name = sheet_name
        self.raw_data = None
        self.strain_data = None
        self.stress_data = None
        self.strain_scaler = StandardScaler()
        self.stress_scaler = StandardScaler()
        
    def load_data(self):
        """
        加载Excel数据文件
        """
        try:
            # 尝试读取Excel文件
            self.raw_data = pd.read_excel(self.data_path, sheet_name=self.sheet_name)
            print(f"成功加载数据，形状: {self.raw_data.shape}")
            
            # 检查数据列
            if self.raw_data.shape[1] < 2:
                raise ValueError("数据至少需要两列: 应变和应力")
                
            # 假设第一列是应变，第二列是应力
            self.strain_data = self.raw_data.iloc[:, 0].values.reshape(-1, 1)
            self.stress_data = self.raw_data.iloc[:, 1].values.reshape(-1, 1)
            
            # 确保数据按应变递增排序
            sorted_indices = np.argsort(self.strain_data.flatten())
            self.strain_data = self.strain_data[sorted_indices]
            self.stress_data = self.stress_data[sorted_indices]
            
            return True
        except Exception as e:
            print(f"加载数据时出错: {e}")
            return False
    
    def preprocess_data(self, normalize=True):
        """
        预处理数据: 清洗、排序和标准化
        
        参数:
            normalize: 是否标准化数据
        
        返回:
            处理后的应变和应力数据
        """
        if self.strain_data is None or self.stress_data is None:
            if not self.load_data():
                return None, None
        
        # 移除缺失值
        valid_indices = ~np.isnan(self.strain_data.flatten()) & ~np.isnan(self.stress_data.flatten())
        strain_clean = self.strain_data[valid_indices]
        stress_clean = self.stress_data[valid_indices]
        
        # 标准化数据
        if normalize:
            strain_norm = self.strain_scaler.fit_transform(strain_clean)
            stress_norm = self.stress_scaler.fit_transform(stress_clean)
            return strain_norm, stress_norm
        
        return strain_clean, stress_clean
    
    def inverse_transform(self, strain_norm=None, stress_norm=None):
        """
        将标准化的数据转换回原始尺度
        """
        strain_orig = self.strain_scaler.inverse_transform(strain_norm) if strain_norm is not None else None
        stress_orig = self.stress_scaler.inverse_transform(stress_norm) if stress_norm is not None else None
        return strain_orig, stress_orig
    
    def split_data(self, strain, stress, train_ratio=0.8, shuffle=True):
        """
        将数据分割为训练集和测试集
        
        参数:
            strain: 应变数据
            stress: 应力数据
            train_ratio: 训练集比例
            shuffle: 是否打乱数据
            
        返回:
            (train_strain, train_stress), (test_strain, test_stress)
        """
        n_samples = len(strain)
        indices = np.arange(n_samples)
        
        if shuffle:
            np.random.shuffle(indices)
        
        train_size = int(n_samples * train_ratio)
        train_indices = indices[:train_size]
        test_indices = indices[train_size:]
        
        # 确保训练数据按应变递增排序
        train_indices = sorted(train_indices, key=lambda i: strain[i].item())
        test_indices = sorted(test_indices, key=lambda i: strain[i].item())
        
        train_strain = strain[train_indices]
        train_stress = stress[train_indices]
        test_strain = strain[test_indices]
        test_stress = stress[test_indices]
        
        return (train_strain, train_stress), (test_strain, test_stress)


class TensileDataset(Dataset):
    """
    混凝土拉伸数据的PyTorch数据集
    """
    def __init__(self, strain_data, stress_data, sequence_length=1):
        """
        初始化数据集
        
        参数:
            strain_data: 应变数据
            stress_data: 应力数据
            sequence_length: 序列长度，用于LSTM输入
        """
        self.strain_data = torch.FloatTensor(strain_data)
        self.stress_data = torch.FloatTensor(stress_data)
        self.sequence_length = sequence_length
        
    def __len__(self):
        return len(self.strain_data) - self.sequence_length + 1
    
    def __getitem__(self, idx):
        # 获取序列数据
        strain_seq = self.strain_data[idx:idx+self.sequence_length]
        # 获取对应的应力值（最后一个时间步的应力）
        stress = self.stress_data[idx+self.sequence_length-1]
        
        return strain_seq, stress


def create_dataloader(strain_data, stress_data, batch_size=32, sequence_length=1, shuffle=True):
    """
    创建PyTorch数据加载器
    
    参数:
        strain_data: 应变数据
        stress_data: 应力数据
        batch_size: 批次大小
        sequence_length: 序列长度
        shuffle: 是否打乱数据
        
    返回:
        PyTorch DataLoader对象
    """
    dataset = TensileDataset(strain_data, stress_data, sequence_length)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=shuffle)
    return dataloader


# 物理参数计算函数
def calculate_damage_threshold(strain, stress, E_0):
    """
    计算损伤阈值 r^+
    
    参数:
        strain: 应变序列
        stress: 应力序列
        E_0: 初始弹性模量
        
    返回:
        损伤阈值 r^+
    """
    # 计算损伤能释放率 Y^+
    Y_plus = E_0 * strain  # 简化计算，忽略塑性应变
    
    # 计算历史最大损伤能释放率
    r_plus = np.maximum.accumulate(Y_plus)
    
    return r_plus


def calculate_damage_variable(r_plus, r_0, A_plus, B_plus):
    """
    计算损伤变量 d^+ - 修正版本确保单调递增且为正值
    
    参数:
        r_plus: 损伤阈值
        r_0: 初始损伤阈值
        A_plus: 损伤参数A^+
        B_plus: 损伤参数B^+
        
    返回:
        损伤变量 d^+
    """
    # 修正的损伤演化公式 - 确保单调递增且为正值
    # 归一化的损伤驱动力
    normalized_drive = np.maximum(0, (r_plus - r_0) / (r_0 + 1e-8))
    
    # 使用修正的指数损伤演化公式
    # d = 1 - exp(-A_plus * normalized_drive^B_plus)
    # 这确保了损伤从0开始单调递增到接近1
    
    # 限制指数参数避免数值溢出
    exp_arg = -A_plus * np.power(normalized_drive + 1e-8, B_plus)
    exp_arg = np.clip(exp_arg, -50, 0)  # 确保指数参数为负
    
    d_plus = 1 - np.exp(exp_arg)
    
    # 确保损伤变量在[0,1)范围内
    d_plus = np.clip(d_plus, 0, 0.99)
    
    return d_plus


def calculate_plastic_strain(strain, strain_rate, stress, d_plus, xi_plus, E_0):
    """
    计算塑性应变增量
    
    参数:
        strain: 应变
        strain_rate: 应变率
        stress: 应力
        d_plus: 损伤变量
        xi_plus: 塑性参数
        E_0: 初始弹性模量
        
    返回:
        塑性应变增量
    """
    # 计算塑性应变增量
    H_d = np.ones_like(d_plus)  # 简化的H(d^+)函数
    delta_epsilon_p = xi_plus * H_d * (strain * strain_rate) / (stress + 1e-8)  # 添加小值避免除零
    
    return delta_epsilon_p