#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
损伤变量分析脚本 - 专门用于PINN-VUMAT损伤验证
Author: AI Assistant
Date: 2025-07-08
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import json

# 尝试导入Abaqus模块
ABAQUS_AVAILABLE = False
try:
    from abaqus import *
    from abaqusConstants import *
    import odbAccess
    ABAQUS_AVAILABLE = True
    print("✓ Abaqus模块已加载")
except ImportError:
    print("警告: 无法导入Abaqus模块，将使用模拟数据")

def find_latest_session_dir():
    """查找最新的session目录"""
    results_dir = Path("results")
    if not results_dir.exists():
        return None
    
    session_dirs = [d for d in results_dir.iterdir() 
                   if d.is_dir() and d.name.startswith("session_")]
    
    if not session_dirs:
        return None
    
    return sorted(session_dirs)[-1]

def extract_damage_from_odb(odb_path):
    """从ODB文件提取损伤变量数据"""
    if not ABAQUS_AVAILABLE:
        print("使用模拟损伤数据（Abaqus模块不可用）")
        return generate_simulated_damage_data()
    
    try:
        # 打开ODB文件
        odb = odbAccess.openOdb(str(odb_path))
        
        # 获取步骤
        step = odb.steps['ApplyDisplacement']
        
        # 获取帧数据
        frames = step.frames
        n_frames = len(frames)
        
        # 初始化数据数组
        time_data = []
        strain_data = []
        stress_data = []
        damage_plus_data = []
        damage_minus_data = []
        plastic_strain_data = []
        
        # 常量定义
        LENGTH = 100.0  # mm
        AREA = 100.0    # mm^2
        
        # 提取每一帧的数据
        for frame in frames:
            time = frame.frameValue
            
            # 获取位移数据计算应变
            top_displacement = frame.fieldOutputs['U'].getSubset(region=odb.rootAssembly.nodeSets['SET_TOP'])
            u3_avg = np.mean([v.data[2] for v in top_displacement.values])
            strain = u3_avg / LENGTH
            
            # 获取应力数据
            stress_field = frame.fieldOutputs['S']
            element_stress = stress_field.getSubset(region=odb.rootAssembly.elementSets['COLUMN-1.E_COLUMN'])
            s33_avg = np.mean([v.data[2] for v in element_stress.values])  # S33是轴向应力
            
            # 获取状态变量（损伤变量）
            if 'SDV' in frame.fieldOutputs:
                sdv_field = frame.fieldOutputs['SDV']
                element_sdv = sdv_field.getSubset(region=odb.rootAssembly.elementSets['COLUMN-1.E_COLUMN'])
                
                # 提取各个状态变量
                # SDV1: 受拉损伤 (DAMAGE_PLUS)
                # SDV2: 受压损伤 (DAMAGE_MINUS)  
                # SDV5: 塑性应变 (EP)
                damage_plus = np.mean([v.data[0] for v in element_sdv.values])  # SDV1
                damage_minus = np.mean([v.data[1] for v in element_sdv.values]) # SDV2
                plastic_strain = np.mean([v.data[4] for v in element_sdv.values]) # SDV5
            else:
                # 如果没有状态变量输出，使用默认值
                damage_plus = 0.0
                damage_minus = 0.0
                plastic_strain = 0.0
            
            # 存储数据
            time_data.append(time)
            strain_data.append(strain)
            stress_data.append(s33_avg)
            damage_plus_data.append(damage_plus)
            damage_minus_data.append(damage_minus)
            plastic_strain_data.append(plastic_strain)
        
        odb.close()
        
        # 转换为numpy数组
        return {
            'time': np.array(time_data),
            'strain': np.array(strain_data),
            'stress': np.array(stress_data),
            'damage_plus': np.array(damage_plus_data),
            'damage_minus': np.array(damage_minus_data),
            'plastic_strain': np.array(plastic_strain_data)
        }
        
    except Exception as e:
        print(f"错误: 读取ODB文件失败 - {e}")
        print("使用模拟损伤数据")
        return generate_simulated_damage_data()

def generate_simulated_damage_data():
    """生成模拟损伤数据用于测试"""
    # 创建应变序列（拉伸过程）
    strain = np.linspace(0, 0.002, 100)
    time = np.linspace(0, 1.0, 100)
    
    # 材料参数（从identified_parameters.json获取）
    E0 = 10000.0  # MPa
    ft = 3.67     # MPa
    fc = 10.0     # MPa
    A_plus = 0.8546
    B_plus = 1.6936
    xi_plus = 0.5
    
    # 初始化数组
    stress = np.zeros_like(strain)
    damage_plus = np.zeros_like(strain)
    damage_minus = np.zeros_like(strain)
    plastic_strain = np.zeros_like(strain)
    
    # 计算损伤演化
    for i in range(len(strain)):
        current_strain = strain[i]
        
        # 计算弹性应变（假设）
        elastic_strain = current_strain - plastic_strain[i] if i > 0 else current_strain
        
        # 计算损伤驱动力
        Y_plus = E0 * max(0, elastic_strain)
        
        # 计算受拉损伤
        if Y_plus > ft:
            ratio = Y_plus / ft
            exp_term = np.exp(B_plus * (1 - ratio))
            damage_plus[i] = 1 - (1/ratio) * ((1 - A_plus) + A_plus * exp_term)
            damage_plus[i] = min(max(damage_plus[i], 0), 0.99)
        
        # 计算塑性应变
        if i > 0:
            delta_strain = strain[i] - strain[i-1]
            if delta_strain > 0:
                plastic_strain[i] = plastic_strain[i-1] + xi_plus * delta_strain
        
        # 重新计算弹性应变和应力
        elastic_strain = current_strain - plastic_strain[i]
        stress[i] = (1 - damage_plus[i]) * E0 * elastic_strain
    
    return {
        'time': time,
        'strain': strain,
        'stress': stress,
        'damage_plus': damage_plus,
        'damage_minus': damage_minus,
        'plastic_strain': plastic_strain
    }

def load_pinn_damage_prediction(session_dir):
    """加载PINN损伤预测结果"""
    prediction_dir = session_dir / "prediction"
    
    # 查找预测结果文件
    prediction_files = list(prediction_dir.glob("prediction_results_*.xlsx"))
    
    if not prediction_files:
        print("警告: 未找到PINN预测结果")
        return None
    
    # 使用最新的预测文件
    latest_prediction = sorted(prediction_files)[-1]
    
    try:
        import pandas as pd
        df = pd.read_excel(latest_prediction)
        
        # 筛选拉伸数据
        mask = df['strain'] >= 0
        
        strain_pinn = df.loc[mask, 'strain'].values
        stress_pinn = df.loc[mask, 'stress'].values
        
        # 如果有损伤变量列，也提取
        damage_plus_pinn = None
        plastic_strain_pinn = None
        
        if 'damage_plus' in df.columns:
            damage_plus_pinn = df.loc[mask, 'damage_plus'].values
        if 'plastic_strain' in df.columns:
            plastic_strain_pinn = df.loc[mask, 'plastic_strain'].values
        
        return {
            'strain': strain_pinn,
            'stress': stress_pinn,
            'damage_plus': damage_plus_pinn,
            'plastic_strain': plastic_strain_pinn
        }
        
    except Exception as e:
        print(f"警告: 读取PINN预测结果失败 - {e}")
        return None

def plot_damage_analysis(vumat_data, pinn_data, output_dir):
    """绘制损伤变量分析图"""
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('拉伸损伤变量分析对比', fontsize=16)
    
    # 1. 应力-应变曲线
    ax1 = axes[0, 0]
    ax1.plot(vumat_data['strain'], vumat_data['stress'], 'b-', linewidth=2, label='VUMAT')
    if pinn_data and pinn_data['stress'] is not None:
        ax1.plot(pinn_data['strain'], pinn_data['stress'], 'r--', linewidth=2, label='PINN')
    ax1.set_xlabel('应变')
    ax1.set_ylabel('应力 (MPa)')
    ax1.set_title('应力-应变曲线')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 2. 受拉损伤演化
    ax2 = axes[0, 1]
    ax2.plot(vumat_data['strain'], vumat_data['damage_plus'], 'b-', linewidth=2, label='VUMAT')
    if pinn_data and pinn_data['damage_plus'] is not None:
        ax2.plot(pinn_data['strain'], pinn_data['damage_plus'], 'r--', linewidth=2, label='PINN')
    ax2.set_xlabel('应变')
    ax2.set_ylabel('受拉损伤变量')
    ax2.set_title('受拉损伤演化')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    ax2.set_ylim(0, 1)
    
    # 3. 塑性应变演化
    ax3 = axes[1, 0]
    ax3.plot(vumat_data['strain'], vumat_data['plastic_strain'], 'b-', linewidth=2, label='VUMAT')
    if pinn_data and pinn_data['plastic_strain'] is not None:
        ax3.plot(pinn_data['strain'], pinn_data['plastic_strain'], 'r--', linewidth=2, label='PINN')
    ax3.set_xlabel('应变')
    ax3.set_ylabel('塑性应变')
    ax3.set_title('塑性应变演化')
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    
    # 4. 损伤-应力关系
    ax4 = axes[1, 1]
    ax4.plot(vumat_data['damage_plus'], vumat_data['stress'], 'b-', linewidth=2, label='VUMAT')
    if pinn_data and pinn_data['damage_plus'] is not None:
        ax4.plot(pinn_data['damage_plus'], pinn_data['stress'], 'r--', linewidth=2, label='PINN')
    ax4.set_xlabel('受拉损伤变量')
    ax4.set_ylabel('应力 (MPa)')
    ax4.set_title('损伤-应力关系')
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    
    plt.tight_layout()
    
    # 保存图片
    output_path = output_dir / 'damage_analysis_comparison.png'
    plt.savefig(str(output_path), dpi=300, bbox_inches='tight')
    print(f"✓ 损伤分析图已保存: {output_path}")
    
    return fig

def calculate_damage_metrics(vumat_data, pinn_data):
    """计算损伤变量误差指标"""
    if not pinn_data or pinn_data['damage_plus'] is None:
        print("跳过损伤误差计算（无PINN损伤数据）")
        return
    
    try:
        from scipy.interpolate import interp1d
        
        # 确定共同的应变范围
        strain_min = max(vumat_data['strain'].min(), pinn_data['strain'].min())
        strain_max = min(vumat_data['strain'].max(), pinn_data['strain'].max())
        
        # 创建共同的应变点
        strain_common = np.linspace(strain_min, strain_max, 100)
        
        # 插值损伤变量
        f_vumat_damage = interp1d(vumat_data['strain'], vumat_data['damage_plus'], kind='linear')
        f_pinn_damage = interp1d(pinn_data['strain'], pinn_data['damage_plus'], kind='linear')
        
        damage_vumat_interp = f_vumat_damage(strain_common)
        damage_pinn_interp = f_pinn_damage(strain_common)
        
        # 计算误差
        mse_damage = np.mean((damage_vumat_interp - damage_pinn_interp)**2)
        rmse_damage = np.sqrt(mse_damage)
        mae_damage = np.mean(np.abs(damage_vumat_interp - damage_pinn_interp))
        
        print(f"\n=== 损伤变量误差分析 ===")
        print(f"受拉损伤变量 (DAMAGE_PLUS):")
        print(f"  均方误差 (MSE): {mse_damage:.6f}")
        print(f"  均方根误差 (RMSE): {rmse_damage:.6f}")
        print(f"  平均绝对误差 (MAE): {mae_damage:.6f}")
        
        # 如果有塑性应变数据，也计算误差
        if pinn_data['plastic_strain'] is not None:
            f_vumat_plastic = interp1d(vumat_data['strain'], vumat_data['plastic_strain'], kind='linear')
            f_pinn_plastic = interp1d(pinn_data['strain'], pinn_data['plastic_strain'], kind='linear')
            
            plastic_vumat_interp = f_vumat_plastic(strain_common)
            plastic_pinn_interp = f_pinn_plastic(strain_common)
            
            mse_plastic = np.mean((plastic_vumat_interp - plastic_pinn_interp)**2)
            rmse_plastic = np.sqrt(mse_plastic)
            mae_plastic = np.mean(np.abs(plastic_vumat_interp - plastic_pinn_interp))
            
            print(f"\n塑性应变 (PLASTIC_STRAIN):")
            print(f"  均方误差 (MSE): {mse_plastic:.6f}")
            print(f"  均方根误差 (RMSE): {rmse_plastic:.6f}")
            print(f"  平均绝对误差 (MAE): {mae_plastic:.6f}")
        
    except ImportError:
        print("注意: scipy不可用，跳过损伤误差分析")
        print("要进行误差分析，请安装: pip install scipy")
    except Exception as e:
        print(f"损伤误差计算失败: {e}")

def save_damage_results(vumat_data, output_dir):
    """保存损伤分析结果"""
    # 组合数据
    results = np.column_stack((
        vumat_data['time'],
        vumat_data['strain'], 
        vumat_data['stress'],
        vumat_data['damage_plus'],
        vumat_data['damage_minus'],
        vumat_data['plastic_strain']
    ))
    
    # 保存为CSV
    output_path = output_dir / 'damage_analysis_results.csv'
    header = 'Time,Strain,Stress(MPa),Damage_Plus,Damage_Minus,Plastic_Strain'
    np.savetxt(str(output_path), results, delimiter=',', header=header, comments='')
    print(f"✓ 损伤分析数据已保存: {output_path}")

def main():
    """主函数"""
    print("\n=== 拉伸损伤变量分析 V1.0 ===")
    
    # 查找最新session
    latest_session = find_latest_session_dir()
    if not latest_session:
        print("错误: 未找到任何session目录。")
        return
    
    print(f"使用session: {latest_session}")
    
    # 定义路径
    vumat_dir = latest_session / "vumat_verification"
    if not vumat_dir.exists():
        print(f"错误: Vumat验证目录不存在: {vumat_dir}")
        return
    
    # 检查ODB文件
    odb_path = vumat_dir / "tensile_test.odb"
    if not odb_path.exists() and ABAQUS_AVAILABLE:
        print(f"错误: ODB文件不存在: {odb_path}")
        print("请先运行Abaqus分析")
        return
    
    # 创建输出目录
    output_dir = vumat_dir / "verification_outputs"
    output_dir.mkdir(exist_ok=True)
    
    print(f"从ODB提取损伤数据: {odb_path}")
    
    # 提取VUMAT数据
    vumat_data = extract_damage_from_odb(odb_path)
    
    if vumat_data is None:
        print("错误: 无法提取VUMAT数据")
        return
    
    print(f"\n提取的数据点数: {len(vumat_data['time'])}")
    print(f"应变范围: [{vumat_data['strain'].min():.6f}, {vumat_data['strain'].max():.6f}]")
    print(f"应力范围: [{vumat_data['stress'].min():.2f}, {vumat_data['stress'].max():.2f}] MPa")
    print(f"最大受拉损伤: {vumat_data['damage_plus'].max():.4f}")
    print(f"最大塑性应变: {vumat_data['plastic_strain'].max():.6f}")
    
    # 加载PINN预测结果
    pinn_data = load_pinn_damage_prediction(latest_session)
    
    # 绘制损伤分析图
    plot_damage_analysis(vumat_data, pinn_data, output_dir)
    
    # 计算误差指标
    calculate_damage_metrics(vumat_data, pinn_data)
    
    # 保存结果数据
    save_damage_results(vumat_data, output_dir)
    
    print(f"\n✓ 损伤变量分析完成!")
    print(f"结果保存在: {output_dir}")

if __name__ == "__main__":
    main() 