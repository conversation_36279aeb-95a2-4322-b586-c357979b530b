"""
数据处理器
严格按照框架要求处理实验数据
"""

import pandas as pd
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

class DataProcessor:
    """
    数据处理器
    从Excel文件读取strain和stress数据，转换为增量式输入格式
    """
    
    def __init__(self, excel_path="tension.xlsx"):
        self.excel_path = excel_path
        self.strain_exp = None
        self.stress_exp = None
        self.strain_increment = None
        
    def load_experimental_data(self):
        """
        从Excel文件加载实验数据
        要求Excel文件包含strain和stress两列
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(self.excel_path)
            
            # 检查是否包含必要的列
            if 'strain' not in df.columns or 'stress' not in df.columns:
                raise ValueError("Excel文件必须包含'strain'和'stress'两列")
            
            # 提取数据并转换为numpy数组
            self.strain_exp = df['strain'].values.astype(np.float32)
            self.stress_exp = df['stress'].values.astype(np.float32)
            
            # 计算应变增量序列 - 核心数据转换
            self.strain_increment = np.diff(self.strain_exp, prepend=0.0)
            
            print(f"成功加载实验数据:")
            print(f"  数据点数: {len(self.strain_exp)}")
            print(f"  应变范围: {self.strain_exp.min():.6f} ~ {self.strain_exp.max():.6f}")
            print(f"  应力范围: {self.stress_exp.min():.6f} ~ {self.stress_exp.max():.6f}")
            print(f"  应变增量范围: {self.strain_increment.min():.6f} ~ {self.strain_increment.max():.6f}")
            
            return True
            
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def prepare_training_data(self):
        """
        准备训练数据
        按照框架要求，整条曲线都作为训练集，不进行拆分
        """
        if self.strain_exp is None:
            raise ValueError("请先调用load_experimental_data()加载数据")
        
        # 转换为PyTorch张量
        strain_total_exp = torch.tensor(self.strain_exp, dtype=torch.float32)
        stress_exp = torch.tensor(self.stress_exp, dtype=torch.float32)
        strain_increment = torch.tensor(self.strain_increment, dtype=torch.float32)
        
        # 为神经网络准备输入格式 [batch_size=1, seq_len, input_size=1]
        strain_increment_input = strain_increment.unsqueeze(0).unsqueeze(-1)
        
        training_data = {
            'strain_total_exp': strain_total_exp,
            'stress_exp': stress_exp,
            'strain_increment': strain_increment,
            'strain_increment_input': strain_increment_input,
            'sequence_length': len(strain_total_exp)
        }
        
        return training_data
    
    def plot_experimental_data(self, save_path="results/experimental_data.png"):
        """
        绘制实验数据
        """
        if self.strain_exp is None:
            raise ValueError("请先加载实验数据")
        
        plt.figure(figsize=(10, 6))
        plt.plot(self.strain_exp, self.stress_exp, 'b-', linewidth=2, label='实验数据')
        plt.xlabel('应变')
        plt.ylabel('应力 (MPa)')
        plt.title('实验应力-应变曲线')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.tight_layout()
        
        # 确保保存目录存在
        Path(save_path).parent.mkdir(parents=True, exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"实验数据图像已保存至: {save_path}")
    
    def validate_data_quality(self):
        """
        验证数据质量
        """
        if self.strain_exp is None:
            return False
        
        # 检查数据完整性
        if len(self.strain_exp) != len(self.stress_exp):
            print("错误: 应变和应力数据长度不匹配")
            return False
        
        # 检查是否有缺失值
        if np.any(np.isnan(self.strain_exp)) or np.any(np.isnan(self.stress_exp)):
            print("错误: 数据中包含缺失值")
            return False
        
        # 检查应变是否单调递增（对于单轴拉伸试验）
        if not np.all(np.diff(self.strain_exp) >= 0):
            print("警告: 应变数据不是单调递增的")
        
        # 检查数据范围合理性
        if self.strain_exp.max() <= 0:
            print("错误: 最大应变小于等于0")
            return False
        
        if self.stress_exp.max() <= 0:
            print("错误: 最大应力小于等于0")
            return False
        
        print("数据质量验证通过")
        return True
    
    def get_material_properties(self):
        """
        从实验数据估算材料属性
        用于初始化物理参数
        """
        if self.strain_exp is None:
            raise ValueError("请先加载实验数据")
        
        # 估算初始弹性模量（取前10%数据的斜率）
        n_early = max(10, len(self.strain_exp) // 10)
        strain_early = self.strain_exp[:n_early]
        stress_early = self.stress_exp[:n_early]
        
        # 线性回归估算弹性模量
        if len(strain_early) > 1:
            E0_estimated = np.polyfit(strain_early, stress_early, 1)[0]
        else:
            E0_estimated = 30000.0  # 默认值
        
        # 抗拉强度（最大应力）
        f_t_estimated = self.stress_exp.max()
        
        properties = {
            'E0_estimated': float(E0_estimated),
            'f_t_estimated': float(f_t_estimated),
            'max_strain': float(self.strain_exp.max()),
            'max_stress': float(self.stress_exp.max())
        }
        
        print(f"估算的材料属性:")
        print(f"  初始弹性模量 E0: {E0_estimated:.2f} MPa")
        print(f"  抗拉强度 f_t: {f_t_estimated:.2f} MPa")
        
        return properties


def test_data_processor():
    """
    测试数据处理器
    """
    print("测试数据处理器...")
    
    processor = DataProcessor("tension.xlsx")
    
    # 加载数据
    if processor.load_experimental_data():
        # 验证数据质量
        if processor.validate_data_quality():
            # 准备训练数据
            training_data = processor.prepare_training_data()
            print(f"训练数据准备完成，序列长度: {training_data['sequence_length']}")
            
            # 绘制实验数据
            processor.plot_experimental_data()
            
            # 获取材料属性
            properties = processor.get_material_properties()
            
            return True
    
    return False


if __name__ == "__main__":
    test_data_processor() 