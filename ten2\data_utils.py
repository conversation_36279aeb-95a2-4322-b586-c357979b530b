import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader, TensorDataset
from typing import Tuple, List, Optional, Union
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler, StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 配置matplotlib中文字体显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 支持中文
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

class ConcreteDataProcessor:
    """
    混凝土拉伸试验数据处理器
    
    功能：
    1. 数据加载和预处理
    2. 数据清洗和平滑
    3. 创建训练数据集
    4. 数据可视化
    """
    
    def __init__(self, normalize: bool = True, scaler_type: str = 'minmax'):
        """
        初始化数据处理器
        
        Args:
            normalize: 是否归一化数据
            scaler_type: 归一化类型 ('minmax' or 'standard')
        """
        self.normalize = normalize
        self.scaler_type = scaler_type
        self.strain_scaler = None
        self.stress_scaler = None
        
        if normalize:
            if scaler_type == 'minmax':
                self.strain_scaler = MinMaxScaler()
                self.stress_scaler = MinMaxScaler()
            else:
                self.strain_scaler = StandardScaler()
                self.stress_scaler = StandardScaler()
    
    def load_data_from_arrays(self, 
                              strain_data: np.ndarray, 
                              stress_data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        从numpy数组加载数据
        
        Args:
            strain_data: 应变数据
            stress_data: 应力数据
            
        Returns:
            处理后的应变和应力数据
        """
        # 确保数据为numpy数组
        strain_data = np.array(strain_data).flatten()
        stress_data = np.array(stress_data).flatten()
        
        # 数据验证
        assert len(strain_data) == len(stress_data), "应变和应力数据长度不一致"
        assert len(strain_data) > 0, "数据为空"
        
        # 移除无效值
        valid_mask = ~(np.isnan(strain_data) | np.isnan(stress_data) | 
                       np.isinf(strain_data) | np.isinf(stress_data))
        strain_data = strain_data[valid_mask]
        stress_data = stress_data[valid_mask]
        
        print(f"加载数据: {len(strain_data)} 个数据点")
        print(f"应变范围: [{strain_data.min():.6f}, {strain_data.max():.6f}]")
        print(f"应力范围: [{stress_data.min():.2f}, {stress_data.max():.2f}] MPa")
        
        return strain_data, stress_data
    
    def load_data_from_csv(self, 
                           file_path: str, 
                           strain_col: str = 'strain', 
                           stress_col: str = 'stress',
                           skiprows: int = 0) -> Tuple[np.ndarray, np.ndarray]:
        """
        从CSV文件加载数据
        
        Args:
            file_path: CSV文件路径
            strain_col: 应变列名
            stress_col: 应力列名
            skiprows: 跳过的行数
            
        Returns:
            应变和应力数据
        """
        try:
            df = pd.read_csv(file_path, skiprows=skiprows)
            strain_data = df[strain_col].values
            stress_data = df[stress_col].values
            
            return self.load_data_from_arrays(strain_data, stress_data)
        
        except Exception as e:
            print(f"加载CSV文件失败: {e}")
            raise
    
    def load_data_from_excel(self, 
                             file_path: str, 
                             sheet_name: Union[str, int] = 0,
                             strain_col: str = 'strain', 
                             stress_col: str = 'stress',
                             skiprows: int = 0) -> Tuple[np.ndarray, np.ndarray]:
        """
        从Excel文件加载数据
        
        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称或索引
            strain_col: 应变列名
            stress_col: 应力列名
            skiprows: 跳过的行数
            
        Returns:
            应变和应力数据
        """
        try:
            print(f"正在读取Excel文件: {file_path}")
            df = pd.read_excel(file_path, sheet_name=sheet_name, skiprows=skiprows)
            
            print(f"✅ Excel文件读取成功")
            print(f"📊 数据形状: {df.shape} (行 x 列)")
            print(f"📋 列名: {list(df.columns)}")
            print(f"📑 前5行数据:")
            print(df.head())
            
            # 检查是否存在指定的列名
            available_cols = list(df.columns)
            
            # 寻找应变列
            actual_strain_col = None
            strain_possibilities = ['strain', 'Strain', 'STRAIN', '应变', 'epsilon', 'ε']
            
            for col in available_cols:
                if str(col).lower() == 'strain':
                    actual_strain_col = col
                    break
                elif any(candidate.lower() in str(col).lower() for candidate in strain_possibilities):
                    actual_strain_col = col
                    break
            
            if actual_strain_col is None:
                actual_strain_col = available_cols[0]  # 使用第一列
                print(f"⚠️  未找到strain列，使用第一列: {actual_strain_col}")
            else:
                print(f"✅ 找到应变列: {actual_strain_col}")
            
            # 寻找应力列
            actual_stress_col = None
            stress_possibilities = ['stress', 'Stress', 'STRESS', '应力', 'sigma', 'σ']
            
            for col in available_cols:
                if str(col).lower() == 'stress':
                    actual_stress_col = col
                    break
                elif any(candidate.lower() in str(col).lower() for candidate in stress_possibilities):
                    actual_stress_col = col
                    break
            
            if actual_stress_col is None:
                if len(available_cols) > 1:
                    actual_stress_col = available_cols[1]  # 使用第二列
                    print(f"⚠️  未找到stress列，使用第二列: {actual_stress_col}")
                else:
                    raise ValueError("Excel文件必须包含至少两列数据")
            else:
                print(f"✅ 找到应力列: {actual_stress_col}")
            
            # 提取数据
            strain_data = df[actual_strain_col].values
            stress_data = df[actual_stress_col].values
            
            print(f"📏 原始数据范围:")
            print(f"   应变: [{np.nanmin(strain_data):.6f}, {np.nanmax(strain_data):.6f}]")
            print(f"   应力: [{np.nanmin(stress_data):.3f}, {np.nanmax(stress_data):.3f}]")
            
            # 移除NaN值
            valid_mask = ~(pd.isna(strain_data) | pd.isna(stress_data))
            strain_data = strain_data[valid_mask]
            stress_data = stress_data[valid_mask]
            
            print(f"✅ 有效数据点: {len(strain_data)}")
            
            return self.load_data_from_arrays(strain_data, stress_data)
        
        except Exception as e:
            print(f"❌ 加载Excel文件失败: {e}")
            print(f"请检查：")
            print(f"1. 文件路径: {file_path}")
            print(f"2. 文件是否存在且可读取")
            print(f"3. Excel文件是否包含'stress'和'strain'列")
            print(f"4. 确保安装了openpyxl: pip install openpyxl")
            raise
    
    def clean_and_smooth_data(self, 
                              strain_data: np.ndarray, 
                              stress_data: np.ndarray,
                              remove_outliers: bool = True,
                              smooth: bool = True,
                              smooth_window: int = 5) -> Tuple[np.ndarray, np.ndarray]:
        """
        数据清洗和平滑
        
        Args:
            strain_data: 应变数据
            stress_data: 应力数据
            remove_outliers: 是否移除异常值
            smooth: 是否平滑数据
            smooth_window: 平滑窗口大小
            
        Returns:
            清洗后的数据
        """
        # 确保应变单调递增（适用于单调加载）
        sorted_indices = np.argsort(strain_data)
        strain_data = strain_data[sorted_indices]
        stress_data = stress_data[sorted_indices]
        
        # 移除重复的应变值
        unique_mask = np.diff(np.concatenate([[0], strain_data])) > 1e-10
        strain_data = strain_data[unique_mask]
        stress_data = stress_data[unique_mask]
        
        if remove_outliers:
            # 使用IQR方法移除应力异常值
            Q1 = np.percentile(stress_data, 25)
            Q3 = np.percentile(stress_data, 75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outlier_mask = (stress_data >= lower_bound) & (stress_data <= upper_bound)
            strain_data = strain_data[outlier_mask]
            stress_data = stress_data[outlier_mask]
            
            print(f"移除异常值后: {len(strain_data)} 个数据点")
        
        if smooth and len(strain_data) > smooth_window:
            # 使用移动平均平滑应力数据
            from scipy.ndimage import uniform_filter1d
            stress_data = uniform_filter1d(stress_data, size=smooth_window, mode='nearest')
            print(f"数据已使用窗口大小 {smooth_window} 进行平滑")
        
        return strain_data, stress_data
    
    def prepare_training_data(self, 
                              strain_data: np.ndarray, 
                              stress_data: np.ndarray,
                              test_split: float = 0.2,
                              random_seed: int = 42) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        准备训练数据
        
        Args:
            strain_data: 应变数据
            stress_data: 应力数据
            test_split: 测试集比例
            random_seed: 随机种子
            
        Returns:
            训练和测试的应变、应力张量
        """
        # 设置随机种子
        np.random.seed(random_seed)
        
        # 归一化数据
        original_strain = strain_data.copy()
        original_stress = stress_data.copy()
        
        if self.normalize:
            strain_data = self.strain_scaler.fit_transform(strain_data.reshape(-1, 1)).flatten()
            stress_data = self.stress_scaler.fit_transform(stress_data.reshape(-1, 1)).flatten()
            print("数据已归一化")
        
        # 分割数据
        n_samples = len(strain_data)
        n_train = int(n_samples * (1 - test_split))
        
        # 为了保持应变的单调性，按顺序分割而不是随机分割
        train_strain = strain_data[:n_train]
        train_stress = stress_data[:n_train]
        test_strain = strain_data[n_train:]
        test_stress = stress_data[n_train:]
        
        # 转换为PyTorch张量
        train_strain_tensor = torch.tensor(train_strain, dtype=torch.float32)
        train_stress_tensor = torch.tensor(train_stress, dtype=torch.float32)
        test_strain_tensor = torch.tensor(test_strain, dtype=torch.float32)
        test_stress_tensor = torch.tensor(test_stress, dtype=torch.float32)
        
        print(f"训练集: {len(train_strain)} 个样本")
        print(f"测试集: {len(test_strain)} 个样本")
        
        # 保存原始数据用于后续可视化
        self.original_strain = original_strain
        self.original_stress = original_stress
        self.train_indices = slice(0, n_train)
        self.test_indices = slice(n_train, None)
        
        return train_strain_tensor, train_stress_tensor, test_strain_tensor, test_stress_tensor
    
    def create_dataloaders(self, 
                           train_strain: torch.Tensor,
                           train_stress: torch.Tensor,
                           test_strain: torch.Tensor,
                           test_stress: torch.Tensor,
                           batch_size: int = 1,
                           shuffle: bool = False) -> Tuple[DataLoader, DataLoader]:
        """
        创建数据加载器
        
        Args:
            train_strain: 训练应变
            train_stress: 训练应力
            test_strain: 测试应变
            test_stress: 测试应力
            batch_size: 批次大小
            shuffle: 是否打乱数据
            
        Returns:
            训练和测试数据加载器
        """
        # 对于序列数据，通常batch_size=1且不打乱
        train_dataset = TensorDataset(train_strain.unsqueeze(0), train_stress.unsqueeze(0))
        test_dataset = TensorDataset(test_strain.unsqueeze(0), test_stress.unsqueeze(0))
        
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=shuffle)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
        
        return train_loader, test_loader
    
    def inverse_transform(self, data: np.ndarray, data_type: str = 'strain') -> np.ndarray:
        """
        反归一化数据
        
        Args:
            data: 归一化后的数据
            data_type: 数据类型 ('strain' or 'stress')
            
        Returns:
            原始尺度的数据
        """
        if not self.normalize:
            return data
        
        data = np.array(data).reshape(-1, 1)
        
        if data_type == 'strain':
            return self.strain_scaler.inverse_transform(data).flatten()
        else:
            return self.stress_scaler.inverse_transform(data).flatten()
    
    def plot_raw_data(self, 
                      strain_data: np.ndarray, 
                      stress_data: np.ndarray,
                      title: str = "Raw Stress-Strain Curve",
                      save_path: Optional[str] = None) -> None:
        """
        绘制原始数据
        
        Args:
            strain_data: 应变数据
            stress_data: 应力数据
            title: 图表标题
            save_path: 保存路径
        """
        plt.figure(figsize=(12, 8))
        plt.plot(strain_data, stress_data, 'bo-', markersize=4, linewidth=1.5, alpha=0.8, label='Raw Data')
        plt.xlabel('Strain', fontsize=14)
        plt.ylabel('Stress [MPa]', fontsize=14)
        plt.title(title, fontsize=16, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.legend(fontsize=12)
        
        # 添加关键信息
        max_stress = np.max(stress_data)
        max_strain = strain_data[np.argmax(stress_data)]
        plt.axhline(y=max_stress, color='r', linestyle='--', alpha=0.7, linewidth=2)
        plt.axvline(x=max_strain, color='r', linestyle='--', alpha=0.7, linewidth=2)
        
        # 标注峰值
        plt.annotate(f'Peak: ({max_strain:.6f}, {max_stress:.2f})', 
                    xy=(max_strain, max_stress), 
                    xytext=(max_strain*1.2, max_stress*0.8),
                    arrowprops=dict(arrowstyle='->', color='red', alpha=0.7),
                    fontsize=12, 
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
        
        # 数据统计信息
        info_text = f'Data Points: {len(strain_data)}\nPeak Stress: {max_stress:.2f} MPa\nPeak Strain: {max_strain:.6f}'
        plt.text(0.02, 0.98, info_text, 
                 transform=plt.gca().transAxes, 
                 verticalalignment='top',
                 fontsize=11,
                 bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.9))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        
        plt.show()
    
    def plot_data_split(self, save_path: Optional[str] = None) -> None:
        """
        绘制数据分割结果
        
        Args:
            save_path: 保存路径
        """
        if not hasattr(self, 'original_strain'):
            print("请先调用 prepare_training_data 方法")
            return
        
        plt.figure(figsize=(12, 8))
        
        # 训练数据
        plt.plot(self.original_strain[self.train_indices], 
                 self.original_stress[self.train_indices], 
                 'bo-', label='Training Data', markersize=4, alpha=0.8, linewidth=1.5)
        
        # 测试数据
        plt.plot(self.original_strain[self.test_indices], 
                 self.original_stress[self.test_indices], 
                 'ro-', label='Test Data', markersize=4, alpha=0.8, linewidth=1.5)
        
        plt.xlabel('Strain', fontsize=14)
        plt.ylabel('Stress [MPa]', fontsize=14)
        plt.title('Training/Test Data Split', fontsize=16, fontweight='bold')
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # 添加数据统计信息
        train_count = len(self.original_strain[self.train_indices])
        test_count = len(self.original_strain[self.test_indices])
        info_text = f'Training: {train_count} points\nTest: {test_count} points\nTotal: {train_count + test_count} points'
        plt.text(0.02, 0.98, info_text, 
                 transform=plt.gca().transAxes, 
                 verticalalignment='top',
                 fontsize=11,
                 bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.9))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        
        plt.show()

def generate_synthetic_tension_data(E0: float = 30000.0, 
                                    ft: float = 3.0,
                                    A_plus: float = 0.7,
                                    B_plus: float = 2.0,
                                    xi: float = 0.1,
                                    max_strain: float = 0.001,
                                    n_points: int = 100,
                                    noise_level: float = 0.02) -> Tuple[np.ndarray, np.ndarray]:
    """
    生成合成的混凝土拉伸试验数据用于测试
    
    Args:
        E0: 初始弹性模量
        ft: 抗拉强度
        A_plus, B_plus, xi: 材料参数
        max_strain: 最大应变
        n_points: 数据点数
        noise_level: 噪声水平
        
    Returns:
        应变和应力数据
    """
    # 生成应变序列
    strain_seq = np.linspace(0, max_strain, n_points)
    stress_seq = np.zeros_like(strain_seq)
    
    # 初始状态
    r_max = ft
    plastic_strain = 0.0
    damage = 0.0
    
    for i, strain in enumerate(strain_seq):
        # 更新塑性应变
        if i > 0:
            delta_strain = strain - strain_seq[i-1]
            plastic_strain += xi * delta_strain
        
        # 计算损伤驱动力
        Y = E0 * (strain - plastic_strain)
        
        # 更新损伤阈值
        if Y > r_max:
            r_max = Y
            # 计算损伤
            term1 = ft / r_max * (1 - A_plus)
            term2 = A_plus * np.exp(B_plus * (1 - r_max / ft))
            damage = 1 - (term1 + term2)
            damage = max(0, min(1, damage))  # 限制在[0,1]
        
        # 计算应力
        stress_seq[i] = E0 * (1 - damage) * (strain - plastic_strain)
    
    # 添加噪声
    if noise_level > 0:
        noise = np.random.normal(0, noise_level * np.max(stress_seq), len(stress_seq))
        stress_seq += noise
        stress_seq = np.maximum(stress_seq, 0)  # 确保应力非负
    
    return strain_seq, stress_seq

# 使用示例
if __name__ == "__main__":
    # 生成合成数据用于测试
    strain_data, stress_data = generate_synthetic_tension_data()
    
    # 创建数据处理器
    processor = ConcreteDataProcessor(normalize=True)
    
    # 处理数据
    strain_clean, stress_clean = processor.clean_and_smooth_data(strain_data, stress_data)
    
    # 绘制原始数据
    processor.plot_raw_data(strain_clean, stress_clean, "合成混凝土拉伸数据")
    
    # 准备训练数据
    train_strain, train_stress, test_strain, test_stress = processor.prepare_training_data(
        strain_clean, stress_clean
    )
    
    # 创建数据加载器
    train_loader, test_loader = processor.create_dataloaders(
        train_strain, train_stress, test_strain, test_stress
    )
    
    print("数据处理完成！") 