import pandas as pd
import torch
import numpy as np
from simplified_column_hysteresis_model import load_excel_data, preprocess_data

def check_dimensions():
    excel_path = 'd:/column/column/column.xlsx'
    print('\n检查数据维度:')
    
    # 加载数据
    static_params, dynamic_data = load_excel_data(excel_path)
    print(f'静态参数数量: {len(static_params)}')
    
    # 预处理数据
    X, y, scalers, original_data = preprocess_data(static_params, dynamic_data)
    print(f'输入特征维度: {X.shape}')
    print(f'静态特征数量: {len(static_params)}')
    print(f'动态特征数量: {X.shape[1] - len(static_params)}')
    
    # 打印静态参数
    print('\n静态参数列表:')
    for i, (key, value) in enumerate(static_params.items()):
        if isinstance(value, (int, float)) and not isinstance(value, bool) and not pd.isna(value):
            print(f'  {i+1}. {key}: {value} (数值型)')
        else:
            print(f'  {i+1}. {key}: {value} (非数值型)')
    
    # 计算数值型静态参数数量
    numeric_params = {}
    for key, value in static_params.items():
        if isinstance(value, (int, float)) and not isinstance(value, bool) and not pd.isna(value):
            numeric_params[key] = value
    
    print(f'\n数值型静态参数数量: {len(numeric_params)}')

if __name__ == "__main__":
    check_dimensions()