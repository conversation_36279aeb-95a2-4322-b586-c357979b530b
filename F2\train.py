import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime

from data_processor import DataProcessor
from pinn_model import PINN, PINNLoss
from font_config import configure_chinese_font

# 配置中文字体
configure_chinese_font()

class Trainer:
    def __init__(self, model, data_processor, learning_rate=0.001, 
                 lambda_phys=1.0, lambda_damage=1.0):
        """
        初始化训练器
        
        Args:
            model: PINN模型
            data_processor: 数据处理器
            learning_rate: 学习率
            lambda_phys: 物理损失权重
            lambda_damage: 损伤损失权重
        """
        self.model = model
        self.data_processor = data_processor
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model.to(self.device)
        
        # 优化器
        self.optimizer = optim.Adam(model.parameters(), lr=learning_rate)
        
        # 损失函数
        self.loss_fn = PINNLoss(model, lambda_phys, lambda_damage)
        
        # 训练历史
        self.history = {
            'epoch': [],
            'total_loss': [],
            'data_loss': [],
            'phys_loss': [],
            'damage_loss': [],
            'increment_loss': []
        }
        
        # 创建保存模型的目录
        self.checkpoint_dir = 'd:/column/F2/checkpoints'
        os.makedirs(self.checkpoint_dir, exist_ok=True)
    
    def train(self, epochs=1000, batch_size=32, eval_interval=100, save_interval=200, use_strain_increment=True):
        """
        训练模型
        
        Args:
            epochs: 训练轮数
            batch_size: 批次大小
            eval_interval: 评估间隔
            save_interval: 保存间隔
            use_strain_increment: 是否使用应变增量作为输入
        """
        # 准备训练数据 - 包含应变增量
        X_train, X_test, y_train, y_test = self.data_processor.prepare_training_data(include_delta=use_strain_increment)
        
        # 转换为PyTorch张量
        X_train_tensor = torch.tensor(X_train, dtype=torch.float32).to(self.device)
        y_train_tensor = torch.tensor(y_train, dtype=torch.float32).to(self.device)
        X_test_tensor = torch.tensor(X_test, dtype=torch.float32).to(self.device)
        y_test_tensor = torch.tensor(y_test, dtype=torch.float32).to(self.device)
        
        # 创建数据加载器
        train_dataset = torch.utils.data.TensorDataset(X_train_tensor, y_train_tensor)
        train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        
        print(f"开始训练，使用设备: {self.device}")
        print(f"训练集大小: {len(X_train)}, 测试集大小: {len(X_test)}")
        
        for epoch in range(1, epochs + 1):
            self.model.train()
            epoch_loss = 0.0
            epoch_data_loss = 0.0
            epoch_phys_loss = 0.0
            epoch_damage_loss = 0.0
            epoch_increment_loss = 0.0
            
            for batch_X, batch_y in train_loader:
                # 前向传播
                self.optimizer.zero_grad()
                total_loss, data_loss, phys_loss, damage_loss, increment_loss = self.loss_fn(batch_X, batch_y)
                
                # 反向传播
                total_loss.backward()
                self.optimizer.step()
                
                # 累计损失
                epoch_loss += total_loss.item()
                epoch_data_loss += data_loss.item()
                epoch_phys_loss += phys_loss.item()
                epoch_damage_loss += damage_loss.item()
                epoch_increment_loss += increment_loss.item()
            
            # 计算平均损失
            avg_loss = epoch_loss / len(train_loader)
            avg_data_loss = epoch_data_loss / len(train_loader)
            avg_phys_loss = epoch_phys_loss / len(train_loader)
            avg_damage_loss = epoch_damage_loss / len(train_loader)
            avg_increment_loss = epoch_increment_loss / len(train_loader)
            
            # 记录训练历史
            self.history['epoch'].append(epoch)
            self.history['total_loss'].append(avg_loss)
            self.history['data_loss'].append(avg_data_loss)
            self.history['phys_loss'].append(avg_phys_loss)
            self.history['damage_loss'].append(avg_damage_loss)
            self.history['increment_loss'].append(avg_increment_loss)
            
            # 打印训练进度
            if epoch % eval_interval == 0 or epoch == 1:
                # 评估模型
                self.model.eval()
                with torch.no_grad():
                    test_loss, test_data_loss, test_phys_loss, test_damage_loss, test_increment_loss = self.loss_fn(X_test_tensor, y_test_tensor)
                
                print(f"Epoch {epoch}/{epochs} - "
                      f"Loss: {avg_loss:.4f} - "
                      f"Data Loss: {avg_data_loss:.4f} - "
                      f"Phys Loss: {avg_phys_loss:.4f} - "
                      f"Damage Loss: {avg_damage_loss:.4f} - "
                      f"Increment Loss: {avg_increment_loss:.4f} - "
                      f"Test Loss: {test_loss.item():.4f}")
            
            # 保存模型
            if epoch % save_interval == 0 or epoch == epochs:
                self.save_model(epoch)
        
        print("训练完成")
        self.save_model("final")
        self.plot_training_history()
        self.evaluate(X_test_tensor, y_test_tensor)
    
    def save_model(self, epoch):
        """
        保存模型
        
        Args:
            epoch: 当前轮数或标识符
        """
        checkpoint_path = os.path.join(self.checkpoint_dir, f"pinn_model_epoch_{epoch}.pth")
        torch.save({
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'history': self.history
        }, checkpoint_path)
        print(f"模型已保存: {checkpoint_path}")
    
    def load_model(self, checkpoint_path):
        """
        加载模型
        
        Args:
            checkpoint_path: 检查点路径
        """
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.history = checkpoint['history']
        print(f"模型已加载: {checkpoint_path}")
    
    def plot_training_history(self):
        """
        绘制训练历史
        """
        plt.figure(figsize=(15, 10))
        
        # 绘制总损失
        plt.subplot(2, 3, 1)
        plt.plot(self.history['epoch'], self.history['total_loss'])
        plt.xlabel('Epoch')
        plt.ylabel('Total Loss')
        plt.title('Total Loss vs. Epoch')
        plt.grid(True)
        
        # 绘制数据损失
        plt.subplot(2, 3, 2)
        plt.plot(self.history['epoch'], self.history['data_loss'])
        plt.xlabel('Epoch')
        plt.ylabel('Data Loss')
        plt.title('Data Loss vs. Epoch')
        plt.grid(True)
        
        # 绘制物理损失
        plt.subplot(2, 3, 3)
        plt.plot(self.history['epoch'], self.history['phys_loss'])
        plt.xlabel('Epoch')
        plt.ylabel('Physics Loss')
        plt.title('Physics Loss vs. Epoch')
        plt.grid(True)
        
        # 绘制损伤损失
        plt.subplot(2, 3, 4)
        plt.plot(self.history['epoch'], self.history['damage_loss'])
        plt.xlabel('Epoch')
        plt.ylabel('Damage Loss')
        plt.title('Damage Loss vs. Epoch')
        plt.grid(True)
        
        # 绘制增量点标识损失
        plt.subplot(2, 3, 5)
        plt.plot(self.history['epoch'], self.history['increment_loss'])
        plt.xlabel('Epoch')
        plt.ylabel('Increment Loss')
        plt.title('Increment Loss vs. Epoch')
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('d:/column/F2/training_history.png')
        plt.close()
        print("训练历史图表已保存")
    
    def evaluate(self, X_test, y_test):
        """
        评估模型
        
        Args:
            X_test: 测试输入
            y_test: 测试目标
        """
        self.model.eval()
        with torch.no_grad():
            # 获取预测 - 注意现在返回三个值：应力、损伤和增量点标识
            pred_stress, pred_damage, pred_increment = self.model.get_stress_damage(X_test)
            
            # 转换为NumPy数组
            X_np = X_test.cpu().numpy()
            y_np = y_test.cpu().numpy()
            pred_stress_np = pred_stress.cpu().numpy()
            pred_damage_np = pred_damage.cpu().numpy()
            
            # 反归一化
            strain_actual = self.data_processor.denormalize_strain(X_np)
            stress_actual = self.data_processor.denormalize_stress(y_np)
            stress_pred = self.data_processor.denormalize_stress(pred_stress_np)
            
            # 绘制应力-应变对比图
            plt.figure(figsize=(10, 6))
            plt.scatter(strain_actual, stress_actual, s=10, label='实验数据')
            plt.scatter(strain_actual, stress_pred, s=10, label='PINN预测')
            plt.xlabel('应变 (ε)')
            plt.ylabel('应力 (MPa)')
            plt.title('应力-应变曲线对比')
            plt.legend()
            plt.grid(True)
            plt.savefig('d:/column/F2/stress_strain_comparison.png')
            plt.close()
            
            # 绘制损伤演化图
            plt.figure(figsize=(10, 6))
            plt.scatter(strain_actual, pred_damage_np, s=10)
            plt.xlabel('应变 (ε)')
            plt.ylabel('损伤变量 (d)')
            plt.title('损伤演化曲线')
            plt.grid(True)
            plt.savefig('d:/column/F2/damage_evolution.png')
            plt.close()
            
            # 验证本构关系
            constitutive_stress = self.model.constitutive_stress(X_test, pred_damage)
            constitutive_stress_np = constitutive_stress.cpu().numpy()
            constitutive_stress_actual = self.data_processor.denormalize_stress(constitutive_stress_np)
            
            plt.figure(figsize=(10, 6))
            plt.scatter(strain_actual, stress_pred, s=10, label='PINN预测应力')
            plt.scatter(strain_actual, constitutive_stress_actual, s=10, label='本构关系计算应力')
            plt.xlabel('应变 (ε)')
            plt.ylabel('应力 (MPa)')
            plt.title('本构关系验证')
            plt.legend()
            plt.grid(True)
            plt.savefig('d:/column/F2/constitutive_verification.png')
            plt.close()
            
            print("评估图表已保存")

# 主函数
if __name__ == "__main__":
    # 数据处理
    file_path = 'd:/column/F2/data.xlsx'
    processor = DataProcessor(file_path)
    processor.load_data()
    processor.convert_to_stress_strain()
    processor.normalize_data()
    processor.plot_raw_data()
    processor.plot_stress_strain()
    
    # 创建模型 - 使用更深更宽的网络结构
    model = PINN(hidden_layers=3, neurons=32)
    
    # 创建训练器 - 调整学习率和损失函数权重
    trainer = Trainer(model, processor, learning_rate=0.0005, lambda_phys=2.0, lambda_damage=0.5)
    
    # 训练模型 - 增加训练轮数
    trainer.train(epochs=2000, batch_size=32, eval_interval=100, save_interval=200)