"""
字体配置文件
确保图像中的中文字体正确显示
"""

import matplotlib.pyplot as plt
import matplotlib
from pathlib import Path
import platform
import warnings

def setup_chinese_font():
    """
    设置中文字体
    """
    try:
        # 根据操作系统选择字体
        system = platform.system()
        
        if system == "Windows":
            # Windows系统常用中文字体
            font_candidates = [
                'SimHei',           # 黑体
                'Microsoft YaHei',  # 微软雅黑
                'SimSun',           # 宋体
                'KaiTi',            # 楷体
            ]
        elif system == "Darwin":  # macOS
            font_candidates = [
                'Heiti TC',         # 黑体-繁
                'PingFang SC',      # 苹方-简
                'Hiragino Sans GB', # 冬青黑体简体中文
                'STHeiti',          # 华文黑体
            ]
        else:  # Linux
            font_candidates = [
                'WenQuanYi Micro Hei',  # 文泉驿微米黑
                'DejaVu Sans',
                'SimHei',
            ]
        
        # 尝试设置字体
        font_set = False
        for font_name in font_candidates:
            try:
                # 设置字体系列，添加更多fallback字体来处理特殊字符
                plt.rcParams['font.sans-serif'] = [
                    font_name, 
                    'DejaVu Sans',           # 支持多种特殊字符
                    'Arial Unicode MS',      # 支持Unicode字符
                    'Segoe UI Symbol',       # 支持符号
                    'Symbol',                # 数学符号
                    'sans-serif'
                ]
                plt.rcParams['axes.unicode_minus'] = False  # 使用Unicode减号
                
                # 测试字体是否可用
                fig, ax = plt.subplots(figsize=(1, 1))
                ax.text(0.5, 0.5, '测试中文', fontsize=12)
                plt.close(fig)
                
                print(f"成功设置中文字体: {font_name}")
                font_set = True
                break
            except Exception as e:
                continue
        
        if not font_set:
            # 如果所有字体都不可用，使用默认设置并发出警告
            warnings.warn("无法找到合适的中文字体，可能影响图像中文显示")
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
            plt.rcParams['axes.unicode_minus'] = True
        
        # 设置其他绘图参数
        plt.rcParams['figure.figsize'] = (10, 6)
        plt.rcParams['figure.dpi'] = 100
        plt.rcParams['savefig.dpi'] = 300
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.labelsize'] = 12
        plt.rcParams['axes.titlesize'] = 14
        plt.rcParams['xtick.labelsize'] = 10
        plt.rcParams['ytick.labelsize'] = 10
        plt.rcParams['legend.fontsize'] = 11
        plt.rcParams['grid.alpha'] = 0.3
        
        # 确保正确处理特殊字符和编码
        plt.rcParams['mathtext.fontset'] = 'dejavusans'  # 数学字体
        plt.rcParams['mathtext.default'] = 'regular'
        plt.rcParams['text.usetex'] = False  # 不使用LaTeX
        
        # 强制使用Unicode编码
        matplotlib.rcParams['font.family'] = 'sans-serif'
        matplotlib.rcParams['axes.unicode_minus'] = False  # 使用Unicode减号
        
        return True
        
    except Exception as e:
        print(f"字体设置失败: {e}")
        return False

def get_font_properties():
    """
    获取当前字体属性
    """
    return {
        'font.sans-serif': plt.rcParams['font.sans-serif'],
        'font.size': plt.rcParams['font.size'],
        'axes.unicode_minus': plt.rcParams['axes.unicode_minus']
    }

def test_chinese_display(save_path='results/font_test.png'):
    """
    测试中文显示效果
    """
    try:
        from pathlib import Path
        
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # 测试各种中文字符
        test_texts = [
            '应变 (千分比)',
            '应力 (MPa)',
            '损伤变量 D',
            '塑性应变',
            '实验数据',
            '预测数据',
            '训练历史',
            '物理信息神经网络',
            '弹性模量 E0',
            '抗拉强度 ft',
            '应变增量',
            '参数 A+, B+, xi'
        ]
        
        for i, text in enumerate(test_texts):
            ax.text(0.1, 0.9 - i*0.1, text, fontsize=12, transform=ax.transAxes)
        
        ax.set_title('中文字体显示测试')
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.grid(True, alpha=0.3)
        
        # 确保保存目录存在
        Path(save_path).parent.mkdir(parents=True, exist_ok=True)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"中文字体测试完成，测试图像已保存至: {save_path}")
        return True
        
    except Exception as e:
        print(f"中文字体测试失败: {e}")
        return False

# 自动设置字体
if __name__ == "__main__":
    print("初始化字体配置...")
    if setup_chinese_font():
        print("字体配置成功")
        print("当前字体属性:", get_font_properties())
        test_chinese_display()
    else:
        print("字体配置失败")
else:
    # 导入时自动设置字体
    setup_chinese_font() 