** ==========================================================
** INP Template for Single Element Cyclic Loading Test
** ==========================================================
*HEADING
Single Element Cyclic Loading Test with VUMAT
**
** PART DEFINITION: 10x10x100mm Column
*PART, NAME=COLUMN
*NODE
  1,   0.0,   0.0,   0.0
  2,  10.0,   0.0,   0.0
  3,  10.0,  10.0,   0.0
  4,   0.0,  10.0,   0.0
  5,   0.0,   0.0, 100.0
  6,  10.0,   0.0, 100.0
  7,  10.0,  10.0, 100.0
  8,   0.0,  10.0, 100.0
*ELEMENT, TYPE=C3D8R, ELSET=E_COLUMN
  1, 1, 2, 3, 4, 5, 6, 7, 8
*NSET, NSET=N_BOTTOM
  1, 2, 3, 4
*NSET, NSET=N_TOP
  5, 6, 7, 8
** Section assignment points to a material name that will be defined
** in the included file.
*SOLID SECTION, ELSET=E_COLUMN, MATERIAL=CONCRETE_VUMAT
*END PART
**
** ASSEMBLY
*ASSEMBLY, NAME=Assm
*INSTANCE, NAME=COLUMN-1, PART=COLUMN
*END INSTANCE
*NSET, NSET=SET_BOTTOM, INSTANCE=COLUMN-1
N_BOTTOM
*NSET, NSET=SET_TOP, INSTANCE=COLUMN-1
N_TOP
*END ASSEMBLY
**
** MATERIAL DEFINITION WILL BE INCLUDED FROM ANOTHER FILE
*INCLUDE, INPUT=material_for_abaqus.inp
**
** AMPLITUDE for cyclic loading (3 cycles)
*AMPLITUDE, NAME=AMP-CYCLIC, DEFINITION=TABULAR
** Time, Amplitude (normalized displacement)
0.0,  0.0
0.1,  1.0   ! First tensile peak (2mm)
0.2,  0.0   ! Return to zero
0.3, -1.0   ! First compressive peak (-3mm)
0.4,  0.0   ! Return to zero
0.5,  1.0   ! Second tensile peak
0.6,  0.0   ! Return to zero
0.7, -1.0   ! Second compressive peak
0.8,  0.0   ! Return to zero
0.9,  1.0   ! Third tensile peak
1.0,  0.0   ! Final return to zero
**
** STEP DEFINITION
*STEP, NAME=CyclicLoading
*DYNAMIC, EXPLICIT
, 3.0
**
** BOUNDARY CONDITIONS
*BOUNDARY
SET_BOTTOM, ENCASTRE
** Applied cyclic displacement in Z-direction
** Maximum tensile: 2mm, Maximum compressive: 3mm
*BOUNDARY, TYPE=DISPLACEMENT, AMP=AMP-CYCLIC
SET_TOP, 3, 3, 2.0
**
** OUTPUT REQUESTS
*OUTPUT, FIELD, FREQUENCY=50
*ELEMENT OUTPUT, ELSET=E_COLUMN
S, E, SDV  ! SDV is for state variables
*NODE OUTPUT
U, RF
*OUTPUT, HISTORY, FREQUENCY=1
*NODE OUTPUT, NSET=SET_TOP
U3
*NODE OUTPUT, NSET=SET_BOTTOM
RF3
*END STEP 