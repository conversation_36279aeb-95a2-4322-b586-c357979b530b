import torch
import numpy as np
import matplotlib.pyplot as plt
import os
import argparse

from data_processor import DataProcessor
from pinn_model import PINN
from font_config import configure_chinese_font

# 配置中文字体
configure_chinese_font()

class Predictor:
    def __init__(self, model_path, data_processor=None):
        """
        初始化预测器
        
        Args:
            model_path: 模型路径
            data_processor: 数据处理器，用于归一化和反归一化
        """
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = PINN(hidden_layers=3, neurons=32).to(self.device)
        self.data_processor = data_processor
        
        # 加载模型
        self.load_model(model_path)
    
    def load_model(self, model_path):
        """
        加载模型
        
        Args:
            model_path: 模型路径
        """
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.eval()
            print(f"模型已加载: {model_path}")
        except Exception as e:
            print(f"加载模型失败: {e}")
    
    def predict(self, strain, delta_strain=None):
        """
        预测应力、损伤和增量点标识
        
        Args:
            strain: 应变值或应变数组
            delta_strain: 应变增量值或应变增量数组，如果为None则自动计算
            
        Returns:
            stress: 预测的应力
            damage: 预测的损伤
            increment_point: 增量点标识
        """
        # 确保输入是NumPy数组
        if not isinstance(strain, np.ndarray):
            strain = np.array([strain]).reshape(-1, 1)
        else:
            strain = strain.reshape(-1, 1)
        
        # 处理应变增量
        if delta_strain is None:
            # 自动计算应变增量
            delta_strain = np.zeros_like(strain)
            if strain.shape[0] > 1:
                delta_strain[1:] = strain[1:] - strain[:-1]
        elif not isinstance(delta_strain, np.ndarray):
            delta_strain = np.array([delta_strain]).reshape(-1, 1)
        else:
            delta_strain = delta_strain.reshape(-1, 1)
        
        # 归一化应变和应变增量（如果有数据处理器）
        if self.data_processor is not None:
            normalized_strain = strain / self.data_processor.strain_max
            normalized_delta = delta_strain / self.data_processor.strain_max
        else:
            normalized_strain = strain  # 假设输入已经归一化
            normalized_delta = delta_strain
        
        # 转换为PyTorch张量
        strain_tensor = torch.tensor(normalized_strain, dtype=torch.float32).to(self.device)
        delta_tensor = torch.tensor(normalized_delta, dtype=torch.float32).to(self.device)
        
        # 预测
        with torch.no_grad():
            pred_stress, pred_damage, pred_increment = self.model.get_stress_damage(strain_tensor, delta_tensor)
        
        # 转换为NumPy数组
        stress = pred_stress.cpu().numpy()
        damage = pred_damage.cpu().numpy()
        increment_point = pred_increment.cpu().numpy()
        
        # 反归一化应力（如果有数据处理器）
        if self.data_processor is not None:
            stress = self.data_processor.denormalize_stress(stress)
        
        return stress, damage, increment_point
    
    def predict_range(self, strain_min, strain_max, num_points=100, include_unloading=True):
        """
        预测一个应变范围内的应力、损伤和增量点标识
        改进版本：更精确地处理加载和卸载阶段的应变变化量
        
        Args:
            strain_min: 最小应变
            strain_max: 最大应变
            num_points: 点数
            include_unloading: 是否包含卸载阶段
            
        Returns:
            strain_range: 应变范围
            stress_pred: 预测的应力
            damage_pred: 预测的损伤
            increment_pred: 预测的增量点标识
        """
        if include_unloading:
            # 创建包含加载和卸载阶段的应变范围
            # 加载阶段：从最小应变到最大应变
            loading_points = num_points // 2
            loading_strain = np.linspace(strain_min, strain_max, loading_points).reshape(-1, 1)
            
            # 卸载阶段：从最大应变回到最小应变
            unloading_points = num_points - loading_points
            unloading_strain = np.linspace(strain_max, strain_min, unloading_points).reshape(-1, 1)
            
            # 合并加载和卸载阶段
            strain_range = np.vstack([loading_strain, unloading_strain])
            
            # 正确计算应变增量（变化量）
            delta_strain = np.zeros_like(strain_range)
            
            # 加载阶段的应变变化量（正值）
            delta_strain[1:loading_points] = strain_range[1:loading_points] - strain_range[:loading_points-1]
            
            # 加载和卸载的过渡点（第一个卸载点）
            # 确保过渡点的变化量为负值，表示开始卸载
            if loading_points < len(strain_range):
                # 过渡点应该是卸载的开始，所以变化量应该是负的
                delta_strain[loading_points] = strain_range[loading_points] - strain_range[loading_points-1]
            
            # 卸载阶段的应变变化量（负值）
            if loading_points+1 < len(strain_range):
                # 修正卸载阶段的变化量计算，确保每个点都与前一个点比较
                for i in range(loading_points+1, len(strain_range)):
                    delta_strain[i] = strain_range[i] - strain_range[i-1]
        else:
            # 只创建加载阶段的应变范围
            strain_range = np.linspace(strain_min, strain_max, num_points).reshape(-1, 1)
            
            # 计算应变增量（变化量）
            delta_strain = np.zeros_like(strain_range)
            delta_strain[1:] = strain_range[1:] - strain_range[:-1]
        
        # 预测
        stress_pred, damage_pred, increment_pred = self.predict(strain_range, delta_strain)
        
        return strain_range, stress_pred, damage_pred, increment_pred
    
    def plot_predictions(self, strain_min, strain_max, num_points=100, save_path=None, include_unloading=True):
        """
        绘制预测结果
        
        Args:
            strain_min: 最小应变
            strain_max: 最大应变
            num_points: 点数
            save_path: 保存路径
            include_unloading: 是否包含卸载阶段
        """
        # 预测
        strain_range, stress_pred, damage_pred, increment_pred = self.predict_range(
            strain_min, strain_max, num_points, include_unloading=include_unloading
        )
        
        # 计算应变变化量
        delta_strain = np.zeros_like(strain_range)
        delta_strain[1:] = strain_range[1:] - strain_range[:-1]
        
        # 确定加载和卸载阶段
        loading_indices = np.where(delta_strain >= 0)[0]
        unloading_indices = np.where(delta_strain < 0)[0]
        
        # 绘制应力-应变曲线
        plt.figure(figsize=(15, 10))
        
        # 应力-应变曲线
        plt.subplot(2, 2, 1)
        # 绘制完整曲线
        plt.plot(strain_range, stress_pred, 'b-', alpha=0.5, label='完整曲线')
        # 分别绘制加载和卸载阶段
        if len(loading_indices) > 0:
            plt.plot(strain_range[loading_indices], stress_pred[loading_indices], 
                   'g-', linewidth=2, label='加载阶段')
        if len(unloading_indices) > 0:
            plt.plot(strain_range[unloading_indices], stress_pred[unloading_indices], 
                   'r-', linewidth=2, label='卸载阶段')
        # 标记增量点
        increment_indices = np.where(increment_pred > 0.5)[0]
        if len(increment_indices) > 0:
            plt.scatter(strain_range[increment_indices], stress_pred[increment_indices], 
                      color='purple', marker='o', s=50, label='增量点')
        plt.xlabel('应变 (ε)')
        plt.ylabel('应力 (MPa)')
        plt.title('预测的应力-应变曲线')
        plt.legend()
        plt.grid(True)
        
        # 损伤-应变曲线
        plt.subplot(2, 2, 2)
        # 绘制完整曲线
        plt.plot(strain_range, damage_pred, 'b-', alpha=0.5, label='完整曲线')
        # 分别绘制加载和卸载阶段
        if len(loading_indices) > 0:
            plt.plot(strain_range[loading_indices], damage_pred[loading_indices], 
                   'g-', linewidth=2, label='加载阶段')
        if len(unloading_indices) > 0:
            plt.plot(strain_range[unloading_indices], damage_pred[unloading_indices], 
                   'r-', linewidth=2, label='卸载阶段')
        # 标记增量点
        if len(increment_indices) > 0:
            plt.scatter(strain_range[increment_indices], damage_pred[increment_indices], 
                      color='purple', marker='o', s=50, label='增量点')
        plt.xlabel('应变 (ε)')
        plt.ylabel('损伤变量 (d)')
        plt.title('预测的损伤-应变曲线')
        plt.legend()
        plt.grid(True)
        
        # 增量点标识-应变曲线
        plt.subplot(2, 2, 3)
        plt.plot(strain_range, increment_pred, 'g-')
        plt.axhline(y=0.5, color='r', linestyle='--', label='阈值 (0.5)')
        plt.xlabel('应变 (ε)')
        plt.ylabel('增量点标识')
        plt.title('预测的增量点标识')
        plt.legend()
        plt.grid(True)
        
        # 应变变化量-应变曲线（改进版）
        plt.subplot(2, 2, 4)
        # 区分加载和卸载阶段的应变变化量
        loading_delta = delta_strain.copy()
        unloading_delta = delta_strain.copy()
        
        # 正确处理加载和卸载阶段的应变变化量
        # 加载阶段：应变增加，变化量为正
        # 卸载阶段：应变减少，变化量为负
        loading_delta[delta_strain < 0] = np.nan  # 将变化量为负的点设为NaN
        unloading_delta[delta_strain >= 0] = np.nan  # 将变化量为正的点设为NaN
        
        # 绘制应变变化量，区分加载和卸载阶段
        plt.plot(strain_range, loading_delta, 'g-', linewidth=2, label='加载阶段应变变化量')
        plt.plot(strain_range, unloading_delta, 'r-', linewidth=2, label='卸载阶段应变变化量')
        
        # 标记零线（区分加载和卸载）
        plt.axhline(y=0, color='b', linestyle='--', label='零线')
        
        # 标记损伤阈值，区分加载和卸载阶段
        if hasattr(self.model, 'eps0'):
            loading_eps0 = self.model.eps0
            unloading_eps0 = self.model.eps0 * 0.5  # 卸载阶段使用更合理的阈值，与模型中保持一致
            
            if self.data_processor is not None:
                loading_eps0 = loading_eps0 * self.data_processor.strain_max
                unloading_eps0 = unloading_eps0 * self.data_processor.strain_max
            
            # 绘制加载阶段阈值线
            plt.axhline(y=loading_eps0, color='g', linestyle='--', label=f'加载阈值 ({loading_eps0:.6f})')
            
            # 绘制卸载阶段阈值线（注意卸载阶段的变化量是负值）
            plt.axhline(y=-unloading_eps0, color='r', linestyle=':', label=f'卸载阈值 (-{unloading_eps0:.6f})')
        
        plt.xlabel('应变 (ε)')
        plt.ylabel('应变变化量 (Δε)')
        plt.title('应变变化量-应变曲线')
        plt.legend()
        plt.grid(True)
        
        # 设置y轴范围，使正负变化量更加对称且更合理
        # 计算实际的最大和最小变化量
        y_max = np.nanmax(loading_delta) if not np.isnan(np.nanmax(loading_delta)) else 0.0001
        y_min = np.nanmin(unloading_delta) if not np.isnan(np.nanmin(unloading_delta)) else -0.0001
        
        # 确保y轴范围合理且对称
        abs_max = max(abs(y_max), abs(y_min)) * 1.2
        plt.ylim(-abs_max, abs_max)
        
        plt.tight_layout()
        
        # 保存图表
        if save_path:
            plt.savefig(save_path)
            print(f"预测图表已保存: {save_path}")
        
        plt.show()

# 主函数
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='PINN混凝土本构模型预测')
    parser.add_argument('--model', type=str, default='d:/column/F2/checkpoints/pinn_model_epoch_final.pth',
                        help='模型路径')
    parser.add_argument('--data', type=str, default='d:/column/F2/data.xlsx',
                        help='数据文件路径，用于归一化和反归一化')
    parser.add_argument('--min_strain', type=float, default=0.0,
                        help='最小应变值')
    parser.add_argument('--max_strain', type=float, default=0.01,
                        help='最大应变值')
    parser.add_argument('--points', type=int, default=100,
                        help='预测点数')
    parser.add_argument('--unloading', action='store_true',
                        help='是否包含卸载阶段')
    parser.add_argument('--no-unloading', dest='unloading', action='store_false',
                        help='不包含卸载阶段')
    parser.set_defaults(unloading=True)
    args = parser.parse_args()
    
    # 加载数据处理器（用于归一化和反归一化）
    processor = DataProcessor(args.data)
    processor.load_data()
    processor.convert_to_stress_strain()
    processor.normalize_data()
    
    # 创建预测器
    predictor = Predictor(args.model, processor)
    
    # 绘制预测结果
    predictor.plot_predictions(
        args.min_strain, 
        args.max_strain, 
        args.points,
        save_path='d:/column/F2/prediction_results.png',
        include_unloading=args.unloading
    )
    
    # 打印提示信息
    if args.unloading:
        print("预测结果包含加载和卸载阶段，可以观察应变变化量（包括增加和减少）对模型的影响")
    else:
        print("预测结果仅包含加载阶段，应变变化量均为正值")