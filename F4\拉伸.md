# 混凝土拉伸损伤公式总结（吴建营模型）

> 根据论文内容，**拉伸损伤**（受拉损伤机制）独立于压缩损伤，需单独建模和拟合。以下是拉伸损伤相关公式的完整总结：

---

## 1. 拉伸损伤能释放率（\(Y^+\)）
\[
Y^+ = \sqrt{E_0 (\sigma^+ : A_0 : \sigma^+)} \quad \text{(式15)}
\]
- **物理意义**：基于拉伸应力分量 \(\sigma^+\) 的能量释放率
- **计算步骤**：
  1. 分解应力张量：\(\sigma^+ = P^+ : \sigma\)（式2）
  2. 计算双点积：\(\sigma^+ : A_0 : \sigma^+\)（\(A_0\) 为初始柔度张量）
  3. 取平方根乘以弹性模量 \(E_0\)

---

## 2. 拉伸损伤阈值演化（\(r^+\)）
### 准静态加载（应变率无关）
\[
r^+_{\text{new}} = \max\left(r^+_{\text{old}}, \, Y^+\right) \quad \text{(式30)}
\]
- **触发条件**：当 \(Y^+ \geq r^+\) 时更新阈值
- **特点**：阈值单调递增，反映不可逆损伤累积

### 动力加载（应变率相关）
\[
\dot{r}^+ = \mu^+ \left\langle \frac{Y^+}{r^+} - 1 \right\rangle^{a^+} \quad \text{(式17)}
\]
- **参数取值**：
  - \(\mu^+ = 2.1 \times 10^3 \, \text{N/(s·m)}\)（粘性系数）
  - \(a^+ = 5.5\)（非线性指数）
- **离散化更新**（后退欧拉法）：
  \[
  r^+_{n+1} = r^+_n + \Delta t \cdot \mu^+ \left\langle \frac{Y^+_n}{r^+_n} - 1 \right\rangle^{a^+}
  \]

---

## 3. 拉伸损伤变量（\(d^+\)）
\[
d^+ = 1 - \left( \frac{r_0^+}{r^+}(1 - A^+) + A^+ \right) \exp\left(B^+\left(1 - \frac{r^+}{r_0^+}\right)\right) \quad \text{(式18)}
\]
- **初始阈值**：\(r_0^+ = f_t\)（单轴抗拉强度）
- **模型参数**：
  - \(A^+\)：控制初始损伤演化速率（典型值 1.0）
  - \(B^+\)：控制损伤饱和趋势（典型值 0.13）

---

## 4. 拉伸损伤参数总结
| 参数       | 符号      | 物理意义               | 典型取值/确定方法       |
| ---------- | --------- | ---------------------- | ----------------------- |
| 初始阈值   | \(r_0^+\) | 线弹性极限强度         | \(f_t\)（实测抗拉强度） |
| 形状参数1  | \(A^+\)   | 初始损伤演化速率       | 1.0（初值，需优化）     |
| 形状参数2  | \(B^+\)   | 损伤饱和趋势           | 0.13（初值，需优化）    |
| 粘性系数   | \(\mu^+\) | 应变率相关阈值更新速率 | \(2.1 \times 10^3\)     |
| 非线性指数 | \(a^+\)   | 阈值演化的非线性程度   | 5.5                     |

---

## 5. 拉伸损伤曲线拟合流程
### 输入数据要求
- **单轴拉伸试验**的应力-应变曲线（含循环加卸载路径）
- **关键特征**：
  - 线弹性阶段（确定 \(r_0^+\)）
  - 拉伸软化段（控制 \(A^+\)）
  - 卸载刚度退化（控制 \(B^+\)）

### 拟合步骤
1. **初始化参数**：
   - \(r_0^+ = f_t\)（试验测得的峰值应力）
   - \(A^+ = 1.0, B^+ = 0.13\)（初值）
2. **遍历加载段**：
   - 计算当前 \(Y^+\)（式15）
   - 若 \(Y^+ \geq r^+\)，更新 \(r^+\)（式30 或 离散化式17）
   - 计算 \(d^+\)（式18）
3. **计算模型应力**：
   \[
   \sigma^{\text{model}} = (1 - d^+) \cdot E_0 \cdot \varepsilon
   \]
4. **优化参数**：
   - 最小化误差：\(\sum (\sigma^{\text{model}} - \sigma^{\text{test}})^2\)
   - 调整 \(A^+, B^+\) 使模型匹配：
     - 软化段下降速率（由 \(A^+\) 控制）
     - 卸载路径斜率（由 \(B^+\) 控制）

---

## 6. 注意事项
1. **卸载段处理**：
   - 卸载时 \(d^+\) 保持不变，刚度退化为 \((1 - d^+)E_0\)
2. **参数敏感性**：
   - \(A^+\) 增大 → 初始损伤演化加速
   - \(B^+\) 增大 → 损伤更快达到饱和
3. **应变率效应**：
   - 若试验含多应变率数据，需同时优化 \(\mu^+, a^+\)
   - 高应变率下 \(r^+\) 增长更快 → 损伤程度降低
