1

   Abaqus 2024                                  Date 11-Jul-2025   Time 10:03:35
   For use by Supplied by Team-SolidSQUAD under license from Dassault Systemes or its subsidiary.



                         The Abaqus Software is a product of:

                           Dassault Systemes SIMULIA Corp.
                           1301 Atwood Avenue, Suite 101W
                              Johnston, RI 02919, USA
 


                   The Abaqus Software is available only under license
                   from Dassault Systemes or its subsidiary and may be
                   used or reproduced only in accordance with the terms
                   of such license.
 
                          On machine while 
                          you are authorized to run
                          Abaqus/Explicit until 31-Dec-2055

                          Your site id is:  


 
                    For assistance or any other information you may
                    obtain contact information for your local office
                    from the world wide web at:

                      https://www.3ds.com/products-services/simulia/services-support/

 
                * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * 
                *                                                         * 
                *                   *****************                     * 
                *                   *  N O T I C E  *                     * 
                *                   *****************                     * 
                *                                                         * 
                *                                                         * 
                *                       Abaqus 2024                       * 
                *                                                         * 
                *       BUILD ID: 2023_09_21-20.55.25 RELr426 190762      * 
                *                                                         * 
                *                                                         * 
                *  Please make sure you are using                         * 
                *  release Abaqus 2024 manuals                            * 
                *  plus the notes accompanying this release.              * 
                *                                                         * 
                *                                                         * 
                *                                                         * 
                *                                                         * 
                * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * 


 



     PROCESSING PART, INSTANCE, AND ASSEMBLY INFORMATION
   *******************************************************


     END PROCESSING PART, INSTANCE, AND ASSEMBLY INFORMATION
   ***********************************************************




     OPTIONS BEING PROCESSED
   ***************************


  *Heading
          Single Element Uniaxial Tensile Test with VUMAT                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 
  *Element, type=C3D8R
  *Elset, elset=ASSEMBLY_COLUMN-1_E_COLUMN
  *material, name=CONCRETE_VUMAT
  *density
  *depvar
  *usermaterial, constants=10
  *solidsection, elset=ASSEMBLY_COLUMN-1_E_COLUMN, material=CONCRETE_VUMAT
  *solidsection, elset=ASSEMBLY_COLUMN-1_E_COLUMN, material=CONCRETE_VUMAT
  *boundary
  *solidsection, elset=ASSEMBLY_COLUMN-1_E_COLUMN, material=CONCRETE_VUMAT
  *elementoutput, elset=ASSEMBLY_COLUMN-1_E_COLUMN
  *output, field
  *output, history, frequency=1
  *Step, name=ApplyDisplacement, nlgeom=NO

 ***WARNING: LINEAR GEOMETRIC KINEMATICS OPTION WILL NOT BE SUPPORTED IN FUTURE 
             for Abaqus/Explicit ANALYSES.  LARGE DEFORMATIONS WILL BE ASSUMED.
  *Step, name=ApplyDisplacement, nlgeom=NO

 ***WARNING: LINEAR GEOMETRIC KINEMATICS OPTION WILL NOT BE SUPPORTED IN FUTURE 
             for Abaqus/Explicit ANALYSES.  LARGE DEFORMATIONS WILL BE ASSUMED.
  *dynamic, explicit
  *boundary
  *output, field
  *elementoutput, elset=ASSEMBLY_COLUMN-1_E_COLUMN
  *nodeoutput
  *output, history, frequency=1
  *nodeoutput, nset=ASSEMBLY_SET_TOP
  *nodeoutput, nset=ASSEMBLY_SET_BOTTOM
  *endstep
  *Step, name=ApplyDisplacement, nlgeom=NO

 ***WARNING: LINEAR GEOMETRIC KINEMATICS OPTION WILL NOT BE SUPPORTED IN FUTURE 
             for Abaqus/Explicit ANALYSES.  LARGE DEFORMATIONS WILL BE ASSUMED.
  *dynamic, explicit
  *boundary

 ***WARNING: THE OPTION *BOUNDARY,TYPE=DISPLACEMENT HAS BEEN USED; CHECK STATUS 
             FILE BETWEEN STEPS FOR WARNINGS ON ANY JUMPS PRESCRIBED ACROSS THE 
             STEPS IN DISPLACEMENT VALUES OF TRANSLATIONAL DOF. FOR ROTATIONAL 
             DOF MAKE SURE THAT THERE ARE NO SUCH JUMPS. ALL JUMPS IN 
             DISPLACEMENTS ACROSS STEPS ARE IGNORED
  *endstep



                            P R O B L E M   S I Z E


          NUMBER OF ELEMENTS IS                                     1
          NUMBER OF NODES IS                                        8
          NUMBER OF NODES DEFINED BY THE USER                       8
          TOTAL NUMBER OF VARIABLES IN THE MODEL                   24
          (DEGREES OF FREEDOM PLUS MAX NO. OF ANY LAGRANGE MULTIPLIER
           VARIABLES. INCLUDE *PRINT,SOLVE=YES TO GET THE ACTUAL NUMBER.)



                              END OF USER INPUT PROCESSING



     JOB TIME SUMMARY
       USER TIME (SEC)      =      0.0    
       SYSTEM TIME (SEC)    =     0.10    
       TOTAL CPU TIME (SEC) =     0.10    
       WALLCLOCK TIME (SEC) =            0
