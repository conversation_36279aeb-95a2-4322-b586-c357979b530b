#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
包含PINN模型的所有配置参数
"""

import torch
from dataclasses import dataclass
from typing import Dict, List, Optional

@dataclass
class ModelConfig:
    """
    模型结构配置
    """
    input_dim: int = 1
    hidden_dims: List[int] = None
    output_dim: int = 3
    activation: str = 'tanh'  # 'tanh', 'relu', 'swish'
    
    def __post_init__(self):
        if self.hidden_dims is None:
            self.hidden_dims = [64, 64, 64]

@dataclass
class MaterialConfig:
    """
    材料参数初始值配置
    """
    # 初始材料参数
    E0_init: float = 30000.0      # 初始弹性模量 (MPa)
    ft_init: float = 3.0          # 抗拉强度 (MPa)
    Gf_init: float = 0.1          # 断裂能 (N/mm)
    alpha_init: float = 0.99      # 最大损伤值
    beta_init: float = 1000.0     # 损伤演化参数
    lch_init: float = 100.0       # 特征长度 (mm)
    
    # 参数约束范围
    E0_bounds: tuple = (10000.0, 50000.0)
    ft_bounds: tuple = (1.0, 10.0)
    Gf_bounds: tuple = (0.01, 1.0)
    alpha_bounds: tuple = (0.8, 1.0)
    beta_bounds: tuple = (100.0, 5000.0)
    lch_bounds: tuple = (50.0, 200.0)

@dataclass
class TrainingConfig:
    """
    训练配置
    """
    # 基本训练参数
    num_epochs: int = 1000
    learning_rate: float = 0.001
    batch_size: int = 32
    
    # 优化器设置
    optimizer: str = 'adam'  # 'adam', 'sgd', 'rmsprop'
    weight_decay: float = 1e-5
    
    # 学习率调度
    use_scheduler: bool = True
    scheduler_type: str = 'step'  # 'step', 'cosine', 'exponential'
    step_size: int = 200
    gamma: float = 0.8
    
    # 早停设置
    use_early_stopping: bool = False
    patience: int = 100
    min_delta: float = 1e-6
    
    # 损失函数权重
    loss_weights: Dict[str, float] = None
    
    def __post_init__(self):
        if self.loss_weights is None:
            self.loss_weights = {
                'constitutive': 1.0,
                'damage_evolution': 0.5,
                'plastic_monotonic': 0.1,
                'initial_condition': 1.0,
                'damage_monotonic': 0.1,
                'stress_positive': 0.1,
                'energy_dissipation': 0.1
            }

@dataclass
class DataConfig:
    """
    数据处理配置
    """
    # 数据文件路径
    data_file: str = "d:/column/tension/tension.xlsx"
    
    # 数据预处理
    normalization_method: str = 'standard'  # 'standard', 'minmax', 'none'
    remove_outliers: bool = True
    outlier_threshold: float = 3.0  # 标准差倍数
    
    # 数据分割
    train_ratio: float = 0.8
    validation_ratio: float = 0.2
    random_seed: int = 42
    
    # 数据增强
    use_data_augmentation: bool = False
    noise_level: float = 0.01
    
    # 列名识别
    strain_keywords: List[str] = None
    stress_keywords: List[str] = None
    
    def __post_init__(self):
        if self.strain_keywords is None:
            self.strain_keywords = ['strain', 'epsilon', 'ε', '应变', '变形']
        if self.stress_keywords is None:
            self.stress_keywords = ['stress', 'sigma', 'σ', '应力', '力']

@dataclass
class OutputConfig:
    """
    输出配置
    """
    # 输出目录
    output_dir: str = "d:/column/tension/results"
    
    # 保存设置
    save_model: bool = True
    save_plots: bool = True
    save_data: bool = True
    save_parameters: bool = True
    
    # 图像设置
    figure_format: str = 'png'  # 'png', 'pdf', 'svg'
    figure_dpi: int = 300
    figure_size: tuple = (12, 8)
    
    # 日志设置
    log_level: str = 'INFO'  # 'DEBUG', 'INFO', 'WARNING', 'ERROR'
    log_interval: int = 100  # 每多少个epoch打印一次
    
    # 结果文件名
    model_filename: str = 'pinn_model.pth'
    parameters_filename: str = 'material_parameters.csv'
    predictions_filename: str = 'pinn_predictions.csv'
    training_history_filename: str = 'training_history.json'

@dataclass
class SystemConfig:
    """
    系统配置
    """
    # 设备设置
    device: str = 'auto'  # 'auto', 'cpu', 'cuda'
    
    # 随机种子
    random_seed: int = 42
    
    # 数值精度
    dtype: str = 'float32'  # 'float32', 'float64'
    
    # 并行设置
    num_workers: int = 0
    pin_memory: bool = True
    
    # 内存管理
    max_memory_usage: float = 0.8  # GPU内存使用上限

class Config:
    """
    主配置类，整合所有配置
    """
    
    def __init__(self):
        self.model = ModelConfig()
        self.material = MaterialConfig()
        self.training = TrainingConfig()
        self.data = DataConfig()
        self.output = OutputConfig()
        self.system = SystemConfig()
        
        # 设置设备
        self._setup_device()
        
        # 设置随机种子
        self._setup_random_seed()
    
    def _setup_device(self):
        """
        设置计算设备
        """
        if self.system.device == 'auto':
            if torch.cuda.is_available():
                self.system.device = 'cuda'
                print(f"使用GPU: {torch.cuda.get_device_name()}")
            else:
                self.system.device = 'cpu'
                print("使用CPU")
        else:
            print(f"使用指定设备: {self.system.device}")
    
    def _setup_random_seed(self):
        """
        设置随机种子以确保可重复性
        """
        import random
        import numpy as np
        
        random.seed(self.system.random_seed)
        np.random.seed(self.system.random_seed)
        torch.manual_seed(self.system.random_seed)
        
        if torch.cuda.is_available():
            torch.cuda.manual_seed(self.system.random_seed)
            torch.cuda.manual_seed_all(self.system.random_seed)
        
        # 确保确定性行为
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    
    def update_from_dict(self, config_dict: Dict):
        """
        从字典更新配置
        """
        for section, params in config_dict.items():
            if hasattr(self, section):
                section_config = getattr(self, section)
                for key, value in params.items():
                    if hasattr(section_config, key):
                        setattr(section_config, key, value)
                    else:
                        print(f"警告: 未知配置参数 {section}.{key}")
            else:
                print(f"警告: 未知配置节 {section}")
    
    def to_dict(self) -> Dict:
        """
        转换为字典格式
        """
        return {
            'model': self.model.__dict__,
            'material': self.material.__dict__,
            'training': self.training.__dict__,
            'data': self.data.__dict__,
            'output': self.output.__dict__,
            'system': self.system.__dict__
        }
    
    def save_config(self, filepath: str):
        """
        保存配置到文件
        """
        import json
        
        config_dict = self.to_dict()
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
        
        print(f"配置已保存到: {filepath}")
    
    def load_config(self, filepath: str):
        """
        从文件加载配置
        """
        import json
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            
            self.update_from_dict(config_dict)
            print(f"配置已从 {filepath} 加载")
            
        except FileNotFoundError:
            print(f"配置文件 {filepath} 不存在，使用默认配置")
        except Exception as e:
            print(f"加载配置文件失败: {e}，使用默认配置")
    
    def print_config(self):
        """
        打印当前配置
        """
        print("=" * 60)
        print("当前配置:")
        print("=" * 60)
        
        config_dict = self.to_dict()
        
        for section, params in config_dict.items():
            print(f"\n[{section.upper()}]")
            for key, value in params.items():
                print(f"  {key}: {value}")
        
        print("=" * 60)

# 预定义配置模板
class ConfigTemplates:
    """
    预定义的配置模板
    """
    
    @staticmethod
    def quick_test() -> Config:
        """
        快速测试配置（少量epoch，简单网络）
        """
        config = Config()
        config.model.hidden_dims = [32, 32]
        config.training.num_epochs = 100
        config.training.batch_size = 16
        return config
    
    @staticmethod
    def high_accuracy() -> Config:
        """
        高精度配置（更多epoch，更深网络）
        """
        config = Config()
        config.model.hidden_dims = [128, 128, 128, 64]
        config.training.num_epochs = 2000
        config.training.learning_rate = 0.0005
        config.training.use_early_stopping = True
        return config
    
    @staticmethod
    def robust_training() -> Config:
        """
        鲁棒训练配置（更强的正则化）
        """
        config = Config()
        config.training.weight_decay = 1e-4
        config.training.loss_weights = {
            'constitutive': 1.0,
            'damage_evolution': 1.0,
            'plastic_monotonic': 0.5,
            'initial_condition': 2.0,
            'damage_monotonic': 0.5,
            'stress_positive': 0.5,
            'energy_dissipation': 0.2
        }
        return config

# 默认配置实例
default_config = Config()

# 使用示例
if __name__ == "__main__":
    # 创建配置
    config = Config()
    
    # 打印配置
    config.print_config()
    
    # 保存配置
    config.save_config("d:/column/tension/config.json")
    
    # 测试模板
    print("\n测试快速配置模板:")
    quick_config = ConfigTemplates.quick_test()
    print(f"快速测试 - Epochs: {quick_config.training.num_epochs}, "
          f"Hidden dims: {quick_config.model.hidden_dims}")
    
    print("\n测试高精度配置模板:")
    high_acc_config = ConfigTemplates.high_accuracy()
    print(f"高精度 - Epochs: {high_acc_config.training.num_epochs}, "
          f"Hidden dims: {high_acc_config.model.hidden_dims}")
    
    print("\n配置模块测试完成!")