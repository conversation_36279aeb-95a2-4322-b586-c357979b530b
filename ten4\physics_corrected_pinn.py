import torch
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置字体
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

class PhysicsCorrectPINN(nn.Module):
    """
    基于文献的物理约束正确的PINN模型
    参考：混凝土弹塑性损伤本构模型研究 I & II（李杰，吴建营）
    """
    def __init__(self, input_size=1, hidden_size=64, num_layers=2, output_size=2):
        super(PhysicsCorrectPINN, self).__init__()
        
        # LSTM网络捕捉路径依赖性
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        
        # 输出层：只预测应力和损伤变量（塑性应变通过物理关系计算）
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_size, hidden_size//2),
            nn.Tanh(),
            nn.Linear(hidden_size//2, output_size)
        )
        
    def forward(self, strain_seq):
        lstm_out, _ = self.lstm(strain_seq)
        output = self.output_layer(lstm_out)
        
        # 网络输出
        stress_hat = output[:, :, 0:1]  # 应力预测
        damage_hat = torch.sigmoid(output[:, :, 1:2])  # 损伤变量 [0,1]
        
        return stress_hat, damage_hat

class PhysicsCorrectTrainer:
    """
    基于文献物理约束的训练器
    """
    def __init__(self, model, E0=30000.0, ft=3.0, device='cpu'):
        self.model = model.to(device)
        self.device = device
        
        # 固定物理参数（从文献获得的材料属性）
        self.E0 = E0  # 初始弹性模量
        self.ft = ft  # 单轴抗拉强度
        self.r0 = ft  # 初始损伤阈值
        
        # 可训练的损伤演化参数（文献公式39）
        self.A_plus = nn.Parameter(torch.tensor(0.7, device=device))
        self.B_plus = nn.Parameter(torch.tensor(5.0, device=device))
        
        # 可训练的塑性参数
        self.alpha_E = nn.Parameter(torch.tensor(0.1, device=device))  # 塑性硬化参数
        
        # 数据归一化参数
        self.strain_scale = 1.0
        self.stress_scale = 1.0
        
        # 损失权重
        self.lambda_data = 1.0       # 数据拟合
        self.lambda_physics = 0.5    # 物理约束
        self.lambda_monotonic = 0.1  # 单调性约束
        
    def normalize_data(self, strain, stress):
        """数据归一化"""
        self.strain_scale = np.max(np.abs(strain))
        self.stress_scale = np.max(np.abs(stress))
        
        strain_norm = strain / self.strain_scale
        stress_norm = stress / self.stress_scale
        
        return strain_norm, stress_norm
    
    def denormalize(self, strain_norm, stress_norm):
        """数据反归一化"""
        strain = strain_norm * self.strain_scale
        stress = stress_norm * self.stress_scale
        return strain, stress
    
    def compute_physics_constraints(self, strain_seq, stress_pred, damage_pred):
        """
        计算基于文献的物理约束
        参考文献的三步骤算法：弹性预测-塑性修正-损伤修正
        """
        batch_size, seq_len = strain_seq.shape[0], strain_seq.shape[1]
        device = strain_seq.device
        
        # 反归一化以进行物理计算
        strain_real = strain_seq.squeeze(-1) * self.strain_scale
        
        # 初始化物理状态变量
        r_max = torch.full((batch_size,), self.ft, device=device)  # 损伤阈值
        eps_p = torch.zeros((batch_size,), device=device)  # 塑性应变
        k_plus = torch.zeros((batch_size,), device=device)  # 硬化参数
        
        # 存储物理计算结果
        stress_physics = []
        damage_physics = []
        plastic_physics = []
        
        for i in range(seq_len):
            # === 步骤1：弹性预测 ===
            epsilon_e = strain_real[:, i] - eps_p  # 弹性应变
            sigma_trial = self.E0 * epsilon_e  # 试算应力
            
            # === 步骤2：塑性修正 ===
            # 检查屈服条件（简化的单轴拉伸屈服面）
            f_trial = torch.abs(sigma_trial) - (self.ft + self.alpha_E * k_plus)
            
            # 如果超过屈服面，进行塑性修正
            plastic_loading = f_trial > 0
            
            if torch.any(plastic_loading):
                # 塑性流动因子（回映算法求解）
                Delta_lambda = f_trial / (self.E0 + self.alpha_E)
                Delta_lambda = torch.clamp(Delta_lambda, min=0)
                
                # 更新塑性应变（关联流动法则）
                deps_p = Delta_lambda * torch.sign(sigma_trial)
                eps_p = eps_p + deps_p * plastic_loading.float()
                
                # 更新硬化参数
                k_plus = k_plus + Delta_lambda * plastic_loading.float()
                
                # 更新有效应力
                sigma_eff = sigma_trial - self.E0 * deps_p * plastic_loading.float()
            else:
                sigma_eff = sigma_trial
            
            # === 步骤3：损伤修正 ===
            # 计算损伤能释放率（文献公式5）
            Y = 0.5 * sigma_eff**2 / self.E0  # 单轴情况下的损伤能释放率
            Y = torch.clamp(Y, min=0)
            
            # 更新损伤阈值（历史最大值准则）
            r_max = torch.max(r_max, Y)
            
            # 计算损伤变量（文献公式39a）
            # d = 1 - (r0/r) * [(1-A+) + A+*exp(B+*(1-r/r0))]
            r_ratio = r_max / self.r0
            term1 = self.r0 / r_max * (1 - self.A_plus)
            term2 = self.A_plus * torch.exp(self.B_plus * (1 - r_ratio))
            d_physics = 1 - term1 - term2
            d_physics = torch.clamp(d_physics, 0, 0.99)
            
            # 计算名义应力（文献公式8）
            sigma_nominal = (1 - d_physics) * sigma_eff
            
            # 存储结果
            stress_physics.append(sigma_nominal / self.stress_scale)  # 归一化
            damage_physics.append(d_physics)
            plastic_physics.append(eps_p / self.strain_scale)  # 归一化
        
        stress_physics = torch.stack(stress_physics, dim=1)
        damage_physics = torch.stack(damage_physics, dim=1)
        plastic_physics = torch.stack(plastic_physics, dim=1)
        
        return stress_physics, damage_physics, plastic_physics
    
    def compute_monotonic_constraints(self, damage_pred):
        """
        损伤单调性约束：损伤只能增加，不能减少
        """
        seq_len = damage_pred.shape[1]
        monotonic_loss = 0.0
        
        for i in range(1, seq_len):
            # 损伤减少的惩罚
            damage_decrease = torch.relu(damage_pred[:, i-1, 0] - damage_pred[:, i, 0])
            monotonic_loss += torch.mean(damage_decrease)
        
        return monotonic_loss / (seq_len - 1)
    
    def train(self, strain_exp, stress_exp, num_epochs=2000, lr=0.001):
        """训练模型"""
        
        # 数据预处理
        strain_norm, stress_norm = self.normalize_data(strain_exp, stress_exp)
        
        # 转换为张量
        strain_tensor = torch.tensor(strain_norm, dtype=torch.float32, device=self.device)
        stress_tensor = torch.tensor(stress_norm, dtype=torch.float32, device=self.device)
        
        strain_tensor = strain_tensor.unsqueeze(0).unsqueeze(-1)  # (1, seq_len, 1)
        stress_tensor = stress_tensor.unsqueeze(0)  # (1, seq_len)
        
        # 优化器
        all_params = list(self.model.parameters()) + [self.A_plus, self.B_plus, self.alpha_E]
        optimizer = optim.Adam(all_params, lr=lr)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=300, factor=0.8)
        
        loss_history = []
        best_loss = float('inf')
        
        print("开始训练（基于文献物理约束）...")
        self.model.train()
        
        for epoch in range(num_epochs):
            optimizer.zero_grad()
            
            # 前向传播
            stress_pred, damage_pred = self.model(strain_tensor)
            
            # 数据拟合损失
            loss_data = torch.mean((stress_pred.squeeze(-1) - stress_tensor)**2)
            
            # 物理约束损失
            stress_physics, damage_physics, _ = self.compute_physics_constraints(
                strain_tensor, stress_pred, damage_pred
            )
            
            loss_stress_physics = torch.mean((stress_pred.squeeze(-1) - stress_physics.squeeze(-1))**2)
            loss_damage_physics = torch.mean((damage_pred.squeeze(-1) - damage_physics.squeeze(-1))**2)
            
            # 单调性约束
            loss_monotonic = self.compute_monotonic_constraints(damage_pred)
            
            # 总损失
            total_loss = (self.lambda_data * loss_data + 
                         self.lambda_physics * (loss_stress_physics + loss_damage_physics) +
                         self.lambda_monotonic * loss_monotonic)
            
            # 反向传播
            total_loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(all_params, max_norm=1.0)
            
            optimizer.step()
            scheduler.step(total_loss)
            
            # 约束参数范围
            with torch.no_grad():
                self.A_plus.data = torch.clamp(self.A_plus.data, 0.0, 1.0)
                self.B_plus.data = torch.clamp(self.B_plus.data, 0.1, 20.0)
                self.alpha_E.data = torch.clamp(self.alpha_E.data, 0.01, 1.0)
            
            loss_history.append(total_loss.item())
            
            if total_loss.item() < best_loss:
                best_loss = total_loss.item()
                torch.save(self.model.state_dict(), 'best_physics_model.pth')
            
            if epoch % 200 == 0:
                print(f"Epoch {epoch:4d}, Total Loss: {total_loss.item():.6f}")
                print(f"  Data: {loss_data.item():.6f}, Physics: {(loss_stress_physics + loss_damage_physics).item():.6f}")
                print(f"  Monotonic: {loss_monotonic.item():.6f}")
                print(f"  A+: {self.A_plus.item():.4f}, B+: {self.B_plus.item():.4f}, αE: {self.alpha_E.item():.4f}")
                print(f"  LR: {optimizer.param_groups[0]['lr']:.6f}")
        
        # 加载最佳模型
        self.model.load_state_dict(torch.load('best_physics_model.pth'))
        print("训练完成！")
        return loss_history
    
    def predict(self, strain_exp):
        """预测"""
        self.model.eval()
        with torch.no_grad():
            # 归一化
            strain_norm = strain_exp / self.strain_scale
            strain_tensor = torch.tensor(strain_norm, dtype=torch.float32, device=self.device)
            strain_tensor = strain_tensor.unsqueeze(0).unsqueeze(-1)
            
            # 网络预测
            stress_pred, damage_pred = self.model(strain_tensor)
            
            # 物理计算（用于对比）
            stress_physics, damage_physics, plastic_physics = self.compute_physics_constraints(
                strain_tensor, stress_pred, damage_pred
            )
            
            # 反归一化
            stress_pred = stress_pred.squeeze().cpu().numpy() * self.stress_scale
            damage_pred = damage_pred.squeeze().cpu().numpy()
            stress_physics = stress_physics.squeeze().cpu().numpy() * self.stress_scale
            damage_physics = damage_physics.squeeze().cpu().numpy()
            plastic_physics = plastic_physics.squeeze().cpu().numpy() * self.strain_scale
            
            return stress_pred, damage_pred, stress_physics, damage_physics, plastic_physics

def plot_physics_results(strain, stress_exp, stress_pred, damage_pred, stress_physics, damage_physics):
    """绘制结果对比"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 10))
    
    # 应力-应变对比
    axes[0,0].plot(strain, stress_exp, 'ro-', label='Experimental', markersize=3, alpha=0.7)
    axes[0,0].plot(strain, stress_pred, 'b-', label='PINN Prediction', linewidth=2)
    axes[0,0].plot(strain, stress_physics, 'g--', label='Physics Calculation', linewidth=1.5)
    axes[0,0].set_xlabel('Strain')
    axes[0,0].set_ylabel('Stress (MPa)')
    axes[0,0].set_title('Stress-Strain Comparison')
    axes[0,0].legend()
    axes[0,0].grid(True, alpha=0.3)
    
    # 损伤演化
    axes[0,1].plot(strain, damage_pred, 'b-', label='PINN Damage', linewidth=2)
    axes[0,1].plot(strain, damage_physics, 'g--', label='Physics Damage', linewidth=1.5)
    axes[0,1].set_xlabel('Strain')
    axes[0,1].set_ylabel('Damage Variable')
    axes[0,1].set_title('Damage Evolution')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)
    
    # 预测误差
    error = stress_exp - stress_pred
    axes[0,2].plot(strain, error, 'r-', linewidth=1.5)
    axes[0,2].axhline(y=0, color='k', linestyle='--', alpha=0.5)
    axes[0,2].set_xlabel('Strain')
    axes[0,2].set_ylabel('Error (MPa)')
    axes[0,2].set_title('Prediction Error')
    axes[0,2].grid(True, alpha=0.3)
    
    # 应力对比散点图
    axes[1,0].scatter(stress_exp, stress_pred, alpha=0.6, s=20, label='PINN vs Exp')
    axes[1,0].scatter(stress_exp, stress_physics, alpha=0.6, s=20, label='Physics vs Exp')
    min_val = min(stress_exp.min(), stress_pred.min())
    max_val = max(stress_exp.max(), stress_pred.max())
    axes[1,0].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
    axes[1,0].set_xlabel('Experimental Stress (MPa)')
    axes[1,0].set_ylabel('Predicted Stress (MPa)')
    axes[1,0].set_title('Stress Correlation')
    axes[1,0].legend()
    axes[1,0].grid(True, alpha=0.3)
    
    # 损伤对比
    axes[1,1].scatter(damage_physics, damage_pred, alpha=0.6, s=20)
    axes[1,1].plot([0, 1], [0, 1], 'r--', linewidth=2)
    axes[1,1].set_xlabel('Physics Damage')
    axes[1,1].set_ylabel('PINN Damage')
    axes[1,1].set_title('Damage Correlation')
    axes[1,1].grid(True, alpha=0.3)
    
    # 残差分析
    residual = (stress_exp - stress_pred) / stress_exp * 100
    axes[1,2].hist(residual, bins=20, alpha=0.7, edgecolor='black')
    axes[1,2].axvline(x=0, color='r', linestyle='--', linewidth=2)
    axes[1,2].set_xlabel('Relative Error (%)')
    axes[1,2].set_ylabel('Frequency')
    axes[1,2].set_title('Error Distribution')
    axes[1,2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('physics_corrected_results.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Device: {device}")
    
    # 加载数据
    try:
        df = pd.read_excel('tension.xlsx')
        strain_exp = df['strain'].values
        stress_exp = df['stress'].values
        print(f"Data loaded: {len(strain_exp)} points")
        print(f"Strain range: [{strain_exp.min():.6f}, {strain_exp.max():.6f}]")
        print(f"Stress range: [{stress_exp.min():.2f}, {stress_exp.max():.2f}] MPa")
    except Exception as e:
        print(f"Error loading data: {e}")
        return
    
    # 创建物理约束正确的模型
    model = PhysicsCorrectPINN(input_size=1, hidden_size=64, num_layers=2, output_size=2)
    trainer = PhysicsCorrectTrainer(model, E0=30000.0, ft=3.0, device=device)
    
    # 训练
    loss_history = trainer.train(strain_exp, stress_exp, num_epochs=2000, lr=0.001)
    
    # 预测
    print("\n开始预测...")
    stress_pred, damage_pred, stress_physics, damage_physics, plastic_physics = trainer.predict(strain_exp)
    
    # 性能评估
    mse = np.mean((stress_exp - stress_pred)**2)
    mae = np.mean(np.abs(stress_exp - stress_pred))
    r2 = 1 - np.sum((stress_exp - stress_pred)**2) / np.sum((stress_exp - np.mean(stress_exp))**2)
    
    # 物理一致性评估
    physics_mse = np.mean((stress_pred - stress_physics)**2)
    damage_consistency = np.mean(np.abs(damage_pred - damage_physics))
    
    print(f"\n=== 性能评估 ===")
    print(f"实验数据拟合:")
    print(f"  MSE: {mse:.4f}")
    print(f"  MAE: {mae:.4f}")
    print(f"  R²: {r2:.4f}")
    print(f"\n物理一致性:")
    print(f"  应力物理一致性MSE: {physics_mse:.4f}")
    print(f"  损伤一致性MAE: {damage_consistency:.4f}")
    print(f"\n识别的物理参数:")
    print(f"  A+ = {trainer.A_plus.item():.4f}")
    print(f"  B+ = {trainer.B_plus.item():.4f}")
    print(f"  αE = {trainer.alpha_E.item():.4f}")
    
    # 绘制结果
    plot_physics_results(strain_exp, stress_exp, stress_pred, damage_pred, 
                        stress_physics, damage_physics)
    
    # 保存结果
    results = np.column_stack((strain_exp, stress_exp, stress_pred, damage_pred, 
                              stress_physics, damage_physics, plastic_physics))
    np.savetxt('physics_corrected_results.txt', results, 
               header='strain stress_exp stress_pred damage_pred stress_physics damage_physics plastic_physics', 
               fmt='%.8f')
    
    # 保存模型和参数
    torch.save({
        'model_state_dict': model.state_dict(),
        'A_plus': trainer.A_plus.item(),
        'B_plus': trainer.B_plus.item(),
        'alpha_E': trainer.alpha_E.item(),
        'E0': trainer.E0,
        'ft': trainer.ft,
        'strain_scale': trainer.strain_scale,
        'stress_scale': trainer.stress_scale
    }, 'physics_corrected_pinn_model.pth')
    
    print("\n模型和结果已保存!")

if __name__ == "__main__":
    main() 