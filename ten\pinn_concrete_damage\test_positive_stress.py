#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试修复后的模型是否能产生正应力值
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from models.pinn_model import ConcreteDamagePINN

def test_positive_stress():
    """
    测试修复后的模型是否产生正应力值
    """
    print("=== 测试修复后的应力计算 ===")
    
    # 创建模型实例
    model = ConcreteDamagePINN()
    model.eval()
    
    # 创建测试应变数据（正值，模拟拉伸）
    strain_values = np.linspace(0.0001, 0.01, 50)  # 从0.01%到1%应变
    
    # 转换为张量
    strain_tensor = torch.tensor(strain_values, dtype=torch.float32).unsqueeze(0).unsqueeze(-1)
    
    print(f"输入应变范围: {strain_values.min():.6f} 到 {strain_values.max():.6f}")
    
    # 重置模型状态
    model.reset_state(batch_size=1)
    
    # 逐步预测应力
    stress_predictions = []
    damage_predictions = []
    plastic_strain_predictions = []
    
    for i in range(len(strain_values)):
        # 创建序列输入（使用滑动窗口）
        if i == 0:
            strain_seq = strain_tensor[:, :1, :]
        elif i == 1:
            strain_seq = strain_tensor[:, :2, :]
        else:
            strain_seq = strain_tensor[:, i-2:i+1, :]
        
        # 前向传播
        with torch.no_grad():
            stress_pred, damage_pred, plastic_strain_pred = model.forward(strain_seq, return_components=True)
            
            stress_predictions.append(stress_pred.item())
            damage_predictions.append(damage_pred.item())
            plastic_strain_predictions.append(plastic_strain_pred.item())
    
    # 转换为numpy数组
    stress_predictions = np.array(stress_predictions)
    damage_predictions = np.array(damage_predictions)
    plastic_strain_predictions = np.array(plastic_strain_predictions)
    
    # 检查结果
    print(f"\n=== 预测结果分析 ===")
    print(f"应力范围: {stress_predictions.min():.6f} 到 {stress_predictions.max():.6f} MPa")
    print(f"负应力数量: {np.sum(stress_predictions < 0)}")
    print(f"损伤变量范围: {damage_predictions.min():.6f} 到 {damage_predictions.max():.6f}")
    print(f"塑性应变范围: {plastic_strain_predictions.min():.6f} 到 {plastic_strain_predictions.max():.6f}")
    
    # 检查物理合理性
    print(f"\n=== 物理合理性检查 ===")
    print(f"所有应力为正值: {np.all(stress_predictions >= 0)}")
    print(f"损伤变量单调递增: {np.all(np.diff(damage_predictions) >= -1e-6)}")
    print(f"塑性应变为正值: {np.all(plastic_strain_predictions >= 0)}")
    
    # 绘制结果
    plt.figure(figsize=(15, 10))
    
    # 应力-应变曲线
    plt.subplot(2, 3, 1)
    plt.plot(strain_values, stress_predictions, 'b-', linewidth=2, label='预测应力')
    plt.xlabel('应变')
    plt.ylabel('应力 (MPa)')
    plt.title('应力-应变曲线')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 损伤演化
    plt.subplot(2, 3, 2)
    plt.plot(strain_values, damage_predictions, 'r-', linewidth=2, label='损伤变量')
    plt.xlabel('应变')
    plt.ylabel('损伤变量 d')
    plt.title('损伤演化')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 塑性应变
    plt.subplot(2, 3, 3)
    plt.plot(strain_values, plastic_strain_predictions, 'g-', linewidth=2, label='塑性应变')
    plt.xlabel('应变')
    plt.ylabel('塑性应变')
    plt.title('塑性应变演化')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 应力分布直方图
    plt.subplot(2, 3, 4)
    plt.hist(stress_predictions, bins=20, alpha=0.7, color='blue', edgecolor='black')
    plt.xlabel('应力 (MPa)')
    plt.ylabel('频次')
    plt.title('应力分布')
    plt.grid(True, alpha=0.3)
    
    # 损伤-应力关系
    plt.subplot(2, 3, 5)
    plt.scatter(damage_predictions, stress_predictions, alpha=0.6, c=strain_values, cmap='viridis')
    plt.colorbar(label='应变')
    plt.xlabel('损伤变量 d')
    plt.ylabel('应力 (MPa)')
    plt.title('损伤-应力关系')
    plt.grid(True, alpha=0.3)
    
    # 有效应变计算
    effective_strain = strain_values - plastic_strain_predictions
    plt.subplot(2, 3, 6)
    plt.plot(strain_values, effective_strain, 'purple', linewidth=2, label='有效应变')
    plt.plot(strain_values, strain_values, 'k--', alpha=0.5, label='总应变')
    plt.xlabel('总应变')
    plt.ylabel('有效应变')
    plt.title('有效应变 vs 总应变')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    plt.tight_layout()
    plt.savefig('test_positive_stress_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 总结
    print(f"\n=== 测试总结 ===")
    if np.all(stress_predictions >= 0):
        print("✅ 成功：所有预测应力均为正值")
    else:
        print("❌ 失败：仍存在负应力值")
        
    if np.all(damage_predictions >= 0) and np.all(damage_predictions <= 1):
        print("✅ 成功：损伤变量在合理范围内 [0,1]")
    else:
        print("❌ 失败：损伤变量超出合理范围")
        
    if np.all(plastic_strain_predictions >= 0):
        print("✅ 成功：塑性应变均为正值")
    else:
        print("❌ 失败：存在负塑性应变")
    
    return stress_predictions, damage_predictions, plastic_strain_predictions

if __name__ == "__main__":
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 运行测试
    stress_pred, damage_pred, plastic_pred = test_positive_stress()