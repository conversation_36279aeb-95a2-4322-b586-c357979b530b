      SUBROUTINE VUMAT(
     1                   NBLOCK, NDIR, NSHR, NSTATEV, NFIELDV, NPROPS,
     2                   PROPS, TEMP, DTIME, STRAN, DSTRAN, TIME,
     3                   STRESS, STATEV, DDSDDE, CELENT)
C
C     一维混凝土损伤本构模型 - 专门优化版本
C     
      INCLUDE 'vaba_param.inc'
C
C     参数定义
      PARAMETER (ZERO=0.D0, ONE=1.D0, TWO=2.D0, THREE=3.D0)
      PARAMETER (TOLER=1.D-6, MAXDAM=0.99D0)
C
C     数组维数声明
      DIMENSION PROPS(NPROPS), STRESS(NBLOCK, 1)
      DIMENSION STATEV(NBLOCK, NSTATEV), STRAN(NBLOCK, 1)
      DIMENSION DSTRAN(NBLOCK, 1)
      DIMENSION DDSDDE(NBLOCK, 1, 1)
C
C     局部变量声明
      REAL*8 E0, FT, FC, A_PLUS, B_PLUS, XI_PLUS
      REAL*8 A_MINUS, B_MINUS, XI_MINUS
      REAL*8 DAMAGE_PLUS_OLD, DAMAGE_MINUS_OLD
      REAL*8 R_MAX_PLUS_OLD, R_MAX_MINUS_OLD, EP_OLD
      REAL*8 DAMAGE_PLUS_NEW, DAMAGE_MINUS_NEW
      REAL*8 R_MAX_PLUS_NEW, R_MAX_MINUS_NEW, EP_NEW
      REAL*8 STRAIN_11, DELTA_STRAIN, ELASTIC_STRAIN
      REAL*8 Y_PLUS, Y_MINUS, RATIO, EXP_TERM
      REAL*8 DAMAGE_EFF, STRESS_11, DELTA_EP
      REAL*8 E_EFFECTIVE
C
C     开始计算循环
      DO K = 1, NBLOCK
C
C       1. 读取材料参数 (9个)
         E0 = PROPS(1)
         FT = PROPS(2)
         FC = PROPS(3)
         A_PLUS = PROPS(4)
         B_PLUS = PROPS(5)
         XI_PLUS = PROPS(6)
         A_MINUS = PROPS(7)
         B_MINUS = PROPS(8)
         XI_MINUS = PROPS(9)
C
C       2. 读取状态变量 (保持二维格式避免越界)
         DAMAGE_PLUS_OLD = STATEV(K, 1)
         DAMAGE_MINUS_OLD = STATEV(K, 2)
         R_MAX_PLUS_OLD = STATEV(K, 3)
         R_MAX_MINUS_OLD = STATEV(K, 4)
         EP_OLD = STATEV(K, 5)
C
C       初始化状态变量 (确保历史变量的最小值)
         IF (R_MAX_PLUS_OLD .LT. FT) R_MAX_PLUS_OLD = FT
         IF (R_MAX_MINUS_OLD .LT. FC) R_MAX_MINUS_OLD = FC
C
C       3. 计算当前总应变 (单轴，第1方向)
         STRAIN_11 = STRAN(K, 1)
         DELTA_STRAIN = DSTRAN(K, 1)
C
C       4. 计算试验弹性应变和损伤驱动力
         ELASTIC_STRAIN = STRAIN_11 - EP_OLD
         Y_PLUS = E0 * DMAX1(ZERO, ELASTIC_STRAIN)
         Y_MINUS = E0 * DMAX1(ZERO, -ELASTIC_STRAIN)
C
C       5. 更新受拉损伤
         DAMAGE_PLUS_NEW = DAMAGE_PLUS_OLD
         R_MAX_PLUS_NEW = R_MAX_PLUS_OLD
         IF (Y_PLUS .GT. R_MAX_PLUS_OLD + TOLER) THEN
            R_MAX_PLUS_NEW = Y_PLUS
            RATIO = R_MAX_PLUS_NEW / FT
            EXP_TERM = DEXP(B_PLUS * (ONE - RATIO))
            DAMAGE_PLUS_NEW = ONE - (ONE/RATIO) * 
     &                       ((ONE - A_PLUS) + A_PLUS * EXP_TERM)
            IF (DAMAGE_PLUS_NEW .LT. ZERO) DAMAGE_PLUS_NEW = ZERO
            IF (DAMAGE_PLUS_NEW .GT. MAXDAM) DAMAGE_PLUS_NEW = MAXDAM
         END IF
C
C       6. 更新受压损伤
         DAMAGE_MINUS_NEW = DAMAGE_MINUS_OLD
         R_MAX_MINUS_NEW = R_MAX_MINUS_OLD
         IF (Y_MINUS .GT. R_MAX_MINUS_OLD + TOLER) THEN
            R_MAX_MINUS_NEW = Y_MINUS
            RATIO = R_MAX_MINUS_NEW / FC
            EXP_TERM = DEXP(B_MINUS * (ONE - RATIO))
            DAMAGE_MINUS_NEW = ONE - (ONE/RATIO) * 
     &                        ((ONE - A_MINUS) + A_MINUS * EXP_TERM)
            IF (DAMAGE_MINUS_NEW .LT. ZERO) DAMAGE_MINUS_NEW = ZERO
            IF (DAMAGE_MINUS_NEW .GT. MAXDAM) DAMAGE_MINUS_NEW = MAXDAM
         END IF
C
C       7. 更新塑性应变
         DELTA_EP = ZERO
         IF (DELTA_STRAIN .GT. TOLER) THEN
            DELTA_EP = XI_PLUS * DELTA_STRAIN
         ELSE IF (DELTA_STRAIN .LT. -TOLER) THEN
            DELTA_EP = XI_MINUS * DELTA_STRAIN
         END IF
         EP_NEW = EP_OLD + DELTA_EP
C
C       8. 重新计算弹性应变和应力
         ELASTIC_STRAIN = STRAIN_11 - EP_NEW
         IF (ELASTIC_STRAIN .GE. ZERO) THEN
            DAMAGE_EFF = DAMAGE_PLUS_NEW
         ELSE
            DAMAGE_EFF = DAMAGE_MINUS_NEW
         END IF
         STRESS_11 = (ONE - DAMAGE_EFF) * E0 * ELASTIC_STRAIN
C
C       9. 设置应力分量 (一维问题：只有单轴应力)
         STRESS(K, 1) = STRESS_11
         STRESS(K, 2) = ZERO
         STRESS(K, 3) = ZERO
C
C       10. 计算有效弹性模量和刚度矩阵
C           对于初始时间步，必须提供纯弹性响应
         IF (TIME(1) .LE. 1.0D-12) THEN
C              初始时间步：纯弹性状态
            E_EFFECTIVE = E0
            STRESS_11 = E0 * ELASTIC_STRAIN
            STRESS(K, 1) = STRESS_11
         ELSE
C              正常时间步：考虑损伤
            E_EFFECTIVE = (ONE - DAMAGE_EFF) * E0
         END IF
C
C       11. 设置完整的弹性刚度矩阵 (3D各向同性)
C           Abaqus/Explicit需要完整的刚度矩阵以计算体积模量
         DDSDDE(K, 1, 1) = E_EFFECTIVE
C
C       13. 更新状态变量
         STATEV(K, 1) = DAMAGE_PLUS_NEW
         STATEV(K, 2) = DAMAGE_MINUS_NEW
         STATEV(K, 3) = R_MAX_PLUS_NEW
         STATEV(K, 4) = R_MAX_MINUS_NEW
         STATEV(K, 5) = EP_NEW
C
      END DO
C
      RETURN
      END 