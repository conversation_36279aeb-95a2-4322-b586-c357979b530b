# -*- coding: utf-8 -*-
"""
项目测试脚本

测试PINN项目的各个组件是否正常工作
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
from font_config import configure_chinese_font

def test_dependencies():
    """
    测试依赖包
    """
    print("1. 测试依赖包...")
    
    required_packages = {
        'torch': 'PyTorch深度学习框架',
        'numpy': '数值计算库',
        'pandas': '数据处理库',
        'matplotlib': '绘图库',
        'openpyxl': 'Excel文件处理库'
    }
    
    success = True
    
    for package, description in required_packages.items():
        try:
            module = __import__(package)
            version = getattr(module, '__version__', '未知版本')
            print(f"  [OK] {package} ({description}): {version}")
        except ImportError:
            print(f"  [FAIL] {package} ({description}): 未安装")
            success = False
    
    return success

def test_font_config():
    """
    测试字体配置
    """
    print("\n2. 测试字体配置...")
    
    try:
        configure_chinese_font()
        print("  [OK] 字体配置成功")
        return True
    except Exception as e:
        print(f"  [FAIL] 字体配置失败: {e}")
        return False

def test_data_processor():
    """
    测试数据处理模块
    """
    print("\n3. 测试数据处理模块...")
    
    try:
        from data_processor import DataProcessor
        
        # 创建测试数据
        test_data = create_test_data()
        test_file = 'test_data.xlsx'
        test_data.to_excel(test_file, index=False)
        
        # 测试数据处理器
        processor = DataProcessor(test_file)
        
        if processor.load_data():
            print("  [OK] 数据加载成功")
            
            if processor.preprocess_data():
                print("  [OK] 数据预处理成功")
                
                training_data = processor.get_training_data()
                if training_data:
                    print("  [OK] 训练数据获取成功")
                    print(f"    - 数据点数: {len(training_data['strain'])}")
                    print(f"    - 应变范围: {training_data['strain_min']:.6f} ~ {training_data['strain_max']:.6f}")
                    print(f"    - 应力范围: {training_data['stress_min']:.2f} ~ {training_data['stress_max']:.2f}")
                    
                    # 清理测试文件
                    os.remove(test_file)
                    return True
        
        print("  [FAIL] 数据处理失败")
        if os.path.exists(test_file):
            os.remove(test_file)
        return False
        
    except Exception as e:
        print(f"  [FAIL] 数据处理模块测试失败: {e}")
        return False

def test_pinn_model():
    """
    测试PINN模型
    """
    print("\n4. 测试PINN模型...")
    
    try:
        from pinn_model import ConstitutivePINN, PINNTrainer
        
        # 创建模型
        model = ConstitutivePINN(hidden_size=10, num_layers=2)
        print("  [OK] 模型创建成功")
        
        # 测试前向传播
        test_strain = torch.tensor([[0.001], [0.002], [0.003]], dtype=torch.float32)
        stress_pred, damage_pred = model(test_strain)
        
        if stress_pred.shape == (3, 1) and damage_pred.shape == (3, 1):
            print("  [OK] 前向传播成功")
            print(f"    - 应力预测形状: {stress_pred.shape}")
            print(f"    - 损伤预测形状: {damage_pred.shape}")
        else:
            print("  [FAIL] 前向传播输出形状错误")
            return False
        
        # 测试物理约束
        physics_loss, components = model.physics_loss(test_strain, stress_pred, damage_pred)
        print("  [OK] 物理约束计算成功")
        print(f"    - 物理损失: {physics_loss.item():.6f}")
        
        # 测试训练器
        trainer = PINNTrainer(model, learning_rate=0.01)
        
        # 创建简单测试数据
        strain_data = torch.linspace(0, 0.01, 50).reshape(-1, 1)
        stress_data = 30000 * strain_data * (1 - 0.5 * torch.clamp(strain_data - 0.001, min=0) / 0.009)
        
        # 简短训练测试
        total_loss, data_loss, physics_loss, _ = trainer.train_step(strain_data, stress_data)
        print("  [OK] 训练步骤成功")
        print(f"    - 总损失: {total_loss:.6f}")
        print(f"    - 数据损失: {data_loss:.6f}")
        print(f"    - 物理损失: {physics_loss:.6f}")
        
        return True
        
    except Exception as e:
        print(f"  [FAIL] PINN模型测试失败: {e}")
        return False

def test_integration():
    """
    测试整体集成
    """
    print("\n5. 测试整体集成...")
    
    try:
        from data_processor import DataProcessor
        from pinn_model import ConstitutivePINN, PINNTrainer
        
        # 创建测试数据
        test_data = create_test_data()
        test_file = 'integration_test_data.xlsx'
        test_data.to_excel(test_file, index=False)
        
        # 数据处理
        processor = DataProcessor(test_file)
        processor.load_data()
        processor.preprocess_data()
        training_data = processor.get_training_data()
        
        # 转换为张量
        strain_tensor = torch.tensor(training_data['strain_normalized'], dtype=torch.float32).reshape(-1, 1)
        stress_tensor = torch.tensor(training_data['stress_normalized'], dtype=torch.float32).reshape(-1, 1)
        
        # 创建和训练模型
        model = ConstitutivePINN(hidden_size=10, num_layers=2)
        trainer = PINNTrainer(model, learning_rate=0.01)
        
        # 短期训练测试
        trainer.train(
            strain_tensor, 
            stress_tensor, 
            epochs=10,
            print_interval=5
        )
        
        print("  [OK] 整体集成测试成功")
        
        # 清理测试文件
        os.remove(test_file)
        return True
        
    except Exception as e:
        print(f"  [FAIL] 整体集成测试失败: {e}")
        if os.path.exists('integration_test_data.xlsx'):
            os.remove('integration_test_data.xlsx')
        return False

def create_test_data():
    """
    创建测试数据
    """
    # 生成模拟的循环加载数据
    strain_values = []
    stress_values = []
    
    # 参数
    E = 30000  # 弹性模量 (MPa)
    max_strain = 0.008
    num_cycles = 3
    points_per_cycle = 100
    
    for cycle in range(num_cycles):
        # 加载段
        strain_loading = np.linspace(0, max_strain, points_per_cycle // 2)
        # 简单的非线性关系
        stress_loading = E * strain_loading * (1 - 0.3 * strain_loading / max_strain)
        
        # 卸载段
        strain_unloading = np.linspace(max_strain, 0, points_per_cycle // 2)
        stress_unloading = E * strain_unloading * (1 - 0.3 * strain_unloading / max_strain) * 0.8
        
        strain_values.extend(strain_loading)
        stress_values.extend(stress_loading)
        strain_values.extend(strain_unloading)
        stress_values.extend(stress_unloading)
    
    # 添加一些噪声
    strain_values = np.array(strain_values)
    stress_values = np.array(stress_values) + np.random.normal(0, 1, len(stress_values))
    
    # 创建DataFrame
    data = pd.DataFrame({
        '应变': strain_values,
        '应力': stress_values
    })
    
    return data

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("PINN混凝土损伤本构建模项目 - 组件测试")
    print("=" * 60)
    
    tests = [
        test_dependencies,
        test_font_config,
        test_data_processor,
        test_pinn_model,
        test_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("[SUCCESS] 所有测试通过! 项目组件工作正常。")
        print("\n可以开始使用项目:")
        print("  - 运行 'python main.py --mode all' 进行完整流程")
        print("  - 运行 'python train.py' 进行模型训练")
        print("  - 查看 README.md 获取详细使用说明")
    else:
        print(f"[ERROR] {total - passed} 个测试失败。请检查错误信息并修复问题。")
        print("\n常见解决方案:")
        print("  - 安装缺失的依赖包: pip install -r requirements.txt")
        print("  - 检查Python版本 (推荐3.7+)")
        print("  - 确保有足够的内存和计算资源")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)