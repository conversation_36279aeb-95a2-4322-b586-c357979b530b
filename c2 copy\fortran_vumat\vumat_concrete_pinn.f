C=====================================================================
C     VUMAT for Concrete Elastoplastic Damage Model
C     Based on PINN-identified parameters
C     Enhanced version with PINN interface
C=====================================================================
      SUBROUTINE VUMAT(
C     Read only variables -
     1  NBLOCK, NDIR, NSHR, NSTATEV, NFIELDV, NPROPS, LANNEAL,
     2  STEPTIME, TOTALTIME, DT, CMNAME, COORDMP, CHARLENGTH,
     3  PROPS, DENSITY, STRAININC, RELSPININC,
     4  TEMPOLD, STRETCHOLD, DEFGRADOLD, FIELDOLD,
     5  STRESSOLD, STATEOLD, ENERINTERNOLD, ENERINELASOLD,
     6  TEMPNEW, STRETCHNEW, DEFGRADNEW, FIELDNEW,
C     Write only variables -
     7  STRESSNEW, STATENEW, ENERINTERNNEW, ENERINELASNEW )
C
      INCLUDE 'VABA_PARAM.INC'
C
      DIMENSION PROPS(NPROPS), DENSITY(NBLOCK), COORDMP(NBLOCK,*),
     1  CHARLENGTH(NBLOCK), STRAININC(NBLOCK,NDIR+NSHR),
     2  RELSPININC(NBLOCK,NSHR), TEMPOLD(NBLOCK),
     3  STRETCHOLD(NBLOCK,NDIR+NSHR),
     4  DEFGRADOLD(NBLOCK,NDIR+NSHR+NSHR),
     5  FIELDOLD(NBLOCK,NFIELDV), STRESSOLD(NBLOCK,NDIR+NSHR),
     6  STATEOLD(NBLOCK,NSTATEV), ENERINTERNOLD(NBLOCK),
     7  ENERINELASOLD(NBLOCK), TEMPNEW(NBLOCK),
     8  STRETCHNEW(NBLOCK,NDIR+NSHR),
     9  DEFGRADNEW(NBLOCK,NDIR+NSHR+NSHR),
     1  FIELDNEW(NBLOCK,NFIELDV), STRESSNEW(NBLOCK,NDIR+NSHR),
     2  STATENEW(NBLOCK,NSTATEV), ENERINTERNNEW(NBLOCK),
     3  ENERINELASNEW(NBLOCK)
C
      CHARACTER*80 CMNAME
C
C     Include material parameters
      INCLUDE 'material_parameters.inc'
C
C     Local variables and parameters
      PARAMETER (ONE=1.D0, TWO=2.D0, THREE=3.D0)
      PARAMETER (HALF=0.5D0, THIRD=1.D0/3.D0)
      PARAMETER (D_MAX=0.99D0)
      PARAMETER (TOL=1.D-10)
C
C     Declare all real variables explicitly
      REAL*8 E0, FT, FC, A_PLUS, B_PLUS, XI_PLUS
      REAL*8 A_MINUS, B_MINUS, XI_MINUS
      REAL*8 EP_OLD, D_PLUS_OLD, D_MINUS_OLD
      REAL*8 R_MAX_PLUS_OLD, R_MAX_MINUS_OLD
      REAL*8 D_PLUS_NEW, D_MINUS_NEW, EP_NEW, R_MAX_PLUS_NEW, R_MAX_MINUS_NEW
      REAL*8 STRAIN_INC, STRAIN_TOTAL, DELTA_EP
      REAL*8 STRAIN_ELASTIC, Y_PLUS, Y_MINUS
      REAL*8 RATIO, EXP_TERM, TEMP_CALC
      REAL*8 D_EFFECTIVE, STRESS_TRIAL
      REAL*8 T_INPUT, SIGMA_PINN, DAMAGE_PINN, XI_PINN
      REAL*8 TIME_STEP_NORMALIZED
C
C     Flag to use direct PINN prediction (1) or physics-based model (0)
      INTEGER USE_DIRECT_PINN
      PARAMETER (USE_DIRECT_PINN = 0)
C
C     Declare integer variables explicitly to override implicit
      INTEGER K, I
C
C     Extract material parameters from PROPS or use PINN-identified values
C     PROPS(1) = E0 (Initial elastic modulus)
C     PROPS(2) = f_t (Tensile strength)
C     PROPS(3) = f_c (Compressive strength)
C     PROPS(4) = A_plus (Tensile damage parameter A)
C     PROPS(5) = B_plus (Tensile damage parameter B)
C     PROPS(6) = xi_plus (Tensile plastic parameter)
C     PROPS(7) = A_minus (Compressive damage parameter A)
C     PROPS(8) = B_minus (Compressive damage parameter B)
C     PROPS(9) = xi_minus (Compressive plastic parameter)
C
      IF (NPROPS .GE. 9) THEN
        E0 = PROPS(1)
        FT = PROPS(2)
        FC = PROPS(3)
        A_PLUS = PROPS(4)
        B_PLUS = PROPS(5)
        XI_PLUS = PROPS(6)
        A_MINUS = PROPS(7)
        B_MINUS = PROPS(8)
        XI_MINUS = PROPS(9)
      ELSE
C       Use default values from PINN identification
        E0 = E0_INIT
        FT = FT_INIT
        FC = FC_INIT
        A_PLUS = A_PLUS
        B_PLUS = B_PLUS
        XI_PLUS = XI_PLUS
        A_MINUS = A_MINUS
        B_MINUS = B_MINUS
        XI_MINUS = XI_MINUS
      ENDIF
C
C     State variables
C     STATEV(1) = Accumulated plastic strain
C     STATEV(2) = d_plus (Tensile damage)
C     STATEV(3) = d_minus (Compressive damage)
C     STATEV(4) = r_max_plus (Maximum tensile damage force)
C     STATEV(5) = r_max_minus (Maximum compressive damage force)
C     STATEV(6) = Time step counter (for PINN input)
C
C     Loop over all material points in the block
      DO 100 K = 1, NBLOCK
C
C       Get old state variables
        EP_OLD = STATEOLD(K,1)
        D_PLUS_OLD = STATEOLD(K,2)
        D_MINUS_OLD = STATEOLD(K,3)
        R_MAX_PLUS_OLD = STATEOLD(K,4)
        R_MAX_MINUS_OLD = STATEOLD(K,5)
C
C       Initialize new state variables with old values
        EP_NEW = EP_OLD
        D_PLUS_NEW = D_PLUS_OLD
        D_MINUS_NEW = D_MINUS_OLD
        R_MAX_PLUS_NEW = R_MAX_PLUS_OLD
        R_MAX_MINUS_NEW = R_MAX_MINUS_OLD
C
C       For single-axis case, only consider first strain component
        STRAIN_INC = STRAININC(K,1)
        STRAIN_TOTAL = STRETCHNEW(K,1) - ONE
C
C       Two implementation options:
C       1. Direct PINN prediction - Uses neural network directly
C       2. Physics-based model - Uses PINN-identified parameters
C
        IF (USE_DIRECT_PINN .EQ. 1) THEN
C         Option 1: Direct PINN prediction
C         Prepare normalized time step as input to PINN
          IF (NSTATEV .GE. 6) THEN
            TIME_STEP_NORMALIZED = STATEOLD(K,6) + 1.0D0
            STATENEW(K,6) = TIME_STEP_NORMALIZED
          ELSE
            TIME_STEP_NORMALIZED = TOTALTIME / 1.0D0
          ENDIF
          
C         Normalize time step to [0,1] range for PINN input
          T_INPUT = TIME_STEP_NORMALIZED / 1000.0D0
          
C         Call PINN forward pass
          CALL PINN_FORWARD(T_INPUT, SIGMA_PINN, DAMAGE_PINN, XI_PINN)
          
C         Update plastic strain based on PINN prediction
          DELTA_EP = XI_PINN * STRAIN_INC
          EP_NEW = EP_OLD + DELTA_EP
          
C         Use PINN-predicted damage
          IF (STRAIN_ELASTIC .GE. 0.0D0) THEN
            D_PLUS_NEW = DAMAGE_PINN
            D_MINUS_NEW = D_MINUS_OLD
          ELSE
            D_PLUS_NEW = D_PLUS_OLD
            D_MINUS_NEW = DAMAGE_PINN
          ENDIF
          
C         Compute stress using damage and elastic modulus
          STRAIN_ELASTIC = STRAIN_TOTAL - EP_NEW
          IF (STRAIN_ELASTIC .GE. 0.0D0) THEN
            D_EFFECTIVE = D_PLUS_NEW
          ELSE
            D_EFFECTIVE = D_MINUS_NEW
          ENDIF
          STRESSNEW(K,1) = (1.0D0 - D_EFFECTIVE) * E0 * STRAIN_ELASTIC
          
        ELSE
C         Option 2: Physics-based model with PINN-identified parameters
C         Update plastic strain based on strain increment direction
          IF (STRAIN_INC .GT. 0.0D0) THEN
C           Tensile increment
            DELTA_EP = XI_PLUS * STRAIN_INC
          ELSE
C           Compressive increment
            DELTA_EP = -XI_MINUS * ABS(STRAIN_INC)
          ENDIF
          EP_NEW = EP_OLD + DELTA_EP
C
C         Calculate effective elastic strain
          STRAIN_ELASTIC = STRAIN_TOTAL - EP_NEW
C
C         Calculate damage driving forces
          IF (STRAIN_ELASTIC .GE. 0.0D0) THEN
            Y_PLUS = E0 * STRAIN_ELASTIC
            Y_MINUS = 0.0D0
          ELSE
            Y_PLUS = 0.0D0
            Y_MINUS = E0 * (-STRAIN_ELASTIC)
          ENDIF
C
C         Update tensile damage if driving force exceeds threshold
          IF (Y_PLUS .GT. R_MAX_PLUS_OLD) THEN
            R_MAX_PLUS_NEW = Y_PLUS
            RATIO = R_MAX_PLUS_NEW / FT
            EXP_TERM = EXP(B_PLUS * (ONE - RATIO))
            D_PLUS_NEW = ONE - (ONE/RATIO) * ((ONE - A_PLUS) + A_PLUS * EXP_TERM)
C           Ensure damage is within bounds
            IF (D_PLUS_NEW .GT. D_MAX) THEN
              D_PLUS_NEW = D_MAX
            ENDIF
          ENDIF
C
C         Update compressive damage if driving force exceeds threshold
          IF (Y_MINUS .GT. R_MAX_MINUS_OLD) THEN
            R_MAX_MINUS_NEW = Y_MINUS
            RATIO = R_MAX_MINUS_NEW / FC
            EXP_TERM = EXP(B_MINUS * (ONE - RATIO))
            D_MINUS_NEW = ONE - (ONE/RATIO) * ((ONE - A_MINUS) + A_MINUS * EXP_TERM)
C           Ensure damage is within bounds
            IF (D_MINUS_NEW .GT. D_MAX) THEN
              D_MINUS_NEW = D_MAX
            ENDIF
          ENDIF
C
C         Calculate effective damage based on strain state
          IF (STRAIN_ELASTIC .GE. 0.0D0) THEN
            D_EFFECTIVE = D_PLUS_NEW
          ELSE
            D_EFFECTIVE = D_MINUS_NEW
          ENDIF
C
C         Calculate stress
          STRESSNEW(K,1) = (1.0D0 - D_EFFECTIVE) * E0 * STRAIN_ELASTIC
        ENDIF
C
C       Set other stress components to zero (for 3D analysis)
        DO I = 2, NDIR+NSHR
          STRESSNEW(K,I) = 0.0D0
        END DO
C
C       Update state variables
        STATENEW(K,1) = EP_NEW
        STATENEW(K,2) = D_PLUS_NEW
        STATENEW(K,3) = D_MINUS_NEW
        STATENEW(K,4) = R_MAX_PLUS_NEW
        STATENEW(K,5) = R_MAX_MINUS_NEW
C
100   CONTINUE
C
      RETURN
      END 