import matplotlib.pyplot as plt
import numpy as np
import os
from matplotlib.gridspec import GridSpec
import torch
from sklearn.metrics import r2_score
from .font_config import setup_chinese_font

# 设置中文字体
setup_chinese_font()


def plot_stress_strain_curve(strain, stress_true, stress_pred, save_path=None):
    """
    绘制应力-应变曲线
    
    参数:
        strain: 应变数据
        stress_true: 真实应力数据
        stress_pred: 预测应力数据
        save_path: 保存路径，如果为None则显示图像
    """
    plt.figure(figsize=(10, 6))
    plt.plot(strain, stress_true, 'b-', label='真实应力')
    plt.plot(strain, stress_pred, 'r--', label='预测应力')
    plt.xlabel('应变')
    plt.ylabel('应力 (MPa)')
    plt.title('应力-应变曲线对比')
    plt.legend()
    plt.grid(True)
    
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()


def plot_damage_evolution(strain, damage, save_path=None):
    """
    绘制损伤演化曲线
    
    参数:
        strain: 应变数据
        damage: 损伤变量数据
        save_path: 保存路径，如果为None则显示图像
    """
    plt.figure(figsize=(10, 6))
    plt.plot(strain, damage, 'g-')
    plt.xlabel('应变')
    plt.ylabel('损伤变量 d^+')
    plt.title('损伤演化曲线')
    plt.grid(True)
    
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()


def plot_plastic_strain(strain, plastic_strain, save_path=None):
    """
    绘制塑性应变曲线
    
    参数:
        strain: 应变数据
        plastic_strain: 塑性应变数据
        save_path: 保存路径，如果为None则显示图像
    """
    plt.figure(figsize=(10, 6))
    plt.plot(strain, plastic_strain, 'm-')
    plt.xlabel('应变')
    plt.ylabel('塑性应变')
    plt.title('塑性应变演化曲线')
    plt.grid(True)
    
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()


def plot_prediction_vs_target(targets, predictions, save_path=None):
    """
    绘制预测vs真实散点图
    
    参数:
        targets: 真实值
        predictions: 预测值
        save_path: 保存路径，如果为None则显示图像
    """
    plt.figure(figsize=(8, 8))
    plt.scatter(targets, predictions, alpha=0.5)
    plt.plot([min(targets), max(targets)], [min(targets), max(targets)], 'k--')
    plt.xlabel('真实应力 (MPa)')
    plt.ylabel('预测应力 (MPa)')
    plt.title('预测vs真实应力')
    plt.grid(True)
    plt.axis('equal')
    
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()


def plot_error_distribution(predictions, targets, save_path=None):
    """
    绘制误差分布直方图
    
    参数:
        predictions: 预测值
        targets: 真实值
        save_path: 保存路径，如果为None则显示图像
    """
    errors = predictions - targets
    plt.figure(figsize=(10, 6))
    plt.hist(errors, bins=30, alpha=0.7, color='blue')
    plt.xlabel('预测误差 (MPa)')
    plt.ylabel('频数')
    plt.title('预测误差分布')
    plt.grid(True)
    
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()


def plot_damage_stress_relation(damage, stress, strain, save_path=None):
    """
    绘制损伤-应力关系
    
    参数:
        damage: 损伤变量
        stress: 应力
        strain: 应变（用于颜色映射）
        save_path: 保存路径，如果为None则显示图像
    """
    plt.figure(figsize=(10, 6))
    scatter = plt.scatter(damage, stress, alpha=0.5, c=strain, cmap='viridis')
    plt.colorbar(scatter, label='应变')
    plt.xlabel('损伤变量 d^+')
    plt.ylabel('应力 (MPa)')
    plt.title('损伤-应力关系')
    plt.grid(True)
    
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()


def plot_training_loss(train_losses, test_losses, save_path=None):
    """
    绘制训练和测试损失曲线
    
    参数:
        train_losses: 训练损失列表
        test_losses: 测试损失列表
        save_path: 保存路径，如果为None则显示图像
    """
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='训练损失')
    plt.plot(test_losses, label='测试损失')
    plt.xlabel('轮次')
    plt.ylabel('损失')
    plt.title('训练和测试损失')
    plt.legend()
    plt.grid(True)
    
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()


def plot_material_params_history(params_history, save_path=None):
    """
    绘制材料参数变化曲线
    
    参数:
        params_history: 材料参数历史记录列表
        save_path: 保存路径，如果为None则显示图像
    """
    # 创建5个子图
    fig, axes = plt.subplots(5, 1, figsize=(10, 15), sharex=True)
    
    # 轮次
    epochs = range(1, len(params_history) + 1)
    
    # 绘制E_0变化
    axes[0].plot(epochs, [p['E_0'] for p in params_history])
    axes[0].set_ylabel('E_0 (MPa)')
    axes[0].set_title('初始弹性模量变化')
    axes[0].grid(True)
    
    # 绘制r_0变化
    axes[1].plot(epochs, [p['r_0'] for p in params_history])
    axes[1].set_ylabel('r_0')
    axes[1].set_title('初始损伤阈值变化')
    axes[1].grid(True)
    
    # 绘制A_plus变化
    axes[2].plot(epochs, [p['A_plus'] for p in params_history])
    axes[2].set_ylabel('A_plus')
    axes[2].set_title('损伤参数A^+变化')
    axes[2].grid(True)
    
    # 绘制B_plus变化
    axes[3].plot(epochs, [p['B_plus'] for p in params_history])
    axes[3].set_ylabel('B_plus')
    axes[3].set_title('损伤参数B^+变化')
    axes[3].grid(True)
    
    # 绘制xi_plus变化
    axes[4].plot(epochs, [p['xi_plus'] for p in params_history])
    axes[4].set_ylabel('xi_plus')
    axes[4].set_title('塑性参数变化')
    axes[4].grid(True)
    
    # 设置x轴标签
    axes[4].set_xlabel('轮次')
    
    # 调整布局
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()


def plot_sensitivity_analysis(param_name, param_values, stress_values, damage_values, plastic_values, save_path=None):
    """
    绘制参数敏感性分析结果
    
    参数:
        param_name: 参数名称
        param_values: 参数值列表
        stress_values: 对应的应力值列表
        damage_values: 对应的损伤值列表
        plastic_values: 对应的塑性应变值列表
        save_path: 保存路径，如果为None则显示图像
    """
    plt.figure(figsize=(12, 8))
    
    plt.subplot(3, 1, 1)
    plt.plot(param_values, stress_values, 'b-o')
    plt.xlabel(param_name)
    plt.ylabel('平均应力 (MPa)')
    plt.title(f'{param_name} 对应力的敏感性')
    plt.grid(True)
    
    plt.subplot(3, 1, 2)
    plt.plot(param_values, damage_values, 'r-o')
    plt.xlabel(param_name)
    plt.ylabel('平均损伤变量')
    plt.title(f'{param_name} 对损伤的敏感性')
    plt.grid(True)
    
    plt.subplot(3, 1, 3)
    plt.plot(param_values, plastic_values, 'g-o')
    plt.xlabel(param_name)
    plt.ylabel('平均塑性应变')
    plt.title(f'{param_name} 对塑性应变的敏感性')
    plt.grid(True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()


def plot_comprehensive_results(strain, stress_true, stress_pred, damage, plastic_strain, save_path=None):
    """
    绘制综合结果图
    
    参数:
        strain: 应变数据
        stress_true: 真实应力数据
        stress_pred: 预测应力数据
        damage: 损伤变量数据
        plastic_strain: 塑性应变数据
        save_path: 保存路径，如果为None则显示图像
    """
    # 确保数据长度一致
    min_length = min(len(strain), len(stress_true), len(stress_pred), len(damage), len(plastic_strain))
    strain = strain[:min_length]
    stress_true = stress_true[:min_length]
    stress_pred = stress_pred[:min_length]
    damage = damage[:min_length]
    plastic_strain = plastic_strain[:min_length]
    
    # 创建一个大图，包含多个子图
    fig = plt.figure(figsize=(15, 10))
    gs = GridSpec(2, 3, figure=fig)
    
    # 1. 应力-应变曲线
    ax1 = fig.add_subplot(gs[0, 0:2])
    ax1.plot(strain, stress_true, 'b-', label='真实应力')
    ax1.plot(strain, stress_pred, 'r--', label='预测应力')
    ax1.set_xlabel('应变')
    ax1.set_ylabel('应力 (MPa)')
    ax1.set_title('应力-应变曲线对比')
    ax1.legend()
    ax1.grid(True)
    
    # 2. 损伤演化曲线
    ax2 = fig.add_subplot(gs[0, 2])
    ax2.plot(strain, damage, 'g-')
    ax2.set_xlabel('应变')
    ax2.set_ylabel('损伤变量 d^+')
    ax2.set_title('损伤演化曲线')
    ax2.grid(True)
    
    # 3. 塑性应变曲线
    ax3 = fig.add_subplot(gs[1, 0])
    ax3.plot(strain, plastic_strain, 'm-')
    ax3.set_xlabel('应变')
    ax3.set_ylabel('塑性应变')
    ax3.set_title('塑性应变演化曲线')
    ax3.grid(True)
    
    # 4. 预测vs真实散点图
    ax4 = fig.add_subplot(gs[1, 1])
    ax4.scatter(stress_true, stress_pred, alpha=0.5)
    min_val = min(np.min(stress_true), np.min(stress_pred))
    max_val = max(np.max(stress_true), np.max(stress_pred))
    ax4.plot([min_val, max_val], [min_val, max_val], 'k--')
    ax4.set_xlabel('真实应力 (MPa)')
    ax4.set_ylabel('预测应力 (MPa)')
    ax4.set_title('预测vs真实应力')
    ax4.grid(True)
    ax4.axis('equal')
    
    # 5. 损伤-应力关系
    ax5 = fig.add_subplot(gs[1, 2])
    scatter = ax5.scatter(damage, stress_pred, alpha=0.5, c=strain, cmap='viridis')
    plt.colorbar(scatter, ax=ax5, label='应变')
    ax5.set_xlabel('损伤变量 d^+')
    ax5.set_ylabel('应力 (MPa)')
    ax5.set_title('损伤-应力关系')
    ax5.grid(True)
    
    # 调整布局
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()


def plot_loss_components(loss_history, save_path=None):
    """
    绘制损失组件变化曲线
    
    参数:
        loss_history: 损失历史记录字典
        save_path: 保存路径，如果为None则显示图像
    """
    plt.figure(figsize=(12, 8))
    
    # 绘制各损失组件
    for loss_name, loss_values in loss_history.items():
        if loss_name != 'total':  # 排除总损失
            plt.plot(loss_values, label=loss_name)
    
    plt.xlabel('轮次')
    plt.ylabel('损失值')
    plt.title('损失组件变化')
    plt.legend()
    plt.grid(True)
    plt.yscale('log')  # 使用对数尺度更容易观察
    
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()


def plot_dynamic_loss_weights(weight_history, save_path=None):
    """
    绘制动态损失权重变化曲线
    
    参数:
        weight_history: 权重历史记录列表
        save_path: 保存路径，如果为None则显示图像
    """
    plt.figure(figsize=(10, 6))
    
    # 获取权重名称
    weight_names = list(weight_history[0].keys())
    epochs = range(1, len(weight_history) + 1)
    
    # 绘制各权重变化
    for name in weight_names:
        weights = [w[name] for w in weight_history]
        plt.plot(epochs, weights, label=name)
    
    plt.xlabel('轮次')
    plt.ylabel('权重值')
    plt.title('动态损失权重变化')
    plt.legend()
    plt.grid(True)
    
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()