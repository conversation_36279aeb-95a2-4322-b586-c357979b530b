"""
训练脚本
严格按照框架中的伪代码实现训练过程
现在支持受拉受压的统一训练
"""

import torch
import torch.nn as nn
from torch.optim import Adam
from torch.optim.lr_scheduler import ReduceLROnPlateau # 引入学习率调度器
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os
import json
from pathlib import Path

from pinn_model_v2 import DamagePINNV2, PhysicsCalculatorV2, LossCalculator
from data_processor import DataProcessor
import font_config # 确保字体配置已导入

class Trainer:
    """
    PINN训练器
    严格按照框架要求的训练工作流
    """
    
    def __init__(self, config=None):
        # 默认配置
        self.config = {
            'E0': 30000.0,           # 初始弹性模量
            'f_t': 3.0,              # 单轴抗拉强度
            'f_c': 30.0,             # 单轴抗压强度 (新增)
            'num_epochs': 2000,      # 训练轮数
            'learning_rate': 0.001,  # 学习率
            'hidden_size': 64,       # 隐藏层大小
            'num_layers': 2,         # GRU层数
            'save_interval': 100,    # 保存间隔
            'print_interval': 50,    # 打印间隔
            'lambda_data': 1.0,      # 数据损失权重
            'lambda_stress': 1.0,    # 本构自洽损失权重
            'lambda_damage_plus': 0.8, # 受拉损伤物理损失权重 (新增)
            'lambda_damage_minus': 0.8, # 受压损伤物理损失权重 (新增)
            'lambda_plastic': 2.0,   # 塑性物理损失权重 (适当增加以强调塑性)
        }
        if config:
            self.config.update(config)
        
        # 创建带时间戳的结果文件夹
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.results_dir = f'results/training_{self.timestamp}'
        Path(self.results_dir).mkdir(parents=True, exist_ok=True)
        print(f"结果将保存到: {self.results_dir}")
        
        # 初始化组件
        self.model = None
        self.physics_calc = None
        self.loss_calc = None
        self.optimizer = None
        self.scheduler = None # 学习率调度器 (新增)
        self.training_data = None
        
        # 可训练物理参数
        self.A_plus = None
        self.B_plus = None
        self.xi_plus = None # 明确为拉伸塑性系数

        self.A_minus = None # 受压损伤参数 A- (新增)
        self.B_minus = None # 受压损伤参数 B- (新增)
        self.xi_minus = None # 受压塑性应变系数 (新增)
        
        # 训练历史
        self.loss_history = []
        self.param_history = []
        
    def initialize_model(self):
        """
        初始化模型和组件
        """
        # 初始化PINN模型
        self.model = DamagePINNV2(
            input_size=1,
            hidden_size=self.config['hidden_size'],
            num_layers=self.config['num_layers'],
            output_size=3 # 输出应力、通用损伤、通用塑性系数
        )
        
        # 初始化物理计算器 (传入 f_c)
        self.physics_calc = PhysicsCalculatorV2(
            E0=self.config['E0'],
            f_t=self.config['f_t'],
            f_c=self.config['f_c'] # 传入 f_c
        )
        
        # 初始化损失计算器 (传入区分拉压的损伤权重)
        self.loss_calc = LossCalculator(
            lambda_data=self.config['lambda_data'],
            lambda_stress=self.config['lambda_stress'],
            lambda_damage_plus=self.config['lambda_damage_plus'],
            lambda_damage_minus=self.config['lambda_damage_minus'],
            lambda_plastic=self.config['lambda_plastic']
        )
        
        # 初始化可训练物理参数 - 按照框架要求并新增受压参数
        self.A_plus = torch.nn.Parameter(torch.tensor(0.5, dtype=torch.float32, requires_grad=True))
        self.B_plus = torch.nn.Parameter(torch.tensor(1.0, dtype=torch.float32, requires_grad=True))
        self.xi_plus = torch.nn.Parameter(torch.tensor(0.05, dtype=torch.float32, requires_grad=True))

        self.A_minus = torch.nn.Parameter(torch.tensor(1.5, dtype=torch.float32, requires_grad=True)) # 经验值
        self.B_minus = torch.nn.Parameter(torch.tensor(0.5, dtype=torch.float32, requires_grad=True))  # 经验值
        self.xi_minus = torch.nn.Parameter(torch.tensor(0.02, dtype=torch.float32, requires_grad=True)) # 经验值
        
        # 初始化优化器，加入所有可训练参数
        self.optimizer = Adam(
            list(self.model.parameters()) + 
            [self.A_plus, self.B_plus, self.xi_plus,
             self.A_minus, self.B_minus, self.xi_minus], # 新增参数
            lr=self.config['learning_rate']
        )
        
        # 学习率调度器: 当总损失停止改善时，降低学习率
        self.scheduler = ReduceLROnPlateau(self.optimizer, mode='min', factor=0.5, 
                                           patience=200, verbose=True, min_lr=1e-6)
        
        print("模型初始化完成")
        print(f"  模型参数数量: {sum(p.numel() for p in self.model.parameters())}")
        print(f"  可训练物理参数 (拉伸): A+={self.A_plus.item():.3f}, B+={self.B_plus.item():.3f}, xi+={self.xi_plus.item():.3f}")
        print(f"  可训练物理参数 (压缩): A-={self.A_minus.item():.3f}, B-={self.B_minus.item():.3f}, xi-={self.xi_minus.item():.3f}")
    
    def load_data(self, excel_path="tension.xlsx"):
        """
        加载训练数据
        """
        processor = DataProcessor(excel_path)
        
        if not processor.load_experimental_data():
            raise ValueError("数据加载失败")
        
        if not processor.validate_data_quality():
            raise ValueError("数据质量验证失败")
        
        # 准备训练数据
        self.training_data = processor.prepare_training_data()
        
        # 绘制实验数据到指定文件夹
        processor.plot_experimental_data(save_path=f"{self.results_dir}/experimental_data.png")
        
        # 获取材料属性并更新配置 (现在也包括 f_c)
        properties = processor.get_material_properties()
        self.config['E0'] = properties['E0_estimated']
        self.config['f_t'] = properties['f_t_estimated']
        self.config['f_c'] = properties['f_c_estimated'] # 将 f_c 加入配置
        
        print(f"数据加载完成，序列长度: {self.training_data['sequence_length']}")
        
    def train_epoch(self):
        """
        训练一个epoch
        严格按照框架中的伪代码
        """
        self.model.train()
        
        # 获取训练数据
        strain_increment_input = self.training_data['strain_increment_input']
        strain_total_exp = self.training_data['strain_total_exp']
        stress_exp = self.training_data['stress_exp']
        strain_increment = self.training_data['strain_increment'] # 用于物理计算器
        
        # --- 步骤 A: 神经网络前向传播 (输入为应变增量) ---
        # 神经网络输出 sigma_hat_seq, d_hat_seq (通用损伤), xi_hat_seq (通用塑性系数)
        sigma_hat_seq, d_hat_seq, xi_hat_seq = self.model(strain_increment_input)
        sigma_hat_seq = sigma_hat_seq.squeeze()
        d_hat_seq = d_hat_seq.squeeze()
        xi_hat_seq = xi_hat_seq.squeeze() # 神经网络输出的xi

        # 计算神经网络预测的累积塑性应变 ep_hat_seq
        # 根据 xi_hat_seq 和 strain_increment 来计算
        # 这里需要根据增量的正负方向来累积，更精确的塑性应变会在 PhysicsCalculator 中。
        # 这里的 ep_hat_seq 应该由模型内部隐式计算得到
        # 或者简化为：ep_increment_hat = xi_hat_seq * torch.relu(strain_increment) # 假设xi_hat_seq是正的
        # 针对拉压，这里可能需要更复杂的逻辑，或者让模型直接输出 ep_increment_hat
        # 目前 DamagePINNV2 已经包含了 ep_seq 的计算并返回，所以可以直接用 ep_hat_seq
        # 如果模型只输出xi_hat_seq，则需要在这里计算ep_hat_seq
        # current_ep_increment = xi_hat_seq * torch.relu(strain_increment) # 假设 xi_hat_seq 是针对拉伸的
        # ep_hat_seq = torch.cumsum(current_ep_increment, dim=0) # 累积塑性应变
        # 重新审查 DamagePINNV2 的 forward 方法：它已经返回了 ep_seq，所以可以直接用！
        # sigma_hat_seq, d_hat_seq, ep_hat_seq = self.model(strain_increment_input) # model already returns ep_seq
        # 实际上 DamagePINNV2 的 forward 返回的第三个参数就是 ep_seq
        
        # --- 步骤 B: 物理约束目标的增量式计算 ---
        # 物理计算器计算 d_plus_phy_seq, d_minus_phy_seq, ep_phy_seq
        d_plus_phy_seq, d_minus_phy_seq, ep_phy_seq = \
            self.physics_calc.calculate_physics_constraints(
                strain_increment, 
                self.A_plus, self.B_plus, self.xi_plus, # 传入拉伸参数
                self.A_minus, self.B_minus, self.xi_minus # 传入压缩参数
            )
        
        # --- 步骤 C: 计算总损失 ---
        total_loss, loss_dict = self.loss_calc.calculate_total_loss(
            sigma_hat_seq, d_hat_seq, ep_hat_seq, # 模型预测值
            stress_exp, strain_total_exp,         # 实验值
            d_plus_phy_seq, d_minus_phy_seq, ep_phy_seq, self.config['E0'] # 物理约束目标
        )
        
        # --- 步骤 D: 反向传播与优化 ---
        self.optimizer.zero_grad()
        total_loss.backward()
        
        # 梯度裁剪，防止梯度爆炸
        torch.nn.utils.clip_grad_norm_(
            list(self.model.parameters()) + 
            [self.A_plus, self.B_plus, self.xi_plus, 
             self.A_minus, self.B_minus, self.xi_minus], # 确保包含所有可训练参数
            max_norm=1.0 # 可以根据训练情况调整
        )
        
        self.optimizer.step()
        
        # 约束物理参数在合理范围内
        with torch.no_grad():
            self.A_plus.data.clamp_(0.01, 0.99)
            self.B_plus.data.clamp_(0.1, 10.0)
            self.xi_plus.data.clamp_(0.005, 0.2)

            self.A_minus.data.clamp_(0.1, 5.0) # 受压参数范围可能更大
            self.B_minus.data.clamp_(0.1, 10.0)
            self.xi_minus.data.clamp_(0.005, 0.2)
        
        return loss_dict
    
    def train(self):
        """
        完整训练过程
        """
        print(f"\n开始训练，共{self.config['num_epochs']}轮")
        print("=" * 60)
        
        for epoch in range(self.config['num_epochs']):
            # 训练一个epoch
            loss_dict = self.train_epoch()
            
            # 记录历史
            self.loss_history.append(loss_dict)
            self.param_history.append({
                'A_plus': self.A_plus.item(),
                'B_plus': self.B_plus.item(),
                'xi_plus': self.xi_plus.item(),
                'A_minus': self.A_minus.item(), # 新增
                'B_minus': self.B_minus.item(), # 新增
                'xi_minus': self.xi_minus.item() # 新增
            })
            
            # 打印训练进度
            if (epoch + 1) % self.config['print_interval'] == 0:
                print(f"Epoch {epoch+1:4d}/{self.config['num_epochs']} | "
                      f"Total Loss: {loss_dict['total_loss']:.6f} | "
                      f"Data: {loss_dict['loss_data']:.6f} | "
                      f"Stress: {loss_dict['loss_stress']:.6f} | "
                      f"D_plus: {loss_dict['loss_damage_plus']:.6f} | " # 打印细分损失
                      f"D_minus: {loss_dict['loss_damage_minus']:.6f} | " # 打印细分损失
                      f"Plastic: {loss_dict['loss_plastic']:.6f}")
                print(f"     Params (T): A+={self.A_plus.item():.4f}, B+={self.B_plus.item():.4f}, xi+={self.xi_plus.item():.4f}")
                print(f"     Params (C): A-={self.A_minus.item():.4f}, B-={self.B_minus.item():.4f}, xi-={self.xi_minus.item():.4f}") # 打印受压参数
            
            # 学习率调度器步进
            self.scheduler.step(loss_dict['total_loss']) # 根据总损失调整学习率

            # 保存检查点
            if (epoch + 1) % self.config['save_interval'] == 0:
                self.save_checkpoint(epoch + 1)
        
        print("=" * 60)
        print("训练完成!")
        print(f"最终识别的参数:")
        print(f"  A+ = {self.A_plus.item():.4f}, B+ = {self.B_plus.item():.4f}, xi+ = {self.xi_plus.item():.4f}")
        print(f"  A- = {self.A_minus.item():.4f}, B- = {self.B_minus.item():.4f}, xi- = {self.xi_minus.item():.4f}")
        
        # 保存最终模型
        self.save_final_model()
        
        # 绘制训练历史
        self.plot_training_history()
        
    def save_checkpoint(self, epoch):
        """
        保存训练检查点
        """
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'A_plus': self.A_plus.item(),
            'B_plus': self.B_plus.item(),
            'xi_plus': self.xi_plus.item(),
            'A_minus': self.A_minus.item(), # 新增
            'B_minus': self.B_minus.item(), # 新增
            'xi_minus': self.xi_minus.item(), # 新增
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(), # 保存调度器状态 (新增)
            'config': self.config,
            'loss_history': self.loss_history,
            'param_history': self.param_history
        }
        
        torch.save(checkpoint, f'{self.results_dir}/checkpoint_epoch_{epoch}.pth')
        print(f"检查点已保存至: {self.results_dir}/checkpoint_epoch_{epoch}.pth")
    
    def save_final_model(self):
        """
        保存最终模型
        """
        model_path = f'{self.results_dir}/pinn_model_{self.timestamp}.pth'
        
        # 保存完整模型信息
        model_info = {
            'model_state_dict': self.model.state_dict(),
            'physics_parameters': {
                'A_plus': self.A_plus.item(),
                'B_plus': self.B_plus.item(),
                'xi_plus': self.xi_plus.item(),
                'A_minus': self.A_minus.item(), # 新增
                'B_minus': self.B_minus.item(), # 新增
                'xi_minus': self.xi_minus.item() # 新增
            },
            'material_constants': {
                'E0': self.config['E0'],
                'f_t': self.config['f_t'],
                'f_c': self.config['f_c'] # 新增 f_c
            },
            'config': self.config,
            'loss_history': self.loss_history,
            'param_history': self.param_history,
            'timestamp': self.timestamp,
            'results_dir': self.results_dir
        }
        
        torch.save(model_info, model_path)
        
        # 保存参数到JSON文件
        params_path = f'{self.results_dir}/identified_parameters_{self.timestamp}.json'
        with open(params_path, 'w', encoding='utf-8') as f:
            json.dump({
                'physics_parameters': model_info['physics_parameters'],
                'material_constants': model_info['material_constants'],
                'training_config': self.config,
                'final_loss': self.loss_history[-1] if self.loss_history else None,
                'timestamp': self.timestamp,
                'results_dir': self.results_dir
            }, f, indent=2, ensure_ascii=False)
        
        print(f"最终模型已保存至: {model_path}")
        print(f"识别参数已保存至: {params_path}")
    
    def plot_training_history(self):
        """
        绘制训练历史
        """
        if not self.loss_history:
            return
        
        epochs = range(1, len(self.loss_history) + 1)
        
        # 绘制损失历史
        fig, axes = plt.subplots(2, 2, figsize=(15, 12)) # 调整 figsize
        ax1, ax2, ax3, ax4 = axes.flatten()
        
        # 总损失
        total_losses = [h['total_loss'] for h in self.loss_history]
        ax1.plot(epochs, total_losses, 'b-', linewidth=2)
        ax1.set_title('总损失', fontsize=14, fontweight='bold')
        ax1.set_xlabel('训练轮数', fontsize=12)
        ax1.set_ylabel('损失值', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.set_yscale('log')
        
        # 各项损失
        data_losses = [h['loss_data'] for h in self.loss_history]
        stress_losses = [h['loss_stress'] for h in self.loss_history]
        damage_plus_losses = [h['loss_damage_plus'] for h in self.loss_history] # 新增
        damage_minus_losses = [h['loss_damage_minus'] for h in self.loss_history] # 新增
        plastic_losses = [h['loss_plastic'] for h in self.loss_history]
        
        ax2.plot(epochs, data_losses, 'r-', label='数据拟合', linewidth=2)
        ax2.plot(epochs, stress_losses, 'g-', label='本构自洽', linewidth=2)
        ax2.plot(epochs, damage_plus_losses, 'c--', label='损伤物理 (拉伸)', linewidth=1.5) # 新增
        ax2.plot(epochs, damage_minus_losses, 'm--', label='损伤物理 (压缩)', linewidth=1.5) # 新增
        ax2.plot(epochs, plastic_losses, 'k-', label='塑性物理', linewidth=2)
        ax2.set_title('各项损失', fontsize=14, fontweight='bold')
        ax2.set_xlabel('训练轮数', fontsize=12)
        ax2.set_ylabel('损失值', fontsize=12)
        ax2.legend(fontsize=10)
        ax2.grid(True, alpha=0.3)
        ax2.set_yscale('log')
        
        # 损伤参数演化
        A_plus_history = [h['A_plus'] for h in self.param_history]
        B_plus_history = [h['B_plus'] for h in self.param_history]
        A_minus_history = [h['A_minus'] for h in self.param_history] # 新增
        B_minus_history = [h['B_minus'] for h in self.param_history] # 新增
        
        ax3.plot(epochs, A_plus_history, 'r-', label='A+', linewidth=2)
        ax3.plot(epochs, B_plus_history, 'g-', label='B+', linewidth=2)
        ax3.plot(epochs, A_minus_history, 'r--', label='A-', linewidth=1.5, alpha=0.7) # 新增，虚线
        ax3.plot(epochs, B_minus_history, 'g--', label='B-', linewidth=1.5, alpha=0.7) # 新增，虚线
        ax3.set_title('损伤参数演化', fontsize=14, fontweight='bold')
        ax3.set_xlabel('训练轮数', fontsize=12)
        ax3.set_ylabel('参数值', fontsize=12)
        ax3.legend(fontsize=10)
        ax3.grid(True, alpha=0.3)
        
        # 塑性参数演化
        xi_plus_history = [h['xi_plus'] for h in self.param_history]
        xi_minus_history = [h['xi_minus'] for h in self.param_history] # 新增
        
        ax4.plot(epochs, xi_plus_history, 'b-', linewidth=2, label='xi+')
        ax4.plot(epochs, xi_minus_history, 'm--', linewidth=1.5, label='xi-', alpha=0.7) # 新增
        ax4.set_title('塑性参数演化', fontsize=14, fontweight='bold')
        ax4.set_xlabel('训练轮数', fontsize=12)
        ax4.set_ylabel('xi值', fontsize=12)
        ax4.grid(True, alpha=0.3)
        ax4.legend(fontsize=10)
        
        plt.tight_layout()
        save_path = f'{self.results_dir}/training_history.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"训练历史图像已保存至: {save_path}")


def main():
    """
    主训练函数
    """
    print("初始化混凝土损伤参数识别PINN训练器")
    print("=" * 60)
    
    # 创建训练器
    trainer = Trainer() # 可以传入自定义config
    
    # 加载数据
    # 请确保 tension.xlsx 文件中包含受拉受压的循环数据，或替换为你的循环数据文件
    trainer.load_data("tension.xlsx") 
    
    # 初始化模型
    trainer.initialize_model()
    
    # 开始训练
    trainer.train()
    
    print("训练程序完成!")


if __name__ == "__main__":
    main()
