"""
字体配置文件
配置matplotlib的中文显示，提供多种回退方案
"""

import matplotlib.pyplot as plt
import matplotlib
from matplotlib import font_manager
import platform
import os
import warnings

def get_available_fonts():
    """
    获取系统中可用的字体列表
    """
    font_list = font_manager.fontManager.ttflist
    available_fonts = []
    
    for font in font_list:
        try:
            font_name = font.name
            font_path = font.fname
            available_fonts.append({
                'name': font_name,
                'path': font_path,
                'family': getattr(font, 'family', 'unknown')
            })
        except:
            continue
    
    return available_fonts

def find_chinese_fonts():
    """
    查找系统中的中文字体
    """
    chinese_font_names = [
        'SimHei', '黑体', 
        'SimSun', '宋体',
        'Microsoft YaHei', '微软雅黑',
        'STHeiti', 'STSong', 'STKaiti',
        'DejaVu Sans', 'Liberation Sans',
        'Arial Unicode MS',
        'Noto Sans CJK SC', 'Source Han Sans SC'
    ]
    
    available_fonts = get_available_fonts()
    found_fonts = []
    
    for font_info in available_fonts:
        font_name = font_info['name']
        for chinese_name in chinese_font_names:
            if chinese_name.lower() in font_name.lower():
                found_fonts.append(font_info)
                break
    
    return found_fonts

def setup_font(verbose=True):
    """
    配置matplotlib的字体，支持中文显示
    
    Args:
        verbose: 是否打印详细信息
    """
    if verbose:
        print("=" * 50)
        print("配置matplotlib中文字体...")
        print("=" * 50)
    
    system = platform.system()
    if verbose:
        print(f"操作系统: {system}")
    
    # 方案1: 尝试使用系统字体路径
    font_path = None
    
    if system == 'Windows':
        # Windows系统字体路径
        font_candidates = [
            'C:/Windows/Fonts/simhei.ttf',      # 黑体
            'C:/Windows/Fonts/simsun.ttc',      # 宋体
            'C:/Windows/Fonts/msyh.ttc',        # 微软雅黑
            'C:/Windows/Fonts/msyhbd.ttc',      # 微软雅黑粗体
            'C:/Windows/Fonts/simsunb.ttf',     # 宋体粗体
        ]
    elif system == 'Darwin':  # macOS
        font_candidates = [
            '/System/Library/Fonts/STHeiti Light.ttc',
            '/System/Library/Fonts/STHeiti Medium.ttc',
            '/System/Library/Fonts/Hiragino Sans GB.ttc',
            '/Library/Fonts/Arial Unicode.ttf',
            '/System/Library/Fonts/PingFang.ttc',
        ]
    else:  # Linux
        font_candidates = [
            '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
            '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc',
            '/usr/share/fonts/truetype/arphic/uming.ttc',
        ]
    
    # 查找可用的字体文件
    for path in font_candidates:
        if os.path.exists(path):
            font_path = path
            if verbose:
                print(f"找到字体文件: {path}")
            break
    
    # 方案2: 如果找不到字体文件，尝试使用字体名称
    if font_path is None:
        if verbose:
            print("未找到字体文件，尝试使用字体名称...")
        
        # 查找系统中的中文字体
        chinese_fonts = find_chinese_fonts()
        if chinese_fonts and verbose:
            print("找到的中文字体:")
            for font_info in chinese_fonts[:5]:  # 只显示前5个
                print(f"  - {font_info['name']} ({font_info['path']})")
        
        # 尝试设置字体族
        font_families = [
            'SimHei', 'Microsoft YaHei', 'SimSun',
            'STHeiti', 'DejaVu Sans', 'Liberation Sans'
        ]
        
        font_set = False
        for family in font_families:
            try:
                plt.rcParams['font.family'] = family
                # 测试是否可以使用
                fig, ax = plt.subplots(figsize=(1, 1))
                ax.text(0.5, 0.5, '测试中文', fontsize=12)
                plt.close(fig)
                if verbose:
                    print(f"成功设置字体族: {family}")
                font_set = True
                break
            except:
                continue
        
        if not font_set:
            if verbose:
                print("警告: 无法设置中文字体族")
    
    # 方案3: 使用字体文件路径
    if font_path:
        try:
            # 添加字体到系统
            font_manager.fontManager.addfont(font_path)
            
            # 获取字体属性
            font_prop = font_manager.FontProperties(fname=font_path)
            font_name = font_prop.get_name()
            
            # 设置为默认字体
            plt.rcParams['font.family'] = font_name
            
            if verbose:
                print(f"成功设置字体: {font_name} ({font_path})")
                
        except Exception as e:
            if verbose:
                print(f"设置字体时出错: {e}")
            # 回退到默认设置
            plt.rcParams['font.family'] = 'DejaVu Sans'
    
    # 通用设置
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    plt.rcParams['figure.dpi'] = 100
    plt.rcParams['savefig.dpi'] = 300
    plt.rcParams['figure.figsize'] = [10, 6]
    plt.rcParams['axes.grid'] = True
    plt.rcParams['grid.alpha'] = 0.3
    
    if verbose:
        print(f"当前字体设置: {plt.rcParams['font.family']}")
        print("=" * 50)
    
    # 测试中文显示
    return test_chinese_display(verbose=verbose)

def test_chinese_display(verbose=True):
    """
    测试中文显示是否正常
    
    Returns:
        bool: 是否支持中文显示
    """
    try:
        fig, ax = plt.subplots(figsize=(6, 4))
        ax.text(0.5, 0.7, '中文显示测试', fontsize=16, ha='center')
        ax.text(0.5, 0.5, '拉伸损伤参数', fontsize=14, ha='center')
        ax.text(0.5, 0.3, '压缩损伤参数', fontsize=14, ha='center')
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.set_title('字体测试')
        
        if verbose:
            plt.savefig('font_test.png', dpi=150, bbox_inches='tight')
            print("字体测试图像已保存: font_test.png")
        
        plt.close(fig)
        
        if verbose:
            print("✓ 中文显示测试通过")
        return True
        
    except Exception as e:
        if verbose:
            print(f"✗ 中文显示测试失败: {e}")
        return False

def get_font_info():
    """
    获取当前字体信息
    """
    return {
        'font_family': plt.rcParams['font.family'],
        'unicode_minus': plt.rcParams['axes.unicode_minus'],
        'system': platform.system(),
        'available_fonts': len(get_available_fonts())
    }

def reset_font():
    """
    重置字体设置为默认值
    """
    matplotlib.rcdefaults()
    print("字体设置已重置为默认值")

if __name__ == "__main__":
    # 如果直接运行此文件，进行字体配置和测试
    setup_font(verbose=True)
    
    # 显示字体信息
    info = get_font_info()
    print("\n当前字体配置信息:")
    for key, value in info.items():
        print(f"  {key}: {value}")
else:
    # 如果被导入，自动执行字体设置（静默模式）
    setup_font(verbose=False) 