# -*- coding: utf-8 -*-
"""
预测脚本

加载训练好的PINN模型进行预测和分析
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from pinn_model import ConstitutivePINN
from font_config import configure_chinese_font

class PINNPredictor:
    """
    PINN预测器
    """
    
    def __init__(self, model_path):
        """
        初始化预测器
        
        Args:
            model_path: 训练好的模型路径
        """
        self.model_path = model_path
        self.model = None
        self.training_data_stats = None
        
        # 配置中文字体
        configure_chinese_font()
    
    def load_model(self, hidden_size=32, num_layers=4):
        """
        加载训练好的模型
        
        Args:
            hidden_size: 隐藏层大小
            num_layers: 隐藏层数
        """
        try:
            # 创建模型结构
            self.model = ConstitutivePINN(hidden_size=hidden_size, num_layers=num_layers)
            
            # 加载模型权重
            checkpoint = torch.load(self.model_path, map_location='cpu')
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.eval()
            
            print(f"模型加载成功: {self.model_path}")
            print(f"模型参数:")
            print(f"  E0 = {self.model.E0.item():.2f} MPa")
            print(f"  r0+ = {self.model.r0_plus.item():.4f}")
            print(f"  r0- = {self.model.r0_minus.item():.4f}")
            print(f"  A+ = {self.model.A_plus.item():.4f}")
            print(f"  A- = {self.model.A_minus.item():.4f}")
            print(f"  B+ = {self.model.B_plus.item():.4f}")
            print(f"  B- = {self.model.B_minus.item():.4f}")
            
            return True
        except Exception as e:
            print(f"模型加载失败: {e}")
            return False
    
    def set_data_stats(self, strain_min, strain_max, stress_min, stress_max):
        """
        设置训练数据的统计信息（用于归一化和反归一化）
        
        Args:
            strain_min: 应变最小值
            strain_max: 应变最大值
            stress_min: 应力最小值
            stress_max: 应力最大值
        """
        self.training_data_stats = {
            'strain_min': strain_min,
            'strain_max': strain_max,
            'stress_min': stress_min,
            'stress_max': stress_max
        }
        print(f"数据统计信息已设置:")
        print(f"  应变范围: {strain_min:.6f} ~ {strain_max:.6f}")
        print(f"  应力范围: {stress_min:.2f} ~ {stress_max:.2f} MPa")
    
    def predict_single(self, strain_value):
        """
        单点预测
        
        Args:
            strain_value: 应变值
            
        Returns:
            stress: 预测应力
            damage: 预测损伤变量
        """
        if self.model is None:
            print("请先加载模型")
            return None, None
        
        if self.training_data_stats is None:
            print("请先设置数据统计信息")
            return None, None
        
        # 归一化输入
        strain_normalized = (strain_value - self.training_data_stats['strain_min']) / \
                           (self.training_data_stats['strain_max'] - self.training_data_stats['strain_min'])
        
        strain_tensor = torch.tensor([[strain_normalized]], dtype=torch.float32)
        
        with torch.no_grad():
            stress_pred, damage_pred = self.model(strain_tensor)
            
            # 反归一化应力
            stress_value = stress_pred.item() * (self.training_data_stats['stress_max'] - self.training_data_stats['stress_min']) + \
                          self.training_data_stats['stress_min']
            damage_value = damage_pred.item()
        
        return stress_value, damage_value
    
    def predict_range(self, strain_start, strain_end, num_points=1000):
        """
        范围预测
        
        Args:
            strain_start: 起始应变
            strain_end: 结束应变
            num_points: 预测点数
            
        Returns:
            strain_array: 应变数组
            stress_array: 应力数组
            damage_array: 损伤变量数组
        """
        if self.model is None:
            print("请先加载模型")
            return None, None, None
        
        if self.training_data_stats is None:
            print("请先设置数据统计信息")
            return None, None, None
        
        # 创建应变数组
        strain_array = np.linspace(strain_start, strain_end, num_points)
        
        # 归一化
        strain_normalized = (strain_array - self.training_data_stats['strain_min']) / \
                           (self.training_data_stats['strain_max'] - self.training_data_stats['strain_min'])
        
        strain_tensor = torch.tensor(strain_normalized.reshape(-1, 1), dtype=torch.float32)
        
        with torch.no_grad():
            stress_pred, damage_pred = self.model(strain_tensor)
            
            # 反归一化应力
            stress_array = stress_pred.numpy().flatten() * (self.training_data_stats['stress_max'] - self.training_data_stats['stress_min']) + \
                          self.training_data_stats['stress_min']
            damage_array = damage_pred.numpy().flatten()
        
        return strain_array, stress_array, damage_array
    
    def predict_cyclic_loading(self, max_strain, num_cycles=3, points_per_cycle=100):
        """
        循环加载预测
        
        Args:
            max_strain: 最大应变
            num_cycles: 循环次数
            points_per_cycle: 每个循环的点数
            
        Returns:
            strain_history: 应变历程
            stress_history: 应力历程
            damage_history: 损伤历程
        """
        if self.model is None:
            print("请先加载模型")
            return None, None, None
        
        # 生成循环加载路径
        strain_history = []
        
        for cycle in range(num_cycles):
            # 加载段
            strain_loading = np.linspace(0, max_strain, points_per_cycle // 2)
            # 卸载段
            strain_unloading = np.linspace(max_strain, 0, points_per_cycle // 2)
            
            strain_history.extend(strain_loading)
            strain_history.extend(strain_unloading)
        
        strain_history = np.array(strain_history)
        
        # 预测
        _, stress_history, damage_history = self.predict_range(
            strain_history.min(), strain_history.max(), len(strain_history)
        )
        
        # 重新排列以匹配循环路径
        strain_sorted_indices = np.argsort(np.linspace(strain_history.min(), strain_history.max(), len(strain_history)))
        strain_target_indices = np.searchsorted(
            np.linspace(strain_history.min(), strain_history.max(), len(strain_history))[strain_sorted_indices],
            strain_history
        )
        
        # 直接使用范围预测结果
        strain_pred, stress_pred, damage_pred = self.predict_range(
            0, max_strain, points_per_cycle * num_cycles
        )
        
        return strain_history, stress_pred[:len(strain_history)], damage_pred[:len(strain_history)]
    
    def analyze_material_response(self, strain_range=(0, 0.01), save_path=None):
        """
        分析材料响应特性
        
        Args:
            strain_range: 应变范围
            save_path: 保存路径
        """
        strain_array, stress_array, damage_array = self.predict_range(
            strain_range[0], strain_range[1], 1000
        )
        
        if strain_array is None:
            return
        
        # 计算切线模量
        tangent_modulus = np.gradient(stress_array, strain_array)
        
        # 计算有效模量
        effective_modulus = stress_array / (strain_array + 1e-10)  # 避免除零
        
        # 绘制分析结果
        plt.figure(figsize=(15, 10))
        
        # 应力-应变曲线
        plt.subplot(2, 3, 1)
        plt.plot(strain_array, stress_array, 'b-', linewidth=2)
        plt.xlabel('应变')
        plt.ylabel('应力 (MPa)')
        plt.title('应力-应变关系')
        plt.grid(True)
        
        # 损伤演化
        plt.subplot(2, 3, 2)
        plt.plot(strain_array, damage_array, 'r-', linewidth=2)
        plt.xlabel('应变')
        plt.ylabel('损伤变量')
        plt.title('损伤演化')
        plt.grid(True)
        
        # 切线模量
        plt.subplot(2, 3, 3)
        plt.plot(strain_array, tangent_modulus, 'g-', linewidth=2)
        plt.xlabel('应变')
        plt.ylabel('切线模量 (MPa)')
        plt.title('切线模量演化')
        plt.grid(True)
        
        # 有效应力
        plt.subplot(2, 3, 4)
        effective_stress = stress_array * (1 - damage_array)
        plt.plot(strain_array, stress_array, 'b-', linewidth=2, label='名义应力')
        plt.plot(strain_array, effective_stress, 'r--', linewidth=2, label='有效应力')
        plt.xlabel('应变')
        plt.ylabel('应力 (MPa)')
        plt.title('名义应力 vs 有效应力')
        plt.legend()
        plt.grid(True)
        
        # 损伤-应力关系
        plt.subplot(2, 3, 5)
        plt.scatter(damage_array, stress_array, c=strain_array, cmap='viridis', s=10)
        plt.xlabel('损伤变量')
        plt.ylabel('应力 (MPa)')
        plt.title('损伤-应力关系')
        plt.colorbar(label='应变')
        plt.grid(True)
        
        # 能量耗散
        plt.subplot(2, 3, 6)
        energy_density = np.trapz(stress_array, strain_array)
        cumulative_energy = np.cumsum(stress_array[:-1] * np.diff(strain_array))
        plt.plot(strain_array[:-1], cumulative_energy, 'purple', linewidth=2)
        plt.xlabel('应变')
        plt.ylabel('累积能量密度')
        plt.title(f'能量耗散 (总计: {energy_density:.2f})')
        plt.grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"材料响应分析图已保存至: {save_path}")
        
        plt.show()
        
        # 输出关键参数
        print("\n材料响应分析结果:")
        print(f"  初始模量: {tangent_modulus[0]:.2f} MPa")
        print(f"  最大应力: {np.max(stress_array):.2f} MPa")
        print(f"  最大损伤: {np.max(damage_array):.4f}")
        print(f"  能量密度: {energy_density:.4f}")

def main():
    """
    主预测流程示例
    """
    print("=" * 50)
    print("PINN模型预测分析")
    print("=" * 50)
    
    # 创建预测器
    predictor = PINNPredictor('d:/column/F3/concrete_pinn_model.pth')
    
    # 加载模型
    if not predictor.load_model():
        print("模型加载失败，程序退出")
        return
    
    # 设置数据统计信息（这些值应该从训练过程中获得）
    # 这里使用示例值，实际使用时应该从训练数据中获取
    predictor.set_data_stats(
        strain_min=0.0,
        strain_max=0.01,
        stress_min=0.0,
        stress_max=300.0
    )
    
    # 单点预测示例
    print("\n单点预测示例:")
    test_strains = [0.001, 0.003, 0.005, 0.008]
    for strain in test_strains:
        stress, damage = predictor.predict_single(strain)
        if stress is not None:
            print(f"  应变 {strain:.3f}: 应力 {stress:.2f} MPa, 损伤 {damage:.4f}")
    
    # 材料响应分析
    print("\n材料响应分析...")
    predictor.analyze_material_response(
        strain_range=(0, 0.012),
        save_path='material_response_analysis.png'
    )
    
    # 循环加载预测
    print("\n循环加载预测...")
    strain_cyclic, stress_cyclic, damage_cyclic = predictor.predict_cyclic_loading(
        max_strain=0.008, num_cycles=3, points_per_cycle=200
    )
    
    if strain_cyclic is not None:
        plt.figure(figsize=(12, 8))
        
        plt.subplot(2, 2, 1)
        plt.plot(strain_cyclic, stress_cyclic, 'b-', linewidth=1)
        plt.xlabel('应变')
        plt.ylabel('应力 (MPa)')
        plt.title('循环加载应力-应变响应')
        plt.grid(True)
        
        plt.subplot(2, 2, 2)
        plt.plot(range(len(damage_cyclic)), damage_cyclic, 'r-', linewidth=1)
        plt.xlabel('加载步数')
        plt.ylabel('损伤变量')
        plt.title('循环加载损伤演化')
        plt.grid(True)
        
        plt.subplot(2, 2, 3)
        plt.plot(range(len(strain_cyclic)), strain_cyclic, 'g-', linewidth=1)
        plt.xlabel('加载步数')
        plt.ylabel('应变')
        plt.title('应变加载历程')
        plt.grid(True)
        
        plt.subplot(2, 2, 4)
        plt.plot(range(len(stress_cyclic)), stress_cyclic, 'orange', linewidth=1)
        plt.xlabel('加载步数')
        plt.ylabel('应力 (MPa)')
        plt.title('应力响应历程')
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('cyclic_loading_prediction.png', dpi=300, bbox_inches='tight')
        print("循环加载预测图已保存至: cyclic_loading_prediction.png")
        plt.show()
        
        # 保存循环加载数据
        cyclic_data = pd.DataFrame({
            'strain': strain_cyclic,
            'stress': stress_cyclic,
            'damage': damage_cyclic
        })
        cyclic_data.to_excel('cyclic_loading_prediction.xlsx', index=False)
        print("循环加载预测数据已保存至: cyclic_loading_prediction.xlsx")
    
    print("\n" + "=" * 50)
    print("预测分析完成!")
    print("=" * 50)

if __name__ == "__main__":
    main()