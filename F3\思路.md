# 循环加卸载试验数据的损伤曲线拟合指南

<!-- 本文档介绍了循环加载试验数据的损伤本构参数拟合流程和公式，适用于材料本构建模与数据分析。 -->

---

## 1. 数据预处理
### 输入数据
- **原始数据**：循环加卸载试验中的力-位移（或应力-应变）曲线。
- **目标输出**：损伤变量 $d^\pm$ 随循环次数或应变累积的变化曲线。

### 数据分段
- 将每个循环分解为 **加载段**（应变增加）和 **卸载段**（应变减少）。
- 标记每个循环的 **起始点**（应变最小值）和 **峰值点**（应变最大值）。

---

## 2. 损伤参数定义与标定
| 参数             | 物理意义                                           | 标定方法                 |
| ---------------- | -------------------------------------------------- | ------------------------ |
| $r_0^\pm$        | 初始损伤阈值（对应线弹性极限强度）                 | 从试验曲线首次屈服点提取 |
| $A^\pm, B^\pm$   | 控制损伤演化曲线的形状参数（指数增长与饱和）       | 通过优化算法拟合试验数据 |
| $\mu^\pm, a^\pm$ | 动力加载下的阈值粘性演化系数（若试验含应变率效应） | 需应变率相关试验数据标定 |
| $\xi^\pm$        | 塑性应变演化系数（控制卸载残余变形）               | 根据卸载段残余应变标定   |

---

## 3. 损伤阈值更新规则
### 准静态加载（应变率无关）
- **触发条件**：在加载段中，当损伤能释放率满足 $Y^\pm \geq r^\pm$。
- **更新公式：**
  $$
  r^\pm_{\text{new}} = \max\left(r^\pm_{\text{old}}, Y^\pm\right)
  $$
  - 阈值仅增大不减小，反映不可逆损伤累积。

### 动力加载（应变率相关）
- **触发条件**：在加载段中，当 $\frac{Y^\pm}{r^\pm} > 1$。
- **更新公式（离散化后退欧拉法）：**
  $$
  r^\pm_{n+1} = r^\pm_n + \Delta t \cdot \mu^\pm \left\langle \frac{Y^\pm_n}{r^\pm_n} - 1 \right\rangle^{a^\pm}
  $$
  - 需根据试验应变率调整 $\mu^\pm$ 和 $a^\pm$。

---

## 4. 损伤变量计算流程
### 步骤说明
1. **初始化参数：**
   - 设置初始值 $r^\pm = r_0^\pm$, $d^\pm = 0$，加载段计数器 $k = 1$。
2. **遍历每个循环：**
   - **加载段：**
     1. 计算当前应变 $\varepsilon_k$ 和应力 $\sigma_k$。
     2. 分解应力张量 $\sigma^\pm$，计算损伤能释放率 $Y^\pm$（式15）：
        $$
        Y^+ = \sqrt{E_0 (\sigma^+ : A_0 : \sigma^+)}, \quad Y^- = \alpha I_1 + \sqrt{3 J_2}
        $$
     3. 判断阈值更新：若 $Y^\pm \geq r^\pm$，按规则更新 $r^\pm$。
     4. 更新损伤变量 $d^\pm$（式18-19）：
        $$
        d^\pm = 1 - \left( \frac{r_0^\pm}{r^\pm}(1 - A^\pm) + A^\pm \right) \exp\left(B^\pm\left(1 - \frac{r^\pm}{r_0^\pm}\right)\right)
        $$
   - **卸载段：**
     - 假设损伤不演化，保持当前 $d^\pm$。
3. **记录数据：**
   - 保存每个循环结束时的 $d^\pm$、$r^\pm$、$\varepsilon_k$、$\sigma_k$。

---

## 5. 损伤曲线拟合方法
### 目标函数
最小化模型预测应力与试验应力的均方误差：
$$
\text{误差} = \sum_{k=1}^{N} \left( \sigma_k^{\text{model}} - \sigma_k^{\text{test}} \right)^2
$$
其中 $\sigma_k^{\text{model}} = (1 - d^\pm) \sigma^{\text{eff}}$，$\sigma^{\text{eff}}$ 为无损材料应力。

### 优化算法
- **参数标定：**使用 **遗传算法**、**粒子群优化（PSO）** 或 **Levenberg-Marquardt** 算法。
- **迭代流程：**
  ```python
  for 参数组合 in 参数空间:
      计算所有循环的 d^± 和 σ_model
      计算误差
      保留误差最小的参数组合
  ```