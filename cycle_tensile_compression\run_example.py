"""
示例运行脚本
演示如何使用新的文件组织结构进行训练和预测
"""

import os
import sys
from pathlib import Path

def run_training_example():
    """
    运行训练示例
    """
    print("=" * 60)
    print("运行训练示例")
    print("=" * 60)
    
    # 检查数据文件是否存在
    data_file = "cyclic_data.xlsx"
    if not os.path.exists(data_file):
        print(f"警告: 数据文件 {data_file} 不存在")
        print("请确保有拉压循环实验数据")
        return False
    
    # 运行训练
    cmd = f"python main.py --train-only --data {data_file} --epochs 100"
    print(f"执行命令: {cmd}")
    result = os.system(cmd)
    
    if result == 0:
        print("✓ 训练示例完成成功")
        return True
    else:
        print("✗ 训练示例失败")
        return False


def run_prediction_example():
    """
    运行预测示例（使用最新的训练模型）
    """
    print("=" * 60)
    print("运行预测示例")
    print("=" * 60)
    
    # 寻找最新的训练结果
    session_dirs = list(Path("results").glob("session_*"))
    if not session_dirs:
        print("未找到训练结果，请先运行训练")
        return False
    
    latest_session = max(session_dirs, key=os.path.getctime)
    model_files = list(latest_session.glob("training/pinn_model_*.pth"))
    
    if not model_files:
        print(f"在 {latest_session} 中未找到模型文件")
        return False
    
    model_path = model_files[0]
    
    # 运行预测
    cmd = f"python main.py --predict-only --model-path {model_path}"
    print(f"执行命令: {cmd}")
    result = os.system(cmd)
    
    if result == 0:
        print("✓ 预测示例完成成功")
        return True
    else:
        print("✗ 预测示例失败")
        return False


def run_complete_example():
    """
    运行完整示例（训练+预测）
    """
    print("=" * 60)
    print("运行完整示例（训练+预测）")
    print("=" * 60)
    
    # 检查数据文件是否存在
    data_file = "cyclic_data.xlsx"
    if not os.path.exists(data_file):
        print(f"警告: 数据文件 {data_file} 不存在")
        print("请确保有拉压循环实验数据")
        return False
    
    # 运行完整流程
    cmd = f"python main.py --data {data_file} --epochs 100"
    print(f"执行命令: {cmd}")
    result = os.system(cmd)
    
    if result == 0:
        print("✓ 完整示例完成成功")
        return True
    else:
        print("✗ 完整示例失败")
        return False


def show_results_structure():
    """
    显示结果文件夹结构
    """
    print("=" * 60)
    print("结果文件夹结构")
    print("=" * 60)
    
    results_dir = Path("results")
    if not results_dir.exists():
        print("results/ 文件夹不存在")
        return
    
    session_dirs = list(results_dir.glob("session_*"))
    if not session_dirs:
        print("未找到会话文件夹")
        return
    
    # 显示最新的几个会话
    latest_sessions = sorted(session_dirs, key=os.path.getctime, reverse=True)[:3]
    
    for session_dir in latest_sessions:
        print(f"\n会话: {session_dir.name}")
        
        # 训练结果
        training_dir = session_dir / "training"
        if training_dir.exists():
            print("  训练结果:")
            for file_path in training_dir.iterdir():
                if file_path.is_file():
                    size_mb = file_path.stat().st_size / (1024 * 1024)
                    print(f"    {file_path.name} ({size_mb:.2f} MB)")
        
        # 预测结果
        prediction_dir = session_dir / "prediction"
        if prediction_dir.exists():
            print("  预测结果:")
            for file_path in prediction_dir.iterdir():
                if file_path.is_file():
                    size_mb = file_path.stat().st_size / (1024 * 1024)
                    print(f"    {file_path.name} ({size_mb:.2f} MB)")
        
        # 总结报告
        summary_file = session_dir / "session_summary.txt"
        if summary_file.exists():
            print(f"  总结报告: {summary_file.name}")


def create_demo_data():
    """
    创建演示数据（如果不存在实际数据）
    """
    print("=" * 60)
    print("创建演示数据")
    print("=" * 60)
    
    import numpy as np
    import pandas as pd
    
    # 生成简单的拉压循环数据
    n_points = 1000
    max_strain = 0.003
    
    # 创建拉压循环应变路径
    strain_path = []
    
    # 第一个循环：拉伸
    strain_path.extend(np.linspace(0, max_strain, n_points//8))
    strain_path.extend(np.linspace(max_strain, 0, n_points//8))
    
    # 第二个循环：压缩
    strain_path.extend(np.linspace(0, -max_strain, n_points//8))
    strain_path.extend(np.linspace(-max_strain, 0, n_points//8))
    
    # 第三个循环：更大幅度拉伸
    strain_path.extend(np.linspace(0, max_strain*1.5, n_points//8))
    strain_path.extend(np.linspace(max_strain*1.5, 0, n_points//8))
    
    # 第四个循环：更大幅度压缩
    strain_path.extend(np.linspace(0, -max_strain*1.5, n_points//8))
    strain_path.extend(np.linspace(-max_strain*1.5, 0, n_points//8))
    
    strain_array = np.array(strain_path)
    
    # 简化的应力计算（双线性）
    E0 = 30000  # MPa
    ft = 3.0    # MPa
    fc = 30.0   # MPa
    
    stress_array = np.zeros_like(strain_array)
    
    for i, strain in enumerate(strain_array):
        if strain > 0:  # 拉伸
            stress_array[i] = min(E0 * strain, ft * (1 - strain / (2 * max_strain)))
        else:  # 压缩
            stress_array[i] = max(E0 * strain, fc * strain / max_strain)
    
    # 添加一些噪声
    noise_level = 0.1
    stress_array += np.random.normal(0, noise_level, len(stress_array))
    
    # 保存到Excel
    df = pd.DataFrame({
        'strain': strain_array,
        'stress': stress_array
    })
    
    df.to_excel('cyclic_data.xlsx', index=False)
    print("✓ 演示数据已创建: cyclic_data.xlsx")
    print(f"  数据点数: {len(strain_array)}")
    print(f"  应变范围: {strain_array.min():.4f} ~ {strain_array.max():.4f}")
    print(f"  应力范围: {stress_array.min():.2f} ~ {stress_array.max():.2f} MPa")


def main():
    """
    主函数
    """
    print("混凝土拉压循环损伤参数识别系统 - 示例运行脚本")
    print("=" * 60)
    
    while True:
        print("\n请选择运行模式:")
        print("1. 创建演示数据")
        print("2. 运行完整示例（训练+预测）")
        print("3. 仅运行训练示例")
        print("4. 仅运行预测示例")
        print("5. 查看结果文件夹结构")
        print("6. 退出")
        
        choice = input("请输入选择 (1-6): ").strip()
        
        if choice == '1':
            create_demo_data()
        elif choice == '2':
            run_complete_example()
        elif choice == '3':
            run_training_example()
        elif choice == '4':
            run_prediction_example()
        elif choice == '5':
            show_results_structure()
        elif choice == '6':
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main() 