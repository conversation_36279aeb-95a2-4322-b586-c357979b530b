# -*- coding: utf-8 -*-
"""
字体配置模块

配置matplotlib使用中文字体，解决中文显示问题。
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import platform

def configure_chinese_font():
    """
    配置matplotlib使用中文字体
    """
    # 检测操作系统
    system = platform.system()
    
    # 查找系统中可用的中文字体
    chinese_fonts = [f.name for f in fm.fontManager.ttflist 
                    if 'SimHei' in f.name or 'Microsoft YaHei' in f.name or 'SimSun' in f.name]
    
    # 设置中文字体
    if system == 'Windows':
        # Windows系统优先使用微软雅黑，其次是SimHei(黑体)
        if chinese_fonts:
            # 使用系统中实际存在的中文字体
            plt.rcParams['font.sans-serif'] = chinese_fonts + ['Arial Unicode MS', 'Arial']
        else:
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'Arial Unicode MS', 'Arial']
    elif system == 'Darwin':  # macOS
        plt.rcParams['font.sans-serif'] = ['PingFang SC', 'Heiti SC', 'STHeiti', 'Arial Unicode MS', 'Arial']
    else:  # Linux等其他系统
        plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'Droid Sans Fallback', 'Arial Unicode MS', 'Arial']
    
    # 解决负号显示问题
    plt.rcParams['axes.unicode_minus'] = False
    
    # 设置DPI以获得更清晰的图像
    plt.rcParams['figure.dpi'] = 300
    
    # 设置全局字体大小
    plt.rcParams['font.size'] = 12
    plt.rcParams['axes.titlesize'] = 14
    plt.rcParams['axes.labelsize'] = 12
    
    # 使用英文标签替代中文标签，避免字体问题
    plt.rcParams['axes.formatter.use_locale'] = False
    
    # 强制使用指定的字体
    plt.rcParams['font.family'] = 'sans-serif'
    
    print(f"已配置字体: {plt.rcParams['font.sans-serif'][0]}")
    print(f"可用中文字体: {chinese_fonts}")

# 当直接运行此文件时，测试字体配置
if __name__ == "__main__":
    configure_chinese_font()
    
    # 创建一个测试图表
    plt.figure(figsize=(10, 6))
    plt.plot([1, 2, 3, 4], [1, 4, 9, 16], 'ro-')
    plt.title('Font Test')
    plt.xlabel('X-axis')
    plt.ylabel('Y-axis')
    plt.grid(True)
    
    # 保存测试图表
    test_image_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'font_test.png')
    plt.savefig(test_image_path)
    print(f"测试图表已保存至: {test_image_path}")