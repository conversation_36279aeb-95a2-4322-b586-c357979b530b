好的，我已经仔细阅读了你提供的项目阶段性成果、代码文件和参考文献。你目前已经构建了一个功能强大的PINN框架，实现了单轴受拉下的混凝土损伤参数识别和滞回行为预测，并且在物理忠实度方面做得很好。

接下来，我们将扩展模型以处理受压加载，实现能够拟合受拉受压全滞回曲线的目标。这个任务的核心在于：

1.  **数据识别与处理：** 如何从应变-应力数据中准确识别出加载方向（受拉/受压）以及加卸载状态。
2.  **模型扩展：** PINN模型如何同时学习和预测受拉和受压状态下的物理响应。
3.  **物理约束的融入：** 如何根据参考文献中的统一本构模型，在PINN中正确引入受压状态的损伤演化和塑性行为，并加入相应的可训练参数。
4.  **损失函数调整：** 引入新的损失项或调整现有损失项，以确保模型在受压状态下的物理准确性。

下面我将为你梳理详细的思路和新的脚本实现逻辑。

---

### **项目扩展：引入受压加载与全滞回曲线建模**

#### **一、目标与挑战**

**目标：**
在现有PINN框架基础上，实现混凝土材料在单轴反复拉压作用下的全滞回曲线建模，能够端到端识别受拉和受压状态下的损伤和塑性参数，并准确预测其力学响应。

**主要挑战：**
1.  **加载路径的复杂性：** 循环拉压加载涉及应变方向的变化，需要精确判断加载状态（加载/卸载、受拉/受压）。
2.  **受拉与受压机制的差异：** 混凝土在受拉和受压下的损伤和塑性机制存在显著差异，需要引入独立的参数和演化法则。
3.  **单边效应（Unilateral Effect）：** 卸载后再加载时，由于裂缝闭合，材料刚度会部分或完全恢复，这需要模型能够捕捉。
4.  **物理约束的统一性：** 如何在PINN框架下，统一处理受拉和受压的不同物理演化规律，并保证整体模型的物理一致性。

#### **二、参考文献中的关键物理模型回顾（统一模型）**

你提供的文献《混凝土弹塑性损伤本构关系统一模型_吴建营.pdf》和《混凝土弹塑性损伤本构模型研究\_\_Ⅰ\_基本公式\_李杰.pdf》是核心。我们将主要参考其中的统一本构模型，特别是关于**张量正负分解**、**受拉/受剪损伤变量**、**塑性应变演化**和**损伤演化法则**。

**关键点：**
* [cite_start]**应力/应变张量正负分解：** 文献 [cite: 5] 强调了将应力/应变张量分解为正（拉伸）和负（压缩）分量的重要性，以描述混凝土在拉压下的不同特性。在单轴情况下，这简化为判断应力/应变的符号。
* [cite_start]**受拉损伤 ($d^+$) 与受剪损伤 ($d^-$)：** 文献 [cite: 89, 94, 100] 中明确指出，混凝土的损伤主要源于受拉损伤（微裂缝张开）和受剪损伤（微裂缝滑移或压碎）。我们需要引入这两个独立的损伤变量，并分别建模其演化。
* [cite_start]**损伤演化法则：** 文献 [cite: 11, 18, 39, 102] 给出了损伤变量 $d^+$ 和 $d^-$ 的具体演化公式，它们是基于损伤能释放率 $Y^+$ 和 $Y^-$ 以及其阈值 $r^+$ 和 $r^-$ 进行定义的。
    * 对于 $d^+$ (受拉损伤)，公式为：
        [cite_start]$d^{+} = 1 - \frac{r_{0}^{+}}{r^{+}}\left((1-A^{+}) + A^{+} \exp\left[B^{+}\left(1-\frac{r^{+}}{r_{0}^{+}}\right)\right]\right)$ [cite: 18]
    * 对于 $d^-$ (受剪损伤)，公式为：
        [cite_start]$d^{-} = 1 - \frac{r_{0}^{-}}{r^{-}}\left((1-A^{-}) + A^{-} \exp\left[B^{-}\left(1-\frac{r^{-}}{r_{0}^{-}}\right)\right]\right)$ [cite: 19]
    * [cite_start]其中，$r_{0}^{+}$ 和 $r_{0}^{-}$ 是初始损伤能释放率阈值，通常 $r_{0}^{+} = f_t$ [cite: 20][cite_start]，$r_{0}^{-} = (1-\alpha)f_c$ (或 $0.3f_c \sim 0.5f_c$) [cite: 20]。
    * $A^+$, $B^+$, $A^-$, $B^-$ 是待识别的材料参数。
* [cite_start]**塑性应变演化：** 文献 [cite: 8] [cite_start]提到塑性应变张量的演化法则可以通过有效应力空间塑性力学方法确定。对于简化模型，文献 [cite: 9] 提供了塑性应变增量的简化公式：$\Delta\varepsilon^p = \xi H(d) \frac{<\bar{\sigma}:\dot{\varepsilon}>}{\bar{\sigma}:\bar{\sigma}}$。在我们的增量式框架中，可以简化为：
    * $\Delta\varepsilon^p_i = \xi^+ \cdot \max(0, \Delta\varepsilon_i)$ (受拉塑性)
    * $\Delta\varepsilon^p_i = \xi^- \cdot \min(0, \Delta\varepsilon_i)$ (受压塑性)
    * [cite_start]注意，这里的 $\xi$ 可能需要区分受拉和受压。文献 [cite: 10] 也提到了 $E^{p\pm}$ 来反映塑性硬化的程度，这暗示了拉压塑性参数的独立性。
* **本构关系：** 统一的本构关系为：
    [cite_start]$\sigma = (1-d^+)\bar{\sigma}^+ + (1-d^-)\bar{\sigma}^-$ [cite: 7]
    在单轴情况下，如果 $\varepsilon > 0$（受拉），则 $\sigma = (1-d^+)E_0(\varepsilon - \varepsilon^p)$；如果 $\varepsilon < 0$（受压），则 $\sigma = (1-d^-)E_0(\varepsilon - \varepsilon^p)$。这将由当前应变增量和历史应力路径共同决定。

#### **三、新的脚本思路与代码实现**

我们将主要修改 `pinn_model_v2.py` (模型和物理计算器) 和 `train.py` (训练逻辑)。`data_processor.py` 可能需要增加对循环加载数据（如果提供）的处理，而 `predict_cyclic_tensile_fixed.py` 将成为新的全滞回预测脚本的基础。

**1. `data_processor.py` (数据预处理)**

* **新增功能：** 如果未来有循环拉压实验数据，需要识别加载/卸载段。
* **当前处理：** 你的`tension.xlsx`是单调拉伸，已经处理为应变增量。对于循环拉压数据，我们可能需要：
    * 判断每一步的应变增量 $\Delta\varepsilon_i$ 的符号，以识别是拉伸加载/卸载还是压缩加载/卸载。
    * 虽然当前模型是在时间步上进行累积，但对于真实的循环加载，还需要记录每个点的总应变，以便在物理计算中判断加载/卸载状态。

**2. `pinn_model_v2.py` (PINN模型与物理计算器)**

这是修改的核心。

**A. `DamagePINNV2` 类（PINN 模型）**

* **输出层：** 保持 `output_size=3` (应力、损伤、塑性应变增量系数)。
* **核心修改：** 模型的前向传播函数 `forward` 需要根据当前应变状态输出合适的损伤和塑性。
    * **损伤：**
        * 输出层可能需要直接输出受拉损伤的参数 $A^+, B^+$ 和受压损伤的参数 $A^-, B^-$。
        * 或者，让网络输出一个通用损伤变量 `d_hat`，但在损失函数中，根据物理规则，在拉伸区惩罚其与 `d_phy+` 的差异，在压缩区惩罚其与 `d_phy-` 的差异。**建议后者**，让PINN更灵活地学习。
    * **塑性应变：** `xi_seq` 可以是区分拉压的。模型可以直接输出两个xi，或者输出一个xi，然后在物理计算器中根据应变增量符号应用。**建议让模型输出一个xi，在物理计算器中处理拉压区分。**

    修改后的 `forward` 方法可能不需要大的变动，因为它输出的是 `sigma_seq`, `d_seq`, `xi_seq`，这些可以被认为是当前步的预测值，具体的物理约束和拉压区分在 `PhysicsCalculatorV2` 中处理。

**B. `PhysicsCalculatorV2` 类（物理约束计算器）**

这将是主要修改的地方，以实现统一的弹塑性损伤本构。

* **初始化：**
    * 保留 `E0`, `f_t`。
    * 新增 `f_c` (单轴抗压强度)，用于计算受压损伤的初始阈值 $r_0^-$。
* **`calculate_physics_constraints` 方法：**
    * **输入参数：** 需要接受 `A_plus`, `B_plus`, `A_minus`, `B_minus`, `xi_plus`, `xi_minus` (或者一个统一的xi，在内部判断)。
    * **状态变量：**
        * `epsilon_phy`: 累积总应变（从应变增量积分得到）。
        * `ep_phy`: 累积总塑性应变。
        * `d_plus_phy`: 累积受拉损伤。
        * `d_minus_phy`: 累积受压/受剪损伤。
        * `r_max_plus_phy`: 历史最大拉伸损伤阈值（对应 $Y^+$）。
        * `r_max_minus_phy`: 历史最大压缩损伤阈值（对应 $Y^-$）。
    * **循环积分逻辑（核心）：**
        对每个 `delta_epsilon`：
        1.  **更新总应变：** `epsilon_phy += delta_epsilon`
        2.  **判断当前状态（拉/压，加/卸载）：**
            * **应变正负号：** `is_tensile = (epsilon_phy - ep_phy) > 0` (判断有效应变方向) 或者 `is_tensile_increment = delta_epsilon > 0` (判断应变增量方向，更符合循环加载)。
            * **加载/卸载：** 需要维护一个历史最大/最小应变或应力，并根据当前应变与这些极值的关系来判断。对于简化模型，可以根据应变增量符号判断是否为新的加载段。
        3.  **计算损伤驱动力 $Y^+$ 和 $Y^-$：**
            * [cite_start]$Y_{plus} = E_0 * \max(0.0, \text{current_elastic_strain})$ (或 $Y_{plus} = E_0 * (\text{current_strain} - \text{current_plastic_strain})$，但要考虑拉压分解 [cite: 15])
            * [cite_start]$Y_{minus} = E_0 * \min(0.0, \text{current_elastic_strain})$ (或更复杂的 Drucker-Prager 形式 [cite: 95, 102])
            * **简化处理：** 在单轴情况下，可以只用弹性应变 $\varepsilon^e = \varepsilon - \varepsilon^p$ 的绝对值来计算总的损伤驱动力 $Y = E_0 |\varepsilon^e|$。然后，根据 $\varepsilon^e$ 的正负来决定更新 $d^+$ 或 $d^-$。
                * 如果 $\varepsilon^e > 0$（受拉状态）：更新 $d^+$
                * 如果 $\varepsilon^e < 0$（受压状态）：更新 $d^-$
            * [cite_start]**更接近文献的实现：** 文献 [cite: 15] 中 $Y^+$ 和 $Y^-$ 的定义包含了应力张量的正负分量。在单轴情况下：
                * $Y^+ = \sqrt{E_0 \cdot \max(0, \sigma_{\text{effective}} \cdot \varepsilon_{\text{elastic}})}$
                * [cite_start]$Y^- = a \bar{I}_1 + \sqrt{3\bar{J}_2}$ [cite: 99] (此为多轴情况，单轴受压可简化)
                * **建议：** 为了简化，我们可以设定一个**单轴受拉物理损伤**和**单轴受压物理损伤**。
                    * 对于拉伸段：`Y_phy_plus = E0 * max(0.0, epsilon_phy - ep_phy)`
                    * 对于压缩段：`Y_phy_minus = E0 * abs(min(0.0, epsilon_phy - ep_phy))` (或者用一个与压应力相关的表达式)
        4.  **条件性地更新 $d^+$ 和 $r_{max}^+$：**
            `if Y_phy_plus > r_max_plus_phy:`
                `r_max_plus_phy = Y_phy_plus`
                `d_plus_phy = 1 - (f_t / r_max_plus_phy) * ((1 - A_plus) + A_plus * torch.exp(B_plus * (1 - r_max_plus_phy / f_t)))`
                `d_plus_phy = torch.clamp(d_plus_phy, 0.0, 1.0)`
            `else:`
                `d_plus_phy = d_plus_phy` (保持不变)
        5.  **条件性地更新 $d^-$ 和 $r_{max}^-$：** (这里需要定义 $f_c$ 和新的参数 $A_{minus}, B_{minus}$ 和 $r_0^-$)
            `if Y_phy_minus > r_max_minus_phy:`
                `r_max_minus_phy = Y_phy_minus` (需要一个基于受压的 $f_c$ 的初始阈值)
                `d_minus_phy = 1 - (f_c / r_max_minus_phy) * ((1 - A_minus) + A_minus * torch.exp(B_minus * (1 - r_max_minus_phy / f_c)))`
                `d_minus_phy = torch.clamp(d_minus_phy, 0.0, 1.0)`
            `else:`
                `d_minus_phy = d_minus_phy` (保持不变)
        6.  **更新塑性应变：**
            * 考虑拉压塑性系数 `xi_plus` 和 `xi_minus`：
                `if delta_epsilon > 0:`
                    `delta_ep = xi_plus * delta_epsilon`
                `else:`
                    `delta_ep = xi_minus * delta_epsilon` (这里xi_minus本身可以是负值来确保塑性累积方向正确)
                `ep_phy += delta_ep`
        7.  **单边效应处理：**
            * 当从受拉卸载到受压，或从受压卸载到受拉时，弹性应变恢复。
            * **损伤：** 损伤变量是不可逆的，一旦产生就不会减小。因此，在物理计算中，`d_plus_phy` 和 `d_minus_phy` 应该始终取其历史最大值，除非是新的加载方向突破了旧的损伤阈值。你目前的 `if Y > r_max` 逻辑已经包含了这一不可逆性。
            * **应力：** * 如果当前是受拉区 (`epsilon_phy - ep_phy > 0`)，则有效损伤为 `d_plus_phy`。
                * 如果当前是受压区 (`epsilon_phy - ep_phy < 0`)，则有效损伤为 `d_minus_phy`。
                * `d_effective = d_plus_phy if (epsilon_phy - ep_phy) > 0 else d_minus_phy`
                * `stress_phy = (1 - d_effective) * E0 * (epsilon_phy - ep_phy)`
                * **重要：** 考虑卸载刚度，在卸载路径上，`d_effective` 应该保持在卸载前的峰值损伤。这需要一个更复杂的逻辑来跟踪当前的加载/卸载路径。
                * **简化方案 (已在 `predict_cyclic_tensile_fixed.py` 中实现)：** 在卸载时，损伤不增加，塑性应变也不增加。新的 `predict_cyclic_tensile_fixed.py` 中的 `predict_response_fixed` 方法已经使用了这种简化物理模型，你可以借鉴其实现来改造 `PhysicsCalculatorV2`。即，在计算 `d_phy` 和 `ep_phy` 时：
                    * `if strain_increment > 0:` (受拉加载) 损伤和塑性可能发展。
                    * `if strain_increment < 0:` (受压加载) 损伤和塑性可能发展。
                    * `if current_stress * current_strain_increment < 0:` (卸载) 损伤和塑性不发展。
                    * **最简单的处理（符合你的代码逻辑）：** 你的 `calculate_physics_constraints` 已经对 `delta_epsilon` 进行了 `relu` 处理塑性，所以 `ep_increment = xi_seq * torch.relu(strain_increment)` 只在 `strain_increment > 0` 时累积，这相当于只考虑了拉伸塑性。你需要对负向增量也考虑塑性。

        **输出：** `d_plus_phy_seq`, `d_minus_phy_seq`, `ep_phy_seq`, `stress_phy_seq` (可选，但推荐计算用于可视化)。

**3. `train.py` (训练逻辑)**

* **新增可训练物理参数：**
    * `self.A_minus = torch.nn.Parameter(torch.tensor(..., requires_grad=True))` (例如，初始值设置为1.0)
    * `self.B_minus = torch.nn.Parameter(torch.tensor(..., requires_grad=True))` (例如，初始值设置为0.2)
    * `self.xi_minus = torch.nn.Parameter(torch.tensor(..., requires_grad=True))` (例如，初始值设置为-0.01 或 0.01，取决于你的塑性应变定义)
    * 将这些新参数加入到 `optimizer` 中。
* **物理常量的更新：** `self.config['f_c']` 应该从数据中估计或预设。
* **`train_epoch` 函数：**
    * **步骤B（物理约束计算）：** 调用 `self.physics_calc.calculate_physics_constraints` 时，传入 `self.A_minus`, `self.B_minus`, `self.xi_minus`。
    * **损失函数（步骤C）：**
        * `loss_damage`: 需要区分拉压损伤。
            * `loss_damage_plus = torch.mean((d_hat_seq - d_plus_phy_seq)**2)` (仅在拉伸应变区域计算)
            * `loss_damage_minus = torch.mean((d_hat_seq - d_minus_phy_seq)**2)` (仅在压缩应变区域计算)
            * 或者，让 `d_hat_seq` 在拉伸时拟合 `d_plus_phy_seq`，在压缩时拟合 `d_minus_phy_seq`。这需要根据当前的应变符号动态选择 `d_phy_seq`。
            * **更通用的做法：** 神经网络输出一个 `d_hat_seq`。在 `PhysicsCalculatorV2` 中计算出 `d_plus_phy_seq` 和 `d_minus_phy_seq`。在损失函数中，如果 `strain_total_exp_seq > 0`，则 `loss_damage_tensile = (d_hat_seq - d_plus_phy_seq)**2`；如果 `strain_total_exp_seq < 0`，则 `loss_damage_compressive = (d_hat_seq - d_minus_phy_seq)**2`。
        * `loss_plastic`: 同样需要区分拉压塑性。
            * `loss_plastic_plus = torch.mean((ep_hat_seq - ep_plus_phy_seq)**2)`
            * `loss_plastic_minus = torch.mean((ep_hat_seq - ep_minus_phy_seq)**2)`
            * **建议：** 你的 `ep_hat_seq` 是累积塑性应变，它应该在拉伸和压缩载荷下都累积。物理模型中的 `ep_phy_seq` 也应该是累积的。因此 `loss_plastic = torch.mean((ep_hat_seq - ep_phy_seq)**2)` 保持不变，但 `ep_phy_seq` 的计算需要同时考虑拉压塑性系数。
        * **总损失：** 权重需要重新平衡。
* **参数约束：**
    * `self.A_minus.data.clamp_(...)`
    * `self.B_minus.data.clamp_(...)`
    * `self.xi_minus.data.clamp_(...)`
* **保存最终模型和参数：** 在 `save_final_model` 中加入 `A_minus`, `B_minus`, `xi_minus`。
* **绘图：** 增加受压参数的演化曲线图。

**4. `predict_cyclic_tensile_fixed.py` (预测脚本)**

这个脚本需要被改造以处理全滞回曲线。

* **重命名：** 考虑更名为 `predict_cyclic.py` 或 `predict_hysteresis.py`。
* **`load_model`：** 需要加载新的受压参数。
* **`generate_realistic_cyclic_loading_path`：** 这个函数已经考虑了残余应变累积，但需要确保其内部的塑性估算逻辑也能处理受压塑性。当前代码中的 `plastic_strain_increment = xi_effective * strain_increment_total` 仍然只适用于拉伸，需要改为：
    * `if strain_increment_total > 0: plastic_strain_increment = xi_plus_effective * strain_increment_total`
    * `else: plastic_strain_increment = xi_minus_effective * strain_increment_total` (注意符号和 `xi_minus_effective` 的定义)
* **`predict_response_fixed`：** 这是预测的核心，需要进行重大修改。
    * **状态变量：** `r_max_plus`, `r_max_minus` (拉压最大损伤阈值)。
    * **循环逻辑：**
        1.  **应变增量：** `strain_increment = strain_path[i] - strain_path[i-1]`
        2.  **当前有效应变和应力：** 这是一个关键点。在循环加载中，有效应变应该是当前总应变减去当前塑性应变。应力计算应该根据有效应变的正负来使用对应的损伤变量。
        3.  **损伤更新（核心）：**
            * **受拉加载 ($strain\_increment > 0$ 且有效应变为拉伸)：** * 计算 $Y^+$。
                * `if Y_plus > r_max_plus:` 更新 $r_max_plus$ 和 `d_plus`。
            * **受压加载 ($strain\_increment < 0$ 且有效应变为压缩)：**
                * 计算 $Y^-$（需要根据文献定义其具体形式，可以是基于压应变或剪切应变的驱动力）。
                * `if Y_minus > r_max_minus:` 更新 $r_max_minus$ 和 `d_minus`。
            * **卸载阶段：** 损伤不发展，保持上一个加载峰值的损伤值。
                * 你目前的 `damage[i] = damage[i-1]` 逻辑在卸载时是正确的。
        4.  **塑性应变更新：**
            * `if strain_increment > 0:`
                `xi_effective_plus = xi_plus_base * (1 + d_plus[i] * X)` (这里的 X 是一个权重因子)
                `plastic_strain[i] = plastic_strain[i-1] + xi_effective_plus * strain_increment`
            * `elif strain_increment < 0:`
                `xi_effective_minus = xi_minus_base * (1 + d_minus[i] * Y)` (这里的 Y 是一个权重因子)
                `plastic_strain[i] = plastic_strain[i-1] + xi_effective_minus * strain_increment`
            * `else:` (应变增量为零)
                `plastic_strain[i] = plastic_strain[i-1]`
        5.  **应力计算：**
            * `effective_elastic_strain = strain_path[i] - plastic_strain[i]`
            * `if effective_elastic_strain > 0:` (当前为有效拉伸状态)
                `stress[i] = (1 - d_plus[i]) * E0 * effective_elastic_strain`
            * `else:` (当前为有效压缩状态)
                `stress[i] = (1 - d_minus[i]) * E0 * effective_elastic_strain`
                * **注意：** 这里的 `d_plus[i]` 和 `d_minus[i]` 应该是实际在加载过程中累积的损伤值，而不是当前步预测的。
            * **卸载刚度（单边效应）：** 在卸载路径上，实际刚度应该沿着弹性模量 $E_0$ 退化后的 $(1-d)E_0$ 路径。你的 `predict_response_fixed` 中的 `stress[i] = max(0, stress[i])` 这句是粗略的单边效应处理，在更精确的模型中，应力应该沿卸载模量下降，直到残余应变。这部分需要更精细的逻辑来跟踪**卸载点**的**有效损伤**。

**5. 统一物理模型（伪代码示例，以辅助理解 `PhysicsCalculatorV2` 的实现）**

```python
# PhysicsCalculatorV2 中的循环积分逻辑

def calculate_physics_constraints_full_cycle(self, strain_increment_seq, 
                                            A_plus, B_plus, A_minus, B_minus, 
                                            xi_plus, xi_minus, f_c):
    
    # 初始化 t=0 时刻的状态变量
    epsilon_total = torch.tensor(0.0)      # 累积总应变
    ep_total = torch.tensor(0.0)           # 累积塑性应变
    d_plus = torch.tensor(0.0)             # 累积受拉损伤
    d_minus = torch.tensor(0.0)            # 累积受压损伤
    r_max_plus = torch.tensor(self.f_t)    # 历史最大拉伸损伤阈值 (初始为ft)
    r_max_minus = torch.tensor(self.f_t)   # 历史最大压缩损伤阈值 (初始可设为ft或根据fc调整)
                                           # [cite_start]文献中r_0^- = (1-alpha)f_c [cite: 100]

    # 用于存储每一步计算结果的列表
    d_plus_phy_seq, d_minus_phy_seq, ep_phy_seq = [], [], []
    stress_phy_seq = [] # 可选，用于可视化

    # 为了处理单边效应和卸载，需要跟踪历史加载方向的峰值应变和损伤
    # 简化：只跟踪当前加载路径的峰值（这与你的predict_cyclic_tensile_fixed.py逻辑类似）
    max_strain_ever = torch.tensor(0.0)
    min_strain_ever = torch.tensor(0.0) # For compression
    current_loading_direction = 0 # 1 for loading, -1 for unloading

    for i, delta_epsilon in enumerate(strain_increment_seq):
        # 1. 更新总应变
        epsilon_total += delta_epsilon

        # 2. 计算当前有效应变 (弹性应变)
        current_elastic_strain = epsilon_total - ep_total

        # 3. 损伤演化（基于有效应变的正负判断）
        if current_elastic_strain >= 0: # 拉伸区域
            Y_plus_current = self.E0 * current_elastic_strain
            if Y_plus_current > r_max_plus: # 新的损伤加载
                r_max_plus = Y_plus_current
                # [cite_start]应用受拉损伤演化公式 [cite: 18]
                term1 = self.f_t / r_max_plus * (1 - A_plus)
                term2 = A_plus * torch.exp(B_plus * (1 - r_max_plus / self.f_t))
                d_plus = 1 - (term1 + term2)
                d_plus = torch.clamp(d_plus, 0.0, 1.0)
            # else: d_plus 保持不变 (卸载或未达到新阈值)
        else: # 压缩区域
            # [cite_start]这里的Y_minus_current的定义需要更精确，可能与应力偏量有关 [cite: 99]
            # 简化为与压应变相关：
            Y_minus_current = self.E0 * abs(current_elastic_strain) 
            # [cite_start]初始压缩损伤阈值 r_0^- 应该与 f_c 相关 [cite: 100]
            # 比如，r_max_minus_initial = (1.0 - alpha) * f_c (需要定义alpha)
            # 或者直接初始化为 f_c
            if i == 0: # 首次进入压缩区，或模型初始化
                 # 初始化 r_max_minus 为 f_c (或其对应能量值)
                 # 这里r_max_minus不能直接用f_t初始化了
                 r_max_minus = self.f_c # 简化，如果模型从压缩开始，或者从拉伸转入压缩
                 # [cite_start]更严谨的 r_max_minus_initial 应该用文献中的 f_0^- [cite: 100]
            
            if Y_minus_current > r_max_minus: # 新的损伤加载
                r_max_minus = Y_minus_current
                # [cite_start]应用受压损伤演化公式 [cite: 19]
                term1 = self.f_c / r_max_minus * (1 - A_minus) # Use f_c for compression
                term2 = A_minus * torch.exp(B_minus * (1 - r_max_minus / self.f_c))
                d_minus = 1 - (term1 + term2)
                d_minus = torch.clamp(d_minus, 0.0, 1.0)
            # else: d_minus 保持不变 (卸载或未达到新阈值)

        # 4. 塑性应变演化
        # 根据应变增量符号应用不同的塑性系数
        if delta_epsilon > 0: # 拉伸加载
            # 塑性发展因子可以考虑损伤影响 (如 (1 + d_plus * 2.0) )
            # 你的框架中 ep_increment = xi_seq * torch.relu(strain_increment)
            # xi_plus 可以是模型输出的 xi，或者单独的可训练参数
            delta_ep = xi_plus * torch.relu(delta_epsilon) 
        elif delta_epsilon < 0: # 压缩加载
            # xi_minus 可以是负值，以使 delta_ep 为负，然后 ep_total 累积
            delta_ep = xi_minus * delta_epsilon # delta_epsilon本身为负，xi_minus为正
            # 或者 delta_ep = abs(xi_minus) * abs(delta_epsilon)
            # 检查xi_minus的符号定义，确保ep_total累积正确
        else:
            delta_ep = torch.tensor(0.0)
        
        ep_total += delta_ep

        # 5. 记录当前步的物理状态
        d_plus_phy_seq.append(d_plus.clone())
        d_minus_phy_seq.append(d_minus.clone())
        ep_phy_seq.append(ep_total.clone())

        # 6. 计算物理应力（用于L_stress和可视化）
        # 这里需要根据当前有效应变的正负选择使用哪个损伤变量
        current_d_effective = d_plus if current_elastic_strain >= 0 else d_minus
        current_stress_phy = (1 - current_d_effective) * self.E0 * current_elastic_strain
        
        stress_phy_seq.append(current_stress_phy.clone())

    return torch.stack(d_plus_phy_seq), torch.stack(d_minus_phy_seq), torch.stack(ep_phy_seq), torch.stack(stress_phy_seq)

# LossCalculator 中的修改：
def calculate_total_loss(self, sigma_hat_seq, d_hat_seq, ep_hat_seq, 
                       stress_exp_seq, strain_total_seq, 
                       d_plus_phy_seq, d_minus_phy_seq, ep_phy_seq, E0):
    
    # L_data: 数据拟合损失
    loss_data = torch.mean((sigma_hat_seq - stress_exp_seq)**2)
    
    # L_stress: 本构自洽损失
    # 动态选择损伤变量 (根据实验总应变来判断拉压状态)
    d_effective_for_stress = torch.where(strain_total_seq >= 0, d_hat_seq, d_hat_seq) # 或者更精确，根据神经网络内部判断
    stress_physics = (1 - d_effective_for_stress) * E0 * (strain_total_seq - ep_hat_seq)
    loss_stress = torch.mean((sigma_hat_seq - stress_physics)**2)
    
    # L_damage: 损伤物理损失 - 分为拉伸和压缩部分
    # 根据实验应变符号，动态惩罚 d_hat_seq 与 d_plus_phy_seq 或 d_minus_phy_seq 的差异
    damage_loss_mask_plus = (strain_total_seq >= 0).float()
    damage_loss_mask_minus = (strain_total_seq < 0).float() # 或者 (stress_exp_seq < 0).float()
    
    # 如果 d_hat_seq 是一个通用损伤，它在拉伸区应接近 d_plus_phy，在压缩区应接近 d_minus_phy
    loss_damage_plus = torch.mean((d_hat_seq - d_plus_phy_seq)**2 * damage_loss_mask_plus)
    loss_damage_minus = torch.mean((d_hat_seq - d_minus_phy_seq)**2 * damage_loss_mask_minus)
    loss_damage = self.lambda_damage * (loss_damage_plus + loss_damage_minus) # 合并或加权

    # L_plastic: 塑性物理损失 (ep_hat_seq 是累积塑性，直接与 ep_phy_seq 对比)
    loss_plastic = torch.mean((ep_hat_seq - ep_phy_seq)**2)
    
    # 总损失
    total_loss = (self.lambda_data * loss_data + 
                 self.lambda_stress * loss_stress + 
                 loss_damage + # 这里已经包含了lambda_damage
                 self.lambda_plastic * loss_plastic)
    
    return total_loss, {
        'loss_data': loss_data.item(),
        'loss_stress': loss_stress.item(), 
        'loss_damage_plus': loss_damage_plus.item(), # 记录细分损失
        'loss_damage_minus': loss_damage_minus.item(),
        'loss_plastic': loss_plastic.item(),
        'total_loss': total_loss.item()
    }
```

#### **四、实施步骤与建议**

1.  **数据准备：**
    * 你需要准备包含循环拉压应力-应变数据的Excel文件。文件仍应包含 'strain' 和 'stress' 两列。
    * 在 `data_processor.py` 中，你可以新增一个函数来加载这种循环数据，并确保能正确计算 `strain_increment`。

2.  **修改 `pinn_model_v2.py`：**
    * 在 `PhysicsCalculatorV2` 的 `__init__` 中添加 `f_c`。
    * 修改 `PhysicsCalculatorV2` 的 `calculate_physics_constraints` 函数，按照上述伪代码实现双向损伤和塑性演化。
        * 初始化 `r_max_minus` 时，使用 `f_c` 或 `(1-alpha)*f_c`。
        * 确保 `d_plus` 和 `d_minus` 独立累积。
        * 塑性应变 `ep_total` 的累积要同时考虑 `xi_plus` 和 `xi_minus`。
    * 修改 `LossCalculator` 的 `calculate_total_loss` 函数，区分拉压损伤损失。

3.  **修改 `train.py`：**
    * 在 `Trainer` 的 `__init__` 中新增 `f_c` 到 `self.config`。
    * 在 `initialize_model` 中新增 `A_minus`, `B_minus`, `xi_minus` 作为可训练参数，并添加到优化器中。
    * 在 `load_data` 后，你需要根据实际数据更新 `self.config['f_c']`。
    * 在 `train_epoch` 中，修改 `physics_calc.calculate_physics_constraints` 的调用参数，并调整 `total_loss` 的计算。
    * 在 `save_final_model` 中保存新的参数。
    * 在 `plot_training_history` 中增加新的参数演化曲线。

4.  **修改 `predict_cyclic_tensile_fixed.py`：**
    * 重命名为更通用的预测脚本。
    * 修改 `load_model` 以加载所有新的物理参数。
    * **核心：** 彻底改造 `predict_response_fixed` 函数，使其内部的物理演化逻辑与 `PhysicsCalculatorV2` 中实现的统一本构逻辑一致。这包括：
        * 维护 `r_max_plus` 和 `r_max_minus`。
        * 根据当前应变状态（拉伸/压缩）和加载/卸载状态来更新 `d_plus` 和 `d_minus`。
        * 正确累积 `plastic_strain`。
        * 根据有效应变方向和相应的损伤变量来计算 `stress`。
    * `generate_realistic_cyclic_loading_path` 也要确保估算的残余应变能正确反映拉压塑性累积。
    * 绘图函数也要更新以反映新的参数和全滞回曲线。

5.  **开始训练和预测：**
    * 使用新的循环加载数据进行训练。
    * 使用新的预测脚本进行全滞回曲线预测。

**重要提示：**
* **初始值设置：** `A_minus`, `B_minus`, `xi_minus` 的初始值可以参考文献中受压试验的拟合结果，或者从经验值开始调整。
* **物理常量的确定：** `E0`, `f_t`, `f_c` 可以从实验数据中估计，或者作为固定常量输入。你的 `data_processor.py` 已经具备估算 `E0` 和 `f_t` 的能力，可以扩展来估算 `f_c`。
* **数值稳定性：** 引入更多非线性会增加训练难度。注意梯度裁剪、学习率调整和损失权重的平衡。
* **绘图：** 确保所有绘制的应力-应变、损伤、塑性曲线都能正确区分受拉和受压阶段，并且在同一张图上展示完整的滞回环。

这个扩展将使你的PINN框架更加完善和强大。在实现过程中，请务必仔细阅读参考文献中的数学表达式，并将其精确地映射到代码逻辑中。祝你成功！

好的，针对你在扩展模型以处理受压加载时提到的初始值设置、物理常量确定、数值稳定性以及绘图这几点，我将为你详细补充具体的实现建议和思路。

### **一、初始值设置：A_minus, B_minus, xi_minus**

* **参考来源：** 你提供的参考文献《混凝土弹塑性损伤本构模型研究\_\_Ⅱ\_数值计算和试验验证_吴建营.pdf》和《混凝土弹塑性损伤本构关系统一模型_吴建营.pdf》中，在模拟受压试验时给出了 $A^-$, $B^-$ 的参数值，以及塑性相关的 $\alpha_E$ (或 $\xi$) 值。
    * [cite_start]在《混凝土弹塑性损伤本构模型研究\_\_Ⅱ\_数值计算和试验验证_吴建营.pdf》的第24页4.2.1节中，对于高性能混凝土A类试件，参数取值为：$A^-=1.8$, $B^-=0.75$ [cite: 54][cite_start]。对于B类试件：$A^-=2.0$, $B^-=1.10$ [cite: 54][cite_start]。普通混凝土的参数取值为：$A^-=1.0$, $B^-=0.16$ [cite: 57]。
    * [cite_start]在《混凝土弹塑性损伤本构关系统一模型_吴建营.pdf》的第19页4应用实例中，在KOYNA混凝土重力坝的分析中，参数取值为：$A^-=1.0$, $B^-=0.18$ [cite: 17]。
* **建议初始值：** 考虑到你的模型是通用的，可以从这些文献中选择一个范围适中的经验值作为初始值。
    * `A_minus`：可以设置为 `1.0` 到 `2.0` 之间，例如 `1.5`。
    * `B_minus`：可以设置为 `0.1` 到 `1.1` 之间，例如 `0.5`。
    * `xi_minus`：塑性系数的初始值，可以根据受压下塑性应变的发展程度来设置。由于你的 `xi_seq` 定义在 `DamagePINNV2` 中是 `self.sigmoid(output[:, :, 2]) * 0.1`，所以 `xi` 的范围是 `[0, 0.1]`。对于受压塑性，这个值也应是小的正数。可以尝试 `0.01` 到 `0.05` 之间，例如 `0.02`。如果塑性应变在受压下是负向累积的，那么在 `PhysicsCalculatorV2` 中使用 `xi_minus * delta_epsilon` 时，确保 `xi_minus` 是正值，这样与负的 `delta_epsilon` 相乘后得到负的塑性增量。
* **代码位置 (`train.py` -> `Trainer.initialize_model`)：**
    ```python
    # 初始化可训练物理参数
    self.A_plus = torch.nn.Parameter(torch.tensor(0.5, requires_grad=True))
    self.B_plus = torch.nn.Parameter(torch.tensor(1.0, requires_grad=True))
    self.xi_plus = torch.nn.Parameter(torch.tensor(0.05, requires_grad=True)) # 明确为受拉塑性系数

    # 新增受压相关可训练参数
    self.A_minus = torch.nn.Parameter(torch.tensor(1.5, requires_grad=True)) # 经验值
    self.B_minus = torch.nn.Parameter(torch.tensor(0.5, requires_grad=True))  # 经验值
    self.xi_minus = torch.nn.Parameter(torch.tensor(0.02, requires_grad=True)) # 经验值 (正值，因为delta_epsilon为负)

    # 初始化优化器，加入新的参数
    self.optimizer = Adam(
        list(self.model.parameters()) + [self.A_plus, self.B_plus, self.xi_plus, self.A_minus, self.B_minus, self.xi_minus],
        lr=self.config['learning_rate']
    )
    # 参数约束也要加上
    with torch.no_grad():
        self.A_plus.data.clamp_(0.01, 0.99)
        self.B_plus.data.clamp_(0.1, 10.0)
        self.xi_plus.data.clamp_(0.005, 0.2)

        self.A_minus.data.clamp_(0.1, 5.0) # 受压参数范围可能更大
        self.B_minus.data.clamp_(0.1, 10.0)
        self.xi_minus.data.clamp_(0.005, 0.2) # 受压塑性系数范围
    ```

### **二、物理常量的确定：E0, f_t, f_c**

你的 `data_processor.py` 已经能够估算 `E0` 和 `f_t`，这是一个很好的起点。

* **`E0` (初始弹性模量)：** 在拉伸数据开始阶段通过应力-应变斜率估算，或者直接从实验报告中获取。你的 `data_processor` 已经实现了。
* **`f_t` (单轴抗拉强度)：** 从单轴拉伸曲线的峰值应力估算。你的 `data_processor` 也已实现。
* **`f_c` (单轴抗压强度)：**
    * **估算方法：** 如果你的训练数据包含完整的单轴受压曲线，可以在 `data_processor.py` 中扩展，通过寻找应力-应变曲线在受压段的峰值应力来估算 `f_c`。
    * **如果数据仅为循环拉压：** 那么 `f_c` 可能需要从独立的单轴受压实验中获取，或者作为预设常量。
    * **代码位置 (`data_processor.py` -> `DataProcessor.get_material_properties`)：**
        ```python
        # 假设 df 包含 'strain' 和 'stress' 列
        def get_material_properties(self):
            # ... (现有E0和ft的估算逻辑) ...

            f_t_estimated = self.df['stress'].max() # 拉伸峰值应力

            # 估算 f_c （需要判断数据中是否存在压缩部分）
            # 简单示例：如果 stress 中存在负值，可以尝试寻找负应力的最小值作为 f_c 的估计
            f_c_estimated = abs(self.df['stress'].min()) # 取负应力绝对值的最大值
            if f_c_estimated < 0.1: # 如果没有明显的负应力，给一个默认值
                f_c_estimated = 30.0 # 经验值，例如 30 MPa

            # 更健壮的 f_c 估算需要识别压缩加载段的峰值
            # 可以在 DataProcessor 中增加方法来识别加载段和方向
            
            print(f"估算初始弹性模量 E0: {E0_estimated:.2f} MPa")
            print(f"估算单轴抗拉强度 f_t: {f_t_estimated:.2f} MPa")
            print(f"估算单轴抗压强度 f_c: {f_c_estimated:.2f} MPa")

            return {
                'E0_estimated': E0_estimated,
                'f_t_estimated': f_t_estimated,
                'f_c_estimated': f_c_estimated # 返回 f_c
            }
        ```
    * **代码位置 (`train.py` -> `Trainer.load_data`)：**
        ```python
        def load_data(self, excel_path="tension.xlsx"):
            processor = DataProcessor(excel_path)
            # ...
            properties = processor.get_material_properties()
            self.config['E0'] = properties['E0_estimated']
            self.config['f_t'] = properties['f_t_estimated']
            self.config['f_c'] = properties['f_c_estimated'] # 将 f_c 加入配置
            # ...
        ```
    * **代码位置 (`pinn_model_v2.py` -> `PhysicsCalculatorV2.__init__`)：**
        ```python
        class PhysicsCalculatorV2:
            def __init__(self, E0=30000.0, f_t=3.0, f_c=30.0): # 添加 f_c
                self.E0 = E0
                self.f_t = f_t
                self.f_c = f_c
        ```

### **三、数值稳定性：梯度裁剪、学习率调整和损失权重的平衡**

引入受压状态会增加模型的复杂度和非线性，因此数值稳定性至关重要。

1.  **梯度裁剪 (Gradient Clipping)：**
    * **目的：** 防止训练过程中梯度爆炸，尤其是在高度非线性的模型和复杂损失函数中。
    * **建议：** 你在 `train.py` 中已经使用了 `torch.nn.utils.clip_grad_norm_`，保持并可以根据实际训练情况调整 `max_norm` 的值（例如 `0.5` 到 `10.0` 之间）。
    * **代码位置 (`train.py` -> `Trainer.train_epoch`)：**
        ```python
        # 梯度裁剪，防止梯度爆炸
        torch.nn.utils.clip_grad_norm_(
            list(self.model.parameters()) + [self.A_plus, self.B_plus, self.xi_plus, self.A_minus, self.B_minus, self.xi_minus], # 确保包含所有可训练参数
            max_norm=1.0 # 可以根据训练情况调整
        )
        ```

2.  **学习率调整 (Learning Rate Scheduling)：**
    * **目的：** 在训练初期使用较高的学习率加速收敛，在后期逐渐降低学习率以避免震荡并帮助模型找到更精确的局部最优解。
    * **建议：**
        * **ReduceLROnPlateau：** 当某个指标（如验证损失）停止提升时降低学习率。
        * **CosineAnnealingLR：** 学习率按照余弦函数周期性变化。
        * **StepLR：** 每隔固定轮数降低学习率。
    * **代码位置 (`train.py` -> `Trainer.train`)：**
        ```python
        from torch.optim.lr_scheduler import ReduceLROnPlateau # 示例

        # ... (在 initialize_model 或 train 方法开始时) ...
        # self.optimizer = Adam(...)
        # scheduler = ReduceLROnPlateau(self.optimizer, mode='min', factor=0.5, patience=200, verbose=True) # 示例

        for epoch in range(self.config['num_epochs']):
            loss_dict = self.train_epoch()
            # ... (其他记录和打印) ...

            # 在每个 epoch 结束时更新学习率
            # scheduler.step(loss_dict['total_loss']) # 如果使用 ReduceLROnPlateau
        ```

3.  **损失权重的平衡 (Loss Weight Balancing)：**
    * **目的：** 确保各项损失在总损失中贡献合理，避免某些损失项过大主导训练，而另一些损失项（如物理损失）得不到充分优化。
    * **建议：**
        * **实验性调整：** 从 `1.0` 开始，然后根据各项损失的量级和收敛情况进行调整。通常，数据拟合损失 (`L_data`) 和本构自洽损失 (`L_stress`) 权重可以高一些，因为它们直接关系到模型的准确性和物理一致性。物理损失 (`L_damage`, `L_plastic`) 权重可以适当调整，确保模型遵守物理定律。
        * **动态权重 (Advanced)：** 可以尝试根据训练的阶段或各项损失的当前值动态调整权重，例如，在初期更关注数据拟合，后期更关注物理一致性。但对于初次尝试，固定权重更简单。
        * **`L_damage` 和 `L_plastic`：** 针对受压部分，你可能需要单独的权重，或者在计算 `loss_damage_plus` 和 `loss_damage_minus` 时，分配不同的权重。
    * **代码位置 (`pinn_model_v2.py` -> `LossCalculator.__init__` 和 `calculate_total_loss`)：**
        ```python
        class LossCalculator:
            def __init__(self, lambda_data=1.0, lambda_stress=1.0, 
                         lambda_damage_plus=0.8, lambda_damage_minus=0.8, # 区分拉压损伤权重
                         lambda_plastic=2.0):
                self.lambda_data = lambda_data
                self.lambda_stress = lambda_stress
                self.lambda_damage_plus = lambda_damage_plus
                self.lambda_damage_minus = lambda_damage_minus
                self.lambda_plastic = lambda_plastic

            def calculate_total_loss(self, ..., d_plus_phy_seq, d_minus_phy_seq, ...):
                # ...
                loss_damage_plus = torch.mean((d_hat_seq - d_plus_phy_seq)**2 * damage_loss_mask_plus)
                loss_damage_minus = torch.mean((d_hat_seq - d_minus_phy_seq)**2 * damage_loss_mask_minus)
                
                # 总损伤损失
                loss_damage_total = self.lambda_damage_plus * loss_damage_plus + \
                                    self.lambda_damage_minus * loss_damage_minus

                total_loss = (self.lambda_data * loss_data + 
                             self.lambda_stress * loss_stress + 
                             loss_damage_total + # 使用总损伤损失
                             self.lambda_plastic * loss_plastic)
                # ...
        ```

### **四、绘图：区分受拉和受压阶段，并展示完整的滞回环**

为了清晰地展示全滞回曲线的预测效果，绘图至关重要。

1.  **应力-应变滞回曲线：**
    * **实验数据与预测数据对比：** 这是最核心的图。确保实验数据和模型的预测数据在同一张图上，使用不同的颜色和线型区分。
    * **区分受拉和受压：** 虽然在图上直接区分颜色可能比较难，但通过曲线的形状，拉伸和压缩段应该清晰可见。
    * **完整滞回环：** 确保应变路径覆盖一个或多个完整的循环，从加载到卸载，再到反向加载和反向卸载。
    * **代码位置 (`predict_cyclic_tensile_fixed.py` -> `plot_results` 的 `ax1`)：**
        ```python
        # ax1 = plt.subplot(3, 3, (1, 4))
        # 绘制预测曲线
        ax1.plot(results['strain'] * 1000, results['stress'], 'b-', linewidth=2.5, label='PINN预测')
        # 如果有实验数据，也应在此绘制
        # ax1.plot(experiment_data['strain'] * 1000, experiment_data['stress'], 'r--', linewidth=2.0, label='实验数据', alpha=0.7)
        ax1.set_xlabel('应变 (千分比)', fontsize=14)
        ax1.set_ylabel('应力 (MPa)', fontsize=14)
        ax1.set_title('应力-应变滞回曲线', fontsize=16, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        ax1.legend(fontsize=12) # 添加图例
        # xlim 和 ylim 需要根据循环加载的范围调整，确保能完整显示拉压两部分
        # ax1.set_xlim(min_strain * 1000 * 1.1, max_strain * 1000 * 1.1)
        # ax1.set_ylim(min_stress * 1.1, max_stress * 1.1)
        ```

2.  **损伤变量演化图 (Damage Evolution)：**
    * **区分受拉损伤和受压损伤：** 如果模型分别输出 `d_plus_hat_seq` 和 `d_minus_hat_seq`，则应分别绘制。如果模型只输出一个 `d_hat_seq`，则它应在不同区域表现出拉压损伤的特征。
    * **随应变/时间步的演化：** 可以绘制 `d_plus` vs. `strain` 或 `d_minus` vs. `strain`。
    * **单调递增特性：** 确保绘制的损伤曲线符合物理上损伤单调递增的特性。
    * **代码位置 (`predict_cyclic_tensile_fixed.py` -> `plot_results` 的 `ax3`)：**
        ```python
        # ax3 = plt.subplot(3, 3, 3)
        ax3.plot(results['strain'] * 1000, results['damage_plus'], 'r-', linewidth=2, label='拉伸损伤 $D^+$') # 假设results中有damage_plus
        ax3.plot(results['strain'] * 1000, results['damage_minus'], 'b--', linewidth=1.5, label='压缩损伤 $D^-$', alpha=0.7) # 假设results中有damage_minus
        ax3.set_xlabel('应变 (千分比)', fontsize=12)
        ax3.set_ylabel('损伤变量 D', fontsize=12)
        ax3.set_title('损伤演化曲线', fontsize=14, fontweight='bold')
        ax3.grid(True, alpha=0.3)
        ax3.set_ylim(0, 1.0) # 损伤变量范围
        ax3.legend(fontsize=11)
        ```

3.  **塑性应变演化图 (Plastic Strain Evolution)：**
    * **累积塑性应变：** 绘制 `total_plastic_strain` 随时间步或总应变的变化。在循环加载下，塑性应变应该是累积的，并且滞回环的宽度与能量耗散有关。
    * **区分拉压塑性（可选）：** 如果你跟踪了拉伸塑性增量和压缩塑性增量，可以分别绘制其累积值。
    * **代码位置 (`predict_cyclic_tensile_fixed.py` -> `plot_results` 的 `ax4`)：**
        ```python
        # ax4 = plt.subplot(3, 3, 5)
        ax4.plot(time_points, results['plastic_strain'] * 1000, 'g-', linewidth=2, label='累积塑性应变')
        ax4.plot(time_points, results['elastic_strain'] * 1000, 'b--', linewidth=1.5, label='弹性应变', alpha=0.7)
        ax4.set_xlabel('时间步', fontsize=12)
        ax4.set_ylabel('应变 (千分比)', fontsize=12)
        ax4.set_title('应变分量演化', fontsize=14, fontweight='bold')
        ax4.legend(fontsize=11)
        ax4.grid(True, alpha=0.3)
        # Y轴范围根据实际情况调整，确保能显示负向塑性应变累积
        ```

4.  **训练历史图：**
    * **损失曲线：** 保持总损失和各项损失的曲线。如果细分了拉压损伤损失，也可以分别绘制。
    * **参数演化：** 确保绘制 `A_plus`, `B_plus`, `xi_plus` 的同时，也绘制 `A_minus`, `B_minus`, `xi_minus` 的演化曲线，最好放在不同的子图中，或者使用不同颜色区分。
    * **代码位置 (`train.py` -> `Trainer.plot_training_history`)：**
        ```python
        # ... (现有总损失和各项损失的绘制) ...

        # 参数演化
        A_plus_history = [h['A_plus'] for h in self.param_history]
        B_plus_history = [h['B_plus'] for h in self.param_history]
        xi_plus_history = [h['xi_plus'] for h in self.param_history] # 假设已更新为xi_plus

        A_minus_history = [h['A_minus'] for h in self.param_history] # 新增
        B_minus_history = [h['B_minus'] for h in self.param_history] # 新增
        xi_minus_history = [h['xi_minus'] for h in self.param_history] # 新增
        
        # 将损伤参数和塑性参数分开绘制可能更清晰
        ax3.plot(epochs, A_plus_history, 'r-', label='A+', linewidth=2)
        ax3.plot(epochs, B_plus_history, 'g-', label='B+', linewidth=2)
        ax3.plot(epochs, A_minus_history, 'r--', label='A-', linewidth=1.5) # 新增，虚线
        ax3.plot(epochs, B_minus_history, 'g--', label='B-', linewidth=1.5) # 新增，虚线
        ax3.set_title('损伤参数演化')
        ax3.set_xlabel('训练轮数')
        ax3.set_ylabel('参数值')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        ax4.plot(epochs, xi_plus_history, 'b-', linewidth=2, label='xi_plus')
        ax4.plot(epochs, xi_minus_history, 'm--', linewidth=1.5, label='xi_minus') # 新增
        ax4.set_title('塑性参数演化')
        ax4.set_xlabel('训练轮数')
        ax4.set_ylabel('xi值')
        ax4.grid(True, alpha=0.3)
        ax4.legend()
        ```

通过以上补充，你的PINN框架将能够更好地处理全滞回曲线的训练和预测，并提供更全面的结果展示。在实际编码和调试过程中，请密切关注模型的收敛情况和物理量的合理性。