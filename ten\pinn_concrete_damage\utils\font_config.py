import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import platform

def setup_chinese_font():
    """
    设置中文字体显示
    """
    system = platform.system()
    
    if system == "Windows":
        # Windows系统字体
        font_paths = [
            'C:/Windows/Fonts/simhei.ttf',  # 黑体
            'C:/Windows/Fonts/simsun.ttc',  # 宋体
            'C:/Windows/Fonts/msyh.ttc',    # 微软雅黑
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                plt.rcParams['font.sans-serif'] = [fm.FontProperties(fname=font_path).get_name()]
                break
        else:
            # 如果没有找到字体，使用系统默认
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    
    elif system == "Darwin":  # macOS
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'Hiragino Sans GB', 'DejaVu Sans']
    
    else:  # Linux
        plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'DejaVu Sans']
    
    # 解决负号显示问题
    plt.rcParams['axes.unicode_minus'] = False
    
    print(f"字体设置完成: {plt.rcParams['font.sans-serif'][0]}")

def get_chinese_font():
    """
    获取中文字体
    """
    setup_chinese_font()
    return plt.rcParams['font.sans-serif'][0]

# 自动设置字体
setup_chinese_font()