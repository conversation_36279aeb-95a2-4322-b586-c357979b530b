import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm

from model.pinn import PINN
from utils.data_generator import generate_training_data
from utils.visualization import plot_results, plot_damage_contour
from utils.evaluation import evaluate_model


def main():
    # 设置随机种子以确保结果可复现
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 设置PyTorch梯度检查点
    torch.autograd.set_detect_anomaly(True)  # 启用异常检测
    
    # 模型参数
    input_dim = 3  # 空间坐标 (x, y, z)
    hidden_dim = 512  # 隐藏层维度 - 与预训练模型匹配
    output_dim = 4  # 位移场 (u, v, w) 和损伤变量 θ
    
    # 物理参数
    E0 = 210e9  # 杨氏模量 (Pa)
    nu = 0.3    # 泊松比
    
    # 创建PINN模型
    model = PINN(input_dim, hidden_dim, output_dim, E0, nu).to(device)
    
    # 确保模型处于评估模式
    model.eval()
    
    # 加载预训练模型权重
    model_path = os.path.join('checkpoints', 'pinn_model.pth')
    try:
        if os.path.exists(model_path):
            model.load_state_dict(torch.load(model_path, map_location=device))
            print(f"成功加载预训练模型: {model_path}")
        else:
            print(f"警告: 预训练模型 {model_path} 不存在，使用随机初始化的模型")
    except Exception as e:
        print(f"加载模型时出错: {e}")
        print("继续使用随机初始化的模型")
    
    # 生成测试数据
    print("\n生成测试数据...")
    test_data_types = {
        'single': '单点损伤',
        'multi': '多点损伤',
        'random': '随机损伤'
    }
    
    # 评估不同损伤模式下的模型性能
    for damage_mode, damage_name in test_data_types.items():
        print(f"\n评估{damage_name}模式下的模型性能...")
        test_data = generate_training_data(is_test=True, damage_mode=damage_mode)
        
        try:
            # 评估模型
            metrics = evaluate_model(model, test_data, device)
            print(f"{damage_name}测试结果:")
            for metric_name, metric_value in metrics.items():
                if isinstance(metric_value, dict):
                    print(f"  {metric_name}:")
                    for sub_name, sub_value in metric_value.items():
                        if isinstance(sub_value, (int, float)) and not isinstance(sub_value, bool):
                            print(f"    {sub_name}: {sub_value:.6f}")
                        else:
                            print(f"    {sub_name}: {sub_value}")
                elif isinstance(metric_value, (int, float)) and not isinstance(metric_value, bool):
                    print(f"  {metric_name}: {metric_value:.6f}")
                else:
                    print(f"  {metric_name}: {metric_value}")
            
            # 可视化结果
            print(f"\n生成{damage_name}可视化结果...")
            plot_results(model, test_data, save_path=f"results_{damage_mode}.png")
            
            # 生成损伤等值线图
            try:
                plot_damage_contour(model, resolution=50, threshold=0.2)
                # 注意：等值图将自动保存到contour_plots文件夹中
            except Exception as e:
                print(f"生成损伤等值线图时出错: {e}")
                
        except Exception as e:
            print(f"评估{damage_name}模式时出错: {e}")
    
    print("\n验证完成!")


if __name__ == "__main__":
    main()