import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 读取数据
hp_data = pd.read_excel('d:/column/single/uniaxial_compression_high_performance.xlsx')
nc_data = pd.read_excel('d:/column/single/uniaxial_compression_normal_concrete.xlsx')

# 创建图表
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
fig.suptitle('混凝土单轴压缩应力应变曲线分析', fontsize=16, fontweight='bold')

# 1. 应力-应变曲线（使用绝对值）
ax1 = axes[0, 0]
ax1.plot(hp_data['strain_abs'], hp_data['stress_abs'], 'r-', linewidth=2, label='高性能混凝土')
ax1.plot(nc_data['strain_abs'], nc_data['stress_abs'], 'b-', linewidth=2, label='普通混凝土')
ax1.set_xlabel('应变 |ε|')
ax1.set_ylabel('应力 |σ| (MPa)')
ax1.set_title('压缩应力-应变曲线')
ax1.legend()
ax1.grid(True, alpha=0.3)

# 2. 损伤演化曲线
ax2 = axes[0, 1]
ax2.plot(hp_data['strain_abs'], hp_data['damage'], 'r-', linewidth=2, label='高性能混凝土')
ax2.plot(nc_data['strain_abs'], nc_data['damage'], 'b-', linewidth=2, label='普通混凝土')
ax2.set_xlabel('应变 |ε|')
ax2.set_ylabel('损伤变量 d-')
ax2.set_title('压缩损伤演化曲线')
ax2.legend()
ax2.grid(True, alpha=0.3)

# 3. 损伤阈值演化
ax3 = axes[1, 0]
ax3.plot(hp_data['strain_abs'], hp_data['r_minus'], 'r-', linewidth=2, label='高性能混凝土')
ax3.plot(nc_data['strain_abs'], nc_data['r_minus'], 'b-', linewidth=2, label='普通混凝土')
ax3.set_xlabel('应变 |ε|')
ax3.set_ylabel('损伤阈值 r- (MPa)')
ax3.set_title('压缩损伤阈值演化')
ax3.legend()
ax3.grid(True, alpha=0.3)

# 4. 应力-损伤关系
ax4 = axes[1, 1]
ax4.plot(hp_data['damage'], hp_data['stress_abs'], 'r-', linewidth=2, label='高性能混凝土')
ax4.plot(nc_data['damage'], nc_data['stress_abs'], 'b-', linewidth=2, label='普通混凝土')
ax4.set_xlabel('损伤变量 d-')
ax4.set_ylabel('应力 |σ| (MPa)')
ax4.set_title('应力-损伤关系')
ax4.legend()
ax4.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('d:/column/single/compression_analysis.png', dpi=300, bbox_inches='tight')
plt.show()

print("压缩曲线图表已生成: d:/column/single/compression_analysis.png")

# 输出数据统计信息
print("\n=== 数据统计信息 ===")
print(f"高性能混凝土:")
print(f"  最大压缩应力: {hp_data['stress_abs'].max():.2f} MPa")
print(f"  最大损伤变量: {hp_data['damage'].max():.4f}")
print(f"  应变范围: 0 ~ {hp_data['strain_abs'].max():.6f}")

print(f"\n普通混凝土:")
print(f"  最大压缩应力: {nc_data['stress_abs'].max():.2f} MPa")
print(f"  最大损伤变量: {nc_data['damage'].max():.4f}")
print(f"  应变范围: 0 ~ {nc_data['strain_abs'].max():.6f}")