# -*- coding: utf-8 -*-
"""
检查Excel文件结构

用于检查Excel文件的工作表和列名，以便理解数据加载问题。
"""

import os
import pandas as pd

def check_excel_structure(excel_path):
    """
    检查Excel文件的结构
    
    参数:
        excel_path (str): Excel文件路径
    """
    print(f"检查Excel文件: {excel_path}")
    
    # 检查文件是否存在
    if not os.path.exists(excel_path):
        print(f"错误: 文件不存在 - {excel_path}")
        return
    
    try:
        # 获取工作表名称
        excel_file = pd.ExcelFile(excel_path)
        sheet_names = excel_file.sheet_names
        print(f"工作表名称: {sheet_names}")
        
        # 检查每个工作表的列名
        for sheet_name in sheet_names:
            print(f"\n工作表 '{sheet_name}' 的列名:")
            df = pd.read_excel(excel_path, sheet_name=sheet_name)
            print(df.columns.tolist())
            
            # 显示前几行数据
            print(f"\n工作表 '{sheet_name}' 的前3行数据:")
            print(df.head(3))
            
            # 如果是力和位移数据表，检查特定列
            if '力' in sheet_name or '位移' in sheet_name:
                force_cols = [col for col in df.columns if '力' in col or 'Force' in col]
                disp_cols = [col for col in df.columns if '位移' in col or 'Deflection' in col]
                print(f"\n力相关列: {force_cols}")
                print(f"位移相关列: {disp_cols}")
    
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")

if __name__ == "__main__":
    # 检查项目目录中的Excel文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    
    # 检查可能的Excel文件路径
    excel_paths = [
        os.path.join(current_dir, "column.xlsx"),
        os.path.join(parent_dir, "column.xlsx")
    ]
    
    for path in excel_paths:
        if os.path.exists(path):
            check_excel_structure(path)
            break
    else:
        print("未找到Excel文件，请确保column.xlsx文件存在于正确的位置。")