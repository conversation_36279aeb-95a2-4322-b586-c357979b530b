import os
import sys
import shutil
import argparse
import subprocess


def setup_environment():
    """
    设置项目环境
    """
    print("\n=== 设置项目环境 ===")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"检测到Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
        print("警告: 推荐使用Python 3.7+版本")
    
    # 安装依赖包
    print("\n安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖包安装成功！")
    except subprocess.CalledProcessError:
        print("警告: 依赖包安装失败，请手动安装requirements.txt中的包")
    
    # 创建必要的目录
    directories = ['saved_models', 'results', 'results/sensitivity', 'test_results']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"创建目录: {directory}")
    
    print("\n环境设置完成！")


def check_data(data_path):
    """
    检查数据文件
    
    参数:
        data_path: 数据文件路径
    """
    print("\n=== 检查数据文件 ===")
    
    if not os.path.exists(data_path):
        print(f"错误: 数据文件不存在: {data_path}")
        return False
    
    # 尝试加载数据
    try:
        import pandas as pd
        data = pd.read_excel(data_path)
        print(f"成功加载数据文件: {data_path}")
        print(f"数据形状: {data.shape}")
        print(f"数据列: {', '.join(data.columns)}")
        
        # 检查数据列数
        if data.shape[1] < 2:
            print("警告: 数据至少需要两列: 应变和应力")
            return False
        
        # 显示数据前5行
        print("\n数据预览 (前5行):")
        print(data.head())
        
        return True
    except Exception as e:
        print(f"错误: 无法加载数据文件: {e}")
        return False


def test_torch():
    """
    测试PyTorch是否正常工作
    """
    print("\n=== 测试PyTorch ===")
    
    try:
        import torch
        print(f"PyTorch版本: {torch.__version__}")
        
        # 检查CUDA是否可用
        cuda_available = torch.cuda.is_available()
        print(f"CUDA是否可用: {cuda_available}")
        
        if cuda_available:
            print(f"CUDA版本: {torch.version.cuda}")
            print(f"GPU设备: {torch.cuda.get_device_name(0)}")
        
        # 创建简单的张量测试
        x = torch.rand(5, 3)
        print("\n测试张量:")
        print(x)
        
        return True
    except Exception as e:
        print(f"错误: PyTorch测试失败: {e}")
        return False


def main():
    """
    主函数
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='设置PINN模型环境')
    parser.add_argument('--data_path', type=str, default='../tension.xlsx',
                        help='数据文件路径')
    
    args = parser.parse_args()
    
    print("====================================")
    print("混凝土拉伸损伤PINN模型环境设置")
    print("====================================")
    
    # 设置环境
    setup_environment()
    
    # 检查数据
    check_data(args.data_path)
    
    # 测试PyTorch
    test_torch()
    
    print("\n====================================")
    print("环境设置和检查完成！")
    print("====================================")
    print("\n您现在可以运行以下命令:")
    print("1. python test_components.py  # 测试模型组件")
    print("2. python example.py          # 运行示例")
    print("3. python main.py --mode train # 训练模型")
    print("\n或者在Windows上运行: run_model.bat")


if __name__ == "__main__":
    main()