# 基于物理信息的神经网络(PINN)柱滞回行为建模

## 项目背景与研究意义
滞回行为是结构工程中柱构件在循环荷载下的关键力学特性，准确预测滞回曲线对结构抗震设计至关重要。传统Bouc-Wen模型参数校准困难，而物理信息神经网络(PINN)通过融合数据驱动方法与物理约束，可有效解决小样本条件下的建模问题。

## 理论基础
1. **Bouc-Wen模型**：经典滞回模型，描述非线性恢复力与位移关系
2. **物理信息神经网络**：将控制方程作为正则项嵌入损失函数
3. **混合建模方法**：结合数据驱动与机理模型的优势

## 1. Excel数据提取与预处理

## 1. Excel数据提取与预处理

### 1.1 数据存储结构

#### 静态参数表（Sheet1）
| 参数名称   | 数值   | 单位 | 公式/来源        |
| ---------- | ------ | ---- | ---------------- |
| L/D        | 2.0    | -    | 几何参数计算     |
| $P/f_cA_g$ | 0.19   | -    | 轴压比计算       |
| $\rho_l$   | 0.032  | -    | 纵向配筋率       |
| $\rho_t$   | 0.0145 | -    | 横向配筋率校准值 |
| 混凝土强度 | 38     | MPa  | 材料特性         |

#### 力-位移数据表（Sheet2）
| 位移 $x$ (mm) | 力 $F$ (kN) | 循环阶段 | 样本ID | 其他动态参数（可选） |
| ------------- | ----------- | -------- | ------ | -------------------- |
| -50           | -850        | 加载     | 1      | ...                  |
| -40           | -720        | 加载     | 1      | ...                  |
| ...           | ...         | ...      | ...    | ...                  |

### 1.2 数据提取步骤

```python
# 1. 读取静态参数
import pandas as pd

# 读取静态参数表
static_df = pd.read_excel("data.xlsx", sheet_name="Sheet1")
static_params = static_df[["L/D", "P/f_cA_g", "rho_l", "rho_t", "混凝土强度"]].values

# 读取力-位移数据表
dynamic_df = pd.read_excel("data.xlsx", sheet_name="Sheet2")
displacement = dynamic_df["位移 x (mm)"].values
force = dynamic_df["力 F (kN)"].values
sample_id = dynamic_df["样本ID"].values  # 用于关联静态参数

# 2. 数据标准化
from sklearn.preprocessing import StandardScaler
scaler = StandardScaler()
static_params_scaled = scaler.fit_transform(static_params)  # 标准化静态参数

# 位移归一化
x_min, x_max = displacement.min(), displacement.max()
x_norm = (displacement - x_min) / (x_max - x_min) * 2 - 1

# 力归一化
F_min, F_max = force.min(), force.max()
F_norm = (force - F_min) / (F_max - F_min) * 2 - 1
```

## 2. 神经网络架构设计

```python
import torch
import torch.nn as nn

class BoucWenPINN(nn.Module):
    def __init__(self, input_dim=5, hidden_dim=100):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.Swish(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.Swish(),
            nn.Linear(hidden_dim, 2)  # 输出F_pred和z
        )
        # Bouc-Wen可训练参数
        self.alpha = nn.Parameter(torch.tensor(0.5))
        self.beta = nn.Parameter(torch.tensor(1.0))
        self.gamma = nn.Parameter(torch.tensor(1.0))
        self.A = nn.Parameter(torch.tensor(1.0))
        self.n = nn.Parameter(torch.tensor(1.0))
        self.k = nn.Parameter(torch.tensor(1.0))

    def forward(self, inputs):
        F_z = self.net(inputs)
        F_pred = F_z[:, 0]
        z_pred = F_z[:, 1]
        return F_pred, z_pred

    def physics_loss(self, x, delta_x, F_pred, z_pred):
        # 恢复力残差
        F_physics = self.alpha * self.k * x + (1 - self.alpha) * self.k * z_pred
        residual1 = F_pred - F_physics
        
        # 滞回变量残差
        sign_dx = torch.sign(delta_x)
        dz_dx = self.A - self.beta * sign_dx * torch.abs(z_pred)**self.n - self.gamma * torch.abs(z_pred)**self.n
        residual2 = torch.autograd.grad(z_pred, x, grad_outputs=torch.ones_like(z_pred), create_graph=True)[0] - dz_dx
        return residual1, residual2
```

## 3. 数据加载与训练流程

```python
class HysteresisDataset(Dataset):
    def __init__(self, static_params, x_norm, F_norm):
        self.static_params = torch.FloatTensor(static_params)
        self.x = torch.FloatTensor(x_norm)
        self.F = torch.FloatTensor(F_norm)

    def __len__(self):
        return len(self.x)

    def __getitem__(self, idx):
        return {
            "input": torch.cat([self.x[idx], self.static_params[idx]]),
            "output": self.F[idx]
        }

# 创建数据加载器
dataset = HysteresisDataset(static_params_scaled, x_norm, F_norm)
train_loader = DataLoader(dataset, batch_size=32, shuffle=False)  # 保持物理连续性

# 训练循环
model = BoucWenPINN()
optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)

for epoch in range(1000):
    for batch in train_loader:
        inputs = batch["input"]
        F_exp = batch["output"]
        F_pred, z_pred = model(inputs)
        
        # 计算位移差分近似速度符号
        x = inputs[:, 0].unsqueeze(1)
        delta_x = x[1:] - x[:-1]
        delta_x = torch.cat([delta_x, delta_x[-1:]])  # 对齐维度
        
        # 计算物理残差
        residual1, residual2 = model.physics_loss(x, delta_x, F_pred, z_pred)
        loss = 1.0 * torch.mean(residual1**2) + 0.5 * torch.mean(residual2**2)
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)  # 梯度裁剪
        optimizer.step()
```

## 4. 预测与验证

```python
# 输入构造
test_inputs = torch.cat([x_norm_test, static_params_scaled_test], dim=1)
F_pred, z_pred = model(test_inputs)
F_denorm = (F_pred + 1) / 2 * (F_max - F_min) + F_min  # 反归一化
```

### 4.1 结果可视化
- 绘制实验与预测滞回曲线
- 检查包络线、捏缩效应、刚度退化是否一致

### 4.2 性能评估
计算定量指标：
- MSE（均方误差）
- MAE（平均绝对误差）
- MAPE（平均绝对百分比误差）