# -*- coding: utf-8 -*-
"""
<PERSON>uc-Wen物理信息神经网络(PINN)模型

实现基于<PERSON><PERSON><PERSON><PERSON>模型的物理约束神经网络，用于钢筋混凝土柱滞回曲线的拟合与预测。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class Swish(nn.Module):
    """
    Swish激活函数: x * sigmoid(x)
    兼顾非线性与光滑性，适合物理信息神经网络
    """
    def forward(self, x):
        return x * torch.sigmoid(x)


class BoucWenPINN(nn.Module):
    """
    基于Bouc-Wen模型的物理信息神经网络
    """
    def __init__(self, input_dim=6, hidden_dim=100, hidden_layers=4):
        """
        初始化模型
        
        参数:
            input_dim (int): 输入维度，默认为6 [归一化位移 + 5个静态参数]
            hidden_dim (int): 隐藏层维度
            hidden_layers (int): 隐藏层数量
        """
        super().__init__()
        
        # 构建神经网络层
        layers = [nn.Linear(input_dim, hidden_dim), Swish()]
        
        # 添加隐藏层
        for _ in range(hidden_layers - 1):
            layers.append(nn.Linear(hidden_dim, hidden_dim))
            layers.append(Swish())
        
        # 输出层：预测力和滞回变量
        layers.append(nn.Linear(hidden_dim, 2))
        
        # 构建神经网络
        self.net = nn.Sequential(*layers)
        
        # Bouc-Wen可训练参数
        self.alpha = nn.Parameter(torch.tensor(0.5))  # 线性刚度占比
        self.beta = nn.Parameter(torch.tensor(1.0))   # 滞回形状参数
        self.gamma = nn.Parameter(torch.tensor(1.0))  # 滞回形状参数
        self.A = nn.Parameter(torch.tensor(1.0))      # 滞回形状参数
        self.n = nn.Parameter(torch.tensor(1.0))      # 滞回形状参数
        self.k = nn.Parameter(torch.tensor(1.0))      # 刚度参数
        
        # 参数约束
        self._apply_constraints()
    
    def _apply_constraints(self):
        """
        应用参数约束，确保物理参数在合理范围内
        """
        # 使用sigmoid函数将alpha约束在[0,1]范围内
        self.alpha.data = torch.sigmoid(self.alpha.data)
        
        # 确保其他参数为正值
        self.beta.data = torch.abs(self.beta.data)
        self.gamma.data = torch.abs(self.gamma.data)
        self.A.data = torch.abs(self.A.data)
        self.n.data = torch.abs(self.n.data)
        self.k.data = torch.abs(self.k.data)
    
    def forward(self, inputs):
        """
        前向传播
        
        参数:
            inputs (torch.Tensor): 输入张量 [归一化位移 + 静态参数]
            
        返回:
            tuple: (预测力, 滞回变量)
        """
        # 应用参数约束
        self._apply_constraints()
        
        # 神经网络输出
        outputs = self.net(inputs)
        
        # 分离预测力和滞回变量
        F_pred = outputs[:, 0]
        z_pred = outputs[:, 1]
        
        return F_pred, z_pred
    
    def compute_physics_residuals(self, x, delta_x, F_pred, z_pred):
        """
        计算物理残差
        
        参数:
            x (torch.Tensor): 归一化位移
            delta_x (torch.Tensor): 位移差分，用于近似速度符号
            F_pred (torch.Tensor): 预测力
            z_pred (torch.Tensor): 滞回变量
            
        返回:
            tuple: (恢复力残差, 滞回变量残差)
        """
        # 1. 恢复力残差: F_pred - (alpha*k*x + (1-alpha)*k*z)
        F_physics = self.alpha * self.k * x + (1 - self.alpha) * self.k * z_pred
        residual_F = F_pred - F_physics
        
        # 2. 滞回变量残差: dz/dx - (A - beta*sign(dx)*|z|^n - gamma*|z|^n)
        sign_dx = torch.sign(delta_x)
        dz_dx_physics = self.A - self.beta * sign_dx * torch.abs(z_pred)**self.n - self.gamma * torch.abs(z_pred)**self.n
        
        # 计算z对x的导数
        if x.requires_grad:
            dz_dx = torch.autograd.grad(
                z_pred, x, 
                grad_outputs=torch.ones_like(z_pred),
                create_graph=True,
                retain_graph=True,
                allow_unused=True
            )[0]
            residual_z = dz_dx - dz_dx_physics if dz_dx is not None else torch.zeros_like(z_pred)
        else:
            # 如果x不需要梯度，返回零张量
            residual_z = torch.zeros_like(z_pred)
        
        return residual_F, residual_z
    
    def get_bouc_wen_params(self):
        """
        获取当前的Bouc-Wen参数
        
        返回:
            dict: 包含所有Bouc-Wen参数的字典
        """
        return {
            "alpha": self.alpha.item(),
            "beta": self.beta.item(),
            "gamma": self.gamma.item(),
            "A": self.A.item(),
            "n": self.n.item(),
            "k": self.k.item()
        }