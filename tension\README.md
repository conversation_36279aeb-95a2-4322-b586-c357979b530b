# PINN混凝土单轴拉伸损伤演化预测项目

基于物理信息神经网络(Physics-Informed Neural Networks, PINN)的混凝土单轴拉伸损伤演化规律识别与预测系统。

## 项目概述

本项目实现了一个完整的PINN模型，用于从混凝土单轴拉伸试验数据中识别材料的损伤演化规律。模型结合了深度学习的数据拟合能力和物理约束的先验知识，能够准确预测混凝土的应力-应变关系、损伤演化过程和材料参数。

### 核心特性

- **物理约束集成**: 集成应力本构关系、损伤演化方程、塑性应变单调性等物理约束
- **材料参数识别**: 自动识别弹性模量、抗拉强度、断裂能等关键材料参数
- **损伤演化预测**: 预测损伤变量随应变的演化过程
- **应变分解**: 分离弹性应变和塑性应变分量
- **完整可视化**: 提供丰富的图表展示训练过程和预测结果
- **灵活配置**: 支持多种配置模板和自定义参数设置

## 文件结构

```
d:/column/tension/
├── README.md                    # 项目说明文档
├── requirements.txt             # Python依赖包列表
├── 思路.md                      # 理论基础和模型设计思路
├── tension.xlsx                 # 输入数据文件
│
├── main.py                      # 主程序入口(简化版)
├── train_pinn.py               # 完整训练脚本
├── config.py                    # 配置管理模块
├── data_processor.py           # 数据处理模块
├── pinn_model.py               # PINN模型核心模块
└── visualization.py            # 可视化模块
```

## 快速开始

### 1. 环境准备

```bash
# 安装依赖包
pip install -r requirements.txt
```

### 2. 数据准备

将混凝土单轴拉伸试验数据保存为Excel文件(`tension.xlsx`)，包含应变和应力两列数据。

### 3. 运行训练

#### 方式一：使用完整训练脚本(推荐)

```bash
python train_pinn.py
```

#### 方式二：使用简化主程序

```bash
python main.py
```

### 4. 查看结果

训练完成后，所有结果将保存在当前目录下，包括：

- `pinn_model.pth`: 训练好的模型文件
- `material_parameters.csv`: 识别的材料参数
- `predictions.csv`: 模型预测结果
- `training_history.json`: 训练历史记录
- `experiment_log.json`: 完整实验日志
- 各种可视化图表(PNG格式)

## 配置选项

### 预定义配置模板

项目提供了三种预定义配置模板：

1. **默认配置** (`Config()`): 平衡性能和速度的标准配置
2. **快速测试** (`ConfigTemplates.quick_test()`): 用于快速验证的轻量配置
3. **高精度配置** (`ConfigTemplates.high_accuracy()`): 追求最高精度的配置
4. **鲁棒训练** (`ConfigTemplates.robust_training()`): 增强训练稳定性的配置

### 自定义配置

可以通过修改`config.py`中的参数来自定义配置：

```python
# 在train_pinn.py中修改配置
config = Config()

# 修改训练参数
config.training.num_epochs = 2000
config.training.learning_rate = 0.001

# 修改模型结构
config.model.hidden_dims = [64, 64, 32]

# 修改损失函数权重
config.training.loss_weights = {
    'data': 1.0,
    'stress_constitutive': 0.1,
    'damage_evolution': 0.1,
    # ...
}
```

### 主要配置参数

#### 模型结构
- `input_dim`: 输入维度(默认1，应变)
- `hidden_dims`: 隐藏层维度列表
- `output_dim`: 输出维度(默认3：应力、损伤、塑性应变)
- `activation`: 激活函数类型

#### 训练参数
- `num_epochs`: 训练轮数
- `learning_rate`: 学习率
- `batch_size`: 批次大小
- `loss_weights`: 各损失项权重

#### 材料参数初值
- `E0_init`: 初始弹性模量
- `ft_init`: 抗拉强度初值
- `Gf_init`: 断裂能初值
- `alpha_init`, `beta_init`: 损伤演化参数初值
- `lch_init`: 特征长度初值

## 模型理论基础

### 物理约束方程

1. **应力本构关系**:
   \[
   \sigma = (1-D) E_0 \varepsilon_e
   \]

2. **损伤演化方程**:
   \[
   D = 1 - \frac{f_t}{\sigma_{max}} \exp\left(-\alpha \frac{\varepsilon_p}{\varepsilon_{f0}}\right)
   \]

3. **应变分解**:
   \[
   \varepsilon = \varepsilon_e + \varepsilon_p
   \]

4. **塑性应变单调性**:
   \[
   \frac{d\varepsilon_p}{d\varepsilon} \geq 0
   \]

详细的理论推导和模型设计思路请参考`思路.md`文档。

## 输出结果说明

### 1. 材料参数识别结果

- `E0`: 弹性模量 (MPa)
- `ft`: 抗拉强度 (MPa)
- `Gf`: 断裂能 (N/m)
- `alpha`: 损伤演化参数
- `beta`: 损伤演化参数
- `lch`: 特征长度 (mm)

### 2. 预测结果

- `strain`: 应变
- `stress`: 应力 (MPa)
- `damage`: 损伤变量 (0-1)
- `plastic_strain`: 塑性应变
- `elastic_strain`: 弹性应变
- `effective_modulus`: 有效弹性模量

### 3. 性能评估指标

- `R²`: 决定系数 (越接近1越好)
- `RMSE`: 均方根误差 (越小越好)
- `MAE`: 平均绝对误差 (越小越好)
- `MAPE`: 平均绝对百分比误差 (越小越好)

### 4. 可视化图表

- `stress_strain_comparison.png`: 应力-应变对比图
- `damage_evolution.png`: 损伤演化图
- `strain_decomposition.png`: 应变分解图
- `effective_modulus.png`: 有效弹性模量演化图
- `comprehensive_results.png`: 综合结果图
- `training_history.png`: 训练历史图
- `material_parameters_evolution.png`: 材料参数演化图
- `error_analysis.png`: 误差分析图

## 故障排除

### 常见问题

1. **数据加载失败**
   - 检查Excel文件路径是否正确
   - 确保Excel文件包含应变和应力数据列
   - 检查数据格式是否正确(数值型)

2. **训练不收敛**
   - 降低学习率
   - 增加训练轮数
   - 调整损失函数权重
   - 使用不同的配置模板

3. **内存不足**
   - 减小批次大小
   - 减少网络层数或神经元数量
   - 使用CPU训练(修改device配置)

4. **预测结果不合理**
   - 检查材料参数初值设置
   - 调整物理约束权重
   - 增加训练数据量
   - 检查数据质量

### 调试建议

1. **使用快速测试配置**进行初步验证
2. **检查训练历史图**了解收敛情况
3. **分析损失函数各项**找出问题所在
4. **逐步调整参数**而非大幅修改

## 扩展功能

### 1. 添加新的物理约束

在`pinn_model.py`的`PINNTrainer`类中添加新的损失函数项：

```python
def custom_physics_loss(self, strain, predictions):
    # 实现自定义物理约束
    pass
```

### 2. 修改网络结构

在`config.py`中修改网络参数或在`pinn_model.py`中自定义网络结构。

### 3. 添加新的材料参数

在`ConcreteDAMAGE_PINN`类中添加新的可训练参数。

## 引用

如果您在研究中使用了本项目，请引用相关文献。

## 许可证

本项目采用MIT许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目地址: [GitHub链接]
- 邮箱: [联系邮箱]

---

**注意**: 本项目仅供学术研究使用，实际工程应用需要进一步验证和优化。