# -*- coding: utf-8 -*-
"""
损失函数模块

实现PINN模型的损失函数，包括数据匹配损失和物理残差损失。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class PINNLoss(nn.Module):
    """
    物理信息神经网络(PINN)损失函数
    
    结合数据匹配损失和物理残差损失
    """
    def __init__(self, lambda_data=1.0, lambda_physics=0.5):
        """
        初始化损失函数
        
        参数:
            lambda_data (float): 数据匹配损失权重
            lambda_physics (float): 物理残差损失权重
        """
        super().__init__()
        self.lambda_data = lambda_data
        self.lambda_physics = lambda_physics
        self.mse_loss = nn.MSELoss()
    
    def forward(self, F_pred, F_exp, residual_F, residual_z):
        """
        计算总损失
        
        参数:
            F_pred (torch.Tensor): 预测力
            F_exp (torch.Tensor): 实验力
            residual_F (torch.Tensor): 恢复力残差
            residual_z (torch.Tensor): 滞回变量残差
            
        返回:
            tuple: (总损失, 数据损失, 物理损失)
        """
        # 数据匹配损失
        data_loss = self.mse_loss(F_pred, F_exp)
        
        # 物理残差损失
        physics_loss_F = torch.mean(residual_F**2)
        physics_loss_z = torch.mean(residual_z**2)
        physics_loss = physics_loss_F + physics_loss_z
        
        # 总损失
        total_loss = self.lambda_data * data_loss + self.lambda_physics * physics_loss
        
        return total_loss, data_loss, physics_loss


class EarlyStoppingLoss(nn.Module):
    """
    带早停的损失函数监控器
    """
    def __init__(self, patience=10, min_delta=1e-4):
        """
        初始化早停监控器
        
        参数:
            patience (int): 容忍的不改善轮数
            min_delta (float): 最小改善阈值
        """
        super().__init__()
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_loss = float('inf')
        self.early_stop = False
    
    def __call__(self, val_loss):
        """
        检查是否应该早停
        
        参数:
            val_loss (float): 当前验证损失
            
        返回:
            bool: 是否应该早停
        """
        if val_loss < self.best_loss - self.min_delta:
            # 损失有显著改善，重置计数器
            self.best_loss = val_loss
            self.counter = 0
        else:
            # 损失没有显著改善，增加计数器
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        
        return self.early_stop
    
    def reset(self):
        """
        重置早停监控器
        """
        self.counter = 0
        self.best_loss = float('inf')
        self.early_stop = False