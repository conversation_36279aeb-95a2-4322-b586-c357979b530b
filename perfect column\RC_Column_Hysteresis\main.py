# -*- coding: utf-8 -*-
"""
钢筋混凝土柱滞回曲线拟合与预测主程序

基于物理信息神经网络(PINN)实现钢筋混凝土柱在循环荷载下的滞回曲线拟合与预测。
"""

import os
import argparse
import torch
import numpy as np

from train.train import train_model
from predict.predict import run_prediction


def main(args):
    """
    主函数
    
    参数:
        args: 命令行参数
    """
    # 设置随机种子，确保结果可复现
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 设置文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    excel_path = os.path.join(os.path.dirname(current_dir), "column.xlsx")
    results_dir = os.path.join(current_dir, "results")
    
    # 创建结果目录
    os.makedirs(results_dir, exist_ok=True)
    os.makedirs(os.path.join(results_dir, "models"), exist_ok=True)
    os.makedirs(os.path.join(results_dir, "figures"), exist_ok=True)
    os.makedirs(os.path.join(results_dir, "metrics"), exist_ok=True)
    
    # 训练模式
    if args.mode == "train" or args.mode == "all":
        print("\n" + "=" * 50)
        print("开始训练钢筋混凝土柱滞回曲线PINN模型...")
        print("=" * 50)
        
        model, history, normalization_params = train_model(
            excel_path=excel_path,
            results_dir=results_dir,
            batch_size=args.batch_size,
            epochs=args.epochs,
            hidden_dim=args.hidden_dim,
            hidden_layers=args.hidden_layers,
            lr=args.learning_rate,
            lambda_data=args.lambda_data,
            lambda_physics=args.lambda_physics,
            patience=args.patience,
            l2_reg=args.l2_reg,
            use_lbfgs=args.use_lbfgs
        )
        
        print("\n训练完成！模型已保存至:", os.path.join(results_dir, "models"))
    
    # 预测模式
    if args.mode == "predict" or args.mode == "all":
        print("\n" + "=" * 50)
        print("开始预测钢筋混凝土柱滞回曲线...")
        print("=" * 50)
        
        # 确定使用哪个模型文件
        if args.mode == "predict":
            if args.model_path:
                model_path = args.model_path
            else:
                # 优先使用存在的模型文件
                best_model = os.path.join(results_dir, "models", "best_model.pth")
                final_model = os.path.join(results_dir, "models", "final_model.pth")
                fine_tuned_model = os.path.join(results_dir, "models", "fine_tuned_model.pth")
                
                # 按优先级检查模型文件是否存在
                if os.path.exists(best_model):
                    model_path = best_model
                    print(f"使用最佳模型: {model_path}")
                elif os.path.exists(final_model):
                    model_path = final_model
                    print(f"使用最终模型: {model_path}")
                elif os.path.exists(fine_tuned_model):
                    model_path = fine_tuned_model
                    print(f"使用精调模型: {model_path}")
                else:
                    raise FileNotFoundError("找不到模型文件，请先训练模型或指定模型路径")
        else:  # mode == "all"
            # 检查可用的模型文件
            best_model = os.path.join(results_dir, "models", "best_model.pth")
            final_model = os.path.join(results_dir, "models", "final_model.pth")
            fine_tuned_model = os.path.join(results_dir, "models", "fine_tuned_model.pth")
            
            # 按优先级检查模型文件是否存在
            if os.path.exists(fine_tuned_model):
                model_path = fine_tuned_model
                print(f"使用精调模型: {model_path}")
            elif os.path.exists(best_model):
                model_path = best_model
                print(f"使用最佳模型: {model_path}")
            elif os.path.exists(final_model):
                model_path = final_model
                print(f"使用最终模型: {model_path}")
            else:
                raise FileNotFoundError("找不到模型文件，训练可能未成功完成")
        
        # 运行预测
        metrics = run_prediction(excel_path, model_path, results_dir)
        
        print("\n预测完成！结果已保存至:", results_dir)


if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="钢筋混凝土柱滞回曲线PINN模型训练与预测")
    
    # 运行模式
    parser.add_argument("--mode", type=str, default="all", choices=["train", "predict", "all"],
                        help="运行模式: train(仅训练), predict(仅预测), all(训练+预测)")
    
    # 训练参数
    parser.add_argument("--batch_size", type=int, default=32, help="批次大小")
    parser.add_argument("--epochs", type=int, default=1000, help="训练轮数")
    parser.add_argument("--hidden_dim", type=int, default=100, help="隐藏层维度")
    parser.add_argument("--hidden_layers", type=int, default=4, help="隐藏层数量")
    parser.add_argument("--learning_rate", type=float, default=1e-3, help="学习率")
    parser.add_argument("--lambda_data", type=float, default=1.0, help="数据匹配损失权重")
    parser.add_argument("--lambda_physics", type=float, default=0.5, help="物理残差损失权重")
    parser.add_argument("--patience", type=int, default=20, help="早停耐心值")
    parser.add_argument("--l2_reg", type=float, default=1e-5, help="L2正则化系数")
    parser.add_argument("--use_lbfgs", action="store_true", help="是否使用L-BFGS优化器进行精细调参")
    
    # 预测参数
    parser.add_argument("--model_path", type=str, default=None, help="模型文件路径（仅在预测模式下使用）")
    
    args = parser.parse_args()
    
    # 运行主函数
    main(args)