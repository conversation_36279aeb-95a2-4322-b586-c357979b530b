import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

def read_excel_data(file_path):
    """读取Excel文件中的数据"""
    try:
        # 读取Excel文件
        xls = pd.ExcelFile(file_path)
        print(f'Excel文件包含的工作表: {xls.sheet_names}')
        
        data_dict = {}
        # 遍历每个工作表
        for sheet in xls.sheet_names:
            df = pd.read_excel(file_path, sheet_name=sheet)
            print(f'\n工作表 {sheet} 的形状: {df.shape}')
            print(f'工作表 {sheet} 的列名: {list(df.columns)}')
            print(f'\n工作表 {sheet} 的前5行:\n{df.head()}')
            
            data_dict[sheet] = df
        
        return data_dict
    except Exception as e:
        print(f'读取Excel文件时出错: {e}')
        return None

def plot_data(data_dict):
    """绘制数据图表"""
    for sheet_name, df in data_dict.items():
        # 检查数据列
        if len(df.columns) >= 2:
            plt.figure(figsize=(10, 6))
            plt.plot(df.iloc[:, 0], df.iloc[:, 1], 'o-', label=sheet_name)
            plt.xlabel(df.columns[0])
            plt.ylabel(df.columns[1])
            plt.title(f'{sheet_name} 数据图表')
            plt.legend()
            plt.grid(True)
            plt.savefig(f'{sheet_name}_data.png')
            plt.close()
            print(f'已保存 {sheet_name} 数据图表')

if __name__ == "__main__":
    file_path = 'd:/column/F2/data.xlsx'
    data = read_excel_data(file_path)
    
    if data:
        plot_data(data)