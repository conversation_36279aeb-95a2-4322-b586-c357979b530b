import numpy as np
import torch

def generate_training_data(is_test=False, noise_level=0.0, damage_mode='single', stage=0):
    """生成训练或测试数据，支持多种损伤模式和课程学习
    
    Args:
        is_test: 是否生成测试数据
        noise_level: 噪声水平 (0.0-1.0)
        damage_mode: 损伤模式 ('single', 'multi', 'random')
        stage: 训练阶段 (0-3)，用于课程学习
        
    Returns:
        data: 包含坐标、位移场、应力场和损伤场的字典
    """
    # 设置随机种子以确保可复现性
    np.random.seed(42 if not is_test else 43)
    
    # 定义板的几何参数
    L_x, L_y, L_z = 1.0, 1.0, 0.1  # 板的尺寸 (m)
    
    # 生成网格点 - 增加点数以提高分辨率
    n_points = 2000 if not is_test else 1000
    
    # 使用更均匀的网格点分布，而不是完全随机
    if stage >= 2:  # 高级阶段使用更均匀的网格
        # 创建均匀网格
        nx, ny, nz = int(np.cbrt(n_points)), int(np.cbrt(n_points)), int(np.cbrt(n_points))
        x = np.linspace(0, L_x, nx)
        y = np.linspace(0, L_y, ny)
        z = np.linspace(0, L_z, nz)
        X, Y, Z = np.meshgrid(x, y, z)
        coords = np.column_stack([X.flatten(), Y.flatten(), Z.flatten()])
        
        # 随机选择所需数量的点
        # 确保不会尝试抽取超过总体大小的样本
        total_points = len(coords)
        if n_points <= total_points:
            indices = np.random.choice(total_points, n_points, replace=False)
            coords = coords[indices]
        else:
            # 如果需要的点数超过了网格点数，则使用有放回采样
            print(f"警告: 请求的点数 {n_points} 超过了网格点数 {total_points}，切换到有放回采样")
            indices = np.random.choice(total_points, n_points, replace=True)
            coords = coords[indices]
        
        # 添加一些随机扰动以避免完全规则的网格
        coords += np.random.normal(0, 0.01, coords.shape)
        
        # 确保坐标在有效范围内
        coords[:, 0] = np.clip(coords[:, 0], 0, L_x)
        coords[:, 1] = np.clip(coords[:, 1], 0, L_y)
        coords[:, 2] = np.clip(coords[:, 2], 0, L_z)
    else:
        # 早期阶段使用随机采样，但增加损伤区域的采样密度
        coords = np.random.rand(n_points, 3)
        coords[:, 0] *= L_x  # x 坐标 [0, L_x]
        coords[:, 1] *= L_y  # y 坐标 [0, L_y]
        coords[:, 2] *= L_z  # z 坐标 [0, L_z]
    
    # 根据损伤模式生成损伤场
    damage = np.zeros(n_points)
    
    if damage_mode == 'single' or (is_test and stage < 2):
        # 单点局部损伤
        damage_center = np.array([0.5, 0.5, 0.05]) if not is_test else np.array([0.3, 0.7, 0.05])
        damage_radius = 0.2 if not is_test else 0.15
        damage_max = 0.8 if not is_test else 0.6
        
        distances = np.sqrt(np.sum((coords - damage_center)**2, axis=1))
        damage[distances < damage_radius] = damage_max * (1 - distances[distances < damage_radius] / damage_radius)
    
    elif damage_mode == 'multi':
        # 多点损伤模式
        centers = [
            np.array([0.3, 0.3, 0.05]),  # 左下
            np.array([0.7, 0.7, 0.05]),  # 右上
            np.array([0.3, 0.7, 0.05])   # 左上
        ]
        radii = [0.15, 0.12, 0.1]  # 不同的损伤半径
        max_damages = [0.7, 0.5, 0.3]  # 不同的最大损伤程度
        
        for center, radius, max_damage in zip(centers, radii, max_damages):
            distances = np.sqrt(np.sum((coords - center)**2, axis=1))
            current_damage = np.zeros(n_points)
            current_damage[distances < radius] = max_damage * (1 - distances[distances < radius] / radius)
            # 取最大值，模拟损伤叠加效应
            damage = np.maximum(damage, current_damage)
    
    elif damage_mode == 'random':
        # 随机分布的多点损伤
        n_damages = np.random.randint(3, 6)  # 随机生成3-5个损伤点
        
        for _ in range(n_damages):
            # 随机生成损伤中心、半径和最大损伤程度
            center = np.array([np.random.uniform(0.2, 0.8), np.random.uniform(0.2, 0.8), np.random.uniform(0.02, 0.08)])
            radius = np.random.uniform(0.05, 0.15)
            max_damage = np.random.uniform(0.3, 0.8)
            
            distances = np.sqrt(np.sum((coords - center)**2, axis=1))
            current_damage = np.zeros(n_points)
            current_damage[distances < radius] = max_damage * (1 - distances[distances < radius] / radius)
            # 取最大值，模拟损伤叠加效应
            damage = np.maximum(damage, current_damage)
    
    # 重塑损伤场
    damage = damage.reshape(-1, 1)
    
    # 生成更真实的位移场，考虑边界条件和物理约束
    # 基础位移场 - 使用更复杂的函数以模拟真实物理场景
    x, y, z = coords[:, 0], coords[:, 1], coords[:, 2]
    
    # 基础位移场 - 考虑边界条件
    u_base = 0.01 * np.sin(np.pi * x / L_x) * np.sin(np.pi * y / L_y) * (z / L_z)
    v_base = 0.01 * np.sin(np.pi * x / L_x) * np.sin(np.pi * y / L_y) * (z / L_z)
    w_base = 0.02 * np.sin(2 * np.pi * x / L_x) * np.sin(2 * np.pi * y / L_y) * (z / L_z)
    
    # 添加边界条件影响 - 底面固定
    boundary_factor = np.minimum(1.0, z / (0.1 * L_z))**2  # 平滑过渡
    u_base *= boundary_factor
    v_base *= boundary_factor
    w_base *= boundary_factor
    
    # 考虑损伤对位移的影响 - 使用更复杂的关系
    damage_effect = 1.0 + 5.0 * damage.flatten() * (1.0 - np.exp(-10.0 * damage.flatten()))
    u = u_base * damage_effect
    v = v_base * damage_effect
    w = w_base * damage_effect * 1.5  # 垂直方向位移更明显
    
    # 添加噪声 - 根据训练阶段调整噪声水平
    if noise_level > 0:
        # 根据阶段调整噪声水平
        stage_noise = noise_level
        if stage == 0:  # 第一阶段 - 低噪声
            stage_noise *= 0.5
        elif stage == 1:  # 第二阶段 - 中等噪声
            stage_noise *= 0.75
        elif stage == 2:  # 第三阶段 - 标准噪声
            stage_noise *= 1.0
        elif stage >= 3:  # 第四阶段 - 高噪声，测试鲁棒性
            stage_noise *= 1.5
        
        u += stage_noise * np.std(u) * np.random.randn(n_points)
        v += stage_noise * np.std(v) * np.random.randn(n_points)
        w += stage_noise * np.std(w) * np.random.randn(n_points)
    
    # 组合位移场
    displacement = np.column_stack([u, v, w])
    
    # 返回数据字典
    data = {
        'coords': coords,
        'displacement': displacement,
        'damage': damage
    }
    
    return data


def generate_boundary_points(n_boundary_points=200):
    """生成边界点
    
    Args:
        n_boundary_points: 边界点数量
        
    Returns:
        boundary_points: 边界点坐标
        boundary_values: 边界点的位移值
    """
    # 设置随机种子以确保可复现性
    np.random.seed(42)
    
    # 定义板的几何参数
    L_x, L_y, L_z = 1.0, 1.0, 0.1  # 板的尺寸 (m)
    
    # 生成边界点（简化为底面固定）
    boundary_points = np.random.rand(n_boundary_points, 3)
    boundary_points[:, 0] *= L_x  # x 坐标 [0, L_x]
    boundary_points[:, 1] *= L_y  # y 坐标 [0, L_y]
    boundary_points[:, 2] = 0.0   # z 坐标 = 0 (底面)
    
    # 边界条件：底面固定
    boundary_values = np.zeros((n_boundary_points, 3))  # 位移为零
    
    return boundary_points, boundary_values