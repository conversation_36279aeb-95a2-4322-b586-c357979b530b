"""
PINN-VUMAT完整工作流自动化脚本
一键执行从训练到验证的全流程
"""

import subprocess
import sys
import time
from pathlib import Path

def run_command(cmd, description):
    """运行命令并显示进度"""
    print(f"\n{'='*60}")
    print(f"执行: {description}")
    print(f"命令: {cmd}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ 成功: {description}")
            if result.stdout:
                print(result.stdout)
        else:
            print(f"✗ 失败: {description}")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            return False
        return True
    except Exception as e:
        print(f"✗ 执行出错: {e}")
        return False

def check_abaqus():
    """检查Abaqus是否可用"""
    try:
        result = subprocess.run("abaqus information=version", shell=True, 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ Abaqus已安装")
            return True
        else:
            print("✗ Abaqus未找到或未正确配置")
            return False
    except:
        print("✗ 无法检测Abaqus")
        return False

def get_latest_vumat_dir():
    """获取最新的vumat验证目录"""
    results_dir = Path("results")
    if not results_dir.exists():
        return None
    
    sessions = sorted([d for d in results_dir.iterdir() 
                      if d.is_dir() and d.name.startswith("session_")])
    if not sessions:
        return None
    
    vumat_dir = sessions[-1] / "vumat_verification"
    return vumat_dir if vumat_dir.exists() else None

def run_full_workflow(skip_training=False, test_type="tensile"):
    """运行完整工作流"""
    print("\n" + "="*60)
    print("PINN-VUMAT 完整工作流 V2.0")
    print("="*60)
    
    start_time = time.time()
    
    # 步骤1: PINN训练（可选）
    if not skip_training:
        if not run_command("python main.py train", "PINN模型训练"):
            print("\n训练失败，是否继续使用现有模型？(y/n)")
            if input().lower() != 'y':
                return
    else:
        print("\n跳过训练，使用现有模型")
    
    # 步骤2: 搭建Vumat测试环境
    if not run_command(f"python setup_vumat_test.py {test_type}", 
                      f"搭建{test_type}测试环境"):
        return
    
    # 步骤3: 生成材料卡片
    if not run_command("python generate_material_card.py", "生成Abaqus材料卡片"):
        return
    
    # 步骤4: 运行Abaqus仿真
    vumat_dir = get_latest_vumat_dir()
    if not vumat_dir:
        print("✗ 未找到vumat验证目录")
        return
    
    # 获取测试文件名
    test_files = list(vumat_dir.glob("*_test.inp"))
    if not test_files:
        print("✗ 未找到测试文件")
        return
    
    job_name = test_files[0].stem
    
    # 检查Abaqus
    if check_abaqus():
        # 切换到工作目录并运行Abaqus
        original_dir = Path.cwd()
        try:
            import os
            os.chdir(vumat_dir)
            print(f"\n当前工作目录: {Path.cwd()}")
            
            abaqus_cmd = f"abaqus job={job_name} user=full_cyclic_vumat.for interactive"
            if run_command(abaqus_cmd, "Abaqus有限元分析"):
                print("\n等待分析完成...")
                time.sleep(5)  # 给Abaqus一些时间完成文件写入
        finally:
            os.chdir(original_dir)
    else:
        print("\n跳过Abaqus分析（Abaqus不可用）")
    
    # 步骤5: 后处理
    if check_abaqus():
        run_command("abaqus python post_process.py", "Abaqus后处理")
    else:
        run_command("python post_process.py", "后处理（模拟模式）")
    
    # 完成
    elapsed_time = time.time() - start_time
    print(f"\n{'='*60}")
    print(f"✓ 工作流完成!")
    print(f"总耗时: {elapsed_time:.1f} 秒")
    print(f"结果保存在: {vumat_dir}")
    print(f"{'='*60}")

def main():
    """主函数"""
    print("PINN-VUMAT 自动化工作流")
    print("\n选项:")
    print("1. 完整流程（包括训练）")
    print("2. 验证流程（跳过训练）")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ")
    
    if choice == '1':
        print("\n选择测试类型:")
        print("1. 单轴拉伸 (tensile)")
        print("2. 单轴压缩 (compression)")
        print("3. 循环加载 (cyclic)")
        
        test_choice = input("\n请选择 (1-3): ")
        test_map = {'1': 'tensile', '2': 'compression', '3': 'cyclic'}
        test_type = test_map.get(test_choice, 'tensile')
        
        run_full_workflow(skip_training=False, test_type=test_type)
        
    elif choice == '2':
        print("\n选择测试类型:")
        print("1. 单轴拉伸 (tensile)")
        print("2. 单轴压缩 (compression)")
        print("3. 循环加载 (cyclic)")
        
        test_choice = input("\n请选择 (1-3): ")
        test_map = {'1': 'tensile', '2': 'compression', '3': 'cyclic'}
        test_type = test_map.get(test_choice, 'tensile')
        
        run_full_workflow(skip_training=True, test_type=test_type)
        
    elif choice == '3':
        print("退出程序")
        return
    else:
        print("无效选择")

if __name__ == "__main__":
    main() 