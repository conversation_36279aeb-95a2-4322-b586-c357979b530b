import os
import torch
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt

# 导入自定义模块
from data_processor import DataProcessor
from pinn_model import PINN, PINNTrainer
from visualization import Visualizer
# 导入中文字体配置
from font_config import configure_chinese_font

def main():
    # 配置中文字体
    configure_chinese_font()
    
    # 设置随机种子，确保结果可复现
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 创建结果和检查点目录
    # 使用绝对路径确保结果保存在正确位置
    current_dir = os.path.dirname(os.path.abspath(__file__))
    results_dir = os.path.join(current_dir, 'results')
    checkpoint_dir = os.path.join(current_dir, 'checkpoints')
    os.makedirs(results_dir, exist_ok=True)
    os.makedirs(checkpoint_dir, exist_ok=True)
    
    # 设置设备 (CPU/GPU)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 1. 数据处理
    print("\n=== 数据处理 ===")
    # 假设试件初始长度为100mm，横截面积为10000mm²
    data_processor = DataProcessor("d:/column/Fortran/data.xlsx", L0=100.0, A=10000.0)
    
    # 加载数据
    if not data_processor.load_data():
        print("数据加载失败，程序退出")
        return
    
    # 归一化数据
    data_processor.normalize_data()
    
    # 创建数据加载器
    train_loader, val_loader = data_processor.create_dataloader(batch_size=32, train_ratio=0.8)
    
    # 绘制原始数据
    data_processor.plot_data(save_path=os.path.join(results_dir, 'raw_data.png'))
    
    # 2. 模型定义
    print("\n=== 模型定义 ===")
    # 创建增强版PINN模型 - 更深更宽的网络结构，增强滞回特性建模能力
    model = PINN(hidden_layers=8, hidden_units=384, history_length=15)
    print(f"模型结构:\n{model}")
    
    # 3. 训练设置
    print("\n=== 训练设置 ===")
    # 设置材料参数
    E = 30000.0  # 弹性模量 (MPa)
    eps0 = 0.0001  # 损伤起始应变阈值
    k = 100.0  # 损伤演化参数
    
    # 创建训练器
    trainer = PINNTrainer(model, E=E, eps0=eps0, k=k, device=device, history_length=15)
    
    # 设置优化器 - 使用更低的初始学习率和权重衰减
    optimizer = optim.Adam(model.parameters(), lr=0.0003, weight_decay=2e-5)
    
    # 设置损失权重调度
    def lambda1_schedule(epoch):
        # 物理损失权重随着训练进行逐渐增加
        return min(2.0, 0.5 + epoch / 500)
    
    def lambda2_schedule(epoch):
        # 损伤损失权重保持适中
        return 1.0
    
    def lambda3_schedule(epoch):
        # 平滑约束权重保持较小
        return 0.05
    
    def lambda4_schedule(epoch):
        # 滞回特性约束权重随训练进行显著增加 - 关键改进
        return min(5.0, 1.0 + epoch / 200)
    
    def lambda5_schedule(epoch):
        # Bouc-Wen模型约束权重随训练进行逐渐增加
        return min(3.0, 1.0 + epoch / 300)
        
    def lambda6_schedule(epoch):
        # 循环路径一致性约束权重 - 训练后期增加
        return min(2.5, 0.5 + epoch / 400)
    
    # 4. 训练模型
    print("\n=== 开始训练 ===")
    num_epochs = 3000  # 显著增加训练轮数以捕捉复杂滞回特性
    history = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        optimizer=optimizer,
        num_epochs=num_epochs,
        lambda1_schedule=lambda1_schedule,
        lambda2_schedule=lambda2_schedule,
        lambda3_schedule=lambda3_schedule,
        lambda4_schedule=lambda4_schedule,
        lambda5_schedule=lambda5_schedule,
        lambda6_schedule=lambda6_schedule,  # 添加循环路径一致性约束
        checkpoint_dir=checkpoint_dir,
        save_freq=100,
        patience=150  # 增加早停耐心值
    )
    
    # 5. 可视化结果
    print("\n=== 可视化结果 ===")
    visualizer = Visualizer(data_processor, trainer)
    visualizer.plot_all(save_dir=results_dir)
    
    print("\n=== 训练完成 ===")
    print(f"结果已保存至 {os.path.abspath(results_dir)}")
    print(f"模型检查点已保存至 {os.path.abspath(checkpoint_dir)}")

if __name__ == "__main__":
    main()