"""
混凝土受拉受压全滞回曲线PINN主程序
整合训练、预测等功能
"""

import sys
import argparse
from pathlib import Path

from train import Trainer
from predict_hysteresis import HysteresisPredictor, main as predict_main


def train_model(args):
    """训练模型"""
    print("开始训练混凝土双向损伤PINN模型")
    print("=" * 60)
    
    # 配置训练参数
    config = {
        'num_epochs': args.epochs,
        'learning_rate': args.lr,
        'batch_size': args.batch_size,
        'hidden_size': args.hidden_size,
        'num_layers': args.num_layers,
        'save_interval': args.save_interval,
        'print_interval': args.print_interval
    }
    
    # 创建训练器
    trainer = Trainer(config)
    
    # 加载数据
    trainer.load_data(args.data_file)
    
    # 初始化模型
    trainer.initialize_model()
    
    # 开始训练
    trainer.train()
    
    print("\n训练完成!")
    return trainer.results_dir


def predict_model(args):
    """使用模型进行预测"""
    print("开始预测混凝土全滞回曲线")
    print("=" * 60)
    
    # 确定模型路径
    model_path = args.model_path
    
    if model_path == "latest":
        # 查找最新的模型
        results_dir = Path("results")
        if results_dir.exists():
            sessions = sorted([d for d in results_dir.iterdir() 
                             if d.is_dir() and d.name.startswith("session_")])
            if sessions:
                latest_session = sessions[-1]
                best_model = latest_session / "training" / "best_model.pth"
                if best_model.exists():
                    model_path = str(best_model)
                    print(f"使用最新模型: {model_path}")
                else:
                    print("错误: 未找到最新的训练模型")
                    return
            else:
                print("错误: 未找到任何训练会话")
                return
        else:
            print("错误: results目录不存在")
            return
    
    # 创建预测器
    try:
        predictor = HysteresisPredictor(model_path)
    except Exception as e:
        print(f"加载模型失败: {e}")
        print("提示: 如果是PyTorch版本兼容性问题，请尝试:")
        print("1. 重新训练模型")
        print("2. 或者降级PyTorch版本: pip install torch==1.13.0")
        return
    
    # 生成加载路径
    print("\n生成循环加载路径...")
    strain_path = predictor.generate_cyclic_loading_path(
        max_tensile_strain=args.max_tensile_strain,
        max_compressive_strain=args.max_compressive_strain,
        num_cycles=args.num_cycles,
        points_per_segment=args.points_per_segment
    )
    
    # 预测响应
    print("预测力学响应...")
    results = predictor.predict_response(strain_path)
    
    # 分析结果
    print("分析滞回特性...")
    analysis = predictor.analyze_hysteresis(results)
    
    print(f"\n分析结果:")
    print(f"  能量耗散: {analysis['energy_dissipation']:.2f}")
    print(f"  最大拉应力: {analysis['max_tensile_stress']:.2f} MPa")
    print(f"  最大压应力: {analysis['max_compressive_stress']:.2f} MPa")
    print(f"  残余应变: {analysis['residual_strain']:.6f}")
    
    # 绘制和保存结果
    print("\n绘制预测结果...")
    predictor.plot_results(results, save_name='hysteresis_prediction')
    
    print("保存预测结果...")
    predictor.save_results(results, analysis)
    
    print("\n预测完成!")


def train_and_predict(args):
    """训练模型并立即进行预测"""
    # 先训练
    results_dir = train_model(args)
    
    # 更新预测参数
    args.model_path = f"{results_dir}/training/best_model.pth"
    
    # 再预测
    print("\n" + "=" * 60)
    print("训练完成，开始预测...")
    print("=" * 60 + "\n")
    
    predict_model(args)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='混凝土受拉受压全滞回曲线PINN')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 训练命令
    parser_train = subparsers.add_parser('train', help='训练PINN模型')
    parser_train.add_argument('--data-file', type=str, default='cyclic_data.xlsx',
                            help='训练数据文件路径 (默认: cyclic_data.xlsx)')
    parser_train.add_argument('--epochs', type=int, default=3000,
                            help='训练轮数 (默认: 3000)')
    parser_train.add_argument('--lr', type=float, default=0.001,
                            help='学习率 (默认: 0.001)')
    parser_train.add_argument('--batch-size', type=int, default=32,
                            help='批次大小 (默认: 32)')
    parser_train.add_argument('--hidden-size', type=int, default=64,
                            help='隐藏层大小 (默认: 64)')
    parser_train.add_argument('--num-layers', type=int, default=6,
                            help='网络层数 (默认: 6)')
    parser_train.add_argument('--save-interval', type=int, default=100,
                            help='模型保存间隔 (默认: 100)')
    parser_train.add_argument('--print-interval', type=int, default=50,
                            help='打印间隔 (默认: 50)')
            
    # 预测命令
    parser_predict = subparsers.add_parser('predict', help='使用训练好的模型预测')
    parser_predict.add_argument('--model-path', type=str, default='latest',
                              help='模型文件路径 (默认: latest，使用最新模型)')
    parser_predict.add_argument('--max-tensile-strain', type=float, default=0.002,
                              help='最大拉应变 (默认: 0.002)')
    parser_predict.add_argument('--max-compressive-strain', type=float, default=-0.003,
                              help='最大压应变 (默认: -0.003)')
    parser_predict.add_argument('--num-cycles', type=int, default=3,
                              help='循环次数 (默认: 3)')
    parser_predict.add_argument('--points-per-segment', type=int, default=50,
                              help='每段点数 (默认: 50)')
        
    # 训练并预测命令
    parser_both = subparsers.add_parser('train-predict', help='训练模型并立即预测')
    parser_both.add_argument('--data-file', type=str, default='cyclic_data.xlsx',
                           help='训练数据文件路径')
    parser_both.add_argument('--epochs', type=int, default=3000,
                           help='训练轮数')
    parser_both.add_argument('--lr', type=float, default=0.001,
                           help='学习率')
    parser_both.add_argument('--batch-size', type=int, default=32,
                           help='批次大小')
    parser_both.add_argument('--hidden-size', type=int, default=64,
                           help='隐藏层大小')
    parser_both.add_argument('--num-layers', type=int, default=6,
                           help='网络层数')
    parser_both.add_argument('--save-interval', type=int, default=100,
                           help='模型保存间隔')
    parser_both.add_argument('--print-interval', type=int, default=50,
                           help='打印间隔')
    parser_both.add_argument('--max-tensile-strain', type=float, default=0.002,
                           help='最大拉应变')
    parser_both.add_argument('--max-compressive-strain', type=float, default=-0.003,
                           help='最大压应变')
    parser_both.add_argument('--num-cycles', type=int, default=3,
                           help='循环次数')
    parser_both.add_argument('--points-per-segment', type=int, default=50,
                           help='每段点数')
    
    args = parser.parse_args()
    
    if args.command is None:
        print("混凝土受拉受压全滞回曲线PINN")
        print("=" * 60)
        print("请使用以下命令之一:")
        print("  python main.py train          - 训练模型")
        print("  python main.py predict        - 使用模型预测")
        print("  python main.py train-predict  - 训练并预测")
        print("\n使用 --help 查看更多选项")
        return
    
    if args.command == 'train':
        train_model(args)
    elif args.command == 'predict':
        predict_model(args)
    elif args.command == 'train-predict':
        train_and_predict(args)


if __name__ == "__main__":
    main() 