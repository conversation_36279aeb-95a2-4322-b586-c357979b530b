的，我已经仔细阅读了您提供的所有文件，包括《混凝土弹塑性损伤本构模型研究 II: 数值计算和试验验证》、《混凝土弹塑性损伤本构关系统一模型》和《混凝土弹塑性损伤本构模型研究 I: 基本公式》，以及您当前的PINN框架阶段性成果和扩展需求。

您目前的PINN框架在单轴受拉混凝土损伤参数识别和滞回行为预测方面表现出色，物理忠实度高。接下来，您需要扩展模型以处理受压加载，最终实现能够拟合受拉受压全滞回曲线的目标。

这个扩展的核心在于：

1.  **数据识别与处理：** 从应变-应力数据中准确识别加载方向（受拉/受压）以及加卸载状态。
2.  **模型扩展：** PINN模型如何同时学习和预测受拉和受压状态下的物理响应。
3.  **物理约束的融入：** 根据参考文献中的统一本构模型，在PINN中正确引入受压状态的损伤演化和塑性行为，并加入相应的可训练参数。
4.  **损失函数调整：** 引入新的损失项或调整现有损失项，以确保模型在受压状态下的物理准确性。

下面我将为您梳理详细的框架总结、架构扩展思路和实现逻辑。

-----

### **框架总结与架构扩展方案**

#### **一、现有PINN框架总结**

您当前的PINN框架主要由以下几个核心部分组成：

1.  **`DataProcessor` (数据预处理)：** 负责加载实验数据（如`tension.xlsx`），计算应变增量，并估算初始弹性模量($E\_0$)和单轴抗拉强度($f\_t$)等材料物理常数。
2.  **`DamagePINNV2` (PINN模型)：** 这是一个基于神经网络的模型，输入是时间步（或应变历史序列的索引），输出是当前时间步的应力($\\sigma$)、通用损伤变量($d$)和塑性应变增量系数($\\xi$)。
3.  **`PhysicsCalculatorV2` (物理约束计算器)：** 这是物理知识注入的核心。它接收神经网络预测的损伤和塑性参数，并结合物理常数($E\_0$, $f\_t$)和应变增量序列，通过步进积分的方式，根据预设的物理本构关系（目前主要是受拉损伤和塑性）计算出物理意义上的应力、损伤和塑性应变序列。它负责将损伤和塑性应变的物理演化规律融入到模型中。
4.  **`LossCalculator` (损失函数计算器)：** 定义了总损失函数，通常包含：
      * **数据拟合损失($L\_{data}$):** 衡量模型预测应力与实验应力之间的差异。
      * **本构自洽损失($L\_{stress}$):** 确保模型预测的应力与物理计算器根据模型预测的损伤和塑性推导出的应力一致。
      * **损伤物理损失($L\_{damage}$):** 衡量神经网络预测的损伤与物理计算器根据损伤演化法则推导出的损伤之间的差异。
      * **塑性物理损失($L\_{plastic}$):** 衡量神经网络预测的塑性应变与物理计算器根据塑性演化法则推导出的塑性应变之间的差异。
        并通过权重(`lambda_data`, `lambda_stress`, `lambda_damage`, `lambda_plastic`)平衡各项损失的贡献。
5.  **`Trainer` (训练器)：** 负责整个训练流程，包括模型初始化、数据加载、优化器设置、训练循环、损失计算、梯度反向传播、参数更新和结果保存。
6.  **预测脚本 (如`predict_cyclic_tensile_fixed.py`)：** 加载训练好的模型和参数，并模拟特定的加载路径（目前是循环拉伸），输出预测的力学响应。

**优点：** 这种架构清晰地分离了神经网络的拟合能力和物理知识的约束，通过损失函数将两者结合，实现了物理信息神经网络的构建。目前对单轴受拉的损伤和塑性模拟已初步验证其有效性。

#### **二、参考文献中的关键物理模型回顾（统一模型）**

您的扩展需求是引入受压加载，实现全滞回曲线建模。这将主要参考吴建营等人的两篇文献，特别是关于**张量正负分解**、**受拉/受剪损伤变量**、**塑性应变演化**和**损伤演化法则**。

  * [cite\_start]**应力/应变张量正负分解：** 文献[cite: 44]强调了将应力/应变张量分解为正（拉伸）和负（压缩）分量的重要性，以描述混凝土在拉压下的不同特性。在单轴情况下，这简化为判断应力/应变的符号。
  * [cite\_start]**受拉损伤 ($d^+$) 与受剪损伤 ($d^-$)：** 文献[cite: 89]中明确指出，混凝土的损伤主要源于受拉损伤（微裂缝张开）和受剪损伤（微裂缝滑移或压碎）。我们需要引入这两个独立的损伤变量，并分别建模其演化。
  * [cite\_start]**损伤演化法则：** 文献[cite: 18, 39, 102]给出了损伤变量 $d^+$ 和 $d^-$ 的具体演化公式，它们是基于损伤能释放率 $Y^+$ 和 $Y^-$ 以及其阈值 $r^+$ 和 $r^-$ 进行定义的。
      * 对于 $d^+$ (受拉损伤)，公式为：
        [cite\_start]$d^{+} = 1 - \\frac{r\_{0}^{+}}{r^{+}}\\left((1-A^{+}) + A^{+} \\exp\\left[B^{+}\\left(1-\\frac{r^{+}}{r\_{0}^{+}}\\right)\\right]\\right)$ [cite: 18]
      * 对于 $d^-$ (受剪损伤)，公式为：
        [cite\_start]$d^{-} = 1 - \\frac{r\_{0}^{-}}{r^{-}}\\left((1-A^{-}) + A^{-} \\exp\\left[B^{-}\\left(1-\\frac{r^{-}}{r\_{0}^{-}}\\right)\\right]\\right)$ [cite: 19]
      * [cite\_start]其中，$r\_{0}^{+}$ 和 $r\_{0}^{-}$ 是初始损伤能释放率阈值，通常 $r\_{0}^{+} = f\_t$[cite: 20][cite\_start]， $r\_{0}^{-} = (1-\\alpha)f\_c$[cite: 20] [cite\_start](或 $0.3f\_c \\sim 0.5f\_c$)[cite: 100]。
      * $A^+$, $B^+$, $A^-$, $B^-$ 是待识别的材料参数。
  * [cite\_start]**塑性应变演化：** 文献[cite: 48, 9][cite\_start]提到塑性应变张量的演化法则可以通过有效应力空间塑性力学方法确定。对于简化模型，文献[cite: 48]提供了塑性应变增量的简化公式，其中 $\\xi^{\\pm}$ 描述了混凝土受拉和受压塑性变形的模型参数。这意味着拉压塑性参数可以是独立的。
  * **本构关系：** 统一的本构关系为：
    [cite\_start]$\\sigma = (1-d^+)\\overline{\\sigma}^+ + (1-d^-)\\overline{\\sigma}^-$ [cite: 7]
    在单轴情况下，如果 $\\varepsilon \> 0$（受拉），则 $\\sigma = (1-d^+)E\_0(\\varepsilon - \\varepsilon^p)$；如果 $\\varepsilon \< 0$（受压），则 $\\sigma = (1-d^-)E\_0(\\varepsilon - \\varepsilon^p)$。这将由当前应变增量和历史应力路径共同决定。

#### **三、架构扩展与实现逻辑**

我们将主要修改 `pinn_model_v2.py` (模型和物理计算器) 和 `train.py` (训练逻辑)。`data_processor.py` 可能需要增加对循环加载数据（如果提供）的识别加卸载过程。原有的`predict_cyclic_tensile_fixed.py`将作为新的全滞回预测脚本的基础。

**1. `data_processor.py` (数据预处理)**

  * **目标：** 识别加卸载过程和正负号（受拉/受压状态）。
  * **实现：**
      * **加载方向判断：** 对于循环拉压实验数据，需要记录每个时间步的应变增量 $\\Delta\\varepsilon\_i$。
          * 当 $\\Delta\\varepsilon\_i \> 0$ 时，通常是拉伸加载或受压卸载（再拉）。
          * 当 $\\Delta\\varepsilon\_i \< 0$ 时，通常是压缩加载或受拉卸载（再压）。
      * **加卸载判断：** 这通常需要跟踪历史最大/最小应变或应力。
          * 例如，在拉伸加载路径上，如果当前应变超过历史最大拉伸应变，则为加载；否则为卸载。
          * 在压缩加载路径上，如果当前应变低于历史最小压缩应变，则为加载；否则为卸载。
      * **`get_material_properties`扩展：**
          * 在现有`E0`和`f_t`估算的基础上，新增`f_c`（单轴抗压强度）的估算。这可以通过寻找数据中压缩段的峰值应力来确定。如果数据不包含单轴受压峰值，可以给一个经验默认值（例如30MPa）。
      * **`process_data`调整：** 如果需要将应变序列划分为拉伸和压缩区域，可以在这里进行标记，以便在损失函数中进行区分处理。

**2. `pinn_model_v2.py` (PINN模型与物理计算器)**

这将是修改的核心。

**A. `DamagePINNV2` 类（PINN 模型）**

  * **输出层：** 保持 `output_size=3` (应力、通用损伤变量`d_hat`、通用塑性应变增量系数`xi_hat`)。
      * 让神经网络输出一个通用的`d_hat`和`xi_hat`，然后在`PhysicsCalculatorV2`和损失函数中根据物理规则（拉压状态）动态地将它们映射到相应的物理量上。这样可以保持网络结构简洁。

**B. `PhysicsCalculatorV2` 类（物理约束计算器）**

这是引入受压物理约束和单边效应的关键。

  * **初始化：**
      * 保留 `E0`, `f_t`。
      * 新增 `f_c` (单轴抗压强度)，用于计算受压损伤的初始阈值 $r\_0^-$。
  * **`calculate_physics_constraints` 方法 (核心修改)：**
      * **输入参数：** 需要接受`A_plus`, `B_plus`, `A_minus`, `B_minus`, `xi_plus`, `xi_minus`作为可训练物理参数。
      * **状态变量：**
          * `epsilon_total`：累积总应变（从应变增量积分得到）。
          * `ep_total`：累积总塑性应变。
          * `d_plus_phy`：累积受拉损伤。
          * `d_minus_phy`：累积受压损伤。
          * `r_max_plus`：历史最大拉伸损伤阈值。
          * `r_max_minus`：历史最大压缩损伤阈值。
      * **循环积分逻辑：** 对每个 `delta_epsilon` 进行迭代计算。
        1.  **更新总应变：** `epsilon_total += delta_epsilon`。
        2.  **计算当前有效弹性应变：** `current_elastic_strain = epsilon_total - ep_total`。
        3.  **判断当前有效应变区域（拉伸/压缩）：** `is_tensile_region = (current_elastic_strain >= 0)`。
        4.  **计算损伤驱动力 $Y^+$ 和 $Y^-$：**
              * 在单轴情况下，简化处理：
                  * `Y_plus_current = self.E0 * torch.relu(current_elastic_strain)` （仅在拉伸弹性应变时非零）
                  * `Y_minus_current = self.E0 * torch.relu(-current_elastic_strain)` （仅在压缩弹性应变时非零，取绝对值）
        5.  **损伤演化（基于当前损伤驱动力与历史阈值）：**
              * **受拉损伤 $d^+$ 更新：**
                `if Y_plus_current > r_max_plus:` (新的拉伸损伤加载)
                `r_max_plus = Y_plus_current`
                `d_plus = 1 - (self.f_t / r_max_plus) * ((1 - A_plus) + A_plus * torch.exp(B_plus * (1 - r_max_plus / self.f_t)))`
                `d_plus = torch.clamp(d_plus, 0.0, 1.0)`
                `else: d_plus` 保持不变 (损伤不可逆)。
              * **受压损伤 $d^-$ 更新：**
                `if Y_minus_current > r_max_minus:` (新的压缩损伤加载)
                \* **注意 `r_max_minus` 的初始化：** 如果是第一次进入压缩区，应将其初始化为 `self.f_c` 或 `(1-alpha)*self.f_c`。
                `r_max_minus = Y_minus_current`
                `d_minus = 1 - (self.f_c / r_max_minus) * ((1 - A_minus) + A_minus * torch.exp(B_minus * (1 - r_max_minus / self.f_c)))`
                `d_minus = torch.clamp(d_minus, 0.0, 1.0)`
                `else: d_minus` 保持不变。
        6.  **塑性应变演化：**
              * 根据`delta_epsilon`的符号选择不同的塑性系数。
              * `if delta_epsilon > 0: delta_ep = xi_plus * torch.relu(delta_epsilon)`
              * `elif delta_epsilon < 0: delta_ep = xi_minus * delta_epsilon` (这里 `xi_minus` 应该是正值，使得 `delta_ep` 为负，与应变负向累积匹配)。
              * `ep_total += delta_ep`
        7.  **单边效应 (Unilateral Effect) 的体现：**
              * 损伤变量 `d_plus` 和 `d_minus` 的更新逻辑已经包含了损伤不可逆性。
              * 在计算物理应力时，根据 `current_elastic_strain` 的正负号，选择对应的损伤变量：
                `current_d_effective = d_plus if is_tensile_region else d_minus`
                `current_stress_phy = (1 - current_d_effective) * self.E0 * current_elastic_strain`
      * **输出：** `d_plus_phy_seq`, `d_minus_phy_seq`, `ep_phy_seq`, `stress_phy_seq`。

**3. `train.py` (训练逻辑)**

  * **新增可训练物理参数：**
      * 在`Trainer.initialize_model`中，新增`self.A_minus`, `self.B_minus`, `self.xi_minus`作为`torch.nn.Parameter`。
      * 将这些新参数加入到`optimizer`的参数列表中。
      * **参数约束：** 对新参数进行`clamp_`操作，限制其在合理范围内。
  * **物理常量的更新：** 在`Trainer.load_data`中，确保`self.config['f_c']`被正确加载。
  * **`train_epoch` 函数：**
      * **步骤B（物理约束计算）：** 调用`self.physics_calc.calculate_physics_constraints`时，传入`self.A_minus`, `self.B_minus`, `self.xi_minus`。
      * **损失函数（步骤C）：**
          * `loss_damage`：需要区分拉压损伤。神经网络输出的是一个通用的`d_hat_seq`。
              * 根据实验应变 `strain_total_exp_seq` 的符号，定义拉伸和压缩区域的损失掩码。
              * `loss_damage_plus = torch.mean((d_hat_seq - d_plus_phy_seq)**2 * damage_loss_mask_plus)`
              * `loss_damage_minus = torch.mean((d_hat_seq - d_minus_phy_seq)**2 * damage_loss_mask_minus)`
              * 总的损伤损失可以是 `lambda_damage_plus * loss_damage_plus + lambda_damage_minus * loss_damage_minus`。
          * `loss_plastic`：`ep_hat_seq`（神经网络预测的累积塑性）应与`ep_phy_seq`（物理计算的累积塑性）进行对比，因为它应该同时累积拉压塑性。因此 `loss_plastic = torch.mean((ep_hat_seq - ep_phy_seq)**2)` 保持不变，但`ep_phy_seq`的计算逻辑已在`PhysicsCalculatorV2`中改变。
          * **总损失权重：** 重新平衡各项损失权重，特别是新增的`lambda_damage_minus`。
  * **保存和绘图：** 在`save_final_model`中加入`A_minus`, `B_minus`, `xi_minus`。在`plot_training_history`中增加受压参数的演化曲线图。

**4. 预测脚本 (如`predict_cyclic_tensile_fixed.py`)**

  * **重命名：** 考虑更名为 `predict_cyclic.py` 或 `predict_hysteresis.py`。
  * **`load_model`：** 加载所有新的受压参数。
  * **`generate_realistic_cyclic_loading_path`：**
      * 该函数当前估算残余应变累积时，`plastic_strain_increment = xi_effective * strain_increment_total` 逻辑仍只适用于拉伸。
      * 需要修改为根据应变增量符号选择不同的`xi_effective`，例如：
        `if strain_increment_total > 0: plastic_strain_increment = xi_plus_effective * strain_increment_total`
        `else: plastic_strain_increment = xi_minus_effective * strain_increment_total`
  * **`predict_response_fixed` (核心改造)：**
      * **状态变量：** 维护`r_max_plus`, `r_max_minus`（拉压最大损伤阈值），`d_plus`，`d_minus`。
      * **循环逻辑：** 内部的物理演化逻辑必须与`PhysicsCalculatorV2`中实现的一致。
        1.  **应变增量：** `strain_increment = strain_path[i] - strain_path[i-1]`
        2.  **当前有效弹性应变：** `effective_elastic_strain = strain_path[i] - plastic_strain[i]`。
        3.  **损伤更新：**
              * 根据`effective_elastic_strain`的正负，计算`Y_plus_current`和`Y_minus_current`。
              * 分别更新`d_plus`和`d_minus`（遵循损伤不可逆原则，即只有当新的损伤驱动力超过历史最大阈值时才更新）。
        4.  **塑性应变更新：** 根据`strain_increment`的正负，使用`xi_plus`或`xi_minus`来累积`plastic_strain`。
        5.  **应力计算：**
              * `if effective_elastic_strain > 0: stress[i] = (1 - d_plus) * E0 * effective_elastic_strain`
              * `else: stress[i] = (1 - d_minus) * E0 * effective_elastic_strain`
              * **单边效应（卸载刚度）：** 卸载时损伤不增加，刚度沿退化后的模量路径下降。当前代码中的`stress[i] = max(0, stress[i])`是一个粗略处理。更精确的做法是，在卸载路径上，`d_effective`应该保持为卸载点的峰值损伤。这可能需要额外的逻辑来追踪加载路径的“转折点”及其对应的损伤值。

**5. 统一物理模型伪代码示例（辅助理解`PhysicsCalculatorV2`的实现）**

```python
# PhysicsCalculatorV2 中的循环积分逻辑 (简化版)

def calculate_physics_constraints_full_cycle(self, strain_increment_seq, 
                                            A_plus, B_plus, A_minus, B_minus, 
                                            xi_plus, xi_minus):
    
    epsilon_total = torch.tensor(0.0)      # 累积总应变
    ep_total = torch.tensor(0.0)           # 累积塑性应变
    d_plus = torch.tensor(0.0)             # 累积受拉损伤
    d_minus = torch.tensor(0.0)            # 累积受压损伤
    r_max_plus = torch.tensor(self.f_t)    # 历史最大拉伸损伤阈值 (初始为ft)
    # 初始压缩损伤阈值 r_0^- 可以设置为 f_c，或者根据文献中的 (1-alpha)*f_c
    r_max_minus = torch.tensor(self.f_c)   

    d_plus_phy_seq, d_minus_phy_seq, ep_phy_seq, stress_phy_seq = [], [], [], []

    for i, delta_epsilon in enumerate(strain_increment_seq):
        epsilon_total += delta_epsilon
        current_elastic_strain = epsilon_total - ep_total

        # 1. 损伤演化
        Y_plus_current = self.E0 * torch.relu(current_elastic_strain)
        Y_minus_current = self.E0 * torch.relu(-current_elastic_strain) 
        
        # 受拉损伤更新
        if Y_plus_current > r_max_plus:
            r_max_plus = Y_plus_current
            d_plus = 1 - (self.f_t / r_max_plus) * ((1 - A_plus) + A_plus * torch.exp(B_plus * (1 - r_max_plus / self.f_t)))
            d_plus = torch.clamp(d_plus, 0.0, 1.0)
        # 卸载或未达到新阈值，损伤保持不变

        # 受压损伤更新
        if Y_minus_current > r_max_minus:
            r_max_minus = Y_minus_current
            d_minus = 1 - (self.f_c / r_max_minus) * ((1 - A_minus) + A_minus * torch.exp(B_minus * (1 - r_max_minus / self.f_c)))
            d_minus = torch.clamp(d_minus, 0.0, 1.0)
        # 卸载或未达到新阈值，损伤保持不变
        
        # 2. 塑性应变演化
        delta_ep = torch.tensor(0.0)
        if delta_epsilon > 0: # 拉伸加载
            delta_ep = xi_plus * torch.relu(delta_epsilon) 
        elif delta_epsilon < 0: # 压缩加载 (delta_epsilon为负值，xi_minus为正值)
            delta_ep = xi_minus * delta_epsilon 
        
        ep_total += delta_ep

        # 3. 记录当前步的物理状态
        d_plus_phy_seq.append(d_plus.clone())
        d_minus_phy_seq.append(d_minus.clone())
        ep_phy_seq.append(ep_total.clone())

        # 4. 计算物理应力
        current_d_effective = d_plus if current_elastic_strain >= 0 else d_minus
        current_stress_phy = (1 - current_d_effective) * self.E0 * current_elastic_strain
        
        stress_phy_seq.append(current_stress_phy.clone())

    return torch.stack(d_plus_phy_seq), torch.stack(d_minus_phy_seq), torch.stack(ep_phy_seq), torch.stack(stress_phy_seq)

# LossCalculator 中的修改示例：
def calculate_total_loss(self, sigma_hat_seq, d_hat_seq, ep_hat_seq, 
                       stress_exp_seq, strain_total_seq, 
                       d_plus_phy_seq, d_minus_phy_seq, ep_phy_seq, E0):
    
    # L_data: 数据拟合损失
    loss_data = torch.mean((sigma_hat_seq - stress_exp_seq)**2)
    
    # L_stress: 本构自洽损失
    # 根据实验总应变来判断拉压状态，选择相应的损伤进行本构计算
    d_effective_for_stress = torch.where(strain_total_seq >= 0, d_hat_seq, d_hat_seq) 
    stress_physics = (1 - d_effective_for_stress) * E0 * (strain_total_seq - ep_hat_seq)
    loss_stress = torch.mean((sigma_hat_seq - stress_physics)**2)
    
    # L_damage: 损伤物理损失 - 分为拉伸和压缩部分
    # 根据实验应变符号，动态惩罚 d_hat_seq 与 d_plus_phy_seq 或 d_minus_phy_seq 的差异
    damage_loss_mask_plus = (strain_total_seq >= 0).float()
    damage_loss_mask_minus = (strain_total_seq < 0).float() 
    
    loss_damage_plus = torch.mean((d_hat_seq - d_plus_phy_seq)**2 * damage_loss_mask_plus)
    loss_damage_minus = torch.mean((d_hat_seq - d_minus_phy_seq)**2 * damage_loss_mask_minus)
    
    loss_damage_total = self.lambda_damage_plus * loss_damage_plus + self.lambda_damage_minus * loss_damage_minus

    # L_plastic: 塑性物理损失 (ep_hat_seq 是累积塑性，直接与 ep_phy_seq 对比)
    loss_plastic = torch.mean((ep_hat_seq - ep_phy_seq)**2)
    
    # 总损失
    total_loss = (self.lambda_data * loss_data + 
                 self.lambda_stress * loss_stress + 
                 loss_damage_total + 
                 self.lambda_plastic * loss_plastic)
    
    return total_loss, {
        'loss_data': loss_data.item(),
        'loss_stress': loss_stress.item(), 
        'loss_damage_plus': loss_damage_plus.item(), 
        'loss_damage_minus': loss_damage_minus.item(),
        'loss_plastic': loss_plastic.item(),
        'total_loss': total_loss.item()
    }
```

#### **四、实施步骤与建议**

1.  **数据准备：**
      * 准备包含循环拉压应力-应变数据的Excel文件。文件仍应包含 'strain' 和 'stress' 两列。
      * 在 `data_processor.py` 中，扩展`get_material_properties`以估算`f_c`。
2.  **修改 `pinn_model_v2.py`：**
      * 在 `PhysicsCalculatorV2` 的 `__init__` 中添加 `f_c`。
      * 修改 `PhysicsCalculatorV2` 的 `calculate_physics_constraints` 函数，按照上述伪代码实现双向损伤和塑性演化。初始化 `r_max_minus` 时，使用 `f_c` 或 `(1-alpha)*f_c`。确保 `d_plus` 和 `d_minus` 独立累积，塑性应变 `ep_total` 的累积要同时考虑 `xi_plus` 和 `xi_minus`。
      * 修改 `LossCalculator` 的 `calculate_total_loss` 函数，区分拉压损伤损失，并调整损失权重。
3.  **修改 `train.py`：**
      * 在 `Trainer` 的 `__init__` 中新增 `f_c` 到 `self.config`。
      * 在 `initialize_model` 中新增 `A_minus`, `B_minus`, `xi_minus` 作为可训练参数，并添加到优化器和参数约束中。
      * 在 `load_data` 后，确保 `self.config['f_c']` 被正确加载。
      * 在 `train_epoch` 中，修改 `physics_calc.calculate_physics_constraints` 的调用参数，并调整 `total_loss` 的计算。
      * 在 `save_final_model` 中保存新的参数。
      * 在 `plot_training_history` 中增加新的参数演化曲线。
4.  **修改预测脚本：**
      * 重命名为更通用的预测脚本（如`predict_hysteresis.py`）。
      * 修改 `load_model` 以加载所有新的物理参数。
      * **核心：** 彻底改造 `predict_response_fixed` 函数，使其内部的物理演化逻辑与 `PhysicsCalculatorV2` 中实现的统一本构逻辑一致。这包括：维护 `r_max_plus` 和 `r_max_minus`，根据当前应变状态（拉伸/压缩）和加载/卸载状态来更新 `d_plus` 和 `d_minus`，正确累积 `plastic_strain`，以及根据有效应变方向和相应的损伤变量来计算 `stress`。
      * `generate_realistic_cyclic_loading_path`也要确保估算的残余应变能正确反映拉压塑性累积。
      * 绘图函数也要更新以反映新的参数和全滞回曲线。

**重要提示：**

  * **初始值设置：** `A_minus`, `B_minus`, `xi_minus` 的初始值可以参考文献中受压试验的拟合结果，例如：`A_minus`设为1.5，`B_minus`设为0.5，`xi_minus`设为0.02。
  * **物理常量的确定：** `E0`, `f_t` 已有估算，`f_c`需要从数据中估算或预设。
  * **数值稳定性：** 引入更多非线性会增加训练难度。注意梯度裁剪、学习率调整和损失权重的平衡。可以尝试`lambda_damage_plus`和`lambda_damage_minus`使用不同的权重。
  * **绘图：** 确保所有绘制的应力-应变、损伤、塑性曲线都能正确区分受拉和受压阶段，并且在同一张图上展示完整的滞回环。

这个扩展将使您的PINN框架更加完善和强大。在实现过程中，请务必仔细阅读参考文献中的数学表达式，并将其精确地映射到代码逻辑中。祝您成功！

补充：
1. 每次所有训练结果和图片都在同一个文件夹分好类
2. 数据在excel，包含strain和stress两列
3. 所有输出图片正确配置字体