import torch
import torch.nn as nn
import torch.optim as optim
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置字体
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial']
plt.rcParams['axes.unicode_minus'] = False

class LiteratureBasedPINN(nn.Module):
    """基于文献的物理约束PINN模型"""
    def __init__(self, hidden_size=64, num_layers=2):
        super(LiteratureBasedPINN, self).__init__()
        
        self.lstm = nn.LSTM(1, hidden_size, num_layers, batch_first=True)
        self.fc = nn.Sequential(
            nn.Linear(hidden_size, 32),
            nn.Tanh(),
            nn.Linear(32, 2)  # 输出应力和损伤
        )
        
    def forward(self, strain_seq):
        lstm_out, _ = self.lstm(strain_seq)
        output = self.fc(lstm_out)
        
        stress = output[:, :, 0:1]
        damage = torch.sigmoid(output[:, :, 1:2])
        
        return stress, damage

class LiteratureTrainer:
    """基于文献三步法的训练器"""
    def __init__(self, model, E0=30000.0, ft=3.0):
        self.model = model
        self.E0 = E0
        self.ft = ft
        
        # 可训练参数（文献公式39）
        self.A_plus = nn.Parameter(torch.tensor(0.5))
        self.B_plus = nn.Parameter(torch.tensor(2.0))
        
        # 归一化参数
        self.strain_scale = 1.0
        self.stress_scale = 1.0
        
    def normalize_data(self, strain, stress):
        self.strain_scale = np.max(strain)
        self.stress_scale = np.max(stress)
        return strain / self.strain_scale, stress / self.stress_scale
    
    def physics_based_calculation(self, strain_seq):
        """
        基于文献的物理计算（三步法）
        1. 弹性预测
        2. 塑性修正
        3. 损伤修正
        """
        batch_size, seq_len = strain_seq.shape[0], strain_seq.shape[1]
        strain_real = strain_seq.squeeze(-1) * self.strain_scale
        
        # 初始化状态变量
        r_max = torch.full((batch_size,), self.ft)  # 损伤阈值
        eps_p = torch.zeros((batch_size,))  # 塑性应变
        
        stress_physics = []
        damage_physics = []
        
        for i in range(seq_len):
            # 步骤1：弹性预测
            eps_e = strain_real[:, i] - eps_p
            sigma_trial = self.E0 * eps_e
            
            # 步骤2：塑性修正（简化）
            # 这里简化处理，实际应该用回映算法
            sigma_eff = sigma_trial
            
            # 步骤3：损伤修正
            # 计算损伤能释放率（文献公式5）
            Y = torch.clamp(sigma_eff**2 / (2 * self.E0), min=0)
            
            # 更新损伤阈值（历史最大值）
            r_max = torch.max(r_max, Y)
            
            # 损伤演化（文献公式39a）
            # d = 1 - (r0/r) * [(1-A+) + A+*exp(B+*(1-r/r0))]
            r_ratio = r_max / self.ft
            term1 = self.ft / r_max * (1 - self.A_plus)
            term2 = self.A_plus * torch.exp(self.B_plus * (1 - r_ratio))
            d_physics = 1 - term1 - term2
            d_physics = torch.clamp(d_physics, 0, 0.99)
            
            # 名义应力（文献公式8）
            sigma_nominal = (1 - d_physics) * sigma_eff
            
            stress_physics.append(sigma_nominal / self.stress_scale)
            damage_physics.append(d_physics)
        
        return torch.stack(stress_physics, dim=1), torch.stack(damage_physics, dim=1)
    
    def train(self, strain_exp, stress_exp, epochs=2000):
        """训练模型"""
        # 数据预处理
        strain_norm, stress_norm = self.normalize_data(strain_exp, stress_exp)
        
        strain_tensor = torch.FloatTensor(strain_norm).unsqueeze(0).unsqueeze(-1)
        stress_tensor = torch.FloatTensor(stress_norm).unsqueeze(0)
        
        # 优化器
        params = list(self.model.parameters()) + [self.A_plus, self.B_plus]
        optimizer = optim.Adam(params, lr=0.001)
        
        print("开始训练（基于文献物理约束）...")
        
        for epoch in range(epochs):
            optimizer.zero_grad()
            
            # 网络预测
            stress_pred, damage_pred = self.model(strain_tensor)
            
            # 物理计算
            stress_physics, damage_physics = self.physics_based_calculation(strain_tensor)
            
            # 损失函数
            loss_data = torch.mean((stress_pred.squeeze(-1) - stress_tensor)**2)
            loss_physics = torch.mean((stress_pred.squeeze(-1) - stress_physics.squeeze(-1))**2)
            loss_damage = torch.mean((damage_pred.squeeze(-1) - damage_physics.squeeze(-1))**2)
            
            # 损伤单调性约束
            monotonic_loss = 0.0
            for i in range(1, strain_tensor.shape[1]):
                monotonic_loss += torch.relu(damage_pred[0, i-1, 0] - damage_pred[0, i, 0])
            
            total_loss = loss_data + 0.5 * loss_physics + 0.3 * loss_damage + 0.1 * monotonic_loss
            
            total_loss.backward()
            optimizer.step()
            
            # 约束参数
            with torch.no_grad():
                self.A_plus.data = torch.clamp(self.A_plus.data, 0.0, 1.0)
                self.B_plus.data = torch.clamp(self.B_plus.data, 0.1, 10.0)
            
            if epoch % 200 == 0:
                print(f"Epoch {epoch:4d}, Loss: {total_loss.item():.6f}")
                print(f"  Data: {loss_data.item():.6f}, Physics: {loss_physics.item():.6f}")
                print(f"  Damage: {loss_damage.item():.6f}, Monotonic: {monotonic_loss:.6f}")
                print(f"  A+: {self.A_plus.item():.4f}, B+: {self.B_plus.item():.4f}")
        
        print("训练完成！")
    
    def predict(self, strain_exp):
        """预测"""
        self.model.eval()
        with torch.no_grad():
            strain_norm = strain_exp / self.strain_scale
            strain_tensor = torch.FloatTensor(strain_norm).unsqueeze(0).unsqueeze(-1)
            
            # 网络预测
            stress_pred, damage_pred = self.model(strain_tensor)
            
            # 物理计算
            stress_physics, damage_physics = self.physics_based_calculation(strain_tensor)
            
            # 反归一化
            stress_pred = stress_pred.squeeze().numpy() * self.stress_scale
            damage_pred = damage_pred.squeeze().numpy()
            stress_physics = stress_physics.squeeze().numpy() * self.stress_scale
            damage_physics = damage_physics.squeeze().numpy()
            
            return stress_pred, damage_pred, stress_physics, damage_physics

def plot_comparison(strain, stress_exp, stress_pred, damage_pred, stress_physics, damage_physics):
    """绘制对比结果"""
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 应力-应变对比
    axes[0,0].plot(strain, stress_exp, 'ro-', label='Experimental', markersize=3)
    axes[0,0].plot(strain, stress_pred, 'b-', label='PINN Prediction', linewidth=2)
    axes[0,0].plot(strain, stress_physics, 'g--', label='Physics Theory', linewidth=1.5)
    axes[0,0].set_xlabel('Strain')
    axes[0,0].set_ylabel('Stress (MPa)')
    axes[0,0].set_title('Literature-based PINN: Stress-Strain')
    axes[0,0].legend()
    axes[0,0].grid(True, alpha=0.3)
    
    # 损伤演化
    axes[0,1].plot(strain, damage_pred, 'b-', label='PINN Damage', linewidth=2)
    axes[0,1].plot(strain, damage_physics, 'g--', label='Physics Damage', linewidth=1.5)
    axes[0,1].set_xlabel('Strain')
    axes[0,1].set_ylabel('Damage Variable')
    axes[0,1].set_title('Damage Evolution')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)
    
    # 预测误差
    error = stress_exp - stress_pred
    axes[1,0].plot(strain, error, 'r-', linewidth=1.5)
    axes[1,0].axhline(y=0, color='k', linestyle='--', alpha=0.5)
    axes[1,0].set_xlabel('Strain')
    axes[1,0].set_ylabel('Error (MPa)')
    axes[1,0].set_title('Prediction Error')
    axes[1,0].grid(True, alpha=0.3)
    
    # 相关性
    axes[1,1].scatter(stress_exp, stress_pred, alpha=0.6, label='PINN vs Exp')
    axes[1,1].scatter(stress_exp, stress_physics, alpha=0.6, label='Physics vs Exp')
    min_val = min(stress_exp.min(), stress_pred.min())
    max_val = max(stress_exp.max(), stress_pred.max())
    axes[1,1].plot([min_val, max_val], [min_val, max_val], 'k--', linewidth=2)
    axes[1,1].set_xlabel('Experimental Stress (MPa)')
    axes[1,1].set_ylabel('Predicted Stress (MPa)')
    axes[1,1].set_title('Correlation')
    axes[1,1].legend()
    axes[1,1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('literature_based_pinn_results.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("Literature-based PINN for Concrete Damage")
    
    # 加载数据
    try:
        df = pd.read_excel('tension.xlsx')
        strain = df['strain'].values
        stress = df['stress'].values
        print(f"Data loaded: {len(strain)} points")
        print(f"Strain: [{strain.min():.6f}, {strain.max():.6f}]")
        print(f"Stress: [{stress.min():.2f}, {stress.max():.2f}] MPa")
    except Exception as e:
        print(f"Data loading failed: {e}")
        return
    
    # 创建模型
    model = LiteratureBasedPINN(hidden_size=64, num_layers=2)
    trainer = LiteratureTrainer(model, E0=30000.0, ft=3.0)
    
    # 训练
    trainer.train(strain, stress, epochs=2000)
    
    # 预测
    print("\n预测中...")
    stress_pred, damage_pred, stress_physics, damage_physics = trainer.predict(strain)
    
    # 评估
    mse = np.mean((stress - stress_pred)**2)
    mae = np.mean(np.abs(stress - stress_pred))
    r2 = 1 - np.sum((stress - stress_pred)**2) / np.sum((stress - np.mean(stress))**2)
    
    physics_consistency = np.mean(np.abs(stress_pred - stress_physics))
    damage_consistency = np.mean(np.abs(damage_pred - damage_physics))
    
    print(f"\n=== 评估结果 ===")
    print(f"数据拟合性能:")
    print(f"  MSE: {mse:.4f}")
    print(f"  MAE: {mae:.4f}")
    print(f"  R²: {r2:.4f}")
    print(f"\n物理一致性:")
    print(f"  应力一致性: {physics_consistency:.4f}")
    print(f"  损伤一致性: {damage_consistency:.4f}")
    print(f"\n识别参数:")
    print(f"  A+ = {trainer.A_plus.item():.4f}")
    print(f"  B+ = {trainer.B_plus.item():.4f}")
    
    # 绘图
    plot_comparison(strain, stress, stress_pred, damage_pred, stress_physics, damage_physics)
    
    # 保存结果
    results = np.column_stack((strain, stress, stress_pred, damage_pred, stress_physics, damage_physics))
    np.savetxt('literature_based_results.txt', results, 
               header='strain stress_exp stress_pred damage_pred stress_physics damage_physics', 
               fmt='%.8f')
    print("\n结果已保存!")

if __name__ == "__main__":
    main() 