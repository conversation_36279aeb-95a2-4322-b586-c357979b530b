*Heading
** Simplest Axial Tension Test with Constant Beam Section

**--------------------------------------------------
**[object Object] DEFINITION
*Part, name=COLUMN
*Node
  1, 0.0, 0.0, 0.0
  2, 0.0, 0.0, 1.0
  3, 0.0, 0.0, 2.0
  4, 0.0, 0.0, 3.0
*Element, type=B31, elset=COLUMN_ELEMS
  1, 1, 2
  2, 2, 3
  3, 3, 4
** BEAM截面定义
*Beam Section, elset=COLUMN_ELEMS, material=CONCRETE_VUMAT, section=RECT
100.0
*End Part
**--------------------------------------------------

**--------------------------------------------------
** ASSEMBLY
*Assembly, name=ASSEMBLY
  *Instance, name=C1, part=COLUMN
  *End Instance

  ** 定义底部节点集
  *Nset, nset=N_BOTTOM, instance=C1
    1, 2
  ** 定义顶部节点集
  *Nset, nset=N_TOP, instance=C1
    3, 4
  ** 把所有梁单元放一个集里，供截面属性使用
  *Elset, elset=ALL_BEAMS, instance=C1
    1, 2, 3

*End Assembly
**--------------------------------------------------

**--------------------------------------------------

**--------------------------------------------------
** MATERIAL + USER SUBROUTINE
*MATERIAL, NAME=CONCRETE_VUMAT
*Density
  2.5e-9
*Depvar
  2             ** 正确写法：下一行直接写“2”
*User Material, constants=1
  30000.0
**--------------------------------------------------

**--------------------------------------------------
** STEP & LOADS
*Step, name=AXIAL_TENSION, nlgeom=NO
*Static
** 底部固定
*Boundary, op=NEW
  N_BOTTOM, 1, 3, 0.0
** 顶部在 Z 方向加载，幅值按 RAMP 从 0 加到 0.1
*Boundary, amplitude=RAMP
  N_TOP, 3, 3, 0.1
*Amplitude, name=RAMP, definition=TABULAR
  0.0, 0.0, 1.0, 1.0
*Output, history, timeinterval=0.1
*Node Output, nset=N_TOP
  U
*End Step
