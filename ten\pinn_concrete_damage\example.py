#!/usr/bin/env python
# -*- coding: utf-8 -*-

import torch
import numpy as np
import matplotlib.pyplot as plt
import os

# 导入自定义模块
from utils.data_processor import TensileDataProcessor
from models.pinn_model import ConcreteDamagePINN
from train import train_fixed_model, test_fixed_model
from predict import load_trained_model, predict_with_model, evaluate_predictions, plot_prediction_results
from utils.visualization import plot_comprehensive_results


def run_example():
    """
    运行示例：训练和评估修复后的PINN模型
    """
    # 设置参数
    data_path = "d:\\column\\ten\\tension.xlsx"  # 数据文件路径
    model_dir = "./saved_models"   # 模型保存目录
    results_dir = "./results"      # 结果保存目录
    
    # 创建保存目录
    os.makedirs(model_dir, exist_ok=True)
    os.makedirs(results_dir, exist_ok=True)
    
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 加载和预处理数据
    print("\n1. 加载和预处理数据...")
    data_processor = TensileDataProcessor(data_path)
    strain_data, stress_data = data_processor.preprocess_data(normalize=True)
    
    if strain_data is None or stress_data is None:
        print("数据加载失败，请检查数据文件路径。")
        return
    
    print(f"数据加载成功，形状: 应变 {strain_data.shape}, 应力 {stress_data.shape}")
    
    # 训练模型
    print("\n2. 训练修复后的PINN模型...")
    model, processor = train_fixed_model()
    print("模型训练完成！")
    
    # 测试模型
    print("\n3. 测试模型性能...")
    predictions, targets = test_fixed_model(model, processor)
    
    # 计算评估指标
    from sklearn.metrics import mean_squared_error, r2_score
    mse = mean_squared_error(targets, predictions)
    r2 = r2_score(targets, predictions)
    mae = np.mean(np.abs(targets - predictions))
    
    print(f"\n模型性能评估:")
    print(f"  均方误差 (MSE): {mse:.6f}")
    print(f"  决定系数 (R²): {r2:.6f}")
    print(f"  平均绝对误差 (MAE): {mae:.6f}")
    
    # 使用预测模块进行额外的分析
    print("\n4. 使用预测模块进行详细分析...")
    
    # 创建数据加载器进行预测
    from utils.data_processor import create_dataloader
    test_loader = create_dataloader(
        strain_data, stress_data, 
        batch_size=16, 
        sequence_length=3, 
        shuffle=False
    )
    
    # 使用预测函数
    pred_results = predict_with_model(model, test_loader, device)
    
    # 评估预测结果
    metrics = evaluate_predictions(pred_results['predictions'], pred_results['targets'])
    print(f"\n详细评估指标:")
    print(f"  MSE: {metrics['mse']:.6f}")
    print(f"  R²: {metrics['r2']:.6f}")
    print(f"  MAE: {metrics['mae']:.6f}")
    
    # 绘制预测结果
    print("\n5. 绘制结果图表...")
    plot_prediction_results(
        pred_results['predictions'], 
        pred_results['targets'], 
        strain_data, 
        results_dir
    )
    
    print("\n=== 示例运行完成 ===")
    print(f"模型已保存到: {model_dir}/best_model.pth")
    print(f"结果图表已保存到: {results_dir}/")
    print("\n主要改进:")
    print("- 使用修复后的损失函数，增强数据拟合能力")
    print("- 简化模型结构，提高训练稳定性")
    print("- 优化超参数，改善收敛性能")
    print("- 添加梯度裁剪，防止梯度爆炸")


if __name__ == '__main__':
    run_example()