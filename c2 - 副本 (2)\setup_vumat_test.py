"""
Vumat测试环境搭建脚本
自动为最新的PINN训练结果创建Vumat验证工作区
"""

import shutil
from pathlib import Path
import sys

TEMPLATE_DIR = Path("vumat_templates")

def find_latest_session_dir():
    """查找最新的session目录"""
    results_dir = Path("results")
    if not results_dir.exists():
        return None
    
    sessions = sorted([d for d in results_dir.iterdir() 
                      if d.is_dir() and d.name.startswith("session_")])
    return sessions[-1] if sessions else None

def get_test_type():
    """获取用户选择的测试类型"""
    print("\n请选择测试类型:")
    print("1. 单轴拉伸测试 (tensile)")
    print("2. 单轴压缩测试 (compression)")
    print("3. 循环加载测试 (cyclic)")
    
    while True:
        choice = input("\n请输入选项 (1-3): ")
        if choice == '1':
            return 'tensile', 'tensile_test.inp'
        elif choice == '2':
            return 'compression', 'compression_test.inp'
        elif choice == '3':
            return 'cyclic', 'cyclic_test.inp'
        else:
            print("无效选项，请重新输入")

def setup_test_environment(test_type=None):
    """搭建Vumat测试环境"""
    print("\n=== Vumat测试环境搭建 V2.0 ===")
    
    # 查找最新session
    latest_session = find_latest_session_dir()
    if not latest_session:
        print("错误: 未找到任何session目录。请先运行PINN训练。")
        return False
    
    print(f"找到最新session: {latest_session}")
    
    # 检查是否有训练结果
    params_file = latest_session / "training" / "identified_parameters.json"
    if not params_file.exists():
        print(f"错误: 该session中未找到训练参数文件: {params_file}")
        return False
    
    # 创建vumat_verification目录
    target_dir = latest_session / "vumat_verification"
    target_dir.mkdir(exist_ok=True)
    print(f"目标工作目录: {target_dir}")
    
    # 检查模板目录
    if not TEMPLATE_DIR.exists():
        print(f"错误: 模板目录 {TEMPLATE_DIR} 不存在。")
        return False
    
    # 如果没有指定测试类型，让用户选择
    if test_type is None:
        test_name, inp_file = get_test_type()
    else:
        # 根据参数确定文件名
        test_map = {
            'tensile': ('tensile', 'tensile_test.inp'),
            'compression': ('compression', 'compression_test.inp'),
            'cyclic': ('cyclic', 'cyclic_test.inp')
        }
        if test_type not in test_map:
            print(f"错误: 未知的测试类型 '{test_type}'")
            return False
        test_name, inp_file = test_map[test_type]
    
    print(f"\n正在搭建 {test_name} 测试环境...")
    
    # 复制必要的文件
    files_to_copy = [
        ('full_cyclic_vumat.for', 'full_cyclic_vumat.for'),  # Vumat子程序
        (inp_file, f'{test_name}_test.inp')  # 对应的INP文件
    ]
    
    for src_name, dst_name in files_to_copy:
        src_path = TEMPLATE_DIR / src_name
        dst_path = target_dir / dst_name
        
        if src_path.exists():
            shutil.copy(src_path, dst_path)
            print(f"✓ 已复制: {src_name} -> {dst_name}")
        else:
            print(f"✗ 警告: 模板文件不存在: {src_path}")
    
    # 创建输出目录
    output_dir = target_dir / "verification_outputs"
    output_dir.mkdir(exist_ok=True)
    print(f"✓ 已创建输出目录: verification_outputs/")
    
    # 保存测试配置信息
    config_file = target_dir / "test_config.txt"
    with open(config_file, 'w') as f:
        f.write(f"Test Type: {test_name}\n")
        f.write(f"INP File: {test_name}_test.inp\n")
        f.write(f"Session: {latest_session.name}\n")
    
    print(f"\n✓ Vumat测试环境搭建完成!")
    print(f"工作目录: {target_dir}")
    print(f"\n下一步:")
    print(f"1. 运行: python generate_material_card.py")
    print(f"2. 进入目录: cd {target_dir}")
    print(f"3. 运行Abaqus: abaqus job={test_name}_test user=full_cyclic_vumat.for interactive")
    
    return True

def main():
    """主函数"""
    # 支持命令行参数
    if len(sys.argv) > 1:
        test_type = sys.argv[1]
        setup_test_environment(test_type)
    else:
        setup_test_environment()

if __name__ == "__main__":
    main() 