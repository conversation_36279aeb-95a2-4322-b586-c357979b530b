PINN模型摘要
==================================================

导出时间: 20250703_164933
模型文件: results\session_20250623_151331\training\best_model.pth

模型结构:
--------------------------------------------------
DamagePINNV2(
  (network): Sequential(
    (0): Linear(in_features=1, out_features=64, bias=True)
    (1): Tanh()
    (2): Linear(in_features=64, out_features=64, bias=True)
    (3): Tanh()
    (4): Linear(in_features=64, out_features=64, bias=True)
    (5): Tanh()
    (6): Linear(in_features=64, out_features=64, bias=True)
    (7): Tanh()
    (8): Linear(in_features=64, out_features=64, bias=True)
    (9): Tanh()
    (10): Linear(in_features=64, out_features=3, bias=True)
  )
)

材料常数:
--------------------------------------------------
初始弹性模量 E0 = 10000.00 MPa
抗拉强度 f_t = 3.67 MPa
抗压强度 f_c = 10.00 MPa

物理参数:
--------------------------------------------------
受拉损伤参数: A+ = 0.8446, B+ = 1.8137
受压损伤参数: A- = 2.0000, B- = 1.3293
塑性参数: ξ+ = 0.5000, ξ- = 0.5003

权重信息:
--------------------------------------------------
network.0.weight: 形状(64, 1)
network.0.bias: 形状(64,)
network.2.weight: 形状(64, 64)
network.2.bias: 形状(64,)
network.4.weight: 形状(64, 64)
network.4.bias: 形状(64,)
network.6.weight: 形状(64, 64)
network.6.bias: 形状(64,)
network.8.weight: 形状(64, 64)
network.8.bias: 形状(64,)
network.10.weight: 形状(3, 64)
network.10.bias: 形状(3,)
