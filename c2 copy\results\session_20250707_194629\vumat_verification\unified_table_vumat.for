      SUBROUTINE VUMAT(
     1                   NBLOCK, NDIR, NSHR, NSTATEV, NFIELDV, NPROPS,
     2                   PROPS, TEMP, DTIME, STRAN, DSTRAN, TIME,
     3                   STRESS, STATEV, DDSDDE, CELENT)
C
C     查找表VUMAT - 直接使用PINN预测结果
C     极简高效版本，完全避免复杂的损伤演化计算
C     
      INCLUDE 'vaba_param.inc'
C
C     参数定义
      PARAMETER (ZERO=0.D0, ONE=1.D0, TABLE_SIZE=700)
C
C     数组维数声明
      DIMENSION PROPS(NPROPS), STRESS(NBLOCK, NDIR+NSHR)
      DIMENSION STATEV(NBLOCK, NSTATEV), STRAN(NBLOCK, NDIR+NSHR)
      DIMENSION DSTRAN(NBLOCK, NDIR+NSHR)
      DIMENSION DDSDDE(NBLOCK, NDIR+NSHR, NDIR+NSHR)
C
C     查找表数组声明（简化为100个点）
      PARAMETER (NPTS=100)
      REAL*8 STRAIN_TABLE(NPTS)
      REAL*8 STRESS_TABLE(NPTS)
      REAL*8 MODULUS_TABLE(NPTS)
      SAVE STRAIN_TABLE, STRESS_TABLE, MODULUS_TABLE
C
C     局部变量
      REAL*8 STRAIN_11, STRESS_11, E_EFFECTIVE
      LOGICAL FIRST_CALL
      SAVE FIRST_CALL
      DATA FIRST_CALL /.TRUE./
C
C     应变数据（直接在VUMAT中定义）
      DATA STRAIN_TABLE /
     &  -5.00000000E-03, -4.97995992E-03, -4.95991984E-03, -4.93987976E-03,
     &  -4.91983968E-03, -4.89979960E-03, -4.87975952E-03, -4.85971944E-03,
     &  -4.83967936E-03, -4.81963928E-03, -4.79959920E-03, -4.77955912E-03,
     &  -4.75951904E-03, -4.73947896E-03, -4.71943888E-03, -4.69939880E-03,
     &  -4.67935872E-03, -4.65931864E-03, -4.63927856E-03, -4.61923848E-03,
     &  -4.59919840E-03, -4.57915832E-03, -4.55911824E-03, -4.53907816E-03,
     &  -4.51903808E-03, -4.49899800E-03, -4.47895792E-03, -4.45891784E-03,
     &  -4.43887776E-03, -4.41883768E-03, -4.39879760E-03, -4.37875752E-03,
     &  -4.35871743E-03, -4.33867735E-03, -4.31863727E-03, -4.29859719E-03,
     &  -4.27855711E-03, -4.25851703E-03, -4.23847695E-03, -4.21843687E-03,
     &  -4.19839679E-03, -4.17835671E-03, -4.15831663E-03, -4.13827655E-03,
     &  -4.11823647E-03, -4.09819639E-03, -4.07815631E-03, -4.05811623E-03,
     &  -4.03807615E-03, -4.01803607E-03, -3.99799599E-03, -3.97795591E-03,
     &  -3.95791583E-03, -3.93787575E-03, -3.91783567E-03, -3.89779559E-03,
     &  -3.87775551E-03, -3.85771543E-03, -3.83767535E-03, -3.81763527E-03,
     &  -3.79759519E-03, -3.77755511E-03, -3.75751503E-03, -3.73747495E-03,
     &  -3.71743487E-03, -3.69739479E-03, -3.67735471E-03, -3.65731463E-03,
     &  -3.63727455E-03, -3.61723447E-03, -3.59719439E-03, -3.57715431E-03,
     &  -3.55711423E-03, -3.53707415E-03, -3.51703407E-03, -3.49699399E-03,
     &  -3.47695391E-03, -3.45691383E-03, -3.43687375E-03, -3.41683367E-03,
     &  -3.39679359E-03, -3.37675351E-03, -3.35671343E-03, -3.33667335E-03,
     &  -3.31663327E-03, -3.29659319E-03, -3.27655311E-03, -3.25651303E-03,
     &  -3.23647295E-03, -3.21643287E-03, -3.19639279E-03, -3.17635271E-03,
     &  -3.15631263E-03, -3.13627255E-03, -3.11623246E-03, -3.09619238E-03,
     &  -3.07615230E-03, -3.05611222E-03, -3.03607214E-03, -3.01603206E-03 /
C
C     应力数据
      DATA STRESS_TABLE /
     &  -2.49888092E-01, -2.48886537E-01, -2.47884981E-01, -2.46883426E-01,
     &  -2.45881870E-01, -2.44880315E-01, -2.43878759E-01, -2.42877204E-01,
     &  -2.41875648E-01, -2.40874093E-01, -2.39872537E-01, -2.38870982E-01,
     &  -2.37869427E-01, -2.36867871E-01, -2.35866316E-01, -2.34864760E-01,
     &  -2.33863205E-01, -2.32861649E-01, -2.31860094E-01, -2.30858538E-01,
     &  -2.29856983E-01, -2.28855427E-01, -2.27853872E-01, -2.26852316E-01,
     &  -2.25850761E-01, -2.24849205E-01, -2.23847650E-01, -2.22846094E-01,
     &  -2.21844539E-01, -2.20842983E-01, -2.19841428E-01, -2.18839872E-01,
     &  -2.17838317E-01, -2.16836761E-01, -2.15835206E-01, -2.14833650E-01,
     &  -2.13832095E-01, -2.12830540E-01, -2.11828984E-01, -2.10827429E-01,
     &  -2.09825873E-01, -2.08824318E-01, -2.07822762E-01, -2.06821207E-01,
     &  -2.05819651E-01, -2.04818096E-01, -2.03816540E-01, -2.02814985E-01,
     &  -2.01813429E-01, -2.00811874E-01, -1.99810318E-01, -1.98808763E-01,
     &  -1.97807207E-01, -1.96805652E-01, -1.95804096E-01, -1.94802541E-01,
     &  -1.93800985E-01, -1.92799430E-01, -1.91797874E-01, -1.90796319E-01,
     &  -1.89794763E-01, -1.88793208E-01, -1.87791653E-01, -1.86790097E-01,
     &  -1.85788542E-01, -1.84786986E-01, -1.83785431E-01, -1.82783875E-01,
     &  -1.81782320E-01, -1.80780764E-01, -1.79779209E-01, -1.78777653E-01,
     &  -1.77776098E-01, -1.76774542E-01, -1.75772987E-01, -1.74771431E-01,
     &  -1.73769876E-01, -1.72768320E-01, -1.71766765E-01, -1.70765209E-01,
     &  -1.69763654E-01, -1.68762098E-01, -1.67760543E-01, -1.66758987E-01,
     &  -1.65757432E-01, -1.64755876E-01, -1.63754321E-01, -1.62752766E-01,
     &  -1.61751210E-01, -1.60749655E-01, -1.59748099E-01, -1.58746544E-01,
     &  -1.57744988E-01, -1.56743433E-01, -1.55741877E-01, -1.90024317E-01,
     &  -3.16524964E-01, -4.44596011E-01, -5.74256953E-01, -7.05527527E-01 /
C
C     弹性模量数据（单位：MPa，确保都是正值且合理）
      DATA MODULUS_TABLE /
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.00000000E+04,
     &  1.00000000E+04, 1.00000000E+04, 1.00000000E+04, 1.22802069E+03,
     &  2.05884938E+03, 2.91085590E+03, 3.78458808E+03, 4.68060995E+03,
     &  5.59950223E+03, 6.54186298E+03, 7.50830817E+03, 8.49947230E+03,
     &  9.51600911E+03, 1.05585922E+04, 1.16279159E+04, 1.27246957E+04,
     &  1.38496693E+04, 1.50035975E+04, 1.61872647E+04, 1.74014802E+04,
     &  1.86470785E+04, 1.99249212E+04, 2.12358970E+04, 2.25809235E+04 /
C
C     开始计算循环
      DO K = 1, NBLOCK
C
C       1. 获取当前应变（一维问题，只考虑第1方向）
         STRAIN_11 = STRAN(K, 1)
C
C       2. 设置固定的初始弹性模量，只有大应变时才查表
         E_EFFECTIVE = 3.0D4  ! 固定初始弹性模量 30000 MPa
         
         IF (ABS(STRAIN_11) .LT. 1.0D-6) THEN
C           小应变：使用线弹性关系
            STRESS_11 = E_EFFECTIVE * STRAIN_11
         ELSE
C           大应变：查表获取应力（但保持固定模量）
            CALL LOOKUP_STRESS_MODULUS(STRAIN_11, STRESS_11, E_EFFECTIVE,
     &                                STRAIN_TABLE, STRESS_TABLE, 
     &                                MODULUS_TABLE, NPTS)
C           强制使用固定模量以保持稳定性
            E_EFFECTIVE = 3.0D4
         END IF
C
C       3. 设置应力分量
         STRESS(K, 1) = STRESS_11
         STRESS(K, 2) = ZERO
         STRESS(K, 3) = ZERO
C
C       4. 设置刚度矩阵（确保正值且合理）
         IF (E_EFFECTIVE .LE. ZERO) THEN
            E_EFFECTIVE = 1.0D4  ! 如果模量异常，使用默认值
         END IF
         
         DDSDDE(K, 1, 1) = E_EFFECTIVE
         DDSDDE(K, 2, 2) = E_EFFECTIVE
         DDSDDE(K, 3, 3) = E_EFFECTIVE
         
C       5. 设置剪切模量（泊松比 = 0.2）
         DDSDDE(K, 4, 4) = E_EFFECTIVE / 2.4D0  ! G = E/(2*(1+nu))
         DDSDDE(K, 5, 5) = E_EFFECTIVE / 2.4D0
         DDSDDE(K, 6, 6) = E_EFFECTIVE / 2.4D0
C
C       6. 可选：存储查表结果到状态变量
         IF (NSTATEV .GE. 1) STATEV(K, 1) = STRESS_11
         IF (NSTATEV .GE. 2) STATEV(K, 2) = E_EFFECTIVE
C
      END DO
C
      RETURN
      END



C =====================================================
C     线性插值查找子程序
C =====================================================
      SUBROUTINE LOOKUP_STRESS_MODULUS(STRAIN_IN, STRESS_OUT, MODULUS_OUT,
     &                                 STRAIN_TABLE, STRESS_TABLE, 
     &                                 MODULUS_TABLE, TABLE_SIZE)
      IMPLICIT NONE
      INTEGER TABLE_SIZE
      REAL*8 STRAIN_IN, STRESS_OUT, MODULUS_OUT
      REAL*8 STRAIN_TABLE(TABLE_SIZE)
      REAL*8 STRESS_TABLE(TABLE_SIZE)
      REAL*8 MODULUS_TABLE(TABLE_SIZE)
      REAL*8 RATIO
      INTEGER I
C
C     边界检查和特殊处理
      IF (ABS(STRAIN_IN) .LT. 1.0D-6) THEN
C         小应变特殊处理
          STRESS_OUT = 3.0D4 * STRAIN_IN  ! 线弹性关系
          MODULUS_OUT = 3.0D4  ! 固定弹性模量
          RETURN
      END IF
      
      IF (STRAIN_IN .LE. STRAIN_TABLE(1)) THEN
          STRESS_OUT = STRESS_TABLE(1)
          MODULUS_OUT = MODULUS_TABLE(1)
          RETURN
      END IF
      
      IF (STRAIN_IN .GE. STRAIN_TABLE(TABLE_SIZE)) THEN
          STRESS_OUT = STRESS_TABLE(TABLE_SIZE)
          MODULUS_OUT = MODULUS_TABLE(TABLE_SIZE)
          RETURN
      END IF
      
C     线性查找
      DO I = 1, TABLE_SIZE-1
          IF (STRAIN_IN .GE. STRAIN_TABLE(I) .AND. 
     &        STRAIN_IN .LE. STRAIN_TABLE(I+1)) THEN
              RATIO = (STRAIN_IN - STRAIN_TABLE(I)) / 
     &               (STRAIN_TABLE(I+1) - STRAIN_TABLE(I))
              STRESS_OUT = STRESS_TABLE(I) + 
     &                    RATIO * (STRESS_TABLE(I+1) - STRESS_TABLE(I))
              MODULUS_OUT = MODULUS_TABLE(I) + 
     &                     RATIO * (MODULUS_TABLE(I+1) - MODULUS_TABLE(I))
              RETURN
          END IF
      END DO
      
C     如果没有找到，使用最后一个值
      STRESS_OUT = STRESS_TABLE(TABLE_SIZE)
      MODULUS_OUT = MODULUS_TABLE(TABLE_SIZE)
      
      RETURN
      END 