"""
字体显示修复脚本
用于诊断和解决matplotlib中文字体显示异常问题
"""

import matplotlib.pyplot as plt
import matplotlib
from matplotlib import font_manager
import platform
import os
import sys
from pathlib import Path

def diagnose_font_issue():
    """
    诊断字体显示问题
    """
    print("=" * 60)
    print("字体显示问题诊断")
    print("=" * 60)
    
    # 1. 系统信息
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python版本: {sys.version}")
    print(f"Matplotlib版本: {matplotlib.__version__}")
    
    # 2. 当前字体设置
    print(f"\n当前字体设置:")
    print(f"  font.family: {plt.rcParams['font.family']}")
    print(f"  font.sans-serif: {plt.rcParams['font.sans-serif']}")
    print(f"  axes.unicode_minus: {plt.rcParams['axes.unicode_minus']}")
    
    # 3. 字体缓存位置
    cache_dir = matplotlib.get_cachedir()
    print(f"\nMatplotlib缓存目录: {cache_dir}")
    fontlist_cache = os.path.join(cache_dir, 'fontlist-v{}.json'.format(matplotlib.__version__.split('.')[0]))
    print(f"字体列表缓存: {fontlist_cache}")
    print(f"缓存文件存在: {os.path.exists(fontlist_cache)}")
    
    # 4. 可用字体统计
    font_list = font_manager.fontManager.ttflist
    print(f"\n系统可用字体数量: {len(font_list)}")
    
    # 5. 查找中文字体
    chinese_fonts = []
    chinese_keywords = ['SimHei', '微软雅黑', 'Microsoft YaHei', 'SimSun', '宋体', '黑体', 
                       'STHeiti', 'STSong', 'PingFang', 'Hiragino', 'Noto']
    
    for font in font_list:
        font_name = font.name
        for keyword in chinese_keywords:
            if keyword.lower() in font_name.lower():
                chinese_fonts.append({
                    'name': font_name,
                    'path': font.fname,
                    'style': getattr(font, 'style', 'unknown')
                })
                break
    
    print(f"\n找到的中文字体: {len(chinese_fonts)} 个")
    for i, font_info in enumerate(chinese_fonts[:10]):  # 显示前10个
        print(f"  {i+1}. {font_info['name']} ({font_info['style']})")
        print(f"     路径: {font_info['path']}")
    
    if len(chinese_fonts) > 10:
        print(f"  ... 还有 {len(chinese_fonts) - 10} 个")
    
    return chinese_fonts

def clear_font_cache():
    """
    清除matplotlib字体缓存
    """
    print("\n清除matplotlib字体缓存...")
    
    cache_dir = matplotlib.get_cachedir()
    if os.path.exists(cache_dir):
        for file in os.listdir(cache_dir):
            if file.startswith('fontlist'):
                cache_file = os.path.join(cache_dir, file)
                try:
                    os.remove(cache_file)
                    print(f"✓ 删除缓存文件: {file}")
                except Exception as e:
                    print(f"✗ 删除失败: {file} - {e}")
    
    # 重新构建字体管理器
    font_manager.fontManager.__init__()
    print("✓ 字体管理器已重新初始化")

def test_font_rendering():
    """
    测试字体渲染效果
    """
    print("\n测试字体渲染...")
    
    test_strings = [
        "中文显示测试",
        "拉伸损伤参数 A+ B+ ξ+",
        "压缩损伤参数 A- B- ξ-",
        "应力 σ (MPa)",
        "应变 ε (‰)",
        "损伤 d",
        "塑性应变 εp"
    ]
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    axes = axes.flatten()
    
    font_candidates = [
        'SimHei',
        'Microsoft YaHei', 
        'DejaVu Sans',
        'default'
    ]
    
    for i, font_family in enumerate(font_candidates):
        ax = axes[i]
        
        if font_family != 'default':
            try:
                plt.rcParams['font.family'] = font_family
            except:
                plt.rcParams['font.family'] = 'DejaVu Sans'
        
        ax.text(0.5, 0.9, f"字体: {font_family}", ha='center', va='center', 
                fontsize=14, fontweight='bold', transform=ax.transAxes)
        
        for j, text in enumerate(test_strings):
            y_pos = 0.8 - j * 0.1
            ax.text(0.1, y_pos, text, ha='left', va='center', 
                   fontsize=10, transform=ax.transAxes)
        
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('font_rendering_test.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("✓ 字体渲染测试完成，结果保存为: font_rendering_test.png")

def fix_font_automatically():
    """
    自动修复字体显示问题
    """
    print("\n自动修复字体显示...")
    
    # 1. 重置matplotlib设置
    matplotlib.rcdefaults()
    
    # 2. 清除字体缓存
    clear_font_cache()
    
    # 3. 诊断可用字体
    chinese_fonts = diagnose_font_issue()
    
    # 4. 尝试设置最佳字体
    success = False
    
    if chinese_fonts:
        # 优先选择常用字体
        preferred_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun']
        
        for preferred in preferred_fonts:
            for font_info in chinese_fonts:
                if preferred.lower() in font_info['name'].lower():
                    try:
                        font_path = font_info['path']
                        if os.path.exists(font_path):
                            # 添加字体到系统
                            font_manager.fontManager.addfont(font_path)
                            
                            # 设置为默认字体
                            plt.rcParams['font.family'] = font_info['name']
                            plt.rcParams['axes.unicode_minus'] = False
                            
                            print(f"✓ 成功设置字体: {font_info['name']}")
                            success = True
                            break
                    except Exception as e:
                        print(f"✗ 设置字体失败: {font_info['name']} - {e}")
                        continue
            
            if success:
                break
    
    if not success:
        # 回退方案
        print("使用回退字体方案...")
        plt.rcParams['font.family'] = 'DejaVu Sans'
        plt.rcParams['axes.unicode_minus'] = False
        print("✓ 设置为DejaVu Sans字体")
    
    # 5. 测试修复效果
    try:
        fig, ax = plt.subplots(figsize=(8, 6))
        ax.text(0.5, 0.7, '字体修复测试', fontsize=18, ha='center', fontweight='bold')
        ax.text(0.5, 0.5, '拉压循环损伤参数识别', fontsize=14, ha='center')
        ax.text(0.5, 0.3, 'A+ B+ ξ+ (拉伸参数)', fontsize=12, ha='center')
        ax.text(0.5, 0.1, 'A- B- ξ- (压缩参数)', fontsize=12, ha='center')
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.set_title('字体修复验证')
        ax.axis('off')
        
        plt.savefig('font_fix_verification.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✓ 字体修复验证完成，结果保存为: font_fix_verification.png")
        return True
        
    except Exception as e:
        print(f"✗ 字体修复验证失败: {e}")
        return False

def install_font_guide():
    """
    显示字体安装指南
    """
    print("\n" + "=" * 60)
    print("字体安装指南")
    print("=" * 60)
    
    system = platform.system()
    
    if system == 'Windows':
        print("Windows系统字体安装:")
        print("1. 下载中文字体文件（如SimHei.ttf、msyh.ttc等）")
        print("2. 右键点击字体文件，选择'安装'")
        print("3. 或者复制字体文件到 C:/Windows/Fonts/ 目录")
        print("4. 重启Python程序")
        
    elif system == 'Darwin':  # macOS
        print("macOS系统字体安装:")
        print("1. 下载中文字体文件")
        print("2. 双击字体文件，点击'安装字体'")
        print("3. 或者复制字体文件到 /Library/Fonts/ 目录")
        print("4. 重启Python程序")
        
    else:  # Linux
        print("Linux系统字体安装:")
        print("1. 安装中文字体包:")
        print("   sudo apt-get install fonts-dejavu-core")
        print("   sudo apt-get install fonts-noto-cjk")
        print("   sudo apt-get install fonts-wqy-zenhei")
        print("2. 或手动下载字体文件到 ~/.fonts/ 目录")
        print("3. 运行: fc-cache -f -v")
        print("4. 重启Python程序")
    
    print("\n推荐字体:")
    print("- Windows: SimHei (黑体), Microsoft YaHei (微软雅黑)")
    print("- macOS: STHeiti, PingFang SC")
    print("- Linux: Noto Sans CJK SC, WenQuanYi Zen Hei")

def main():
    """
    主函数
    """
    print("字体显示修复工具")
    print("=" * 60)
    
    while True:
        print("\n请选择操作:")
        print("1. 诊断字体问题")
        print("2. 清除字体缓存")
        print("3. 测试字体渲染")
        print("4. 自动修复字体")
        print("5. 查看字体安装指南")
        print("6. 退出")
        
        choice = input("请输入选择 (1-6): ").strip()
        
        if choice == '1':
            diagnose_font_issue()
        elif choice == '2':
            clear_font_cache()
        elif choice == '3':
            test_font_rendering()
        elif choice == '4':
            if fix_font_automatically():
                print("\n✓ 字体修复完成！")
                print("请重新运行主程序测试字体显示效果。")
            else:
                print("\n✗ 字体修复失败，请查看字体安装指南。")
        elif choice == '5':
            install_font_guide()
        elif choice == '6':
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main() 