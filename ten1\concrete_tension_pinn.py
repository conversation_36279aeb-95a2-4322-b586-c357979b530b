import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

class ConcreteTensionPINN(nn.Module):
    """
    混凝土拉伸损伤曲线的物理信息神经网络模型
    
    该模型基于LSTM架构，能够学习应变序列到应力、损伤变量和塑性应变的映射，
    同时满足混凝土损伤力学的物理约束。
    """
    
    def __init__(self, input_size=1, hidden_size=128, num_layers=3, output_size=3):
        super(ConcreteTensionPINN, self).__init__()
        
        # LSTM网络用于捕捉历史依赖性
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, 
                           batch_first=True, dropout=0.1)
        
        # 全连接层
        self.fc_layers = nn.Sequential(
            nn.Linear(hidden_size, hidden_size),
            nn.Tanh(),
            nn.Linear(hidden_size, hidden_size//2),
            nn.Tanh(),
            nn.Linear(hidden_size//2, output_size)
        )
        
        # 可训练的物理参数
        self.A_plus = nn.Parameter(torch.tensor(0.5, dtype=torch.float32))  # 损伤演化形状参数
        self.B_plus = nn.Parameter(torch.tensor(1.0, dtype=torch.float32))  # 损伤演化速率参数
        self.xi = nn.Parameter(torch.tensor(0.1, dtype=torch.float32))      # 塑性应变发展系数
        
        # 固定物理参数（需要根据实际材料设定）
        self.register_buffer('E0', torch.tensor(30000.0))  # 初始弹性模量 (MPa)
        self.register_buffer('ft', torch.tensor(3.0))      # 单轴抗拉强度 (MPa)
        
    def forward(self, strain_sequence):
        """
        前向传播
        
        Args:
            strain_sequence: 应变序列 [batch_size, seq_len, 1]
            
        Returns:
            stress: 应力预测 [batch_size, seq_len, 1]
            damage: 损伤变量预测 [batch_size, seq_len, 1]
            plastic_strain: 塑性应变预测 [batch_size, seq_len, 1]
        """
        # LSTM前向传播
        lstm_out, _ = self.lstm(strain_sequence)
        
        # 全连接层输出
        output = self.fc_layers(lstm_out)
        
        # 分离三个输出
        stress = output[:, :, 0:1]
        damage = torch.sigmoid(output[:, :, 1:2])  # 损伤变量限制在[0,1]
        plastic_strain = output[:, :, 2:3]
        
        return stress, damage, plastic_strain

class PhysicsConstraints:
    """
    物理约束计算类
    
    实现混凝土损伤力学的各种物理约束方程
    """
    
    @staticmethod
    def compute_damage_threshold(strain_seq, plastic_strain_seq, E0, ft):
        """
        计算损伤阈值序列 r(t) = max(r_old, Y(t))
        
        Args:
            strain_seq: 应变序列
            plastic_strain_seq: 塑性应变序列
            E0: 初始弹性模量
            ft: 抗拉强度
            
        Returns:
            r_seq: 损伤阈值序列
        """
        batch_size, seq_len = strain_seq.shape[:2]
        r_seq = torch.zeros_like(strain_seq)
        
        for b in range(batch_size):
            r_max = ft  # 初始损伤阈值
            for t in range(seq_len):
                # 计算损伤驱动力 Y = E0 * (ε - ε^p)
                Y = E0 * (strain_seq[b, t] - plastic_strain_seq[b, t])
                # 更新损伤阈值
                r_max = torch.max(r_max, Y)
                r_seq[b, t] = r_max
                
        return r_seq
    
    @staticmethod
    def compute_physical_damage(r_seq, r0, A_plus, B_plus):
        """
        根据物理公式计算损伤变量
        
        d = 1 - (r0/r) * [(1-A+) + A+ * exp(B+ * (1 - r/r0))]
        
        Args:
            r_seq: 损伤阈值序列
            r0: 初始损伤阈值 (ft)
            A_plus, B_plus: 损伤演化参数
            
        Returns:
            damage_seq: 物理损伤变量序列
        """
        # 避免除零
        r_seq_safe = torch.clamp(r_seq, min=r0)
        
        term1 = r0 / r_seq_safe * (1 - A_plus)
        term2 = A_plus * torch.exp(B_plus * (1 - r_seq_safe / r0))
        damage_seq = 1 - (term1 + term2)
        
        # 确保损伤变量在合理范围内
        damage_seq = torch.clamp(damage_seq, min=0.0, max=0.99)
        
        return damage_seq
    
    @staticmethod
    def compute_physical_plastic_strain(strain_seq, xi):
        """
        计算物理塑性应变累积
        
        ε^p(t) = ε^p(t-1) + ξ * Δε
        
        Args:
            strain_seq: 应变序列
            xi: 塑性应变发展系数
            
        Returns:
            plastic_strain_seq: 物理塑性应变序列
        """
        batch_size, seq_len = strain_seq.shape[:2]
        plastic_strain_seq = torch.zeros_like(strain_seq)
        
        for b in range(batch_size):
            ep_cum = 0.0
            for t in range(seq_len):
                if t > 0:
                    delta_strain = strain_seq[b, t] - strain_seq[b, t-1]
                    ep_cum += xi * delta_strain
                plastic_strain_seq[b, t] = ep_cum
                
        return plastic_strain_seq

class ConcreteTensionTrainer:
    """
    PINN模型训练器
    
    实现完整的训练流程，包括数据加载、损失计算、优化等
    """
    
    def __init__(self, model, device='cpu'):
        self.model = model.to(device)
        self.device = device
        self.physics = PhysicsConstraints()
        
        # 损失权重
        self.lambda_data = 1.0
        self.lambda_stress = 0.8
        self.lambda_damage = 0.5
        self.lambda_plastic = 0.5
        
        # 优化器
        self.optimizer = optim.Adam(self.model.parameters(), lr=0.001)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.8, patience=50, verbose=True
        )
        
        # 训练历史
        self.train_history = {
            'total_loss': [],
            'data_loss': [],
            'stress_loss': [],
            'damage_loss': [],
            'plastic_loss': [],
            'A_plus': [],
            'B_plus': [],
            'xi': []
        }
    
    def load_data_from_excel(self, excel_path, strain_col='应变', stress_col='应力'):
        """
        从Excel文件加载实验数据
        
        Args:
            excel_path: Excel文件路径
            strain_col: 应变列名
            stress_col: 应力列名
            
        Returns:
            strain_data: 应变数据
            stress_data: 应力数据
        """
        try:
            # 尝试读取Excel文件
            df = pd.read_excel(excel_path)
            
            # 如果列名不存在，尝试使用数字索引
            if strain_col not in df.columns:
                print(f"列名 '{strain_col}' 不存在，使用第一列作为应变数据")
                strain_data = df.iloc[:, 0].values
            else:
                strain_data = df[strain_col].values
                
            if stress_col not in df.columns:
                print(f"列名 '{stress_col}' 不存在，使用第二列作为应力数据")
                stress_data = df.iloc[:, 1].values
            else:
                stress_data = df[stress_col].values
                
            # 移除NaN值
            valid_mask = ~(np.isnan(strain_data) | np.isnan(stress_data))
            strain_data = strain_data[valid_mask]
            stress_data = stress_data[valid_mask]
            
            print(f"成功加载数据：{len(strain_data)} 个数据点")
            print(f"应变范围：{strain_data.min():.6f} ~ {strain_data.max():.6f}")
            print(f"应力范围：{stress_data.min():.3f} ~ {stress_data.max():.3f} MPa")
            
            return strain_data, stress_data
            
        except Exception as e:
            print(f"读取Excel文件失败：{e}")
            # 生成示例数据
            print("使用示例数据进行演示")
            return self.generate_sample_data()
    
    def generate_sample_data(self, n_points=100):
        """
        生成示例拉伸试验数据
        
        Args:
            n_points: 数据点数量
            
        Returns:
            strain_data: 应变数据
            stress_data: 应力数据
        """
        # 生成单调递增的应变序列
        strain_max = 0.003  # 最大应变 0.3%
        strain_data = np.linspace(0, strain_max, n_points)
        
        # 模拟混凝土拉伸应力-应变曲线
        E0 = 30000.0  # MPa
        ft = 3.0      # MPa
        
        stress_data = np.zeros_like(strain_data)
        for i, strain in enumerate(strain_data):
            if strain <= ft/E0:  # 线性阶段
                stress_data[i] = E0 * strain
            else:  # 软化阶段
                # 简化的指数软化
                stress_data[i] = ft * np.exp(-10 * (strain - ft/E0))
        
        return strain_data, stress_data
    
    def prepare_training_data(self, strain_data, stress_data):
        """
        准备训练数据
        
        Args:
            strain_data: 应变数据
            stress_data: 应力数据
            
        Returns:
            strain_tensor: 应变张量
            stress_tensor: 应力张量
        """
        # 转换为张量
        strain_tensor = torch.tensor(strain_data, dtype=torch.float32).unsqueeze(0).unsqueeze(-1)
        stress_tensor = torch.tensor(stress_data, dtype=torch.float32).unsqueeze(0).unsqueeze(-1)
        
        return strain_tensor.to(self.device), stress_tensor.to(self.device)
    
    def compute_losses(self, strain_seq, stress_exp, stress_pred, damage_pred, plastic_pred):
        """
        计算各项损失
        
        Args:
            strain_seq: 应变序列
            stress_exp: 实验应力
            stress_pred: 预测应力
            damage_pred: 预测损伤
            plastic_pred: 预测塑性应变
            
        Returns:
            losses: 损失字典
        """
        # 1. 数据拟合损失
        loss_data = nn.MSELoss()(stress_pred, stress_exp)
        
        # 2. 计算物理约束损失
        # 计算物理塑性应变
        plastic_phy = self.physics.compute_physical_plastic_strain(
            strain_seq, self.model.xi
        )
        
        # 计算损伤阈值
        r_seq = self.physics.compute_damage_threshold(
            strain_seq, plastic_phy, self.model.E0, self.model.ft
        )
        
        # 计算物理损伤
        damage_phy = self.physics.compute_physical_damage(
            r_seq, self.model.ft, self.model.A_plus, self.model.B_plus
        )
        
        # 应力本构关系损失
        stress_constitutive = self.model.E0 * (1 - damage_pred) * (strain_seq - plastic_pred)
        loss_stress = nn.MSELoss()(stress_pred, stress_constitutive)
        
        # 损伤演化损失
        loss_damage = nn.MSELoss()(damage_pred, damage_phy)
        
        # 塑性应变累积损失
        loss_plastic = nn.MSELoss()(plastic_pred, plastic_phy)
        
        # 总损失
        total_loss = (self.lambda_data * loss_data + 
                     self.lambda_stress * loss_stress + 
                     self.lambda_damage * loss_damage + 
                     self.lambda_plastic * loss_plastic)
        
        return {
            'total': total_loss,
            'data': loss_data,
            'stress': loss_stress,
            'damage': loss_damage,
            'plastic': loss_plastic
        }
    
    def train_epoch(self, strain_seq, stress_exp):
        """
        训练一个epoch
        
        Args:
            strain_seq: 应变序列
            stress_exp: 实验应力
            
        Returns:
            losses: 损失字典
        """
        self.model.train()
        self.optimizer.zero_grad()
        
        # 前向传播
        stress_pred, damage_pred, plastic_pred = self.model(strain_seq)
        
        # 计算损失
        losses = self.compute_losses(
            strain_seq, stress_exp, stress_pred, damage_pred, plastic_pred
        )
        
        # 反向传播
        losses['total'].backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
        
        self.optimizer.step()
        
        return losses
    
    def train(self, strain_data, stress_data, epochs=2000, print_interval=100):
        """
        完整训练流程
        
        Args:
            strain_data: 应变数据
            stress_data: 应力数据
            epochs: 训练轮数
            print_interval: 打印间隔
        """
        # 准备数据
        strain_seq, stress_exp = self.prepare_training_data(strain_data, stress_data)
        
        print("开始训练PINN模型...")
        print(f"数据形状：应变 {strain_seq.shape}, 应力 {stress_exp.shape}")
        
        for epoch in range(epochs):
            # 训练一个epoch
            losses = self.train_epoch(strain_seq, stress_exp)
            
            # 记录历史
            self.train_history['total_loss'].append(losses['total'].item())
            self.train_history['data_loss'].append(losses['data'].item())
            self.train_history['stress_loss'].append(losses['stress'].item())
            self.train_history['damage_loss'].append(losses['damage'].item())
            self.train_history['plastic_loss'].append(losses['plastic'].item())
            self.train_history['A_plus'].append(self.model.A_plus.item())
            self.train_history['B_plus'].append(self.model.B_plus.item())
            self.train_history['xi'].append(self.model.xi.item())
            
            # 学习率调度
            self.scheduler.step(losses['total'])
            
            # 打印进度
            if (epoch + 1) % print_interval == 0:
                print(f"Epoch {epoch+1}/{epochs}:")
                print(f"  总损失: {losses['total'].item():.6f}")
                print(f"  数据损失: {losses['data'].item():.6f}")
                print(f"  应力损失: {losses['stress'].item():.6f}")
                print(f"  损伤损失: {losses['damage'].item():.6f}")
                print(f"  塑性损失: {losses['plastic'].item():.6f}")
                print(f"  参数: A+={self.model.A_plus.item():.4f}, "
                      f"B+={self.model.B_plus.item():.4f}, "
                      f"ξ={self.model.xi.item():.4f}")
                print("-" * 50)
        
        print("训练完成！")
        print(f"最终参数: A+={self.model.A_plus.item():.4f}, "
              f"B+={self.model.B_plus.item():.4f}, "
              f"ξ={self.model.xi.item():.4f}")
    
    def predict(self, strain_data):
        """
        使用训练好的模型进行预测
        
        Args:
            strain_data: 应变数据
            
        Returns:
            predictions: 预测结果字典
        """
        self.model.eval()
        
        with torch.no_grad():
            strain_tensor = torch.tensor(strain_data, dtype=torch.float32).unsqueeze(0).unsqueeze(-1).to(self.device)
            stress_pred, damage_pred, plastic_pred = self.model(strain_tensor)
            
            # 计算物理量
            plastic_phy = self.physics.compute_physical_plastic_strain(
                strain_tensor, self.model.xi
            )
            r_seq = self.physics.compute_damage_threshold(
                strain_tensor, plastic_phy, self.model.E0, self.model.ft
            )
            damage_phy = self.physics.compute_physical_damage(
                r_seq, self.model.ft, self.model.A_plus, self.model.B_plus
            )
            
            return {
                'strain': strain_data,
                'stress_pred': stress_pred.squeeze().cpu().numpy(),
                'damage_pred': damage_pred.squeeze().cpu().numpy(),
                'plastic_pred': plastic_pred.squeeze().cpu().numpy(),
                'damage_phy': damage_phy.squeeze().cpu().numpy(),
                'plastic_phy': plastic_phy.squeeze().cpu().numpy(),
                'damage_threshold': r_seq.squeeze().cpu().numpy()
            }
    
    def plot_results(self, strain_data, stress_data, predictions=None, save_path=None):
        """
        绘制训练结果
        
        Args:
            strain_data: 应变数据
            stress_data: 应力数据
            predictions: 预测结果
            save_path: 保存路径
        """
        if predictions is None:
            predictions = self.predict(strain_data)
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('混凝土拉伸损伤曲线PINN学习结果', fontsize=16, fontweight='bold')
        
        # 1. 应力-应变曲线对比
        axes[0, 0].plot(strain_data, stress_data, 'bo-', label='实验数据', markersize=4)
        axes[0, 0].plot(predictions['strain'], predictions['stress_pred'], 'r-', label='PINN预测', linewidth=2)
        axes[0, 0].set_xlabel('应变')
        axes[0, 0].set_ylabel('应力 (MPa)')
        axes[0, 0].set_title('应力-应变曲线对比')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 损伤演化曲线
        axes[0, 1].plot(predictions['strain'], predictions['damage_pred'], 'g-', label='网络预测损伤', linewidth=2)
        axes[0, 1].plot(predictions['strain'], predictions['damage_phy'], 'r--', label='物理计算损伤', linewidth=2)
        axes[0, 1].set_xlabel('应变')
        axes[0, 1].set_ylabel('损伤变量')
        axes[0, 1].set_title('损伤演化曲线')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 塑性应变演化
        axes[0, 2].plot(predictions['strain'], predictions['plastic_pred'], 'b-', label='网络预测', linewidth=2)
        axes[0, 2].plot(predictions['strain'], predictions['plastic_phy'], 'm--', label='物理计算', linewidth=2)
        axes[0, 2].set_xlabel('应变')
        axes[0, 2].set_ylabel('塑性应变')
        axes[0, 2].set_title('塑性应变演化')
        axes[0, 2].legend()
        axes[0, 2].grid(True, alpha=0.3)
        
        # 4. 损失函数历史
        axes[1, 0].semilogy(self.train_history['total_loss'], 'k-', label='总损失', linewidth=2)
        axes[1, 0].semilogy(self.train_history['data_loss'], 'b-', label='数据损失')
        axes[1, 0].semilogy(self.train_history['stress_loss'], 'r-', label='应力损失')
        axes[1, 0].semilogy(self.train_history['damage_loss'], 'g-', label='损伤损失')
        axes[1, 0].semilogy(self.train_history['plastic_loss'], 'm-', label='塑性损失')
        axes[1, 0].set_xlabel('训练轮数')
        axes[1, 0].set_ylabel('损失值 (对数尺度)')
        axes[1, 0].set_title('训练损失历史')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 5. 参数演化历史
        ax1 = axes[1, 1]
        ax2 = ax1.twinx()
        
        line1 = ax1.plot(self.train_history['A_plus'], 'r-', label='A+', linewidth=2)
        line2 = ax1.plot(self.train_history['B_plus'], 'g-', label='B+', linewidth=2)
        line3 = ax2.plot(self.train_history['xi'], 'b-', label='ξ', linewidth=2)
        
        ax1.set_xlabel('训练轮数')
        ax1.set_ylabel('A+, B+', color='black')
        ax2.set_ylabel('ξ', color='blue')
        ax1.set_title('材料参数演化历史')
        
        # 合并图例
        lines = line1 + line2 + line3
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='upper right')
        ax1.grid(True, alpha=0.3)
        
        # 6. 损伤阈值演化
        axes[1, 2].plot(predictions['strain'], predictions['damage_threshold'], 'orange', linewidth=2, label='损伤阈值 r')
        axes[1, 2].axhline(y=self.model.ft.item(), color='red', linestyle='--', label=f'初始阈值 ft={self.model.ft.item():.1f}')
        axes[1, 2].set_xlabel('应变')
        axes[1, 2].set_ylabel('损伤阈值 (MPa)')
        axes[1, 2].set_title('损伤阈值演化')
        axes[1, 2].legend()
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"结果图已保存至: {save_path}")
        
        plt.show()

def main():
    """
    主函数：演示完整的PINN训练流程
    """
    print("=" * 60)
    print("混凝土拉伸损伤曲线PINN学习系统")
    print("=" * 60)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建模型
    model = ConcreteTensionPINN(input_size=1, hidden_size=128, num_layers=3, output_size=3)
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters())}")
    
    # 创建训练器
    trainer = ConcreteTensionTrainer(model, device)
    
    # 加载数据
    excel_path = "d:/column/ten1/tension.xlsx"
    strain_data, stress_data = trainer.load_data_from_excel(excel_path)
    
    # 训练模型
    trainer.train(strain_data, stress_data, epochs=1000, print_interval=100)
    
    # 预测和可视化
    predictions = trainer.predict(strain_data)
    trainer.plot_results(strain_data, stress_data, predictions, 
                        save_path="d:/column/ten1/pinn_results.png")
    
    # 保存模型
    model_path = "d:/column/ten1/concrete_tension_pinn_model.pth"
    torch.save({
        'model_state_dict': model.state_dict(),
        'train_history': trainer.train_history,
        'final_params': {
            'A_plus': model.A_plus.item(),
            'B_plus': model.B_plus.item(),
            'xi': model.xi.item()
        }
    }, model_path)
    print(f"模型已保存至: {model_path}")
    
    print("\n=" * 60)
    print("训练完成！")
    print(f"最终识别的材料参数:")
    print(f"  A+ (损伤演化形状参数): {model.A_plus.item():.4f}")
    print(f"  B+ (损伤演化速率参数): {model.B_plus.item():.4f}")
    print(f"  ξ  (塑性应变发展系数): {model.xi.item():.4f}")
    print("=" * 60)

if __name__ == "__main__":
    main()