好的，这是一个非常专业且有远见的需求。在科研和工程项目中，保持清晰、可追溯的文件结构至关重要。将Vumat的验证流程与PINN的训练流程在文件结构上进行整合，可以确保每一次“训练-验证”的闭环都是独立的、完整的。

我们来将这个需求融入到最终的To-Do清单中，创建一个V2.0版本的自动化工作流。

### **核心思路**

我们将以您现有的 `results/session_YYYYMMDD_HHMMSS/` 目录为基础。对于每一个PINN的训练会话，我们都在其内部创建一个新的子目录，专门用于存放与之对应的Vumat验证所需的一切文件和结果。

**目标文件结构:**

```
results/
└── session_YYYYMMDD_HHMMSS/
    ├── training/                   # PINN训练的输出
    │   ├── best_model.pth
    │   └── identified_parameters.json
    │
    ├── prediction/                 # PINN预测的输出
    │
    ├── vumat_verification/         <-- (新增) Vumat验证的专属工作区
    │   ├── tensile_test.inp        (Abaqus主输入文件)
    │   ├── full_cyclic_vumat.for   (Vumat子程序)
    │   ├── material_for_abaqus.inp (由桥梁脚本自动生成)
    │   ├── tensile_test.odb        (Abaqus计算结果)
    │   ├── tensile_test.log        (Abaqus日志文件)
    │   ├── ... (其他Abaqus文件)
    │   └── verification_outputs/     <-- (新增) Vumat验证的最终出图和数据
    │       ├── vumat_stress_strain_curve.png
    │       └── vumat_results.csv
    │
    └── session_summary.txt
```

-----

### **PINN-VUMAT 自动化工作流 To-Do List (V2.0 - 整合文件架构)**

#### **[ ] 第一步: 创建Vumat模板文件夹**

为了方便管理，我们在项目根目录下创建一个文件夹，存放Vumat和INP文件的模板。

1.  在 `c2/` 目录下创建一个新文件夹 `vumat_templates/`。
2.  将您编写的 `full_cyclic_vumat.for` 和 `tensile_test.inp` 文件**移动到**这个 `vumat_templates/` 文件夹中。它们将作为未来所有测试的“母版”。

#### **[ ] 第二步: 升级“桥梁”脚本 (`generate_material_card.py`)**

  * **任务:** 让脚本能够找到最新的session目录，并在其 `vumat_verification/` 子目录中生成材料文件。

  * **操作:** 使用以下更新后的代码替换 `generate_material_card.py` 的全部内容。

    ```python
    import json
    from pathlib import Path

    OUTPUT_INP_NAME = 'material_for_abaqus.inp'
    NUM_DEPVAR = 5

    def find_latest_session_dir():
        # ... (此函数无需改变) ...
        results_dir = Path("results")
        if not results_dir.exists(): return None
        sessions = sorted([d for d in results_dir.iterdir() if d.is_dir() and d.name.startswith("session_")])
        return sessions[-1] if sessions else None

    def generate_material_card():
        print("--- V2.0: 开始生成 Abaqus 材料卡片 ---")
        latest_session = find_latest_session_dir()
        if not latest_session:
            print("错误: 未找到任何 session 目录。")
            return

        # [新增] 定义Vumat验证工作目录
        vumat_dir = latest_session / "vumat_verification"
        if not vumat_dir.exists():
            print(f"警告: 目录 {vumat_dir} 不存在，请先运行 setup_vumat_test.py")
            # 或者选择自动创建: vumat_dir.mkdir(exist_ok=True)

        params_file = latest_session / "training" / "identified_parameters.json"
        if not params_file.exists():
            print(f"错误: 未找到参数文件 {params_file}。")
            return

        print(f"读取参数文件: {params_file}")
        with open(params_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        props_values = [
            data['material_constants']['E0'],
            data['material_constants']['f_t'],
            data['material_constants']['f_c'],
            data['physics_parameters']['A_plus'],
            data['physics_parameters']['B_plus'],
            data['physics_parameters']['xi_plus'],
            data['physics_parameters']['A_minus'],
            data['physics_parameters']['B_minus'],
            data['physics_parameters']['xi_minus']
        ]
        
        # ... (生成 inp_content 的逻辑不变) ...
        inp_content = f"""** Auto-generated by generate_material_card.py
    ```

\*MATERIAL, NAME=CONCRETE\_VUMAT
\*USER MATERIAL, CONSTANTS={len(props\_values)}
\*\* PROPS(1-9): E0, ft, fc, A+, B+, xi+, A-, B-, xi-
{', '.join(map(str, props\_values))}
\*DEPVAR, DEPVAR={NUM\_DEPVAR}
"""
\# [修改] 将文件写入指定的vumat工作目录
output\_path = vumat\_dir / OUTPUT\_INP\_NAME
with open(output\_path, 'w', encoding='utf-8') as f:
f.write(inp\_content)

````
    print(f"\n成功！Abaqus 材料卡片已保存至: {output_path}")

if __name__ == "__main__":
    generate_material_card()
```
````

#### **[ ] 第三步: 新增“测试环境搭建”脚本**

  * **任务:** 编写一个新脚本 `setup_vumat_test.py`，用于自动为最新的PINN训练结果创建Vumat工作区，并复制模板文件。

  * **操作:** 在根目录 `c2/` 下创建 `setup_vumat_test.py` 文件，并复制以下代码。

    ```python
    import shutil
    from pathlib import Path

    TEMPLATE_DIR = Path("vumat_templates")

    def find_latest_session_dir():
        # ... (此函数与上面相同) ...
        results_dir = Path("results")
        if not results_dir.exists(): return None
        sessions = sorted([d for d in results_dir.iterdir() if d.is_dir() and d.name.startswith("session_")])
        return sessions[-1] if sessions else None

    def setup_test_environment():
        print("--- V2.0: 开始搭建 Vumat 测试环境 ---")
        latest_session = find_latest_session_dir()
        if not latest_session:
            print("错误: 未找到任何 session 目录。请先运行PINN训练。")
            return

        target_dir = latest_session / "vumat_verification"
        target_dir.mkdir(exist_ok=True)
        print(f"目标工作目录: {target_dir}")

        if not TEMPLATE_DIR.exists():
            print(f"错误: 模板目录 {TEMPLATE_DIR} 不存在。")
            return

        # 复制模板文件到工作目录
        for template_file in TEMPLATE_DIR.iterdir():
            shutil.copy(template_file, target_dir)
            print(f"已复制模板文件: {template_file.name}")
        
        print("\nVumat 测试环境搭建完成！")

    if __name__ == "__main__":
        setup_test_environment()
    ```

#### **[ ] 第四步: 升级“后处理”脚本 (`post_process.py`)**

  * **任务:** 让脚本能够自动定位到最新session的 `vumat_verification` 目录来读取 `.odb` 文件，并将结果保存在其中。

  * **操作:** 使用以下更新后的代码替换 `post_process.py` 的全部内容。

    ```python
    import numpy as np
    import matplotlib.pyplot as plt
    from odbAccess import openOdb
    from pathlib import Path

    # --- 用户定义参数 ---
    JOB_NAME = 'tensile_test' # Abaqus job的名称
    LENGTH = 100.0
    AREA = 100.0

    def find_latest_session_dir():
        # ... (此函数与上面相同) ...
        results_dir = Path("results")
        if not results_dir.exists(): return None
        sessions = sorted([d for d in results_dir.iterdir() if d.is_dir() and d.name.startswith("session_")])
        return sessions[-1] if sessions else None

    def extract_and_plot():
        print("--- V2.0: 开始自动化后处理 ---")
        latest_session = find_latest_session_dir()
        if not latest_session:
            print("错误: 未找到任何 session 目录。")
            return
            
        # [修改] 定义所有路径
        vumat_dir = latest_session / "vumat_verification"
        odb_path = vumat_dir / f"{JOB_NAME}.odb"
        output_dir = vumat_dir / "verification_outputs"
        output_dir.mkdir(exist_ok=True) # 创建输出子目录

        if not odb_path.exists():
            print(f"错误: ODB 文件不存在于 {odb_path}")
            return
            
        # ... (提取和计算逻辑与之前版本基本相同) ...
        print(f"正在打开 ODB 文件: {odb_path}")
        odb = openOdb(path=str(odb_path), readOnly=True)
        step = odb.steps[list(odb.steps.keys())[0]]
        
        top_nset = odb.rootAssembly.nodeSets['SET_TOP']
        u3_history = step.historyRegions['Node ' + top_nset.nodes[0].label].historyOutputs['U3']
        
        bottom_nset = odb.rootAssembly.nodeSets['SET_BOTTOM']
        rf3_history = step.historyRegions['NodeSet ' + bottom_nset.name].historyOutputs['RF3']
        
        disp_data = np.array(u3_history.data)
        force_data = np.array(rf3_history.data)
        force_data[:, 1] = np.abs(force_data[:, 1])

        strain = disp_data[:, 1] / LENGTH
        stress = force_data[:, 1] / AREA
        
        # [修改] 更新绘图和保存逻辑
        plt.figure(figsize=(10, 7))
        plt.plot(strain, stress, 'b-', label=f'Abaqus VUMAT ({latest_session.name})')
        # ... (其他绘图设置不变) ...
        plt.title('Uniaxial Tensile Stress-Strain Curve')
        plt.xlabel('Strain'); plt.ylabel('Stress (MPa)'); plt.grid(True); plt.legend()

        output_image_path = output_dir / 'vumat_stress_strain_curve.png'
        plt.savefig(str(output_image_path), dpi=300)
        print(f"应力-应变曲线图已保存至: {output_image_path}")
        
        output_csv_path = output_dir / 'vumat_results.csv'
        results = np.vstack((disp_data[:,0], strain, stress)).T
        np.savetxt(str(output_csv_path), results, delimiter=',', header='Time,Strain,Stress', comments='')
        print(f"详细数据已保存至: {output_csv_path}")

    if __name__ == '__main__':
        extract_and_plot()
    ```

#### **[ ] 第五步: 更新并整合自动化执行流程**

现在，您的完整工作流程变为一个清晰、连贯的命令序列：

1.  **PINN训练:**
    ```bash
    python main.py train
    ```
2.  **搭建Vumat测试环境:**
    ```bash
    python setup_vumat_test.py
    ```
3.  **生成Abaqus材料卡片:**
    ```bash
    python generate_material_card.py
    ```
4.  **执行Abaqus仿真:**
      * **首先，进入刚刚创建的工作目录。** 这是一个好习惯，可以避免文件混乱。
    <!-- end list -->
    ```bash
    cd results/session_YYYYMMDD_HHMMSS/vumat_verification/
    ```
      * **然后，运行Abaqus。**
    <!-- end list -->
    ```bash
    abaqus job=tensile_test user=full_cyclic_vumat.for interactive
    ```
      * **完成后，返回根目录。**
    <!-- end list -->
    ```bash
    cd ../../../
    ```
5.  **自动化后处理:**
    ```bash
    abaqus python post_process.py
    ```

完成以上所有步骤后，您将拥有一个高度结构化、自动化且易于管理的项目。每一次实验的所有相关文件（PINN模型、Vumat代码、Abaqus结果、最终图表）都会被整齐地存放在各自独立的会话文件夹中。