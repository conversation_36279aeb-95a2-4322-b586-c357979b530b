import pandas as pd
import numpy as np
import os

def process_cyclic_data(input_path, output_path):
    """
    Processes cyclic loading data to convert Force and Deflection to Stress and Strain.

    Args:
        input_path (str): Path to the input Excel file.
        output_path (str): Path to save the processed Excel file.
    """
    # Static parameters
    D = 400  # mm
    L0 = 800  # mm

    # Calculate cross-sectional area
    area = np.pi * (D / 2) ** 2

    try:
        # Read the data
        # The user provided a path to a temporary file, we'll try the likely actual file name
        actual_input_path = input_path.replace('~$', '')
        df = pd.read_excel(actual_input_path)

        # Check for required columns
        if 'Force (KN)' not in df.columns or 'Deflection (MM)' not in df.columns:
            print("错误：输入文件必须包含 'Force (KN)' 和 'Deflection (MM)' 列。")
            return

        # Convert Force (KN) to Stress (MPa)
        # 1 KN = 1000 N
        # Stress (MPa) = Force (N) / Area (mm^2)
        df['Stress (MPa)'] = (df['Force (KN)'] * 1000) / area

        # Convert Deflection (MM) to Strain
        # Strain is dimensionless
        df['Strain'] = df['Deflection (MM)'] / L0

        # Create a new dataframe with the processed data
        processed_df = df[['Stress (MPa)', 'Strain']]

        # Save the processed data to a new Excel file
        processed_df.to_excel(output_path, index=False)
        print(f"处理完成的数据已保存至 {output_path}")

    except FileNotFoundError:
        print(f"错误：在路径 {actual_input_path} 未找到输入文件。")
    except Exception as e:
        print(f"发生错误：{e}")

if __name__ == '__main__':
    # Define file paths
    input_file = r'd:\column\cycle\cyclic_data.xlsx'
    output_file = r'd:\column\cycle\cyclic_processed_data.xlsx'

    process_cyclic_data(input_file, output_file)
