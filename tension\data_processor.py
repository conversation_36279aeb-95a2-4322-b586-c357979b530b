#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理模块
用于处理混凝土单轴拉伸试验数据
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class TensionDataProcessor:
    """
    拉伸试验数据处理器
    """
    
    def __init__(self, file_path):
        self.file_path = file_path
        self.raw_data = None
        self.processed_data = None
        self.strain_scaler = StandardScaler()
        self.stress_scaler = StandardScaler()
        
    def load_data(self):
        """
        加载Excel数据
        """
        try:
            # 尝试读取Excel文件
            self.raw_data = pd.read_excel(self.file_path)
            print(f"数据文件加载成功: {self.file_path}")
            print(f"数据形状: {self.raw_data.shape}")
            print(f"列名: {self.raw_data.columns.tolist()}")
            
            # 显示数据基本信息
            print("\n数据基本统计信息:")
            print(self.raw_data.describe())
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            print("生成模拟数据用于测试...")
            self._generate_synthetic_data()
            return False
    
    def _generate_synthetic_data(self):
        """
        生成模拟的混凝土拉伸试验数据
        """
        # 参数设置
        E0 = 30000  # 初始弹性模量 (MPa)
        ft = 3.0    # 抗拉强度 (MPa)
        epsilon_peak = ft / E0  # 峰值应变
        
        # 应变范围
        strain = np.linspace(0, 0.01, 200)
        
        # 简化的混凝土拉伸应力-应变关系
        stress = np.zeros_like(strain)
        
        for i, eps in enumerate(strain):
            if eps <= epsilon_peak:
                # 线弹性阶段
                stress[i] = E0 * eps
            else:
                # 软化阶段
                stress[i] = ft * np.exp(-50 * (eps - epsilon_peak))
        
        # 添加噪声
        noise = np.random.normal(0, 0.05, len(stress))
        stress += noise * np.max(stress)
        stress = np.maximum(stress, 0)  # 确保应力非负
        
        self.raw_data = pd.DataFrame({
            'strain': strain,
            'stress': stress
        })
        
        print("已生成模拟数据")
    
    def identify_columns(self):
        """
        自动识别应变和应力列
        """
        columns = self.raw_data.columns.tolist()
        strain_col = None
        stress_col = None
        
        # 常见的列名模式
        strain_keywords = ['strain', 'epsilon', 'ε', '应变', '变形']
        stress_keywords = ['stress', 'sigma', 'σ', '应力', '力']
        
        for col in columns:
            col_lower = str(col).lower()
            if any(keyword in col_lower for keyword in strain_keywords):
                strain_col = col
            elif any(keyword in col_lower for keyword in stress_keywords):
                stress_col = col
        
        # 如果没有找到，使用前两列
        if strain_col is None or stress_col is None:
            if len(columns) >= 2:
                strain_col = columns[0]
                stress_col = columns[1]
                print(f"未找到明确的列名，使用前两列: {strain_col} (应变), {stress_col} (应力)")
        
        return strain_col, stress_col
    
    def clean_data(self, strain_col=None, stress_col=None):
        """
        数据清洗
        """
        if strain_col is None or stress_col is None:
            strain_col, stress_col = self.identify_columns()
        
        # 提取数据
        strain_data = self.raw_data[strain_col].values
        stress_data = self.raw_data[stress_col].values
        
        # 移除NaN值
        valid_mask = ~(np.isnan(strain_data) | np.isnan(stress_data))
        strain_data = strain_data[valid_mask]
        stress_data = stress_data[valid_mask]
        
        # 移除负应变和负应力
        positive_mask = (strain_data >= 0) & (stress_data >= 0)
        strain_data = strain_data[positive_mask]
        stress_data = stress_data[positive_mask]
        
        # 按应变排序
        sort_indices = np.argsort(strain_data)
        strain_data = strain_data[sort_indices]
        stress_data = stress_data[sort_indices]
        
        # 移除重复的应变值
        unique_indices = np.unique(strain_data, return_index=True)[1]
        strain_data = strain_data[unique_indices]
        stress_data = stress_data[unique_indices]
        
        self.processed_data = pd.DataFrame({
            'strain': strain_data,
            'stress': stress_data
        })
        
        print(f"数据清洗完成，有效数据点: {len(strain_data)}")
        
        return strain_data, stress_data
    
    def normalize_data(self, method='standard'):
        """
        数据标准化
        """
        if self.processed_data is None:
            raise ValueError("请先进行数据清洗")
        
        strain_data = self.processed_data['strain'].values.reshape(-1, 1)
        stress_data = self.processed_data['stress'].values.reshape(-1, 1)
        
        if method == 'standard':
            strain_normalized = self.strain_scaler.fit_transform(strain_data)
            stress_normalized = self.stress_scaler.fit_transform(stress_data)
        elif method == 'minmax':
            self.strain_scaler = MinMaxScaler()
            self.stress_scaler = MinMaxScaler()
            strain_normalized = self.strain_scaler.fit_transform(strain_data)
            stress_normalized = self.stress_scaler.fit_transform(stress_data)
        else:
            raise ValueError("标准化方法必须是 'standard' 或 'minmax'")
        
        return strain_normalized.flatten(), stress_normalized.flatten()
    
    def inverse_transform_stress(self, stress_normalized):
        """
        应力反标准化
        """
        return self.stress_scaler.inverse_transform(stress_normalized.reshape(-1, 1)).flatten()
    
    def inverse_transform_strain(self, strain_normalized):
        """
        应变反标准化
        """
        return self.strain_scaler.inverse_transform(strain_normalized.reshape(-1, 1)).flatten()
    
    def plot_raw_data(self, save_path=None):
        """
        绘制原始数据
        """
        if self.processed_data is None:
            raise ValueError("请先进行数据处理")
        
        plt.figure(figsize=(10, 6))
        plt.plot(self.processed_data['strain'], self.processed_data['stress'], 
                'bo-', markersize=3, linewidth=1, label='试验数据')
        plt.xlabel('应变')
        plt.ylabel('应力 (MPa)')
        plt.title('混凝土单轴拉伸试验数据')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def analyze_data(self):
        """
        数据分析
        """
        if self.processed_data is None:
            raise ValueError("请先进行数据处理")
        
        strain = self.processed_data['strain'].values
        stress = self.processed_data['stress'].values
        
        # 找到峰值应力
        peak_stress_idx = np.argmax(stress)
        peak_stress = stress[peak_stress_idx]
        peak_strain = strain[peak_stress_idx]
        
        # 估算初始弹性模量
        # 取前10%的数据点进行线性拟合
        n_points = max(5, len(strain) // 10)
        initial_slope = np.polyfit(strain[:n_points], stress[:n_points], 1)[0]
        
        # 计算断裂能（应力-应变曲线下的面积）
        fracture_energy = np.trapz(stress, strain)
        
        analysis_results = {
            'peak_stress': peak_stress,
            'peak_strain': peak_strain,
            'initial_modulus': initial_slope,
            'fracture_energy': fracture_energy,
            'total_points': len(strain),
            'strain_range': [strain.min(), strain.max()],
            'stress_range': [stress.min(), stress.max()]
        }
        
        print("\n数据分析结果:")
        print(f"峰值应力: {peak_stress:.4f} MPa")
        print(f"峰值应变: {peak_strain:.6f}")
        print(f"初始弹性模量: {initial_slope:.2f} MPa")
        print(f"断裂能: {fracture_energy:.6f} N/mm")
        print(f"应变范围: {strain.min():.6f} ~ {strain.max():.6f}")
        print(f"应力范围: {stress.min():.4f} ~ {stress.max():.4f} MPa")
        
        return analysis_results
    
    def save_processed_data(self, save_path):
        """
        保存处理后的数据
        """
        if self.processed_data is None:
            raise ValueError("请先进行数据处理")
        
        self.processed_data.to_csv(save_path, index=False)
        print(f"处理后的数据已保存到: {save_path}")

def main():
    """
    测试数据处理模块
    """
    print("=" * 50)
    print("数据处理模块测试")
    print("=" * 50)
    
    # 创建数据处理器
    processor = TensionDataProcessor("d:/column/tension/tension.xlsx")
    
    # 加载数据
    processor.load_data()
    
    # 数据清洗
    strain_data, stress_data = processor.clean_data()
    
    # 数据分析
    analysis_results = processor.analyze_data()
    
    # 绘制原始数据
    output_dir = "d:/column/tension/results"
    os.makedirs(output_dir, exist_ok=True)
    processor.plot_raw_data(os.path.join(output_dir, 'raw_data.png'))
    
    # 保存处理后的数据
    processor.save_processed_data(os.path.join(output_dir, 'processed_data.csv'))
    
    # 数据标准化测试
    strain_norm, stress_norm = processor.normalize_data()
    print(f"\n标准化后数据范围:")
    print(f"应变: {strain_norm.min():.4f} ~ {strain_norm.max():.4f}")
    print(f"应力: {stress_norm.min():.4f} ~ {stress_norm.max():.4f}")
    
    print("\n数据处理完成!")

if __name__ == "__main__":
    main()