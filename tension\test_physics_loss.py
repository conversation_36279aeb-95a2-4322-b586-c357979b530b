import torch
from pinn_model import ConcreteDAMAGE_PINN

# 创建模型
model = ConcreteDAMAGE_PINN()

# 创建测试数据
strain = torch.randn(5, 1)
stress, damage, plastic = model(strain)

# 测试physics_loss方法
print("Testing physics_loss method...")
try:
    result = model.physics_loss(strain, stress, damage, plastic)
    print(f"Result type: {type(result)}")
    if hasattr(result, 'keys'):
        print(f"Keys: {list(result.keys())}")
    else:
        print("Result is not a dictionary")
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()