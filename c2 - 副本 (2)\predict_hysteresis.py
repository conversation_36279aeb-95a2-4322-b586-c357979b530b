"""
混凝土全滞回曲线预测脚本
支持受拉受压双向加载
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import json
import pandas as pd
from pathlib import Path

from pinn_model_v2 import DamagePINNV2
from font_config import setup_font
from model_utils import safe_load_model


class HysteresisPredictor:
    """滞回曲线预测器"""
    
    def __init__(self, model_path):
        """
        初始化预测器
        
        Args:
            model_path: 训练好的模型路径
        """
        self.model_path = model_path
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 加载模型
        self.model_info = None
        self.model = None
        self.physics_params = None
        self.material_constants = None
        
        self.load_model()
        
        # 创建预测结果保存目录
        # 智能推断results_dir：如果模型路径包含session目录，使用该目录；否则使用默认目录
        self.results_dir = self._infer_results_dir()
        self.prediction_dir = f"{self.results_dir}/prediction"
        Path(self.prediction_dir).mkdir(parents=True, exist_ok=True)
        
        print(f"预测结果保存目录设置为: {self.prediction_dir}")
    
    def _infer_results_dir(self):
        """从模型路径智能推断results目录"""
        model_path_str = str(self.model_path)
        print(f"推断results目录，模型路径: {model_path_str}")
        
        # 检查模型路径是否包含session目录
        if "session_" in model_path_str:
            # 找到session_开头的部分，支持Windows和Unix路径分隔符
            import re
            # 匹配到session目录为止（包含session目录本身）
            match = re.search(r'(.*[\\/]session_[^/\\]+)', model_path_str.replace('\\', '/'))
            if match:
                session_dir = match.group(1).replace('/', '\\') if '\\' in model_path_str else match.group(1)
                print(f"检测到session目录: {session_dir}")
                return session_dir
            else:
                # 如果没有匹配到路径分隔符，可能是相对路径
                match = re.search(r'(.*session_[^/\\]+)', model_path_str)
                if match:
                    session_dir = match.group(1)
                    print(f"检测到session目录(相对路径): {session_dir}")
                    return session_dir
        
        # 如果没有找到session目录，尝试从模型信息中获取
        model_results_dir = self.model_info.get('results_dir', None)
        if model_results_dir and "session_" in model_results_dir:
            print(f"从模型信息中获取results目录: {model_results_dir}")
            return model_results_dir
        
        # 默认使用results/default
        print("使用默认results目录: results/default")
        return 'results/default'
        
    def load_model(self):
        """加载训练好的模型"""
        # 使用安全加载函数
        self.model_info = safe_load_model(self.model_path, self.device)
        
        # 提取物理参数
        self.physics_params = self.model_info.get('physics_parameters', {})
        self.material_constants = self.model_info.get('material_constants', {})
        
        # 检查必要参数是否存在
        if not self.physics_params or not self.material_constants:
            print("警告: 模型文件可能不完整，缺少物理参数或材料常数")
            print("如果这是一个旧版本的模型文件，请重新训练")
            # 设置默认值
            if not self.physics_params:
                self.physics_params = {
                    'A_plus': 0.5, 'B_plus': 1.0, 'xi_plus': 0.01,
                    'A_minus': 1.5, 'B_minus': 0.5, 'xi_minus': 0.02
                }
            if not self.material_constants:
                self.material_constants = {'E0': 30000.0, 'f_t': 3.0, 'f_c': 30.0}
        
        # 重建模型
        config = self.model_info.get('config', {})
        self.model = DamagePINNV2(
            input_size=1,
            hidden_size=config.get('hidden_size', 64),
            num_layers=config.get('num_layers', 6),
            output_size=3
        ).to(self.device)
        
        # 加载模型权重
        self.model.load_state_dict(self.model_info['model_state_dict'])
        self.model.eval()
        
        print("模型加载成功!")
        print(f"材料常数:")
        print(f"  E0 = {self.material_constants['E0']:.0f} MPa")
        print(f"  f_t = {self.material_constants['f_t']:.2f} MPa")
        print(f"  f_c = {self.material_constants['f_c']:.2f} MPa")
        print(f"识别的物理参数:")
        print(f"  受拉: A+ = {self.physics_params['A_plus']:.4f}, "
              f"B+ = {self.physics_params['B_plus']:.4f}, "
              f"ξ+ = {self.physics_params['xi_plus']:.4f}")
        print(f"  受压: A- = {self.physics_params['A_minus']:.4f}, "
              f"B- = {self.physics_params['B_minus']:.4f}, "
              f"ξ- = {self.physics_params['xi_minus']:.4f}")
    
    def generate_cyclic_loading_path(self, 
                                   max_tensile_strain=0.002,
                                   max_compressive_strain=-0.003,
                                   num_cycles=3,
                                   points_per_segment=50):
        """
        生成循环加载路径
        
        Args:
            max_tensile_strain: 最大拉应变
            max_compressive_strain: 最大压应变
            num_cycles: 循环次数
            points_per_segment: 每段的点数
        
        Returns:
            strain_path: 应变路径
        """
        strain_path = [0.0]  # 从零开始
        
        for cycle in range(num_cycles):
            # 拉伸加载
            tensile_strain = max_tensile_strain * (cycle + 1) / num_cycles
            strain_path.extend(np.linspace(strain_path[-1], tensile_strain, points_per_segment)[1:])
            
            # 卸载到压缩
            compressive_strain = max_compressive_strain * (cycle + 1) / num_cycles
            strain_path.extend(np.linspace(strain_path[-1], compressive_strain, points_per_segment * 2)[1:])
            
            # 再加载回零点
            strain_path.extend(np.linspace(strain_path[-1], 0, points_per_segment)[1:])
        
        return np.array(strain_path)
    
    def predict_response(self, strain_path):
        """
        预测给定应变路径的响应
        
        Args:
            strain_path: 应变路径
        
        Returns:
            results: 包含应力、损伤等的字典
        """
        n_steps = len(strain_path)
        
        # 初始化结果数组
        stress = np.zeros(n_steps)
        d_plus = np.zeros(n_steps)
        d_minus = np.zeros(n_steps)
        plastic_strain = np.zeros(n_steps)
        
        # 获取材料常数和物理参数
        E0 = self.material_constants['E0']
        f_t = self.material_constants['f_t']
        f_c = self.material_constants['f_c']
        
        A_plus = self.physics_params['A_plus']
        B_plus = self.physics_params['B_plus']
        xi_plus = self.physics_params['xi_plus']
        
        A_minus = self.physics_params['A_minus']
        B_minus = self.physics_params['B_minus']
        xi_minus = self.physics_params['xi_minus']
        
        # 初始化状态变量
        r_max_plus = f_t
        r_max_minus = f_c
        
        # 诊断信息
        print(f"\n物理参数:")
        print(f"  ξ+ = {xi_plus:.4f}, ξ- = {xi_minus:.4f}")
        
        # 逐步计算
        with torch.no_grad():
            for i in range(n_steps):
                if i == 0:
                    continue
                
                # 计算应变增量
                strain_increment = strain_path[i] - strain_path[i-1]
                
                # 计算有效弹性应变
                effective_elastic_strain = strain_path[i] - plastic_strain[i-1]
                
                # 计算损伤驱动力
                Y_plus_current = E0 * max(0, effective_elastic_strain)
                Y_minus_current = E0 * max(0, -effective_elastic_strain)
                
                # 更新受拉损伤
                if Y_plus_current > r_max_plus:
                    r_max_plus = Y_plus_current
                    ratio_plus = r_max_plus / f_t
                    exp_term_plus = np.exp(B_plus * (1 - ratio_plus))
                    d_plus[i] = 1 - (1/ratio_plus) * ((1 - A_plus) + A_plus * exp_term_plus)
                    d_plus[i] = np.clip(d_plus[i], 0.0, 0.99)
                else:
                    d_plus[i] = d_plus[i-1]
                
                # 更新受压损伤
                if Y_minus_current > r_max_minus:
                    r_max_minus = Y_minus_current
                    ratio_minus = r_max_minus / f_c
                    exp_term_minus = np.exp(B_minus * (1 - ratio_minus))
                    d_minus[i] = 1 - (1/ratio_minus) * ((1 - A_minus) + A_minus * exp_term_minus)
                    d_minus[i] = np.clip(d_minus[i], 0.0, 0.99)
                else:
                    d_minus[i] = d_minus[i-1]
                
                # 更新塑性应变
                if strain_increment > 0:
                    # 拉伸增量，使用拉伸塑性系数
                    delta_ep = xi_plus * strain_increment
                else:
                    # 压缩增量，使用压缩塑性系数（注意strain_increment是负的）
                    delta_ep = xi_minus * abs(strain_increment)  # 保证delta_ep为正
                    delta_ep = -delta_ep  # 压缩塑性应变为负
                
                plastic_strain[i] = plastic_strain[i-1] + delta_ep
                
                # 重新计算有效弹性应变
                effective_elastic_strain = strain_path[i] - plastic_strain[i]
                
                # 计算应力
                if effective_elastic_strain >= 0:
                    stress[i] = (1 - d_plus[i]) * E0 * effective_elastic_strain
                else:
                    stress[i] = (1 - d_minus[i]) * E0 * effective_elastic_strain
        
        return {
            'strain': strain_path,
            'stress': stress,
            'd_plus': d_plus,
            'd_minus': d_minus,
            'plastic_strain': plastic_strain,
            'elastic_strain': strain_path - plastic_strain
        }
    
    def plot_results(self, results, save_name='hysteresis_prediction'):
        """绘制预测结果并保存到prediction文件夹"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 确保prediction目录存在
        Path(self.prediction_dir).mkdir(parents=True, exist_ok=True)
        
        # 创建图形
        fig = plt.figure(figsize=(16, 12))
        
        # 1. 滞回曲线
        ax1 = plt.subplot(2, 2, 1)
        ax1.plot(results['strain'], results['stress'], 'b-', linewidth=2)
        ax1.set_xlabel('应变')
        ax1.set_ylabel('应力 (MPa)')
        ax1.set_title('预测的滞回曲线')
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        ax1.axvline(x=0, color='k', linestyle='--', alpha=0.5)
        
        # 2. 损伤演化
        ax2 = plt.subplot(2, 2, 2)
        ax2.plot(results['strain'], results['d_plus'], 'r-', label='d+ (受拉)', linewidth=2)
        ax2.plot(results['strain'], results['d_minus'], 'b-', label='d- (受压)', linewidth=2)
        ax2.set_xlabel('应变')
        ax2.set_ylabel('损伤变量')
        ax2.set_title('损伤演化')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, 1)
        
        # 3. 塑性应变演化
        ax3 = plt.subplot(2, 2, 3)
        ax3.plot(results['strain'], results['plastic_strain'], 'g-', linewidth=2)
        ax3.set_xlabel('总应变')
        ax3.set_ylabel('塑性应变')
        ax3.set_title('塑性应变演化')
        ax3.grid(True, alpha=0.3)
        
        # 4. 应力-时间曲线
        ax4 = plt.subplot(2, 2, 4)
        time_steps = np.arange(len(results['strain']))
        ax4.plot(time_steps, results['stress'], 'b-', linewidth=2)
        ax4.set_xlabel('时间步')
        ax4.set_ylabel('应力 (MPa)')
        ax4.set_title('应力时程')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片到prediction文件夹
        save_path = f"{self.prediction_dir}/{save_name}_{timestamp}.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"预测结果图已保存至prediction文件夹: {save_path}")
        
        return save_path
    
    def analyze_hysteresis(self, results):
        """分析滞回特性"""
        strain = results['strain']
        stress = results['stress']
        
        # 找到加载反转点
        strain_diff = np.diff(strain)
        reversal_points = np.where(np.diff(np.sign(strain_diff)))[0] + 1
        
        # 计算能量耗散
        energy_dissipation = 0
        if len(reversal_points) > 1:
            for i in range(len(reversal_points) - 1):
                start_idx = reversal_points[i]
                end_idx = reversal_points[i + 1]
                
                # 使用梯形积分计算面积
                cycle_energy = np.trapz(
                    stress[start_idx:end_idx], 
                    strain[start_idx:end_idx]
                )
                energy_dissipation += abs(cycle_energy)
        
        # 计算等效刚度退化
        max_points = []
        min_points = []
        
        for idx in reversal_points:
            if strain[idx] > 0:
                max_points.append((strain[idx], stress[idx]))
            else:
                min_points.append((strain[idx], stress[idx]))
        
        # 获取最终的塑性应变作为残余应变
        plastic_strain = results.get('plastic_strain', np.zeros_like(strain))
        residual_plastic_strain = float(plastic_strain[-1])
        
        # 计算最终位置的残余应变（应力为0时的应变）
        final_elastic_strain = stress[-1] / self.material_constants['E0'] if stress[-1] != 0 else 0
        residual_strain_at_zero_stress = float(strain[-1] - final_elastic_strain)
        
        analysis = {
            'energy_dissipation': energy_dissipation,
            'reversal_points': reversal_points.tolist(),
            'max_points': max_points,
            'min_points': min_points,
            'max_tensile_stress': float(np.max(stress)),
            'max_compressive_stress': float(np.min(stress)),
            'residual_plastic_strain': residual_plastic_strain,  # 累积塑性应变
            'residual_strain': residual_strain_at_zero_stress,   # 应力为0时的应变
            'final_strain': float(strain[-1]),
            'final_stress': float(stress[-1])
        }
        
        return analysis
    
    def save_results(self, results, analysis, save_name='prediction_results'):
        """保存预测结果到prediction文件夹"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 确保prediction目录存在
        Path(self.prediction_dir).mkdir(parents=True, exist_ok=True)
        
        # 保存数据到Excel
        df = pd.DataFrame({
            'strain': results['strain'],
            'stress': results['stress'],
            'd_plus': results['d_plus'],
            'd_minus': results['d_minus'],
            'plastic_strain': results['plastic_strain'],
            'elastic_strain': results['elastic_strain']
        })
        
        excel_path = f"{self.prediction_dir}/{save_name}_{timestamp}.xlsx"
        df.to_excel(excel_path, index=False)
        
        # 保存分析结果到JSON
        analysis_data = {
            'timestamp': timestamp,
            'model_path': self.model_path,
            'physics_parameters': self.physics_params,
            'material_constants': self.material_constants,
            'analysis': analysis
        }
        
        json_path = f"{self.prediction_dir}/analysis_results_{timestamp}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, indent=2, ensure_ascii=False)
        
        # 生成预测报告文档
        report_path = self.generate_prediction_report(results, analysis, timestamp)
        
        print(f"\n预测结果文档已全部保存到prediction文件夹:")
        print(f"  数据文件: {excel_path}")
        print(f"  分析结果: {json_path}")
        print(f"  预测报告: {report_path}")
        print(f"  目标目录: {self.prediction_dir}")
        
        return excel_path, json_path, report_path
    
    def generate_prediction_report(self, results, analysis, timestamp):
        """生成预测报告文档"""
        report_path = f"{self.prediction_dir}/prediction_report_{timestamp}.txt"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("混凝土全滞回曲线预测报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"生成时间: {timestamp}\n")
            f.write(f"模型文件: {self.model_path}\n\n")
            
            f.write("材料常数:\n")
            f.write(f"  初始弹性模量 E0 = {self.material_constants['E0']:.0f} MPa\n")
            f.write(f"  受拉强度 f_t = {self.material_constants['f_t']:.2f} MPa\n")
            f.write(f"  受压强度 f_c = {self.material_constants['f_c']:.2f} MPa\n\n")
            
            f.write("识别的物理参数:\n")
            f.write(f"  受拉损伤参数: A+ = {self.physics_params['A_plus']:.4f}, "
                   f"B+ = {self.physics_params['B_plus']:.4f}\n")
            f.write(f"  受压损伤参数: A- = {self.physics_params['A_minus']:.4f}, "
                   f"B- = {self.physics_params['B_minus']:.4f}\n")
            f.write(f"  塑性参数: ξ+ = {self.physics_params['xi_plus']:.4f}, "
                   f"ξ- = {self.physics_params['xi_minus']:.4f}\n\n")
            
            f.write("滞回分析结果:\n")
            f.write(f"  能量耗散: {analysis['energy_dissipation']:.2f}\n")
            f.write(f"  最大拉应力: {analysis['max_tensile_stress']:.2f} MPa\n")
            f.write(f"  最大压应力: {analysis['max_compressive_stress']:.2f} MPa\n")
            f.write(f"  累积塑性应变: {analysis['residual_plastic_strain']:.6f}\n")
            f.write(f"  残余应变(应力=0): {analysis['residual_strain']:.6f}\n")
            f.write(f"  最终应变: {analysis['final_strain']:.6f}\n")
            f.write(f"  最终应力: {analysis['final_stress']:.2f} MPa\n\n")
            
            f.write("数据统计:\n")
            f.write(f"  数据点数: {len(results['strain'])}\n")
            f.write(f"  应变范围: [{np.min(results['strain']):.6f}, {np.max(results['strain']):.6f}]\n")
            f.write(f"  应力范围: [{np.min(results['stress']):.2f}, {np.max(results['stress']):.2f}] MPa\n")
            f.write(f"  最大拉损伤: {np.max(results['d_plus']):.4f}\n")
            f.write(f"  最大压损伤: {np.max(results['d_minus']):.4f}\n\n")
            
            f.write("输出文件:\n")
            f.write(f"  数据文件: prediction_results_{timestamp}.xlsx\n")
            f.write(f"  分析结果: analysis_results_{timestamp}.json\n")
            f.write(f"  预测图片: hysteresis_prediction_{timestamp}.png\n")
            f.write(f"  本报告: prediction_report_{timestamp}.txt\n")
        
        return report_path
    
    def organize_prediction_outputs(self):
        """整理prediction文件夹中的输出文件"""
        print(f"\n整理prediction文件夹中的文件...")
        print(f"目标目录: {self.prediction_dir}")
        
        if not Path(self.prediction_dir).exists():
            print("prediction文件夹不存在!")
            return
        
        # 获取所有文件
        prediction_files = list(Path(self.prediction_dir).glob("*"))
        
        if not prediction_files:
            print("prediction文件夹为空")
            return
        
        # 按类型分类文件
        image_files = [f for f in prediction_files if f.suffix.lower() in ['.png', '.jpg', '.jpeg']]
        data_files = [f for f in prediction_files if f.suffix.lower() in ['.xlsx', '.csv']]
        json_files = [f for f in prediction_files if f.suffix.lower() == '.json']
        report_files = [f for f in prediction_files if f.suffix.lower() == '.txt']
        
        print(f"\nprediction文件夹内容统计:")
        print(f"  图片文件: {len(image_files)} 个")
        for img in image_files:
            print(f"    - {img.name}")
        
        print(f"  数据文件: {len(data_files)} 个")
        for data in data_files:
            print(f"    - {data.name}")
        
        print(f"  分析文件: {len(json_files)} 个")
        for json_file in json_files:
            print(f"    - {json_file.name}")
        
        print(f"  报告文件: {len(report_files)} 个")
        for report in report_files:
            print(f"    - {report.name}")
        
        print(f"\n所有预测文档已确认位于prediction文件夹: {self.prediction_dir}")


def main():
    """主函数"""
    print("混凝土全滞回曲线预测")
    print("=" * 60)
    
    # 自动查找最新的模型文件
    model_path = None
    
    # 检查最新的模型
    results_dir = Path("results")
    if results_dir.exists():
        sessions = sorted([d for d in results_dir.iterdir() if d.is_dir() and d.name.startswith("session_")])
        if sessions:
            # 从最新的session开始查找
            for session in reversed(sessions):
                best_model = session / "training" / "best_model.pth"
                if best_model.exists():
                    model_path = str(best_model)
                    print(f"找到最新模型: {model_path}")
                    break
    
    # 如果没有找到模型文件，报错并退出
    if model_path is None:
        print("错误: 未找到任何可用的模型文件!")
        print("请确保以下路径存在模型文件:")
        print("  - results/session_*/training/best_model.pth")
        print("\n请先运行训练脚本生成模型文件。")
        return
    
    print(f"使用模型: {model_path}")
    
    # 创建预测器（会自动推断正确的prediction目录）
    predictor = HysteresisPredictor(model_path)
    
    print(f"预测结果将保存到: {predictor.prediction_dir}")
    print("-" * 40)
    
    # 生成循环加载路径
    print("\n生成循环加载路径...")
    strain_path = predictor.generate_cyclic_loading_path(
        max_tensile_strain=0.002,
        max_compressive_strain=-0.003,
        num_cycles=3,
        points_per_segment=50
    )
    
    # 预测响应
    print("预测力学响应...")
    results = predictor.predict_response(strain_path)
    
    # 分析滞回特性
    print("分析滞回特性...")
    analysis = predictor.analyze_hysteresis(results)
    
    print(f"\n分析结果:")
    print(f"  能量耗散: {analysis['energy_dissipation']:.2f}")
    print(f"  最大拉应力: {analysis['max_tensile_stress']:.2f} MPa")
    print(f"  最大压应力: {analysis['max_compressive_stress']:.2f} MPa")
    print(f"  累积塑性应变: {analysis['residual_plastic_strain']:.6f}")
    print(f"  残余应变(应力=0): {analysis['residual_strain']:.6f}")
    print(f"  最终应变: {analysis['final_strain']:.6f}")
    print(f"  最终应力: {analysis['final_stress']:.2f} MPa")
    
    # 绘制结果
    print("\n绘制预测结果...")
    predictor.plot_results(results, save_name='cyclic_analysis')
    
    # 保存结果
    print("保存预测结果...")
    predictor.save_results(results, analysis)
    
    # 整理prediction文件夹中的文件
    predictor.organize_prediction_outputs()
    
    print("\n预测完成! 所有结果文档已保存到prediction文件夹。")


if __name__ == "__main__":
    main()