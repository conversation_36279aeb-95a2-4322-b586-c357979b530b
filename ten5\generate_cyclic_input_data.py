"""
生成单轴反复受拉的输入数据
用于PINN模型预测滞回曲线
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import os
import font_config  # 导入字体配置

def generate_cyclic_loading_path(loading_scheme, n_points_per_segment=100):
    """
    生成循环加载路径
    
    Args:
        loading_scheme: 加载方案，格式为 [(max_strain, n_cycles), ...]
        n_points_per_segment: 每个加载/卸载段的点数
        
    Returns:
        strain_path: 应变路径
        segment_info: 段信息
    """
    strain_path = []
    segment_info = []
    
    for max_strain, n_cycles in loading_scheme:
        for cycle in range(n_cycles):
            # 加载段: 0 -> max_strain
            if len(strain_path) == 0:
                loading = np.linspace(0, max_strain, n_points_per_segment)
            else:
                loading = np.linspace(0, max_strain, n_points_per_segment)[1:]
            
            strain_path.extend(loading)
            segment_info.append({
                'type': 'loading',
                'start_idx': len(strain_path) - len(loading),
                'end_idx': len(strain_path) - 1,
                'max_strain': max_strain,
                'cycle': cycle + 1
            })
            
            # 卸载段: max_strain -> 0
            unloading = np.linspace(max_strain, 0, n_points_per_segment)[1:]
            strain_path.extend(unloading)
            segment_info.append({
                'type': 'unloading',
                'start_idx': len(strain_path) - len(unloading),
                'end_idx': len(strain_path) - 1,
                'max_strain': max_strain,
                'cycle': cycle + 1
            })
    
    return np.array(strain_path), segment_info

def save_input_data(strain_path, segment_info, filename='cyclic_input_data.xlsx'):
    """
    保存输入数据到Excel文件
    """
    # 创建数据框架
    df = pd.DataFrame({
        'index': np.arange(len(strain_path)),
        'strain': strain_path,
        'strain_percent': strain_path * 100,  # 百分比应变
        'strain_permille': strain_path * 1000  # ‰应变
    })
    
    # 添加段信息
    df['segment_type'] = ''
    df['cycle_number'] = 0
    df['max_strain'] = 0.0
    
    for seg in segment_info:
        start = seg['start_idx']
        end = seg['end_idx'] + 1
        df.loc[start:end-1, 'segment_type'] = seg['type']
        df.loc[start:end-1, 'cycle_number'] = seg['cycle']
        df.loc[start:end-1, 'max_strain'] = seg['max_strain']
    
    # 保存到Excel
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 主数据表
        df.to_excel(writer, sheet_name='Data', index=False)
        
        # 写入元数据
        metadata = pd.DataFrame({
            '参数': ['总数据点数', '每段点数', '生成时间'],
            '值': [len(strain_path), 100,  # 默认值，因为函数内部无法访问
                   datetime.now().strftime('%Y-%m-%d %H:%M:%S')]
        })
        metadata.to_excel(writer, sheet_name='Metadata', index=False)
    
    print(f"数据已保存到: {filename}")
    return df

def plot_loading_path(strain_path, segment_info, save_path='loading_path.png'):
    """
    绘制加载路径图
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # 应变时程曲线
    time_steps = np.arange(len(strain_path))
    ax1.plot(time_steps, strain_path * 1000, 'b-', linewidth=2)
    ax1.set_xlabel('时间步', fontsize=12)
    ax1.set_ylabel('应变 (‰)', fontsize=12)
    ax1.set_title('应变加载路径', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    
    # 标注峰值
    peak_indices = []
    for seg in segment_info:
        if seg['type'] == 'loading':
            peak_indices.append(seg['end_idx'])
    
    for idx in peak_indices:
        ax1.plot(idx, strain_path[idx] * 1000, 'ro', markersize=8)
        ax1.annotate(f'{strain_path[idx]*1000:.1f}', 
                    xy=(idx, strain_path[idx] * 1000),
                    xytext=(5, 5), textcoords='offset points')
    
    # 应变增量
    strain_increment = np.diff(strain_path, prepend=0)
    ax2.plot(time_steps, strain_increment * 1000, 'g-', linewidth=1.5)
    ax2.set_xlabel('时间步', fontsize=12)
    ax2.set_ylabel('应变增量 (‰)', fontsize=12)
    ax2.set_title('应变增量时程', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"加载路径图已保存到: {save_path}")

# 预定义的加载方案
LOADING_SCHEMES = {
    'standard': [  # 标准递增加载
        (0.0001, 2),   # 0.1‰，2个循环
        (0.0002, 2),   # 0.2‰，2个循环
        (0.0003, 2),   # 0.3‰，2个循环
        (0.0004, 2),   # 0.4‰，2个循环
        (0.0005, 2),   # 0.5‰，2个循环
        (0.0006, 2),   # 0.6‰，2个循环
    ],
    'simple': [    # 简单加载
        (0.0002, 3),   # 0.2‰，3个循环
        (0.0004, 3),   # 0.4‰，3个循环
    ],
    'single': [    # 单一幅值
        (0.0003, 5),   # 0.3‰，5个循环
    ],
    'large': [     # 大幅值加载
        (0.0005, 2),   # 0.5‰，2个循环
        (0.0010, 2),   # 1.0‰，2个循环
        (0.0015, 2),   # 1.5‰，2个循环
    ]
}

if __name__ == "__main__":
    print("生成单轴反复受拉输入数据")
    print("=" * 60)
    
    # 选择加载方案
    print("\n可用的加载方案:")
    for i, (name, scheme) in enumerate(LOADING_SCHEMES.items(), 1):
        print(f"{i}. {name}")
        for strain, cycles in scheme:
            print(f"   - {strain*1000:.1f}‰ × {cycles}个循环")
    
    try:
        choice = input("\n请选择加载方案 (1-4，默认为1): ").strip()
    except EOFError:
        choice = '1'
    
    if not choice:
        choice = '1'
    
    scheme_names = list(LOADING_SCHEMES.keys())
    try:
        scheme_idx = int(choice) - 1
        if 0 <= scheme_idx < len(scheme_names):
            selected_scheme = scheme_names[scheme_idx]
            loading_scheme = LOADING_SCHEMES[selected_scheme]
        else:
            print("无效选择，使用默认方案")
            selected_scheme = 'standard'
            loading_scheme = LOADING_SCHEMES['standard']
    except:
        print("无效输入，使用默认方案")
        selected_scheme = 'standard'
        loading_scheme = LOADING_SCHEMES['standard']
    
    print(f"\n使用加载方案: {selected_scheme}")
    
    # 设置每段的点数
    try:
        n_points = input("\n每个加载/卸载段的点数 (默认100): ").strip()
    except EOFError:
        n_points = ""
    
    if not n_points:
        n_points_per_segment = 100
    else:
        try:
            n_points_per_segment = int(n_points)
        except:
            n_points_per_segment = 100
    
    print(f"每段点数: {n_points_per_segment}")
    
    # 生成数据
    print("\n生成加载路径...")
    strain_path, segment_info = generate_cyclic_loading_path(
        loading_scheme, n_points_per_segment)
    
    print(f"总数据点数: {len(strain_path)}")
    print(f"总循环数: {sum(cycles for _, cycles in loading_scheme)}")
    
    # 保存数据
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = 'input_data'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    excel_file = os.path.join(output_dir, 
                             f'cyclic_input_{selected_scheme}_{timestamp}.xlsx')
    df = save_input_data(strain_path, segment_info, excel_file)
    
    # 绘制图形
    plot_file = os.path.join(output_dir, 
                            f'loading_path_{selected_scheme}_{timestamp}.png')
    plot_loading_path(strain_path, segment_info, plot_file)
    
    # 打印统计信息
    print("\n统计信息:")
    print(f"应变范围: 0 - {np.max(strain_path)*1000:.2f}‰")
    print(f"最大应变增量: {np.max(np.abs(np.diff(strain_path)))*1000:.4f}‰")
    
    print("\n数据生成完成!")
    print(f"Excel文件: {excel_file}")
    print(f"图形文件: {plot_file}") 