"""
测试损伤演化计算
验证修正后的损伤公式是否正确工作
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from pinn_model_v2_extended import PhysicsCalculatorV2Extended
import font_config

def test_damage_evolution():
    """
    测试损伤演化公式
    """
    print("测试损伤演化计算...")
    
    # 材料参数
    E0 = 30000.0  # MPa
    f_t = 3.0     # MPa 
    f_c = 30.0    # MPa
    
    # 损伤参数（典型值）
    A_plus = 0.5
    B_plus = 1.0
    xi_plus = 0.05
    A_minus = 1.5
    B_minus = 0.5
    xi_minus = 0.02
    
    # 创建物理计算器
    physics_calc = PhysicsCalculatorV2Extended(E0=E0, f_t=f_t, f_c=f_c)
    
    # 测试1：单调拉伸加载
    print("\n测试1：单调拉伸加载")
    strain_max = 0.005  # 0.5%应变
    n_steps = 100
    strain_increments = torch.linspace(0, strain_max, n_steps+1)[1:] - torch.linspace(0, strain_max, n_steps+1)[:-1]
    
    d_plus_seq, d_minus_seq, ep_seq, stress_seq = physics_calc.calculate_physics_constraints(
        strain_increments,
        torch.tensor(A_plus), torch.tensor(B_plus),
        torch.tensor(A_minus), torch.tensor(B_minus),
        torch.tensor(xi_plus), torch.tensor(xi_minus)
    )
    
    total_strain = torch.cumsum(strain_increments, dim=0)
    
    print(f"  最大应变: {total_strain[-1].item():.4f}")
    print(f"  最大拉伸损伤: {d_plus_seq.max().item():.4f}")
    print(f"  最大压缩损伤: {d_minus_seq.max().item():.4f}")
    print(f"  最终塑性应变: {ep_seq[-1].item():.6f}")
    print(f"  最大应力: {stress_seq.max().item():.2f} MPa")
    
    # 测试2：单调压缩加载
    print("\n测试2：单调压缩加载")
    strain_min = -0.005  # -0.5%应变
    strain_increments = torch.linspace(0, strain_min, n_steps+1)[1:] - torch.linspace(0, strain_min, n_steps+1)[:-1]
    
    d_plus_seq_c, d_minus_seq_c, ep_seq_c, stress_seq_c = physics_calc.calculate_physics_constraints(
        strain_increments,
        torch.tensor(A_plus), torch.tensor(B_plus),
        torch.tensor(A_minus), torch.tensor(B_minus),
        torch.tensor(xi_plus), torch.tensor(xi_minus)
    )
    
    total_strain_c = torch.cumsum(strain_increments, dim=0)
    
    print(f"  最小应变: {total_strain_c[-1].item():.4f}")
    print(f"  最大拉伸损伤: {d_plus_seq_c.max().item():.4f}")
    print(f"  最大压缩损伤: {d_minus_seq_c.max().item():.4f}")
    print(f"  最终塑性应变: {ep_seq_c[-1].item():.6f}")
    print(f"  最小应力: {stress_seq_c.min().item():.2f} MPa")
    
    # 测试3：拉压循环加载
    print("\n测试3：拉压循环加载")
    cycle_strain = 0.003  # ±0.3%应变
    
    # 生成循环应变路径
    strain_path = []
    
    # 第一阶段：拉伸到峰值
    strain_path.extend(np.linspace(0, cycle_strain, 50))
    # 第二阶段：卸载到零
    strain_path.extend(np.linspace(cycle_strain, 0, 25)[1:])
    # 第三阶段：压缩到谷值
    strain_path.extend(np.linspace(0, -cycle_strain, 50)[1:])
    # 第四阶段：卸载到零
    strain_path.extend(np.linspace(-cycle_strain, 0, 25)[1:])
    
    strain_path = np.array(strain_path)
    strain_increments_cycle = torch.tensor(np.diff(strain_path, prepend=0.0), dtype=torch.float32)
    
    d_plus_seq_cyc, d_minus_seq_cyc, ep_seq_cyc, stress_seq_cyc = physics_calc.calculate_physics_constraints(
        strain_increments_cycle,
        torch.tensor(A_plus), torch.tensor(B_plus),
        torch.tensor(A_minus), torch.tensor(B_minus),
        torch.tensor(xi_plus), torch.tensor(xi_minus)
    )
    
    print(f"  应变范围: {strain_path.min():.4f} ~ {strain_path.max():.4f}")
    print(f"  最大拉伸损伤: {d_plus_seq_cyc.max().item():.4f}")
    print(f"  最大压缩损伤: {d_minus_seq_cyc.max().item():.4f}")
    print(f"  最终塑性应变: {ep_seq_cyc[-1].item():.6f}")
    print(f"  应力范围: {stress_seq_cyc.min().item():.2f} ~ {stress_seq_cyc.max().item():.2f} MPa")
    
    # 绘制结果
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 单调拉伸
    ax1.plot(total_strain.numpy() * 1000, stress_seq.numpy(), 'r-', linewidth=2, label='应力')
    ax1_twin = ax1.twinx()
    ax1_twin.plot(total_strain.numpy() * 1000, d_plus_seq.numpy(), 'b--', linewidth=2, label='拉伸损伤')
    ax1.set_xlabel('应变 (‰)')
    ax1.set_ylabel('应力 (MPa)', color='r')
    ax1_twin.set_ylabel('损伤', color='b')
    ax1.set_title('单调拉伸')
    ax1.grid(True, alpha=0.3)
    
    # 单调压缩
    ax2.plot(total_strain_c.numpy() * 1000, stress_seq_c.numpy(), 'r-', linewidth=2, label='应力')
    ax2_twin = ax2.twinx()
    ax2_twin.plot(total_strain_c.numpy() * 1000, d_minus_seq_c.numpy(), 'g--', linewidth=2, label='压缩损伤')
    ax2.set_xlabel('应变 (‰)')
    ax2.set_ylabel('应力 (MPa)', color='r')
    ax2_twin.set_ylabel('损伤', color='g')
    ax2.set_title('单调压缩')
    ax2.grid(True, alpha=0.3)
    
    # 循环加载 - 滞回曲线
    ax3.plot(strain_path * 1000, stress_seq_cyc.numpy(), 'b-', linewidth=2)
    ax3.set_xlabel('应变 (‰)')
    ax3.set_ylabel('应力 (MPa)')
    ax3.set_title('拉压循环滞回曲线')
    ax3.grid(True, alpha=0.3)
    
    # 循环加载 - 损伤演化
    time_steps = np.arange(len(strain_path))
    ax4.plot(time_steps, d_plus_seq_cyc.numpy(), 'r-', linewidth=2, label='拉伸损伤 D+')
    ax4.plot(time_steps, d_minus_seq_cyc.numpy(), 'b-', linewidth=2, label='压缩损伤 D-')
    ax4.plot(time_steps, ep_seq_cyc.numpy() * 1000, 'g--', linewidth=2, label='塑性应变×1000')
    ax4.set_xlabel('时间步')
    ax4.set_ylabel('损伤/塑性应变')
    ax4.set_title('损伤和塑性应变演化')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('damage_test_results.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"\n测试结果已保存至: damage_test_results.png")
    
    # 返回是否成功（损伤大于0表示成功）
    return d_plus_seq_cyc.max().item() > 0 or d_minus_seq_cyc.max().item() > 0

def test_parameter_sensitivity():
    """
    测试参数敏感性
    """
    print("\n" + "="*50)
    print("参数敏感性测试")
    print("="*50)
    
    # 基础参数
    E0, f_t, f_c = 30000.0, 3.0, 30.0
    physics_calc = PhysicsCalculatorV2Extended(E0=E0, f_t=f_t, f_c=f_c)
    
    # 测试应变
    strain_max = 0.004  # 0.4%应变
    n_steps = 50
    strain_increments = torch.linspace(0, strain_max, n_steps+1)[1:] - torch.linspace(0, strain_max, n_steps+1)[:-1]
    total_strain = torch.cumsum(strain_increments, dim=0)
    
    # 不同参数组合
    parameter_sets = [
        {"A+": 0.2, "B+": 0.5, "A-": 1.0, "B-": 0.3, "name": "低损伤"},
        {"A+": 0.5, "B+": 1.0, "A-": 1.5, "B-": 0.5, "name": "中等损伤"},
        {"A+": 0.8, "B+": 2.0, "A-": 2.0, "B-": 1.0, "name": "高损伤"},
    ]
    
    results = {}
    
    for params in parameter_sets:
        d_plus_seq, d_minus_seq, ep_seq, stress_seq = physics_calc.calculate_physics_constraints(
            strain_increments,
            torch.tensor(params["A+"]), torch.tensor(params["B+"]),
            torch.tensor(params["A-"]), torch.tensor(params["B-"]),
            torch.tensor(0.05), torch.tensor(0.02)  # 固定塑性参数
        )
        
        results[params["name"]] = {
            'strain': total_strain.numpy(),
            'stress': stress_seq.numpy(),
            'damage_plus': d_plus_seq.numpy(),
            'plastic_strain': ep_seq.numpy()
        }
        
        print(f"{params['name']}:")
        print(f"  参数: A+={params['A+']}, B+={params['B+']}, A-={params['A-']}, B-={params['B-']}")
        print(f"  最大损伤: {d_plus_seq.max().item():.4f}")
        print(f"  最终塑性应变: {ep_seq[-1].item():.6f}")
        print(f"  最大应力: {stress_seq.max().item():.2f} MPa")
    
    # 绘制对比图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    colors = ['blue', 'green', 'red']
    for i, (name, data) in enumerate(results.items()):
        ax1.plot(data['strain'] * 1000, data['stress'], 
                linewidth=2, color=colors[i], label=name)
        ax2.plot(data['strain'] * 1000, data['damage_plus'], 
                linewidth=2, color=colors[i], label=name)
    
    ax1.set_xlabel('应变 (‰)')
    ax1.set_ylabel('应力 (MPa)')
    ax1.set_title('不同参数下的应力-应变曲线')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    ax2.set_xlabel('应变 (‰)')
    ax2.set_ylabel('拉伸损伤')
    ax2.set_title('不同参数下的损伤演化')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('parameter_sensitivity_test.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"\n参数敏感性测试结果已保存至: parameter_sensitivity_test.png")

def main():
    """
    主测试函数
    """
    print("损伤演化计算测试程序")
    print("="*60)
    
    # 执行基础测试
    success = test_damage_evolution()
    
    if success:
        print("\n✓ 基础损伤计算测试通过")
        
        # 执行参数敏感性测试
        test_parameter_sensitivity()
        
        print("\n✓ 所有测试完成")
        print("请查看生成的图像文件验证结果：")
        print("  - damage_test_results.png")
        print("  - parameter_sensitivity_test.png")
    else:
        print("\n✗ 损伤计算测试失败")
        print("损伤值始终为0，需要进一步检查公式实现")

if __name__ == "__main__":
    main() 