"""
基于增量式输入的混凝土损伤参数识别PINN模型
严格按照框架要求实现
"""

import torch
import torch.nn as nn
import numpy as np

class DamagePINN(nn.Module):
    """
    混凝土损伤PINN模型
    基于GRU架构，输入应变增量序列，输出应力、损伤、塑性应变
    """
    
    def __init__(self, input_size=1, hidden_size=64, num_layers=2, output_size=3):
        super(DamagePINN, self).__init__()
        
        # GRU层 - 用于处理增量式序列输入
        self.gru = nn.GRU(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=0.1 if num_layers > 1 else 0
        )
        
        # 全连接层 - 输出三个物理量
        self.fc_layers = nn.Sequential(
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_size, output_size)
        )
        
        # 激活函数确保物理约束
        self.sigmoid = nn.Sigmoid()  # 用于损伤变量 [0,1]
        self.softplus = nn.Softplus()  # 用于塑性应变，平滑版本的ReLU
        
    def forward(self, strain_increment_seq):
        """
        前向传播
        
        Args:
            strain_increment_seq: 应变增量序列 [batch_size, seq_len, 1]
            
        Returns:
            sigma_seq: 应力序列 [batch_size, seq_len]
            d_seq: 损伤变量序列 [batch_size, seq_len] 
            ep_seq: 塑性应变序列 [batch_size, seq_len]
        """
        # GRU处理序列
        gru_out, _ = self.gru(strain_increment_seq)  # [batch_size, seq_len, hidden_size]
        
        # 全连接层输出
        output = self.fc_layers(gru_out)  # [batch_size, seq_len, 3]
        
        # 分离三个输出并应用物理约束
        sigma_seq = output[:, :, 0]  # 应力可以为负
        d_seq = self.sigmoid(output[:, :, 1])  # 损伤 [0,1]
        
        # 改进塑性应变输出
        # 直接输出塑性应变的累积值
        ep_raw = output[:, :, 2]
        # 使用tanh将输出映射到[-1, 1]，然后缩放到合理范围
        ep_seq = (torch.tanh(ep_raw) + 1.0) * 0.0004  # 映射到[0, 0.0008]范围
        
        return sigma_seq, d_seq, ep_seq


class PhysicsCalculator:
    """
    物理约束计算器
    按照框架要求实现增量式物理计算
    """
    
    def __init__(self, E0=30000.0, f_t=3.0):
        self.E0 = E0  # 初始弹性模量
        self.f_t = f_t  # 单轴抗拉强度
    
    def calculate_physics_constraints(self, strain_increment_seq, A_plus, B_plus, xi):
        """
        按增量步计算物理约束目标
        
        Args:
            strain_increment_seq: 应变增量序列 [seq_len]
            A_plus, B_plus, xi: 待识别物理参数
            
        Returns:
            d_phy_seq: 物理损伤序列
            ep_phy_seq: 物理塑性应变序列
        """
        # 初始化状态变量
        epsilon_phy = torch.tensor(0.0, device=strain_increment_seq.device)
        ep_phy = torch.tensor(0.0, device=strain_increment_seq.device) 
        d_phy = torch.tensor(0.0, device=strain_increment_seq.device)
        r_max_phy = torch.tensor(self.f_t, device=strain_increment_seq.device)
        
        d_phy_seq = []
        ep_phy_seq = []
        
        # 按增量步进行循环积分
        for delta_epsilon in strain_increment_seq:
            # 1. 更新应变状态
            epsilon_phy += delta_epsilon
            delta_ep = xi * torch.relu(delta_epsilon)  # 塑性应变增量
            ep_phy += delta_ep
            
            # 2. 计算损伤驱动力
            Y_phy = self.E0 * (epsilon_phy - ep_phy)
            
            # 3. 条件性地更新损伤和阈值
            if Y_phy > r_max_phy:
                r_max_phy = Y_phy
                # 损伤演化公式
                term1 = self.f_t / r_max_phy * (1 - A_plus)
                term2 = A_plus * torch.exp(B_plus * (1 - r_max_phy / self.f_t))
                d_phy = 1 - (term1 + term2)
                # 确保损伤在[0,1]范围内
                d_phy = torch.clamp(d_phy, 0.0, 1.0)
            
            # 4. 记录当前步的状态
            d_phy_seq.append(d_phy.clone())
            ep_phy_seq.append(ep_phy.clone())
        
        return torch.stack(d_phy_seq), torch.stack(ep_phy_seq)
    
    def calculate_stress_from_state(self, strain_total_seq, d_seq, ep_seq):
        """
        根据本构关系计算应力
        σ = (1 - d) * E0 * (ε - εp)
        """
        return (1 - d_seq) * self.E0 * (strain_total_seq - ep_seq)


class LossCalculator:
    """
    损失函数计算器
    实现框架中定义的四部分损失
    """
    
    def __init__(self, lambda_data=1.0, lambda_stress=1.0, lambda_damage=0.8, lambda_plastic=0.5):
        self.lambda_data = lambda_data
        self.lambda_stress = lambda_stress
        self.lambda_damage = lambda_damage
        self.lambda_plastic = lambda_plastic
    
    def calculate_total_loss(self, sigma_hat_seq, d_hat_seq, ep_hat_seq, 
                           stress_exp_seq, strain_total_seq, 
                           d_phy_seq, ep_phy_seq, E0):
        """
        计算总损失
        """
        # L_data: 数据拟合损失
        loss_data = torch.mean((sigma_hat_seq - stress_exp_seq)**2)
        
        # L_stress: 本构自洽损失
        stress_physics = (1 - d_hat_seq) * E0 * (strain_total_seq - ep_hat_seq)
        loss_stress = torch.mean((sigma_hat_seq - stress_physics)**2)
        
        # L_damage: 损伤物理损失
        loss_damage = torch.mean((d_hat_seq - d_phy_seq)**2)
        
        # L_plastic: 塑性物理损失
        loss_plastic = torch.mean((ep_hat_seq - ep_phy_seq)**2)
        
        # 总损失
        total_loss = (self.lambda_data * loss_data + 
                     self.lambda_stress * loss_stress + 
                     self.lambda_damage * loss_damage + 
                     self.lambda_plastic * loss_plastic)
        
        return total_loss, {
            'loss_data': loss_data.item(),
            'loss_stress': loss_stress.item(), 
            'loss_damage': loss_damage.item(),
            'loss_plastic': loss_plastic.item(),
            'total_loss': total_loss.item()
        } 