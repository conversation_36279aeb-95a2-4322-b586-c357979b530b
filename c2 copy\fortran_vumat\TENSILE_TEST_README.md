# 混凝土单轴拉伸测试指南

本文档详细介绍了如何使用PINN模型参数进行混凝土单轴拉伸测试的完整流程。

## 1. 文件说明

### 1.1 核心文件

- `tensile_test.inp`: ABAQUS输入文件，定义了单轴拉伸测试的几何模型、边界条件和加载方式
- `vumat_tensile_test.f`: 简化版VUMAT材料模型，专门用于单轴拉伸测试
- `run_tensile_test.bat`: 批处理脚本，用于运行单轴拉伸测试
- `process_tensile_results.py`: Python脚本，用于后处理和可视化测试结果

### 1.2 PINN模型接口文件

- `export_pinn_for_vumat.py`: 将训练好的PINN模型参数导出为VUMAT可用的格式
- `material_parameters.inc`: 包含PINN识别的材料参数（由导出工具生成）

## 2. 几何模型

- **几何尺寸**: 100mm×100mm×250mm的长方体
- **单元类型**: 单个C3D8R单元（8节点线性砖，减缩积分）
- **边界条件**: 底面固定，顶面施加Z方向位移

## 3. 使用流程

### 3.1 导出PINN模型参数

首先，使用导出工具将训练好的PINN模型参数导出为VUMAT可用的格式：

```bash
python export_pinn_for_vumat.py --model path/to/best_model.pth --output-dir fortran_vumat
```

这将在`fortran_vumat`目录下生成`material_parameters.inc`文件。

### 3.2 运行单轴拉伸测试

运行批处理脚本以执行单轴拉伸测试：

```bash
cd fortran_vumat
run_tensile_test.bat
```

该脚本将：
1. 清理旧的结果文件
2. 检查PINN模型参数
3. 编译VUMAT
4. 运行ABAQUS分析
5. 提取基本结果

### 3.3 处理和可视化结果

使用Python脚本处理和可视化测试结果：

```bash
# 在Abaqus Python环境中运行
abaqus python process_tensile_results.py

# 或者，如果已经有CSV格式的结果
python process_tensile_results.py tensile_test_results.csv
```

该脚本将生成以下图表：
- 应力-应变曲线
- 损伤演化曲线
- 塑性应变演化曲线
- 综合结果图表

所有结果将保存在以时间戳命名的目录中（例如：`tensile_results_20250705_123456`）。

## 4. 参数说明

### 4.1 材料参数

VUMAT材料模型使用以下参数：

- `E0`: 初始弹性模量 (MPa)
- `f_t`: 拉伸强度 (MPa)
- `f_c`: 压缩强度 (MPa)
- `A_plus`: 拉伸损伤参数A
- `B_plus`: 拉伸损伤参数B
- `xi_plus`: 拉伸塑性参数
- `A_minus`: 压缩损伤参数A
- `B_minus`: 压缩损伤参数B
- `xi_minus`: 压缩塑性参数

这些参数可以从PINN模型中识别，也可以在输入文件中直接指定。

### 4.2 状态变量

VUMAT材料模型使用以下状态变量：

1. `SDV1`: 累积塑性应变
2. `SDV2`: 拉伸损伤变量 (d+)
3. `SDV3`: 压缩损伤变量 (d-)
4. `SDV4`: 最大拉伸损伤驱动力 (r_max+)
5. `SDV5`: 最大压缩损伤驱动力 (r_max-)

## 5. 结果分析

### 5.1 应力-应变曲线

应力-应变曲线展示了混凝土在单轴拉伸下的完整力学响应，包括：
- 线性弹性阶段
- 非线性损伤发展阶段
- 软化阶段

### 5.2 损伤演化

损伤演化曲线展示了拉伸损伤(d+)和压缩损伤(d-)随应变的变化。在单轴拉伸测试中，主要关注拉伸损伤的发展。

### 5.3 塑性应变演化

塑性应变演化曲线展示了塑性应变随总应变的变化，反映了材料的不可恢复变形。

## 6. 注意事项

1. 确保Abaqus环境正确配置
2. 如果使用自定义PINN模型参数，请确保`material_parameters.inc`文件格式正确
3. 为获得更平滑的曲线，可以在输入文件中增加输出频率
4. 单轴拉伸测试中应变率效应被忽略，结果仅反映准静态响应

## 7. 故障排除

### 7.1 常见问题

1. **VUMAT编译失败**
   - 检查Fortran编译器是否正确安装
   - 检查VUMAT代码中是否有语法错误

2. **分析中断**
   - 检查输入文件中的单位是否一致
   - 检查材料参数是否合理

3. **结果处理失败**
   - 确保在正确的Python环境中运行
   - 检查结果文件是否存在

### 7.2 联系支持

如有任何问题，请联系项目维护人员获取支持。 