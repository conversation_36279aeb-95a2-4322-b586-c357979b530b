<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>混凝土单轴受压应力-应变曲线 - 基于吴建营研究</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script
        src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@2.1.0/dist/chartjs-plugin-annotation.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 20px;
            background: linear-gradient(135deg, #e6f7ff 0%, #f0f8ff 100%);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            border: 1px solid #e0e6ed;
        }

        header {
            background: linear-gradient(135deg, #1a5f9e 0%, #2c3e50 100%);
            color: white;
            padding: 35px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        header::before {
            content: "";
            position: absolute;
            top: -50px;
            left: -50px;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
        }

        header::after {
            content: "";
            position: absolute;
            bottom: -80px;
            right: -80px;
            width: 250px;
            height: 250px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 50%;
        }

        h1 {
            font-size: 2.6rem;
            margin-bottom: 15px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 2;
        }

        .subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .content {
            padding: 30px 40px;
        }

        .section {
            margin-bottom: 40px;
            padding: 30px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid #eaeff5;
        }

        h2 {
            color: #1a5f9e;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 25px;
            font-size: 1.9rem;
            display: flex;
            align-items: center;
        }

        h2 i {
            margin-right: 12px;
            background: #e3f2fd;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #1a5f9e;
        }

        h3 {
            color: #2980b9;
            margin: 25px 0 15px;
            font-size: 1.45rem;
            padding-left: 8px;
            border-left: 4px solid #3498db;
        }

        p {
            margin-bottom: 18px;
            font-size: 1.15rem;
            line-height: 1.7;
            color: #444;
        }

        .parameters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .parameter-card {
            background: #f0f9ff;
            border-left: 5px solid #3498db;
            padding: 20px;
            border-radius: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }

        .parameter-card::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: #3498db;
        }

        .parameter-card:hover {
            transform: translateY(-7px);
            background: #e6f7ff;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }

        .parameter-card h4 {
            color: #2c3e50;
            margin-bottom: 12px;
            font-size: 1.25rem;
            display: flex;
            align-items: center;
        }

        .parameter-card h4 i {
            margin-right: 10px;
            color: #3498db;
        }

        .parameter-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #1a5f9e;
            margin: 5px 0;
        }

        .parameter-desc {
            font-size: 0.95rem;
            color: #666;
            margin-top: 8px;
        }

        .chart-container {
            height: 550px;
            margin: 35px 0;
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            border: 1px solid #e0e6ed;
            background: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
            border-radius: 10px;
            overflow: hidden;
        }

        .data-table th {
            background: #2c6ca0;
            color: white;
            text-align: left;
            padding: 16px;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .data-table td {
            padding: 14px 16px;
            border-bottom: 1px solid #eaeff5;
        }

        .data-table tr:nth-child(even) {
            background-color: #f8fbff;
        }

        .data-table tr:hover {
            background-color: #e6f7ff;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: #2c6ca0;
            color: white;
            padding: 16px 32px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.15rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 6px 12px rgba(44, 108, 160, 0.3);
        }

        .btn i {
            margin-right: 10px;
            font-size: 1.3rem;
        }

        .btn:hover {
            background: #1a5f9e;
            transform: translateY(-3px);
            box-shadow: 0 8px 18px rgba(44, 108, 160, 0.4);
        }

        .btn:active {
            transform: translateY(-1px);
        }

        .btn-download {
            background: #27ae60;
            box-shadow: 0 6px 12px rgba(39, 174, 96, 0.3);
        }

        .btn-download:hover {
            background: #219653;
            box-shadow: 0 8px 18px rgba(39, 174, 96, 0.4);
        }

        .btn-container {
            text-align: center;
            margin: 35px 0;
        }

        .curve-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 35px 0;
        }

        .curve-stage {
            padding: 25px;
            border-radius: 12px;
            background: #fff;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            border-top: 5px solid;
        }

        .curve-stage:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .stage-title {
            font-weight: bold;
            font-size: 1.3rem;
            margin-bottom: 18px;
            color: #2c3e50;
            display: flex;
            align-items: center;
        }

        .stage-title i {
            margin-right: 10px;
            font-size: 1.4rem;
        }

        .stage-1 {
            border-top-color: #3498db;
        }

        .stage-2 {
            border-top-color: #27ae60;
        }

        .stage-3 {
            border-top-color: #e74c3c;
        }

        .stage-points {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px dashed #e0e6ed;
        }

        .reference {
            background: #f0f9ff;
            padding: 25px;
            border-radius: 12px;
            margin-top: 35px;
            border-left: 5px solid #3498db;
        }

        footer {
            text-align: center;
            padding: 30px;
            background: #1a3a5f;
            color: #ecf0f1;
            margin-top: 30px;
        }

        .footer-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .footer-content p {
            color: #d1e7ff;
            margin-bottom: 10px;
        }

        .footer-title {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: white;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 2rem;
            }

            .subtitle {
                font-size: 1.05rem;
            }

            .parameter-card {
                padding: 18px;
            }

            .chart-container {
                height: 400px;
            }

            .content {
                padding: 20px;
            }

            .section {
                padding: 20px;
            }

            .btn {
                padding: 14px 26px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <header>
            <h1>混凝土单轴受压应力-应变曲线</h1>
            <p class="subtitle">基于吴建营等《混凝土弹塑性损伤本构模型研究Ⅱ：数值计算和试验验证》的参数生成</p>
        </header>

        <div class="content">
            <div class="section">
                <h2><i>📚</i> 研究背景</h2>
                <p>本数据基于吴建营与李杰在2005年发表的《混凝土弹塑性损伤本构模型研究Ⅱ：数值计算和试验验证》中的研究成果生成。该论文建立了混凝土弹塑性损伤本构模型的数值分析框架，提出了无条件稳定的应力更新算法。
                </p>
                <p>根据文献中第4.2.2节普通混凝土单轴受压试验的模拟参数，我们生成了完整的应力-应变曲线数据点，覆盖从弹性阶段到软化阶段的全过程（应变范围：0 - 0.008）。</p>

                <div class="parameters">
                    <div class="parameter-card">
                        <h4><i>⚖️</i> 弹性模量 (E₀)</h4>
                        <div class="parameter-value">31,700 MPa</div>
                        <div class="parameter-desc">混凝土在弹性阶段的刚度</div>
                    </div>
                    <div class="parameter-card">
                        <h4><i>📈</i> 抗压强度 (f_c)</h4>
                        <div class="parameter-value">27.6 MPa</div>
                        <div class="parameter-desc">混凝土的最大抗压强度</div>
                    </div>
                    <div class="parameter-card">
                        <h4><i>🔰</i> 初始损伤阈值 (f₀⁻)</h4>
                        <div class="parameter-value">10.2 MPa</div>
                        <div class="parameter-desc">开始发生损伤的应力值</div>
                    </div>
                    <div class="parameter-card">
                        <h4><i>📊</i> 损伤参数 (A⁻, B⁻)</h4>
                        <div class="parameter-value">1.0, 0.16</div>
                        <div class="parameter-desc">控制损伤演化的关键参数</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2><i>📉</i> 应力-应变曲线</h2>
                <div class="chart-container">
                    <canvas id="stressStrainChart"></canvas>
                </div>

                <div class="curve-info">
                    <div class="curve-stage stage-1">
                        <div class="stage-title"><i>➡️</i> 弹性阶段 (ε &lt; 0.00087)</div>
                        <p>应力随应变线性增长：</p>
                        <p><strong>σ = E₀ × ε</strong></p>
                        <div class="stage-points">
                            <p><strong>关键点：</strong></p>
                            <p>• 初始损伤阈值：10.2 MPa</p>
                            <p>• 弹性阶段终点应变：0.00087</p>
                        </div>
                    </div>
                    <div class="curve-stage stage-2">
                        <div class="stage-title"><i>⬆️</i> 强化阶段 (0.00087 ≤ ε ≤ 0.002)</div>
                        <p>非线性增长至峰值强度</p>
                        <p>塑性变形开始累积</p>
                        <p>损伤开始发展</p>
                        <div class="stage-points">
                            <p><strong>关键点：</strong></p>
                            <p>• 峰值应力：27.6 MPa</p>
                            <p>• 峰值应变：0.002</p>
                        </div>
                    </div>
                    <div class="curve-stage stage-3">
                        <div class="stage-title"><i>⬇️</i> 软化阶段 (ε > 0.002)</div>
                        <p>材料强度逐渐降低</p>
                        <p>损伤持续发展</p>
                        <p>残余强度随应变增加而减小</p>
                        <div class="stage-points">
                            <p><strong>关键点：</strong></p>
                            <p>• 极限应变：0.008</p>
                            <p>• 残余强度：约3.1 MPa</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2><i>📋</i> 数据点表格</h2>
                <p>下表展示了混凝土单轴受压应力-应变数据点（前10个数据点）：</p>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>应变 (ε)</th>
                            <th>应力 (MPa)</th>
                            <th>阶段</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <!-- Table content will be generated by JavaScript -->
                    </tbody>
                </table>

                <div class="btn-container">
                    <button class="btn btn-download" id="downloadBtn"><i>💾</i> 下载完整Excel数据</button>
                </div>
            </div>

            <div class="section reference">
                <h2><i>📖</i> 参考文献</h2>
                <p><strong>吴建营, 李杰.</strong> 混凝土弹塑性损伤本构模型研究Ⅱ：数值计算和试验验证[J]. 土木工程学报, 2005, 38(9): 21-27.</p>
                <p>参数来源：第4.2.2节 普通混凝土单轴受压试验模拟</p>
                <p>DOI: 10.15951/j.tmgxb.2005.09.004</p>
                <p>试验数据来源：Karsan 和 Jinsa (1969) 普通混凝土单轴受压试验</p>
            </div>
        </div>

        <footer>
            <div class="footer-content">
                <div class="footer-title">混凝土弹塑性损伤本构模型数据生成器</div>
                <p>数据基于吴建营等(2005)的研究成果生成</p>
                <p>© 2023 混凝土力学性能分析工具 | 仅供学术研究使用</p>
            </div>
        </footer>
    </div>

    <script>
        // 生成应力-应变数据
        function generateStressStrainData() {
            const data = [];
            const E0 = 31700; // 弹性模量 (MPa)
            const fc = 27.6;  // 抗压强度 (MPa)
            const f0_minus = 10.2; // 初始损伤阈值 (MPa)
            const epsilon0 = f0_minus / E0; // 初始损伤应变
            const epsilon_c = 0.002; // 峰值应变
            const epsilon_u = 0.008; // 极限应变

            let peakStress = 0;
            let peakStrain = 0;

            // 生成应变值 (0到0.008，步长0.0000625)
            for (let epsilon = 0; epsilon <= epsilon_u; epsilon += 0.0000625) {
                let stress;
                let stage;

                // 弹性阶段
                if (epsilon <= epsilon0) {
                    stress = E0 * epsilon;
                    stage = "弹性阶段";
                }
                // 强化阶段
                else if (epsilon <= epsilon_c) {
                    const ratio = (epsilon - epsilon0) / (epsilon_c - epsilon0);
                    stress = f0_minus + (fc - f0_minus) * Math.pow(ratio, 1.5);
                    stage = "强化阶段";
                }
                // 软化阶段
                else {
                    const ratio = (epsilon - epsilon_c) / (epsilon_u - epsilon_c);
                    stress = fc * (1 - Math.pow(ratio, 1.2));
                    stage = "软化阶段";
                }

                // 记录峰值点
                if (stress > peakStress) {
                    peakStress = stress;
                    peakStrain = epsilon;
                }

                data.push({
                    strain: parseFloat(epsilon.toFixed(6)),
                    stress: parseFloat(stress.toFixed(2)),
                    stage: stage
                });
            }

            return {
                data: data,
                peakStress: peakStress,
                peakStrain: peakStrain
            };
        }

        // 创建图表
        function createChart(dataPoints, peakStrain, peakStress) {
            const ctx = document.getElementById('stressStrainChart').getContext('2d');

            // 提取应变和应力数据
            const strains = dataPoints.map(item => item.strain);
            const stresses = dataPoints.map(item => item.stress);

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: strains,
                    datasets: [{
                        label: '应力 (MPa)',
                        data: stresses,
                        borderColor: '#2980b9',
                        backgroundColor: 'rgba(41, 128, 185, 0.1)',
                        borderWidth: 4,
                        pointRadius: 0,
                        fill: true,
                        tension: 0.2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function (context) {
                                    return `应力: ${context.parsed.y.toFixed(2)} MPa`;
                                },
                                afterLabel: function (context) {
                                    const strain = context.parsed.x;
                                    if (strain < 0.00087) return '弹性阶段';
                                    if (strain < 0.002) return '强化阶段';
                                    return '软化阶段';
                                }
                            }
                        },
                        annotation: {
                            annotations: {
                                peakPoint: {
                                    type: 'point',
                                    xValue: peakStrain,
                                    yValue: peakStress,
                                    radius: 8,
                                    backgroundColor: 'rgba(231, 76, 60, 1)',
                                    borderColor: '#fff',
                                    borderWidth: 3
                                },
                                peakLabel: {
                                    type: 'label',
                                    xValue: peakStrain,
                                    yValue: peakStress,
                                    content: `峰值: ${peakStress.toFixed(1)} MPa`,
                                    position: 'top',
                                    backgroundColor: 'rgba(231, 76, 60, 0.9)',
                                    color: '#fff',
                                    font: {
                                        size: 16,
                                        weight: 'bold'
                                    },
                                    padding: 8,
                                    borderRadius: 6
                                },
                                elasticEnd: {
                                    type: 'line',
                                    xMin: 0.00087,
                                    xMax: 0.00087,
                                    yMin: 0,
                                    yMax: peakStress * 1.05,
                                    borderColor: '#3498db',
                                    borderWidth: 2,
                                    borderDash: [5, 5]
                                },
                                elasticLabel: {
                                    type: 'label',
                                    xValue: 0.00087,
                                    yValue: peakStress * 0.1,
                                    content: '弹性阶段结束',
                                    position: 'bottom',
                                    backgroundColor: 'rgba(52, 152, 219, 0.8)',
                                    color: '#fff',
                                    font: {
                                        size: 14
                                    },
                                    padding: 6,
                                    borderRadius: 4
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '应变 (ε)',
                                font: {
                                    size: 18,
                                    weight: 'bold'
                                },
                                padding: { top: 10 }
                            },
                            ticks: {
                                callback: function (value) {
                                    return value.toFixed(3);
                                },
                                font: {
                                    size: 14
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '应力 (MPa)',
                                font: {
                                    size: 18,
                                    weight: 'bold'
                                },
                                padding: { bottom: 10 }
                            },
                            suggestedMin: 0,
                            suggestedMax: peakStress * 1.2,
                            ticks: {
                                font: {
                                    size: 14
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        // 填充表格
        function fillTable(data) {
            const tableBody = document.getElementById('tableBody');
            // 只显示前10个数据点
            for (let i = 0; i < 10; i++) {
                const point = data[i];
                const row = document.createElement('tr');

                row.innerHTML = `
                    <td>${point.strain.toFixed(6)}</td>
                    <td>${point.stress.toFixed(2)}</td>
                    <td>${point.stage}</td>
                `;

                tableBody.appendChild(row);
            }
        }

        // 导出Excel文件
        function exportToExcel(data) {
            // 创建工作簿
            const wb = XLSX.utils.book_new();

            // 准备工作表数据
            const wsData = [
                ['混凝土单轴受压应力-应变数据 (基于吴建营等研究)'],
                ['弹性模量 E₀ = 31,700 MPa', '抗压强度 f_c = 27.6 MPa', '初始损伤阈值 f₀⁻ = 10.2 MPa'],
                ['应变 (ε)', '应力 (MPa)', '阶段'],
                ...data.map(item => [item.strain, item.stress, item.stage])
            ];

            // 创建工作表
            const ws = XLSX.utils.aoa_to_sheet(wsData);

            // 设置列宽
            ws['!cols'] = [{ wch: 15 }, { wch: 15 }, { wch: 15 }];

            // 合并标题行
            ws['!merges'] = [
                { s: { r: 0, c: 0 }, e: { r: 0, c: 2 } },
                { s: { r: 1, c: 0 }, e: { r: 1, c: 2 } }
            ];

            // 添加工作表到工作簿
            XLSX.utils.book_append_sheet(wb, ws, '应力-应变数据');

            // 导出文件
            XLSX.writeFile(wb, '混凝土单轴受压应力应变曲线数据.xlsx');
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function () {
            // 生成数据
            const result = generateStressStrainData();
            const data = result.data;
            const peakStress = result.peakStress;
            const peakStrain = result.peakStrain;

            // 创建图表
            createChart(data, peakStrain, peakStress);

            // 填充表格
            fillTable(data);

            // 设置下载按钮事件
            document.getElementById('downloadBtn').addEventListener('click', function () {
                exportToExcel(data);
            });
        });
    </script>
</body>

</html>