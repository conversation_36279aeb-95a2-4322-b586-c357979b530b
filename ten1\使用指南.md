# PINN模型使用指南

## 快速开始

### 1. 环境检查

首先确保您的Python环境已安装必要的依赖包：

```bash
pip install torch numpy pandas matplotlib openpyxl
```

或者使用提供的requirements.txt：

```bash
pip install -r requirements.txt
```

### 2. 验证环境

运行简单测试脚本验证环境：

```bash
python simple_test.py
```

如果看到"✅ 所有基本功能测试通过！"，说明环境配置正确。

### 3. 运行完整测试

运行功能测试脚本：

```bash
python test_pinn.py
```

这将测试PINN模型的所有核心功能。

### 4. 训练模型

运行主程序开始训练：

```bash
python concrete_tension_pinn.py
```

## 文件说明

### 核心文件

- **`concrete_tension_pinn.py`** - 主要的PINN模型实现
- **`test_pinn.py`** - 完整功能测试脚本
- **`simple_test.py`** - 基础环境验证脚本
- **`requirements.txt`** - Python依赖包列表
- **`README.md`** - 详细技术文档
- **`使用指南.md`** - 本文件，快速使用指南

### 数据文件

- **`tension.xlsx`** - 实验数据文件（应变-应力数据）

### 输出文件（训练后生成）

- **`concrete_tension_pinn_model.pth`** - 训练好的模型文件
- **`pinn_results.png`** - 训练结果可视化图

## 模型架构概述

### 核心组件

1. **ConcreteTensionPINN** - 主神经网络模型
   - LSTM层：捕捉历史依赖性
   - 全连接层：映射到物理量
   - 可训练参数：A+, B+, ξ

2. **PhysicsConstraints** - 物理约束计算
   - 损伤阈值计算
   - 物理损伤演化
   - 塑性应变累积

3. **ConcreteTensionTrainer** - 训练管理器
   - 数据加载和预处理
   - 损失函数计算
   - 训练循环管理
   - 结果可视化

### 物理方程

模型集成了以下混凝土损伤力学方程：

1. **应力-应变关系**：σ = E₀(1-d)(ε-εᵖ)
2. **损伤驱动力**：Y = E₀(ε-εᵖ)
3. **损伤阈值**：r(t) = max(r_old, Y(t))
4. **损伤演化**：d = 1 - (r₀/r)[(1-A⁺) + A⁺exp(B⁺(1-r/r₀))]
5. **塑性累积**：εᵖ(t) = εᵖ(t-1) + ξΔε

## 训练流程

### 1. 数据准备

```python
# 自动加载Excel数据
strain_data, stress_data = trainer.load_data_from_excel("tension.xlsx")

# 或使用示例数据
strain_data, stress_data = trainer.generate_sample_data(n_points=100)
```

### 2. 模型初始化

```python
model = ConcreteTensionPINN(
    input_size=1,      # 输入维度（应变）
    hidden_size=128,   # LSTM隐藏层大小
    num_layers=3,      # LSTM层数
    output_size=3      # 输出维度（应力、损伤、塑性应变）
)
```

### 3. 训练配置

```python
trainer = ConcreteTensionTrainer(model)

# 可调整的训练参数
trainer.lambda_data = 1.0      # 数据拟合权重
trainer.lambda_stress = 0.8    # 应力约束权重
trainer.lambda_damage = 0.5    # 损伤约束权重
trainer.lambda_plastic = 0.5   # 塑性约束权重
```

### 4. 开始训练

```python
trainer.train(
    strain_data, 
    stress_data, 
    epochs=1000,        # 训练轮数
    print_interval=100  # 打印间隔
)
```

## 结果分析

### 训练过程监控

训练过程中会显示：
- 各项损失函数值的变化
- 材料参数的实时更新
- 学习率自动调整

### 最终输出

1. **识别的材料参数**：
   - A⁺：损伤演化形状参数
   - B⁺：损伤演化速率参数
   - ξ：塑性应变发展系数

2. **可视化结果**：
   - 应力-应变曲线对比
   - 损伤变量演化
   - 塑性应变发展
   - 训练损失历史
   - 参数演化过程

### 模型验证

```python
# 使用训练好的模型进行预测
predictions = trainer.predict(new_strain_data)

# 获取预测结果
stress_pred = predictions['stress_pred']
damage_pred = predictions['damage_pred']
plastic_pred = predictions['plastic_pred']
```

## 参数调优建议

### 网络结构调优

- **hidden_size**：128-256，更大的网络容量
- **num_layers**：2-4，平衡复杂度和训练稳定性
- **dropout**：0.1-0.2，防止过拟合

### 训练参数调优

- **learning_rate**：0.001-0.01，根据收敛情况调整
- **epochs**：500-2000，确保充分训练
- **batch_size**：通常为1（单条曲线）

### 损失权重调优

根据训练效果调整各项损失的权重：
- 如果数据拟合不好，增加`lambda_data`
- 如果物理约束不满足，增加对应的物理损失权重

## 常见问题解决

### 1. 训练不收敛

**症状**：损失函数不下降或震荡

**解决方案**：
- 降低学习率（0.0001-0.001）
- 调整损失权重比例
- 增加训练轮数
- 检查数据质量

### 2. 预测结果不合理

**症状**：预测的应力、损伤或塑性应变超出物理范围

**解决方案**：
- 检查材料参数设置（E₀, ft）
- 验证实验数据的正确性
- 调整网络架构
- 增强物理约束权重

### 3. 参数不更新

**症状**：可训练参数A+, B+, ξ保持初值不变

**解决方案**：
- 检查参数是否正确设置为可训练
- 增加物理约束损失权重
- 调整参数初始值

### 4. 内存不足

**症状**：训练过程中出现内存错误

**解决方案**：
- 减少序列长度
- 降低网络规模（hidden_size, num_layers）
- 使用CPU训练
- 分批处理数据

## 高级使用

### 1. 自定义物理参数

```python
# 修改材料属性
model.E0 = torch.tensor(35000.0)  # 弹性模量
model.ft = torch.tensor(3.5)      # 抗拉强度

# 设置参数初值
model.A_plus.data = torch.tensor(0.3)
model.B_plus.data = torch.tensor(0.8)
model.xi.data = torch.tensor(0.05)
```

### 2. 加载预训练模型

```python
# 加载已训练的模型
checkpoint = torch.load("concrete_tension_pinn_model.pth")
model.load_state_dict(checkpoint['model_state_dict'])

# 查看训练历史
train_history = checkpoint['train_history']
final_params = checkpoint['final_params']
```

### 3. 批量处理多条曲线

```python
# 准备多条实验曲线
curve_files = ["curve1.xlsx", "curve2.xlsx", "curve3.xlsx"]

for file in curve_files:
    strain, stress = trainer.load_data_from_excel(file)
    trainer.train(strain, stress, epochs=500)
```

### 4. 参数敏感性分析

```python
# 分析不同参数对结果的影响
A_values = [0.3, 0.5, 0.7]
results = {}

for A in A_values:
    model.A_plus.data = torch.tensor(A)
    pred = trainer.predict(strain_data)
    results[A] = pred['stress_pred']
```

## 技术支持

如果遇到问题：

1. 首先运行`simple_test.py`确认环境配置
2. 运行`test_pinn.py`检查模型功能
3. 查看详细的技术文档`README.md`
4. 检查训练过程中的损失函数变化
5. 验证输入数据的格式和质量

## 版本信息

- **模型版本**：1.0
- **支持的PyTorch版本**：≥1.9.0
- **Python版本要求**：≥3.7
- **最后更新**：2024年

---

**祝您使用愉快！如有问题，请参考详细文档或联系技术支持。**