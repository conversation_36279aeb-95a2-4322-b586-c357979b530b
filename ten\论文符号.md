根据提供的两篇论文（Part I 和 Part II），我将整理主要公式符号的运用规则。这些规则包括符号的定义、表示方法（如标量、向量、张量）、上下标含义等。由于两篇论文是连续的，Part I 介绍基本公式，Part II 介绍数值算法和验证，因此符号体系是一致的。

## 主要符号规则总结

### 1. **基本约定**

- 张量：使用粗体符号表示（如应力张量 $\boldsymbol{\sigma}$，应变张量 $\boldsymbol{\varepsilon}$）。

- 向量：使用粗体小写字母（如硬化参数向量 $\boldsymbol{\kappa}$）。

- 标量：使用普通斜体（如损伤变量 $d^+$, $d^-$）。

- 四阶张量：使用空心粗体（如刚度张量 $\mathbb{C}_0$，投影张量 $\mathbb{P}^+$）或有时用粗体（需根据上下文判断，如文中用 $\mathbb{C}_0$ 表示四阶刚度张量）。

### 2. **关键符号及其含义**

#### (1) 应力和应变

- $\boldsymbol{\sigma}$: 柯西应力张量（二阶）。

- $\bar{\boldsymbol{\sigma}}$: 有效应力张量（二阶），定义见 Part I 公式 (1a):

$$\bar{\boldsymbol{\sigma}} = \mathbb{C}_0 : (\boldsymbol{\varepsilon} - \boldsymbol{\varepsilon}^p)$$

- $\boldsymbol{\varepsilon}$: 总应变张量（二阶），分解为弹性部分和塑性部分：

$$\boldsymbol{\varepsilon} = \boldsymbol{\varepsilon}^e + \boldsymbol{\varepsilon}^p$$

- $\boldsymbol{\varepsilon}^e$: 弹性应变张量。

- $\boldsymbol{\varepsilon}^p$: 塑性应变张量。

#### (2) 损伤变量

- $d^+$: 受拉损伤变量（标量，0 表示无损，1 表示完全损伤）。

- $d^-$: 受剪（或受压）损伤变量（标量）。

- 损伤张量 $\mathbb{D}$ (四阶):

$$\mathbb{D} = d^+ \mathbb{P}^+ + d^- \mathbb{P}^-$$

其中 $\mathbb{P}^+$ 和 $\mathbb{P}^-$ 是正负投影张量。

#### (3) 有效应力的分解

- 有效应力分解为正部分和负部分：

$$\bar{\boldsymbol{\sigma}} = \bar{\boldsymbol{\sigma}}^+ + \bar{\boldsymbol{\sigma}}^-$$

其中：

$$\bar{\boldsymbol{\sigma}}^+ = \mathbb{P}^+ : \bar{\boldsymbol{\sigma}}, \quad \bar{\boldsymbol{\sigma}}^- = \mathbb{P}^- : \bar{\boldsymbol{\sigma}}$$

- 投影张量 $\mathbb{P}^+$ 和 $\mathbb{P}^-$ 的定义（基于特征分解）：

$$\mathbb{P}^+ = \sum_{i=1}^{3} H(\bar{\sigma}_i) (\mathbf{p}_i \otimes \mathbf{p}_i \otimes \mathbf{p}_i \otimes \mathbf{p}_i)$$

其中 $H(\cdot)$ 是 Heaviside 函数，$\bar{\sigma}_i$ 是有效应力的特征值，$\mathbf{p}_i$ 是特征向量。

#### (4) 自由能函数

- 弹性 Helmholtz 自由能 $\Psi^e$ 分解为：

$$\Psi^e = \Psi^{e+} + \Psi^{e-} = (1-d^+)\Psi_0^{e+} + (1-d^-)\Psi_0^{e-}$$

其中 $\Psi_0^{e\pm}$ 是无损材料的弹性自由能分量。

#### (5) 本构关系

- 应力-应变关系（Part I 公式 (8)）:

$$\boldsymbol{\sigma} = (1-d^+)\bar{\boldsymbol{\sigma}}^+ + (1-d^-)\bar{\boldsymbol{\sigma}}^- = (\mathbb{I}-\mathbb{D}) : \bar{\boldsymbol{\sigma}}$$

其中 $\mathbb{I}$ 是四阶单位张量。

#### (6) 塑性部分

- 屈服函数 $F$ 和塑性势函数 $F^*$ （Part I 公式 (14)）:

$$F(\bar{\boldsymbol{\sigma}}, \boldsymbol{\kappa}) = (\alpha I_1 + \sqrt{3J_2} + R\langle\hat{\sigma}_{\text{max}}\rangle) - (1-\alpha)c$$

其中：

- $I_1 = \text{tr}(\bar{\boldsymbol{\sigma}})$ 是有效应力的第一不变量。

- $J_2 = \frac{1}{2}\mathbf{s}:\mathbf{s}$ 是有效应力偏量的第二不变量（$\mathbf{s} = \bar{\boldsymbol{\sigma}} - \frac{1}{3}I_1\mathbf{I}$）。

- $\hat{\sigma}_{\text{max}}$ 是最大主应力。

- $\alpha, R, c$ 是参数，与硬化参数 $\boldsymbol{\kappa}$ 有关。

#### (7) 损伤能释放率

- 受拉损伤能释放率 $Y^+$:

$$Y^+ = \frac{1}{E_0}(\bar{\boldsymbol{\sigma}}^+ : \mathbb{A}_0 : \bar{\boldsymbol{\sigma}})$$

其中 $\mathbb{A}_0 = \mathbb{C}_0^{-1}$ 是初始柔度张量。

- 受剪损伤能释放率 $Y^-$:

$$Y^- = \alpha I_1 + \beta \sqrt{J_2}$$

其中 $\alpha, \beta$ 是材料参数。

#### (8) 损伤演化

- 损伤准则:

$$g^\pm(Y^\pm, r^\pm) = Y^\pm - r^\pm \leq 0$$

其中 $r^\pm$ 是损伤阈值。

- 损伤演化律（Part I 公式 (39)）:

$$d^\pm = G^\pm(r^\pm)$$

具体函数形式为：

- 受拉损伤:

$$d^+ = 1 - \frac{r_0^+}{r^+}[(1-A^+)\exp(B^+(1-\frac{r^+}{r_0^+})) + A^+]$$

- 受剪损伤:

$$d^- = 1 - \frac{r_0^-}{r^-}[(1-A^-) - A^-\exp(B^-(1-\frac{r^-}{r_0^-}))]$$

其中 $A^\pm, B^\pm$ 是材料参数。

### 3. **数值算法中的关键符号（Part II）**

- 增量步标记：$t_n$ 和 $t_{n+1}$，状态变量在 $t_n$ 已知，更新到 $t_{n+1}$。

- 弹性预测步：

- 试算有效应力: $\bar{\boldsymbol{\sigma}}_{n+1}^{\text{trial}} = \mathbb{C}_0 : (\boldsymbol{\varepsilon}_{n+1} - \boldsymbol{\varepsilon}_n^p)$

- 塑性修正：如果屈服条件 $F > 0$，进行回映修正。

- 损伤修正：更新损伤变量 $d_{n+1}^\pm$ 和应力 $\boldsymbol{\sigma}_{n+1}$。

- 算法一致性切线模量 $\mathbb{C}^{\text{alg}} = \frac{\partial\boldsymbol{\sigma}_{n+1}}{\partial\boldsymbol{\varepsilon}_{n+1}}$ 的推导（Part II 公式 (22)）:

$$\frac{d\boldsymbol{\sigma}}{d\boldsymbol{\varepsilon}} = (\mathbb{I} - \boldsymbol{\omega} - \mathbb{R}) : \mathbb{C}^{dp}$$

其中 $\mathbb{C}^{dp}$ 是有效弹塑性切线模量，$\boldsymbol{\omega}$ 和 $\mathbb{R}$ 是与损伤演化相关的张量。

### 4. **上下标规则**

- 上标：

- `+`：与受拉相关的量（如 $d^+$, $\bar{\boldsymbol{\sigma}}^+$）。

- `-`：与受剪（受压）相关的量（如 $d^-$, $\bar{\boldsymbol{\sigma}}^-$）。

- `e`：弹性（如 $\boldsymbol{\varepsilon}^e$）。

- `p`：塑性（如 $\boldsymbol{\varepsilon}^p$）。

- `trial`：试算值（如 $\bar{\boldsymbol{\sigma}}^{\text{trial}}$）。

- 下标：

- `0`：初始值（如 $\mathbb{C}_0$）。

- `n`, `n+1`：时间步标记（如 $\boldsymbol{\varepsilon}_n$）。

- `i,j`：特征值或方向索引（如 $\bar{\sigma}_i$）。

### 5. **特殊符号和函数**

- $\langle x \rangle$: Macaulay 括号，定义为 $\langle x \rangle = \frac{x + |x|}{2}$。

- $H(x)$: Heaviside 函数（单位阶跃函数）。

- `tr(·)`: 张量的迹（第一不变量）。

- `:`：双点积（二阶张量之间或四阶张量与二阶张量之间）。

## 总结表（Markdown格式）

| 符号类别         | 符号表示           | 含义与规则                                                                 |
|------------------|--------------------|----------------------------------------------------------------------------|
| **张量表示**     | 粗体（$\boldsymbol{\sigma}$） | 二阶张量（应力、应变）                                                     |
|                  | 空心粗体或粗体（$\mathbb{C}_0$） | 四阶张量（刚度、投影张量）                                                |
| **基本变量**     | $\boldsymbol{\sigma}$         | 柯西应力张量                                                               |
|                  | $\bar{\boldsymbol{\sigma}}$      | 有效应力张量                                                               |
|                  | $\boldsymbol{\varepsilon}$     | 总应变张量                                                                 |
|                  | $\boldsymbol{\varepsilon}^e$    | 弹性应变张量                                                               |
|                  | $\boldsymbol{\varepsilon}^p$    | 塑性应变张量                                                               |
| **损伤变量**     | $d^+$             | 受拉损伤变量（标量）                                                       |
|                  | $d^-$             | 受剪损伤变量（标量）                                                       |
|                  | $\mathbb{D}$      | 损伤张量（四阶）                                                           |
| **投影张量**     | $\mathbb{P}^+$     | 正投影张量（将有效应力投影到受拉部分）                                     |
|                  | $\mathbb{P}^-$     | 负投影张量（将有效应力投影到受剪部分）                                     |
| **自由能**       | $\Psi^e$           | 弹性Helmholtz自由能                                                        |
|                  | $\Psi^{e\pm}$      | 自由能的正/负部分                                                          |
| **塑性**         | $F$              | 屈服函数                                                                   |
|                  | $F^*$             | 塑性势函数                                                                 |
|                  | $\boldsymbol{\kappa}$          | 硬化参数向量                                                               |
| **损伤准则**     | $Y^\pm$           | 损伤能释放率（受拉/受剪）                                                  |
|                  | $r^\pm$           | 损伤阈值                                                                   |
|                  | $g^\pm$           | 损伤准则函数                                                               |
| **数值算法**     | $\bar{\boldsymbol{\sigma}}^{\text{trial}}$  | 弹性试算应力                                                             |
|                  | $\mathbb{C}^{\text{alg}}$       | 算法一致性切线模量                                                         |
| **特殊函数**     | $\langle x \rangle$ | Macaulay括号（取正值部分）                                                 |
|                  | $H(x)$           | Heaviside函数（阶跃函数）                                                  |

## 注意事项

1. 在Part II的数值算法中，有效应力的更新采用了谱分解方法（见Part II公式(9)-(13)），避免了重复计算特征向量，提高了效率。

2. 损伤演化与塑性变形在数值算法中是解耦的（先塑性修正，再损伤修正）。

3. 符号表示在论文中有时会因排版原因略有差异（如四阶张量用黑体或空心黑体），但含义一致。

以上整理基于两篇论文的公式和描述，确保了符号规则的一致性。

混凝土弹塑性损伤本构模型符号规则总结
1. 基本张量表示
符号	含义	规则
σ	柯西应力张量	二阶张量（粗体）
σ̄	有效应力张量	二阶张量（粗体），定义：σ̄ = C₀ : (ε - εᵖ)
ε	总应变张量	二阶张量（粗体），分解为 ε = εᵉ + εᵖ
εᵉ	弹性应变张量	二阶张量（粗体）
εᵖ	塑性应变张量	二阶张量（粗体）
C₀	初始刚度张量	四阶张量（粗体），C₀ = 2G₀I + K₀I ⊗ I
Λ₀	初始柔度张量	四阶张量（粗体），Λ₀ = C₀⁻¹
P⁺, P⁻	正/负投影张量	四阶张量（粗体），由特征值分解定义（见公式 (3a,b)）
2. 损伤变量与分解
符号	含义	规则
d⁺	受拉损伤变量	标量（0 ≤ d⁺ ≤ 1）
d⁻	受剪（受压）损伤变量	标量（0 ≤ d⁻ ≤ 1）
D	损伤张量	四阶张量，D = d⁺P⁺ + d⁻P⁻
σ̄⁺, σ̄⁻	有效应力正/负分量	二阶张量，σ̄⁺ = P⁺ : σ̄, σ̄⁻ = P⁻ : σ̄
ψ₀ᵉ⁺, ψ₀ᵉ⁻	无损弹性自由能分量	标量，ψ₀ᵉ⁺ = 0.5 σ̄⁺ : εᵉ, ψ₀ᵉ⁻ = 0.5 σ̄⁻ : εᵉ
3. 本构关系与自由能
公式	描述
σ = (1 - d⁺)σ̄⁺ + (1 - d⁻)σ̄⁻	应力-应变关系（考虑损伤）
ψᵉ(εᵉ, d⁺, d⁻) = (1 - d⁺)ψ₀ᵉ⁺ + (1 - d⁻)ψ₀ᵉ⁻	损伤后弹性 Helmholtz 自由能
Y⁺ = -∂ψ/∂d⁺ = ψ₀ᵉ⁺
Y⁻ = -∂ψ/∂d⁻ = ψ₀ᵉ⁻	损伤能释放率（热力学广义力）
4. 塑性模型
符号	含义	规则
F(σ̄, κ)	屈服函数	标量，采用修正 Drucker-Prager 形式（公式 (14a)）
G(σ̄)	塑性势函数	标量，非关联流动准则（公式 (14b)）
κ	硬化参数向量	向量，κ = {κ⁺, κ⁻}ᵀ
κ⁺, κ⁻	等效累积塑性变形	标量，κ⁺ = ∫‖εᵖ‖ dt（受拉）, κ⁻ = ∫‖εᵖ‖ dt（受压）
λ	塑性流动因子	标量，由塑性一致性条件确定
5. 损伤演化准则
公式	描述
g⁺(Y⁺, r⁺) = Y⁺ - r⁺ ≤ 0
g⁻(Y⁻, r⁻) = Y⁻ - r⁻ ≤ 0	损伤准则（线性区域边界）
d⁺ = G⁺(r⁺) = 1 - (r₀⁺/r⁺)[(1-A⁺)exp(B⁺(1-r⁺/r₀⁺)) + A⁺]	受拉损伤演化律（非线性函数）
d⁻ = G⁻(r⁻) = 1 - (r₀⁻/r⁻)[(1-A⁻) - A⁻exp(B⁻(1-r⁻/r₀⁻))]	受剪损伤演化律（非线性函数）
r⁺ = max{ rₙ⁺, max_{τ∈[0,t]} Y⁺(τ) }	损伤阈值更新（历史最大值）
6. 数值算法关键符号
符号	含义	规则
σ̄ᵗʳⁱᵃˡ	弹性试算应力	σ̄ᵗʳⁱᵃˍ = C₀ : (εₙ₊₁ - εₙᵖ)
Δγ	塑性流动因子增量	标量，后退欧拉法迭代求解
c₁, c₂	谱分解系数	c₁ = 1 - Δγ⋅2G₀/‖s‖, c₂ = Δγ⋅(⟨I₁⟩/‖s‖⋅G₀ - 3αK₀)
Cᵃˡᵍ	算法一致性切线模量	四阶张量，dσ/dε = (I - ω - R) : Cᵈᵖ
ω	损伤退化张量	ω = d⁺Q⁺ + d⁻Q⁻（四阶张量）
Q⁺, Q⁻	有效应力率投影张量	四阶张量，由特征值定义（公式 (16a,b)）
7. 上下标规则
上标
⁺：受拉相关量（如 d⁺, σ̄⁺）
⁻：受剪/受压相关量（如 d⁻, σ̄⁻）
ᵉ：弹性分量（如 εᵉ）
ᵖ：塑性分量（如 εᵖ）
ᵗʳⁱᵃˡ：试算值（如 σ̄ᵗʳⁱᵃˡ）

下标
₀：初始值（如 C₀）
ₙ, ₙ₊₁：时间步标记（如 εₙ）
i,j：特征值/方向索引（如 σᵢ）

8. 特殊符号与函数
符号	含义	规则
〈x〉	Macaulay 括号	〈x〉 = (x + |x|)/2（取正值部分）
H(x)	Heaviside 函数	单位阶跃函数（x>0 时为 1）
tr(·)	张量迹	第一不变量（如 I₁ = tr(σ̄)）
:	双点积	张量缩并（如 σ : ε）
注：所有张量运算遵循连续介质力学标准约定，四阶张量操作采用缩并规则（如 C : ε）。损伤与塑性解耦的数值框架分为三步：弹性预测 → 塑性修正 → 损伤修正。