import pandas as pd
import os

def check_excel_sheets(excel_path):
    try:
        if not os.path.exists(excel_path):
            print(f"错误: 文件 {excel_path} 不存在")
            return
            
        # 读取Excel文件并打印工作表名称
        excel_file = pd.ExcelFile(excel_path)
        sheet_names = excel_file.sheet_names
        
        print(f"\nExcel文件 {excel_path} 中的工作表名称:")
        for i, name in enumerate(sheet_names):
            print(f"  {i+1}. {name}")
            
    except Exception as e:
        print(f"读取Excel文件时出错: {str(e)}")

if __name__ == "__main__":
    # 检查主Excel文件
    main_excel = "d:\\column\\column\\column.xlsx"
    check_excel_sheets(main_excel)
    
    # 检查滞回曲线目录下的Excel文件
    other_excel = "d:\\column\\column\\滞回曲线\\column1.xlsx"
    check_excel_sheets(other_excel)