#!/usr/bin/env python3
"""
测试已训练的PINN模型
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from concrete_pinn_integrated import ConcreteTensionPINN, ConcreteDataProcessor
import glob
import os

# 配置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def test_latest_model():
    """测试最新保存的模型"""
    
    print("正在寻找最新的模型文件...")
    
    # 寻找最新的模型文件
    model_files = glob.glob("models/concrete_pinn_model_*.pth")
    if not model_files:
        print("未找到模型文件")
        return
    
    latest_model = max(model_files, key=os.path.getctime)
    print(f"找到模型文件: {latest_model}")
    
    # 设置设备
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 创建模型
    model = ConcreteTensionPINN(device=device)
    
    # 加载模型
    try:
        checkpoint = torch.load(latest_model, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print("模型加载成功")
        
        # 打印模型参数
        print(f"\n模型参数:")
        print(f"A+ = {model.A_plus.item():.4f}")
        print(f"B+ = {model.B_plus.item():.4f}")
        print(f"xi = {model.xi.item():.4f}")
        
    except Exception as e:
        print(f"模型加载失败: {e}")
        return
    
    # 加载数据
    print("\n正在加载测试数据...")
    processor = ConcreteDataProcessor(normalize=True)
    
    try:
        strain_data, stress_data = processor.load_data_from_excel("tension.xlsx")
        strain_clean, stress_clean = processor.clean_and_smooth_data(strain_data, stress_data)
        print(f"数据加载成功: {len(strain_clean)} 个数据点")
    except Exception as e:
        print(f"数据加载失败: {e}")
        return
    
    # 测试预测
    print("\n正在进行预测...")
    model.eval()
    
    with torch.no_grad():
        # 归一化数据
        strain_norm = processor.strain_scaler.fit_transform(strain_clean.reshape(-1, 1)).flatten()
        stress_norm = processor.stress_scaler.fit_transform(stress_clean.reshape(-1, 1)).flatten()
        
        strain_tensor = torch.tensor(strain_norm, dtype=torch.float32, device=device)
        
        # 预测
        stress_pred, damage_pred, plastic_pred, _ = model(strain_tensor)
        
        # 反归一化
        stress_pred_np = stress_pred.cpu().numpy().flatten()
        stress_pred_np = processor.stress_scaler.inverse_transform(stress_pred_np.reshape(-1, 1)).flatten()
        
        damage_pred_np = damage_pred.cpu().numpy().flatten()
        plastic_pred_np = plastic_pred.cpu().numpy().flatten()
        plastic_pred_np = processor.strain_scaler.inverse_transform(plastic_pred_np.reshape(-1, 1)).flatten()
    
    # 计算误差
    mse = np.mean((stress_clean - stress_pred_np) ** 2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(stress_clean - stress_pred_np))
    
    print(f"\n预测精度:")
    print(f"MSE = {mse:.6f}")
    print(f"RMSE = {rmse:.6f}")
    print(f"MAE = {mae:.6f}")
    
    # 生成快速测试图
    print("\n正在生成测试图像...")
    
    plt.figure(figsize=(15, 5))
    
    # 应力-应变对比
    plt.subplot(1, 3, 1)
    plt.plot(strain_clean, stress_clean, 'bo-', label='实验数据', markersize=2)
    plt.plot(strain_clean, stress_pred_np, 'r-', label='PINN预测', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('应力 (MPa)')
    plt.title('应力-应变曲线对比')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 损伤演化
    plt.subplot(1, 3, 2)
    plt.plot(strain_clean, damage_pred_np, 'g-', label='损伤演化', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('损伤变量')
    plt.title('损伤演化曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 塑性应变演化
    plt.subplot(1, 3, 3)
    plt.plot(strain_clean, plastic_pred_np, 'm-', label='塑性应变', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('塑性应变')
    plt.title('塑性应变演化')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 确保figures目录存在
    os.makedirs('figures', exist_ok=True)
    plt.savefig('figures/test_results.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("测试图像已保存: figures/test_results.png")
    print("\n测试完成!")

if __name__ == "__main__":
    test_latest_model() 