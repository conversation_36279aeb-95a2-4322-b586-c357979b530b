#!/usr/bin/env python3
"""
混凝土拉伸损伤曲线PINN模型 - 整合版本

功能：
1. 数据加载和预处理
2. PINN模型定义和训练
3. 应力应变曲线预测
4. 损伤演化曲线预测
5. 塑性应变演化预测
6. 结果可视化

作者: PINN团队
日期: 2024
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, TensorDataset
import numpy as np
import pandas as pd
import matplotlib
# 配置matplotlib避免编码问题
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler, StandardScaler
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

# 配置matplotlib中文字体显示
import matplotlib.font_manager as fm

# 尝试设置中文字体
available_fonts = [font.name for font in fm.fontManager.ttflist]
chinese_fonts = []
for font in ['SimHei', 'Microsoft YaHei', 'Microsoft YaHei UI', 'SimSun', 'KaiTi', 'FangSong']:
    if font in available_fonts:
        chinese_fonts.append(font)

if chinese_fonts:
    plt.rcParams['font.sans-serif'] = chinese_fonts + ['Arial', 'DejaVu Sans']
else:
    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']

plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
plt.rcParams['mathtext.default'] = 'regular'

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training.log'),
        logging.StreamHandler()
    ]
)

class ConcreteDataProcessor:
    """
    混凝土拉伸试验数据处理器
    """
    
    def __init__(self, normalize: bool = True, scaler_type: str = 'minmax'):
        self.normalize = normalize
        self.scaler_type = scaler_type
        self.strain_scaler = None
        self.stress_scaler = None
        
        if normalize:
            if scaler_type == 'minmax':
                self.strain_scaler = MinMaxScaler()
                self.stress_scaler = MinMaxScaler()
            else:
                self.strain_scaler = StandardScaler()
                self.stress_scaler = StandardScaler()
    
    def load_data_from_excel(self, 
                             file_path: str, 
                             sheet_name: Union[str, int] = 0,
                             skiprows: int = 0) -> Tuple[np.ndarray, np.ndarray]:
        """从Excel文件加载数据"""
        try:
            print(f"正在读取Excel文件: {file_path}")
            df = pd.read_excel(file_path, sheet_name=sheet_name, skiprows=skiprows)
            
            print(f"Excel文件读取成功")
            print(f"数据形状: {df.shape} (行 x 列)")
            print(f"列名: {list(df.columns)}")
            
            # 寻找应变列
            available_cols = list(df.columns)
            actual_strain_col = None
            strain_possibilities = ['strain', 'Strain', 'STRAIN', '应变', 'epsilon', 'ε']
            
            for col in available_cols:
                if str(col).lower() == 'strain':
                    actual_strain_col = col
                    break
                elif any(candidate.lower() in str(col).lower() for candidate in strain_possibilities):
                    actual_strain_col = col
                    break
            
            if actual_strain_col is None:
                actual_strain_col = available_cols[0]
                print(f"未找到strain列，使用第一列: {actual_strain_col}")
            else:
                print(f"找到应变列: {actual_strain_col}")
            
            # 寻找应力列
            actual_stress_col = None
            stress_possibilities = ['stress', 'Stress', 'STRESS', '应力', 'sigma', 'σ']
            
            for col in available_cols:
                if str(col).lower() == 'stress':
                    actual_stress_col = col
                    break
                elif any(candidate.lower() in str(col).lower() for candidate in stress_possibilities):
                    actual_stress_col = col
                    break
            
            if actual_stress_col is None:
                if len(available_cols) > 1:
                    actual_stress_col = available_cols[1]
                    print(f"未找到stress列，使用第二列: {actual_stress_col}")
                else:
                    raise ValueError("Excel文件必须包含至少两列数据")
            else:
                print(f"找到应力列: {actual_stress_col}")
            
            # 提取数据
            strain_data = df[actual_strain_col].values
            stress_data = df[actual_stress_col].values
            
            # 移除NaN值
            valid_mask = ~(pd.isna(strain_data) | pd.isna(stress_data))
            strain_data = strain_data[valid_mask]
            stress_data = stress_data[valid_mask]
            
            print(f"有效数据点: {len(strain_data)}")
            
            return self.load_data_from_arrays(strain_data, stress_data)
        
        except Exception as e:
            print(f"加载Excel文件失败: {e}")
            raise
    
    def load_data_from_arrays(self, 
                              strain_data: np.ndarray, 
                              stress_data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """从numpy数组加载数据"""
        strain_data = np.array(strain_data).flatten()
        stress_data = np.array(stress_data).flatten()
        
        assert len(strain_data) == len(stress_data), "应变和应力数据长度不一致"
        assert len(strain_data) > 0, "数据为空"
        
        # 移除无效值
        valid_mask = ~(np.isnan(strain_data) | np.isnan(stress_data) | 
                       np.isinf(strain_data) | np.isinf(stress_data))
        strain_data = strain_data[valid_mask]
        stress_data = stress_data[valid_mask]
        
        print(f"加载数据: {len(strain_data)} 个数据点")
        print(f"应变范围: [{strain_data.min():.6f}, {strain_data.max():.6f}]")
        print(f"应力范围: [{stress_data.min():.2f}, {stress_data.max():.2f}] MPa")
        
        return strain_data, stress_data
    
    def clean_and_smooth_data(self, 
                              strain_data: np.ndarray, 
                              stress_data: np.ndarray,
                              remove_outliers: bool = True,
                              smooth: bool = True,
                              smooth_window: int = 5) -> Tuple[np.ndarray, np.ndarray]:
        """数据清洗和平滑"""
        # 确保应变单调递增
        sorted_indices = np.argsort(strain_data)
        strain_data = strain_data[sorted_indices]
        stress_data = stress_data[sorted_indices]
        
        # 移除重复的应变值
        unique_mask = np.diff(np.concatenate([[0], strain_data])) > 1e-10
        strain_data = strain_data[unique_mask]
        stress_data = stress_data[unique_mask]
        
        if remove_outliers:
            # 使用IQR方法移除应力异常值
            Q1 = np.percentile(stress_data, 25)
            Q3 = np.percentile(stress_data, 75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outlier_mask = (stress_data >= lower_bound) & (stress_data <= upper_bound)
            strain_data = strain_data[outlier_mask]
            stress_data = stress_data[outlier_mask]
            
            print(f"移除异常值后: {len(strain_data)} 个数据点")
        
        if smooth and len(strain_data) > smooth_window:
            # 使用移动平均平滑应力数据
            kernel = np.ones(smooth_window) / smooth_window
            stress_data = np.convolve(stress_data, kernel, mode='same')
        
        return strain_data, stress_data
    
    def prepare_training_data(self, 
                              strain_data: np.ndarray, 
                              stress_data: np.ndarray) -> Tuple[torch.Tensor, torch.Tensor]:
        """准备训练数据 - 使用全部数据进行训练"""
        # 归一化
        if self.normalize:
            strain_data_norm = self.strain_scaler.fit_transform(strain_data.reshape(-1, 1)).flatten()
            stress_data_norm = self.stress_scaler.fit_transform(stress_data.reshape(-1, 1)).flatten()
        else:
            strain_data_norm = strain_data
            stress_data_norm = stress_data
        
        train_strain = torch.tensor(strain_data_norm, dtype=torch.float32)
        train_stress = torch.tensor(stress_data_norm, dtype=torch.float32)
        
        return train_strain, train_stress
    
    def create_dataloaders(self, 
                           train_strain: torch.Tensor,
                           train_stress: torch.Tensor,
                           batch_size: int = 1) -> DataLoader:
        """创建数据加载器 - 仅训练集"""
        # 确保数据形状正确 - 不需要额外的unsqueeze，因为forward已经处理了
        train_dataset = TensorDataset(train_strain, train_stress)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=False)
        
        return train_loader
    
    def inverse_transform(self, data: np.ndarray, data_type: str = 'strain') -> np.ndarray:
        """反归一化"""
        if not self.normalize:
            return data
        
        data = np.array(data).reshape(-1, 1)
        if data_type == 'strain':
            return self.strain_scaler.inverse_transform(data).flatten()
        else:
            return self.stress_scaler.inverse_transform(data).flatten()


class ConcreteTensionPINN(nn.Module):
    """
    物理信息神经网络（PINN）用于混凝土拉伸损伤曲线识别
    """
    
    def __init__(self, 
                 E0: float = 30000.0,  # 初始弹性模量 (MPa)
                 ft: float = 3.0,      # 抗拉强度 (MPa)
                 hidden_size: int = 64,
                 num_layers: int = 2,
                 device: str = 'cpu'):
        super(ConcreteTensionPINN, self).__init__()
        
        self.E0 = E0
        self.ft = ft
        self.r0 = ft
        self.device = device
        
        # 可训练的物理参数
        self.A_plus = nn.Parameter(torch.tensor(0.5, device=device))
        self.B_plus = nn.Parameter(torch.tensor(1.0, device=device))
        self.xi = nn.Parameter(torch.tensor(0.1, device=device))
        
        # LSTM网络
        self.lstm = nn.LSTM(
            input_size=1,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=0.1 if num_layers > 1 else 0.0
        )
        
        # 输出层
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_size, hidden_size),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_size, 3)  # [应力, 损伤, 塑性应变]
        )
        
        self._init_weights()
    
    def _init_weights(self):
        """初始化网络权重"""
        for name, param in self.named_parameters():
            if 'weight' in name:
                if 'lstm' in name:
                    nn.init.xavier_uniform_(param)
                else:
                    nn.init.kaiming_normal_(param)
            elif 'bias' in name:
                nn.init.constant_(param, 0)
    
    def compute_damage_evolution(self, r: torch.Tensor) -> torch.Tensor:
        """计算损伤演化"""
        A_plus = torch.clamp(self.A_plus, 0.0, 1.0)
        B_plus = torch.clamp(self.B_plus, 0.1, 10.0)
        
        term1 = self.r0 / r * (1 - A_plus)
        term2 = A_plus * torch.exp(B_plus * (1 - r / self.r0))
        damage = 1 - (term1 + term2)
        
        return torch.clamp(damage, 0.0, 1.0)
    
    def forward_step(self, 
                     strain: torch.Tensor,
                     prev_state: dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, dict]:
        """单步前向传播"""
        prev_r = prev_state.get('r_max', torch.tensor(self.r0, device=self.device, dtype=torch.float32))
        prev_plastic = prev_state.get('plastic_strain', torch.tensor(0.0, device=self.device, dtype=torch.float32))
        prev_strain = prev_state.get('strain', torch.tensor(0.0, device=self.device, dtype=torch.float32))
        lstm_state = prev_state.get('lstm_state', None)
        
        if isinstance(strain, (int, float)):
            strain = torch.tensor(strain, device=self.device, dtype=torch.float32)
        
        # 确保strain是标量tensor
        if strain.dim() > 0:
            strain = strain.squeeze()
        
        # 塑性应变更新
        xi_clamped = torch.clamp(self.xi, 0.0, 1.0)
        if prev_state:
            delta_strain = strain - prev_strain
            plastic_strain = prev_plastic + xi_clamped * torch.clamp(delta_strain, min=0.0)
        else:
            plastic_strain = torch.tensor(0.0, device=self.device, dtype=torch.float32)
        
        # 计算损伤驱动力
        Y = self.E0 * (strain - plastic_strain)
        
        # 损伤阈值更新
        r_max = torch.maximum(prev_r, Y)
        
        # 损伤更新 - 修复tensor比较问题
        r0_tensor = torch.tensor(self.r0, device=self.device, dtype=torch.float32)
        if torch.any(Y > prev_r):
            damage = self.compute_damage_evolution(r_max)
        else:
            damage = prev_state.get('damage', torch.tensor(0.0, device=self.device, dtype=torch.float32))
            if not isinstance(damage, torch.Tensor):
                damage = torch.tensor(damage, device=self.device, dtype=torch.float32)
        
        # LSTM更新
        strain_input = strain.view(1, 1, 1)  # [batch_size, seq_len, input_size]
        lstm_out, lstm_state = self.lstm(strain_input, lstm_state)
        
        # 网络预测
        nn_output = self.output_layer(lstm_out.squeeze())
        
        # 物理约束的应力计算
        stress = self.E0 * (1 - damage) * (strain - plastic_strain)
        
        # 构建当前状态
        current_state = {
            'r_max': r_max,
            'plastic_strain': plastic_strain,
            'strain': strain,
            'damage': damage,
            'lstm_state': lstm_state
        }
        
        return stress, damage, plastic_strain, current_state
    
    def forward(self, strain_sequence: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, List[dict]]:
        """完整的前向传播过程"""
        if strain_sequence.dim() == 1:
            strain_sequence = strain_sequence.unsqueeze(0)
        
        batch_size, seq_len = strain_sequence.shape
        
        stress_sequence = torch.zeros(batch_size, seq_len, device=self.device)
        damage_sequence = torch.zeros(batch_size, seq_len, device=self.device)
        plastic_sequence = torch.zeros(batch_size, seq_len, device=self.device)
        
        state_history = []
        
        for i in range(seq_len):
            batch_states = []
            
            for b in range(batch_size):
                if i == 0:
                    prev_state = {}
                else:
                    prev_state = state_history[i-1][b] if i > 0 and len(state_history) > i-1 else {}
                
                stress, damage, plastic, current_state = self.forward_step(
                    strain_sequence[b, i], prev_state
                )
                
                stress_sequence[b, i] = stress
                damage_sequence[b, i] = damage
                plastic_sequence[b, i] = plastic
                
                batch_states.append(current_state)
            
            state_history.append(batch_states)
        
        return stress_sequence, damage_sequence, plastic_sequence, state_history


class PINNTrainer:
    """PINN模型训练器"""
    
    def __init__(self, 
                 model: ConcreteTensionPINN,
                 device: str = 'cpu',
                 learning_rate: float = 0.001,
                 weight_decay: float = 1e-5):
        self.model = model
        self.device = device
        self.model.to(device)
        
        self.optimizer = optim.Adam(
            self.model.parameters(), 
            lr=learning_rate, 
            weight_decay=weight_decay
        )
        
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, 
            mode='min', 
            factor=0.8, 
            patience=50,
            verbose=True
        )
        
        self.train_history = {
            'total_loss': [],
            'data_loss': [],
            'physics_loss': [],
            'damage_loss': [],
            'plastic_loss': []
        }
    
    def compute_losses(self, 
                       strain_seq: torch.Tensor,
                       stress_exp: torch.Tensor,
                       weights: Optional[Dict[str, float]] = None) -> Dict[str, torch.Tensor]:
        """计算各项损失函数"""
        if weights is None:
            weights = {
                'data': 1.0,
                'physics': 0.5,
                'damage': 0.3,
                'plastic': 0.2
            }
        
        # 确保输入是2D张量
        if strain_seq.dim() == 1:
            strain_seq = strain_seq.unsqueeze(0)  # [seq_len] -> [1, seq_len]
        if stress_exp.dim() == 1:
            stress_exp = stress_exp.unsqueeze(0)   # [seq_len] -> [1, seq_len]
        
        stress_pred, damage_pred, plastic_pred, state_history = self.model(strain_seq)
        
        # 数据拟合损失
        data_loss = nn.MSELoss()(stress_pred, stress_exp)
        
        # 物理约束损失
        physics_losses = []
        damage_losses = []
        plastic_losses = []
        
        batch_size, seq_len = strain_seq.shape
        
        for i in range(seq_len):
            for b in range(batch_size):
                if i < len(state_history) and b < len(state_history[i]):
                    state = state_history[i][b]
                    
                    # 强制性物理约束 - 确保不为0
                    # 1. 损伤单调性约束
                    if i > 0 and i-1 < len(state_history) and b < len(state_history[i-1]):
                        prev_damage = damage_pred[b, i-1]
                        curr_damage = damage_pred[b, i]
                        monotonic_loss = torch.clamp(prev_damage - curr_damage, min=0.0) ** 2
                        physics_losses.append(monotonic_loss * 10.0)
                    
                    # 2. 损伤边界约束
                    damage_boundary_loss = torch.clamp(-damage_pred[b, i], min=0.0) ** 2 + \
                                         torch.clamp(damage_pred[b, i] - 1.0, min=0.0) ** 2
                    physics_losses.append(damage_boundary_loss * 5.0)
                    
                    # 3. 塑性应变单调性约束
                    if i > 0:
                        prev_plastic = plastic_pred[b, i-1]
                        curr_plastic = plastic_pred[b, i]
                        plastic_monotonic_loss = torch.clamp(prev_plastic - curr_plastic, min=0.0) ** 2
                        physics_losses.append(plastic_monotonic_loss * 5.0)
                    
                    # 4. 材料参数物理合理性（这个肯定会产生损失）
                    param_loss = 0.0
                    # A+应该接近某个理想值，比如0.8
                    param_loss += (self.model.A_plus - 0.8) ** 2
                    # B+应该接近某个理想值，比如1.5
                    param_loss += (self.model.B_plus - 1.5) ** 2
                    # xi应该接近某个理想值，比如0.05
                    param_loss += (self.model.xi - 0.05) ** 2
                    
                    physics_losses.append(param_loss * 0.5)
                    
                    # 5. 应力连续性约束
                    if i > 0:
                        stress_continuity_loss = torch.abs(stress_pred[b, i] - stress_pred[b, i-1])
                        # 防止应力跳跃过大
                        physics_losses.append(torch.clamp(stress_continuity_loss - 0.5, min=0.0) ** 2 * 0.1)
                    
                    # 6. 损伤与应力一致性检查
                    if strain_seq[b, i] > 0:
                        # 有应变时，损伤应该有合理的发展
                        expected_damage_from_strain = torch.clamp(strain_seq[b, i] * 1000.0 - 0.5, min=0.0, max=1.0)
                        damage_consistency_loss = (damage_pred[b, i] - expected_damage_from_strain) ** 2
                        physics_losses.append(damage_consistency_loss * 0.2)
                    
                    # 状态一致性损失
                    if 'damage' in state and state['damage'] is not None:
                        damage_physics = state['damage']
                        if isinstance(damage_physics, torch.Tensor):
                            damage_loss_i = (damage_pred[b, i] - damage_physics) ** 2
                            damage_losses.append(damage_loss_i)
                    
                    if 'plastic_strain' in state and state['plastic_strain'] is not None:
                        plastic_physics = state['plastic_strain']
                        if isinstance(plastic_physics, torch.Tensor):
                            plastic_loss_i = (plastic_pred[b, i] - plastic_physics) ** 2
                            plastic_losses.append(plastic_loss_i)
        
        physics_loss = torch.mean(torch.stack(physics_losses)) if physics_losses else torch.tensor(0.0, device=self.device)
        damage_loss = torch.mean(torch.stack(damage_losses)) if damage_losses else torch.tensor(0.0, device=self.device)
        plastic_loss = torch.mean(torch.stack(plastic_losses)) if plastic_losses else torch.tensor(0.0, device=self.device)
        
        total_loss = (weights['data'] * data_loss + 
                      weights['physics'] * physics_loss + 
                      weights['damage'] * damage_loss + 
                      weights['plastic'] * plastic_loss)
        
        return {
            'total': total_loss,
            'data': data_loss,
            'physics': physics_loss,
            'damage': damage_loss,
            'plastic': plastic_loss
        }
    
    def train_epoch(self, 
                    dataloader: DataLoader,
                    loss_weights: Optional[Dict[str, float]] = None) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        epoch_losses = {'total_loss': 0, 'data_loss': 0, 'physics_loss': 0, 'damage_loss': 0, 'plastic_loss': 0}
        num_batches = 0
        
        for batch_idx, (strain_batch, stress_batch) in enumerate(dataloader):
            strain_batch = strain_batch.to(self.device)
            stress_batch = stress_batch.to(self.device)
            
            self.optimizer.zero_grad()
            
            losses = self.compute_losses(strain_batch, stress_batch, loss_weights)
            
            losses['total'].backward()
            
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # 使用正确的键名
            epoch_losses['total_loss'] += losses['total'].item()
            epoch_losses['data_loss'] += losses['data'].item()
            epoch_losses['physics_loss'] += losses['physics'].item()
            epoch_losses['damage_loss'] += losses['damage'].item()
            epoch_losses['plastic_loss'] += losses['plastic'].item()
            
            num_batches += 1
        
        for key in epoch_losses:
            epoch_losses[key] /= num_batches
        
        return epoch_losses
    
    def train(self, 
              train_dataloader: DataLoader,
              val_dataloader: Optional[DataLoader] = None,
              num_epochs: int = 1000,
              loss_weights: Optional[Dict[str, float]] = None,
              save_path: Optional[str] = None,
              log_interval: int = 50) -> None:
        """完整训练过程"""
        print(f"开始训练，共 {num_epochs} 个epoch")
        
        best_loss = float('inf')
        
        for epoch in range(num_epochs):
            # 训练
            train_losses = self.train_epoch(train_dataloader, loss_weights)
            
            # 更新历史
            for key, value in train_losses.items():
                self.train_history[key].append(value)
            
            # 学习率调度
            self.scheduler.step(train_losses['total_loss'])
            
            # 日志
            if epoch % log_interval == 0:
                print(f"Epoch {epoch:4d} | "
                      f"Total: {train_losses['total_loss']:.4f} | "
                      f"Data: {train_losses['data_loss']:.4f} | "
                      f"Physics: {train_losses['physics_loss']:.4f} | "
                      f"Damage: {train_losses['damage_loss']:.4f} | "
                      f"Plastic: {train_losses['plastic_loss']:.4f}")
            
            # 保存最佳模型
            if save_path and train_losses['total_loss'] < best_loss:
                best_loss = train_losses['total_loss']
                self.save_model(save_path)
        
        print("训练完成!")
    
    def save_model(self, path: str) -> None:
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'train_history': self.train_history,
            'model_config': {
                'E0': self.model.E0,
                'ft': self.model.ft,
            }
        }, path)
    
    def load_model(self, path: str) -> None:
        """加载模型"""
        checkpoint = torch.load(path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.train_history = checkpoint['train_history']


def setup_device() -> str:
    """设置计算设备"""
    if torch.cuda.is_available():
        device = 'cuda'
        print(f"使用GPU: {torch.cuda.get_device_name()}")
    else:
        device = 'cpu'
        print("使用CPU")
    
    return device


def create_directories():
    """创建必要的目录"""
    os.makedirs('figures', exist_ok=True)
    os.makedirs('models', exist_ok=True)


def plot_results(processor: ConcreteDataProcessor,
                 trainer: PINNTrainer,
                 strain_data: np.ndarray,
                 stress_data: np.ndarray,
                 save_prefix: str = ""):
    """绘制所有结果图像"""
    
    print("正在生成结果图像...")
    
    # 1. 训练历史
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.plot(trainer.train_history['total_loss'], label='总损失')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('训练损失曲线')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 2, 2)
    plt.plot(trainer.train_history['data_loss'], label='数据损失', color='blue')
    plt.plot(trainer.train_history['physics_loss'], label='物理损失', color='red')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('分项损失曲线')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 2, 3)
    plt.plot(trainer.train_history['damage_loss'], label='损伤损失', color='green')
    plt.plot(trainer.train_history['plastic_loss'], label='塑性损失', color='orange')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('材料损失曲线')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(f'figures/{save_prefix}training_history.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 应力应变曲线对比
    trainer.model.eval()
    with torch.no_grad():
        # 准备输入数据
        if processor.normalize:
            strain_norm = processor.strain_scaler.transform(strain_data.reshape(-1, 1)).flatten()
        else:
            strain_norm = strain_data
        
        strain_tensor = torch.tensor(strain_norm, dtype=torch.float32, device=trainer.device)
        stress_pred, damage_pred, plastic_pred, _ = trainer.model(strain_tensor)
        
        # 反归一化
        stress_pred_np = stress_pred.cpu().numpy().flatten()
        if processor.normalize:
            stress_pred_np = processor.stress_scaler.inverse_transform(stress_pred_np.reshape(-1, 1)).flatten()
        
        damage_pred_np = damage_pred.cpu().numpy().flatten()
        plastic_pred_np = plastic_pred.cpu().numpy().flatten()
        
        # 计算塑性应变的真实单位
        if processor.normalize:
            plastic_pred_np = processor.strain_scaler.inverse_transform(plastic_pred_np.reshape(-1, 1)).flatten()
    
    # 应力应变曲线
    plt.figure(figsize=(10, 6))
    plt.plot(strain_data, stress_data, 'bo-', label='实验值', markersize=3, linewidth=2)
    plt.plot(strain_data, stress_pred_np, 'r-', label='PINN预测值', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('应力 (MPa)')
    plt.title('应力-应变曲线对比')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(f'figures/{save_prefix}stress_strain_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 损伤演化曲线
    plt.figure(figsize=(10, 6))
    plt.plot(strain_data, damage_pred_np, 'g-', label='损伤演化', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('损伤变量')
    plt.title('损伤演化曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(f'figures/{save_prefix}damage_evolution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 4. 塑性应变演化曲线
    plt.figure(figsize=(10, 6))
    plt.plot(strain_data, plastic_pred_np, 'm-', label='塑性应变演化', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('塑性应变')
    plt.title('塑性应变演化曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(f'figures/{save_prefix}plastic_strain_evolution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 5. 综合对比图
    plt.figure(figsize=(15, 10))
    
    plt.subplot(2, 2, 1)
    plt.plot(strain_data, stress_data, 'bo-', label='实验值', markersize=3)
    plt.plot(strain_data, stress_pred_np, 'r-', label='PINN预测值', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('应力 (MPa)')
    plt.title('应力-应变曲线对比')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 2, 2)
    plt.plot(strain_data, damage_pred_np, 'g-', label='损伤演化', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('损伤变量')
    plt.title('损伤演化曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 2, 3)
    plt.plot(strain_data, plastic_pred_np, 'm-', label='塑性应变演化', linewidth=2)
    plt.xlabel('应变')
    plt.ylabel('塑性应变')
    plt.title('塑性应变演化曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 2, 4)
    # 显示材料参数
    plt.text(0.1, 0.8, f'模型参数:', fontsize=14, fontweight='bold')
    plt.text(0.1, 0.7, f'E₀ = {trainer.model.E0:.0f} MPa', fontsize=12)
    plt.text(0.1, 0.6, f'fₜ = {trainer.model.ft:.2f} MPa', fontsize=12)
    plt.text(0.1, 0.5, f'A+ = {trainer.model.A_plus.item():.3f}', fontsize=12)
    plt.text(0.1, 0.4, f'B+ = {trainer.model.B_plus.item():.3f}', fontsize=12)
    plt.text(0.1, 0.3, f'xi = {trainer.model.xi.item():.3f}', fontsize=12)
    plt.text(0.1, 0.1, f'最终损失: {trainer.train_history["total_loss"][-1]:.6f}', fontsize=12)
    plt.xlim(0, 1)
    plt.ylim(0, 1)
    plt.title('模型参数')
    plt.axis('off')
    
    plt.tight_layout()
    plt.savefig(f'figures/{save_prefix}comprehensive_results.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("所有图像已保存到 figures/ 文件夹")


def main():
    """主函数"""
    print("="*60)
    print("         混凝土拉伸损伤曲线PINN模型 - 整合版本")
    print("="*60)
    
    # 创建目录
    create_directories()
    
    # 设置设备
    device = setup_device()
    
    # 配置参数
    config = {
        'E0': 30000.0,
        'ft': 3.0,
        'hidden_size': 64,
        'num_layers': 2,
        'learning_rate': 0.001,
        'weight_decay': 1e-5,
        'num_epochs': 1000,
        'batch_size': 1,
        'loss_weights': {
            'data': 1.0,
            'physics': 0.5,
            'damage': 0.3,
            'plastic': 0.2
        },
        'normalize': True,
        'remove_outliers': True,
        'smooth_data': True,
        'smooth_window': 5,
        'log_interval': 50
    }
    
    # 数据文件路径
    data_file = r"D:\column\ten3\tension.xlsx"
    
    try:
        # 数据处理
        processor = ConcreteDataProcessor(
            normalize=config['normalize'],
            scaler_type='minmax'
        )
        
        # 加载数据
        strain_data, stress_data = processor.load_data_from_excel(data_file)
        
        # 数据清洗
        strain_clean, stress_clean = processor.clean_and_smooth_data(
            strain_data, stress_data,
            remove_outliers=config['remove_outliers'],
            smooth=config['smooth_data'],
            smooth_window=config['smooth_window']
        )
        
        # 准备训练数据
        train_strain, train_stress = processor.prepare_training_data(
            strain_clean, stress_clean
        )
        
        # 创建数据加载器
        train_loader = processor.create_dataloaders(
            train_strain, train_stress,
            batch_size=config['batch_size']
        )
        
        print(f"训练数据: {len(train_strain)} 个点")
        
        # 创建模型
        model = ConcreteTensionPINN(
            E0=config['E0'],
            ft=config['ft'],
            hidden_size=config['hidden_size'],
            num_layers=config['num_layers'],
            device=device
        )
        
        print(f"模型参数总数: {sum(p.numel() for p in model.parameters())}")
        
        # 创建训练器
        trainer = PINNTrainer(
            model=model,
            device=device,
            learning_rate=config['learning_rate'],
            weight_decay=config['weight_decay']
        )
        
        # 训练模型
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_save_path = f"models/concrete_pinn_model_{timestamp}.pth"
        
        trainer.train(
            train_dataloader=train_loader,
            val_dataloader=None,
            num_epochs=config['num_epochs'],
            loss_weights=config['loss_weights'],
            save_path=model_save_path,
            log_interval=config['log_interval']
        )
        
        print(f"模型已保存: {model_save_path}")
        
        # 生成结果图像
        plot_results(processor, trainer, strain_clean, stress_clean, f"{timestamp}_")
        
        # 打印最终参数
        print("\n" + "="*50)
        print("训练完成！最终模型参数:")
        print(f"A+ = {model.A_plus.item():.4f}")
        print(f"B+ = {model.B_plus.item():.4f}")
        print(f"xi = {model.xi.item():.4f}")
        print(f"最终损失 = {trainer.train_history['total_loss'][-1]:.6f}")
        print("="*50)
        
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 