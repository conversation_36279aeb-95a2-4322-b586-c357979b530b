好的，遵照您的要求，这里是一份详细、具体、可执行的 To-Do List，旨在将我们讨论的思路转化为您的编码实践。

### **混凝土本构“查表法”VUMAT开发任务清单 (To-Do List)**

-----

#### **阶段一：离线数据生成 (在 Python 环境中完成)**

**目标：** 生成一个名为 `lookup_table_1d.dat` 的文本文件，它包含了材料在不同损伤程度下的完整一维滞回行为。

  * **[ ] 任务 1.1：创建新的 Python 脚本**

      * 创建一个新文件，例如 `generate_lookup_table.py`。

  * **[ ] 任务 1.2：加载 PINN 训练成果**

      * 在此脚本中，编写代码读取 `results/session_.../training/identified_parameters.json` 文件。
      * 将解析出的物理参数 (`A+`, `B+`, `xi+`, `A-`, `B-`, `xi-`) 和材料常数 (`E0`, `f_t`, `f_c`) 存储在变量中。

  * **[ ] 任务 1.3：复用物理计算逻辑**

      * 从 `predict_hysteresis.py` 中，复制或导入 `predict_response` 函数背后的物理计算逻辑。您需要的是那个根据应变增量、当前状态和物理参数来逐步计算应力、损伤和塑性应变的循环。

  * **[ ] 任务 1.4：定义查询表的网格参数**

      * 定义代表损伤历史的“应变幅值包络” (`r_max`) 的离散等级。
        ```python
        r_max_levels = np.linspace(0.0002, 0.004, 20)  # 示例：从0.02%到0.4%应变，共20个损伤等级
        ```
      * 定义每个滞回环上的应变采样点数或步长，确保曲线平滑。
        ```python
        points_per_segment = 200 # 示例：每个加载段采样200个点
        ```

  * **[ ] 任务 1.5：实现数据生成循环**

      * 编写一个主循环，遍历 `r_max_levels` 中的每一个 `r_max_level`。
      * 在循环内部：
        1.  构造一个加载路径，例如 `0 -> r_max_level -> -r_max_level -> 0`。
        2.  调用**任务 1.3** 中的物理计算逻辑，模拟该加载路径下的响应，得到一系列精细的 `(strain, stress)` 数据点。
        3.  将这些数据点与当前的 `r_max_level` 关联起来。

  * **[ ] 任务 1.6：写入文本文件**

      * 打开一个新文件 `lookup_table_1d.dat` 进行写入。
      * 将**任务 1.5** 中生成的所有数据点，按照 `r_max_level, strain, stress` 的格式，以空格或逗号分隔，逐行写入该文件。
      * **强烈建议**在文件头部添加注释行（以`#`开头），说明各列的含义。

-----

#### **阶段二：Abaqus 输入文件 (.inp) 设置**

**目标：** 配置 Abaqus 模型，使其能够调用您的 VUMAT 并正确管理历史状态。

  * **[ ] 任务 2.1：定义用户材料**

      * 在 `*Material` 部分，使用 `*User Material, Constants=N` 关键字，其中 `N` 是您需要传入 VUMAT 的材料常数个数（可以为1，传入`E0`）。

  * **[ ] 任务 2.2：定义状态变量**

      * 紧跟在 `*User Material` 下方，使用 `*Depvar` 关键字。
      * 为了实现我们的逻辑，并保证数值稳定性，**推荐定义2个状态变量**：`*Depvar, n=2`。
          * `statev(1)`: 存储应变幅值包络 `r_max`。
          * `statev(2)`: 存储总应变 `ε_total`。

-----

#### **阶段三：VUMAT 子程序编码 (在 Fortran 环境中完成)**

**目标：** 编写 `vumat.for` 文件，其核心功能是在内存中对离线生成的查询表进行高效插值。

  * **[ ] 任务 3.1：VUMAT 结构与变量声明**

      * 使用标准的 VUMAT 子程序接口 `SUBROUTINE VUMAT(nblock, ...)`。
      * 在声明部分，定义一个足够大的二维数组用于存储查询表数据，并使用 `SAVE` 属性或 `COMMON` 块使其在不同的调用之间保持不变。
        ```fortran
        DOUBLE PRECISION, ALLOCATABLE, SAVE :: LOOKUP_DATA(:,:)
        LOGICAL, SAVE :: INITIALIZED = .FALSE.
        ```
      * 声明所有需要的局部变量。

  * **[ ] 任务 3.2：实现初始化模块**

      * 在子程序开头，使用 `IF (.NOT. INITIALIZED)` 逻辑块。
      * 在此逻辑块中：
        1.  `ALLOCATE` `LOOKUP_DATA` 数组为预估的大小。
        2.  `OPEN` `lookup_table_1d.dat` 文件。
        3.  使用 `DO` 循环和 `READ` 语句，将文件内容逐行读入 `LOOKUP_DATA` 数组。
        4.  `CLOSE` 文件。
        5.  设置 `INITIALIZED = .TRUE.`。

  * **[ ] 任务 3.3：实现主计算逻辑**

      * 在 `DO K = 1, NBLOCK` 循环内部，针对每个积分点 `K`：
        1.  **获取旧状态**：
            ```fortran
            R_MAX_OLD = STATEV(K, 1)
            STRAIN_OLD = STATEV(K, 2)
            ```
        2.  **计算新应变**：
            ```fortran
            STRAIN_NEW = STRAIN_OLD + STRAININCREMENT(K, 1)
            ```
        3.  **实现二维插值函数** (这是核心)：
              * 编写一个独立的 Fortran `FUNCTION`，例如 `GET_STRESS_INTERP(target_strain, target_r_max)`。
              * 此函数在 `LOOKUP_DATA` 数组中执行我们之前讨论的二维线性插值逻辑。
              * 返回插值计算出的应力值。
        4.  **调用插值函数**：
            ```fortran
            STRESS_NEW_11 = GET_STRESS_INTERP(STRAIN_NEW, R_MAX_OLD)
            ```
        5.  **更新应力张量**：
            ```fortran
            STRESSNEW(K, 1) = STRESS_NEW_11
            ! 将其他分量清零
            STRESSNEW(K, 2) = 0.0D0
            ...
            ```
        6.  **更新状态变量**：
            ```fortran
            STATEV(K, 1) = MAX(R_MAX_OLD, ABS(STRAIN_NEW))
            STATEV(K, 2) = STRAIN_NEW
            ```

-----
好的，我们来聚焦于最终的验证环节。为了验证您的 VUMAT 子程序，最直接的方法就是进行一次单轴试验的数值模拟，并将结果与您的理论曲线进行对比。

以下是一份为该目的量身定制的、详细具体可行的 To-Do List，它将指导您编写一个完整的 Abaqus 输入文件 (`.inp`)。

### **Abaqus 输入文件 (.inp) 编写任务清单**

**目标：** 创建一个对单根柱单元进行单轴拉伸或压缩的模拟，以验证 VUMAT 子程序的正确性。

-----

  * **[ ] 任务 1：文件创建与作业标题**

      * 创建一个新的文本文件，并将其命名为 `verification_monotonic.inp`。
      * 在文件第一行添加标题，以说明文件用途。
        ```abaqus
        *HEADING
        Single Column Element Verification for Concrete VUMAT - Monotonic Loading
        ```

  * **[ ] 任务 2：创建部件与网格 (Part & Mesh)**

      * 定义一个名为 "COLUMN" 的部件。
      * 使用 `*NODE` 定义两个节点，代表柱的底部和顶部。例如，柱高为 1000mm。
        ```abaqus
        *PART, NAME=COLUMN
        *NODE
          1, 0., 0., 0.   ! 节点1 (底部)
          2, 0., 0., 1000. ! 节点2 (顶部)
        ```
      * 使用 `*ELEMENT` 定义一个连接这两个节点的单元。**T3D2** (三维2节点桁架单元) 是最理想的选择，因为它只承受轴向力，与您的一维本构假设完美匹配。
        ```abaqus
        *ELEMENT, TYPE=T3D2, ELSET=COLUMN_ELS
          1, 1, 2          ! 单元1, 连接节点1和2
        *END PART
        ```

  * **[ ] 任务 3：定义材料属性 (VUMAT)**

      * 定义一个名为 "CONCRETE\_VUMAT" 的材料。
      * 由于 VUMAT 用于 Abaqus/Explicit (显式动力学)，必须提供**密度**和**弹性**属性 (弹性用于计算稳定时间增量)。
        ```abaqus
        *MATERIAL, NAME=CONCRETE_VUMAT
        *DENSITY
          2.5E-9,          ! 混凝土密度 (吨/立方毫米)
        *ELASTIC
          30000.0, 0.3     ! 初始弹性模量E0 (MPa), 泊松比
        ```
      * 使用 `*USER MATERIAL` 和 `*DEPVAR` 关键字来指定您的子程序和状态变量。
        ```abaqus
        *USER MATERIAL, CONSTANTS=1
          30000.0          ! 传入VUMAT的常数 (props), 这里传入E0
        *DEPVAR, N=2
        ```

  * **[ ] 任务 4：定义截面并赋予属性**

      * 桁架单元需要定义截面面积。我们定义一个 `*TRUSS SECTION`。
      * 将此截面赋予给之前创建的名为 "COLUMN\_ELS" 的单元集。
        ```abaqus
        *TRUSS SECTION, ELSET=COLUMN_ELS, MATERIAL=CONCRETE_VUMAT
          10000.0,         ! 截面面积 (平方毫米), 例如 100mm x 100mm
        ```

  * **[ ] 任务 5：装配 (Assembly)**

      * 创建一个部件实例。
      * 为方便施加边界条件和荷载，创建分别代表底部和顶部节点的节点集。
        ```abaqus
        *ASSEMBLY, NAME=Assembly-1
        *INSTANCE, NAME=COLUMN-1, PART=COLUMN
        *NSET, NSET=N_BOTTOM, INSTANCE=COLUMN-1
          1,
        *NSET, NSET=N_TOP, INSTANCE=COLUMN-1
          2,
        *END ASSEMBLY
        ```

  * **[ ] 任务 6：定义分析步、边界条件和荷载**

      * 定义一个 `*DYNAMIC, EXPLICIT` 分析步。
      * 为了使加载平稳，避免动力冲击，强烈建议使用 `*AMPLITUDE` 定义一个斜坡函数。
        ```abaqus
        *AMPLITUDE, NAME=RAMP
          0.0, 0.0, 1.0, 1.0
        ```
      * 在分析步中，使用 `*BOUNDARY` 定义约束和位移荷载。
        ```abaqus
        *STEP
        *DYNAMIC, EXPLICIT
          , 1.0              ! 分析时间为 1.0 秒
        *BOUNDARY
        ! 底部固定约束: 约束节点集 N_BOTTOM 的所有平动自由度 (1, 2, 3)
          N_BOTTOM, 1, 3, 0.0
        !
        ! 顶部施加位移荷载: 请根据需要选择以下一种 (取消另一种的注释)
        ! --- 选项 A: 单轴拉伸 ---
          N_TOP, 3, 3, 5.0, AMP=RAMP ! 沿Z轴施加5mm的拉伸位移
        ! --- 选项 B: 单轴压缩 ---
        ! N_TOP, 3, 3, -5.0, AMP=RAMP ! 沿Z轴施加5mm的压缩位移
        ```

  * **[ ] 任务 7：定义输出请求**

      * 这是验证结果的关键一步。我们需要请求节点和单元的详细输出。
        ```abaqus
        *OUTPUT, FIELD, TIME INTERVAL=0.01
        *NODE OUTPUT
          U,
        *ELEMENT OUTPUT, ELSET=COLUMN_ELS
          S, E, SDV          ! S=应力, E=应变, SDV=状态变量
        *OUTPUT, HISTORY, TIME INTERVAL=0.01
        *NODE HISTORY, NSET=N_TOP
          U3, RF3            ! U3=Z向位移, RF3=Z向支反力
        ```

  * **[ ] 任务 8：结束与执行**

      * 添加 `*END STEP` 来结束分析步的定义。
      * **执行前检查**：
        1.  确保此 `.inp` 文件与您的 `vumat.for` 文件，以及 `lookup_table_1d.dat` 文件位于同一个工作目录中。
        2.  确认 VUMAT 代码中读取的文件名与 `lookup_table_1d.dat` 完全一致。
      * **提交作业**：
        ```bash
        abaqus job=verification_monotonic user=vumat.for interactive
        ```

  * **[ ] 任务 9：后处理与验证**

      * 作业完成后，打开 `.odb` 文件。
      * **绘制应力-应变曲线**：通过 `XY Data` 功能，绘制单元输出的轴向应力 `S11` vs. 轴向应变 `E11`。
      * **绘制荷载-位移曲线**：绘制顶部节点的反力 `RF3` vs. 位移 `U3`。
      * **对比结果**：将 Abaqus 输出的应力-应变曲线与您用 Python 脚本生成的理论曲线进行对比。两者应该高度吻合。
      * **检查状态变量**：绘制状态变量 `SDV1` (`r_max`) 和 `SDV2` (`ε_total`) 的时程曲线，确保它们按预期演化。