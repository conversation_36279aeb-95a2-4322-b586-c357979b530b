"""
数据处理模块
负责实验数据的加载、预处理和材料属性的初步估算
"""

import pandas as pd
import numpy as np
import torch
import matplotlib.pyplot as plt
import os
import font_config # 确保字体配置已导入

class DataProcessor:
    """
    数据处理器
    加载实验数据，进行预处理，并估算初始材料属性
    """

    def __init__(self, excel_path):
        self.excel_path = excel_path
        self.df = None
        self.sequence_length = 0

    def load_experimental_data(self):
        """
        从Excel文件加载实验数据 (strain, stress)
        """
        try:
            self.df = pd.read_excel(self.excel_path)
            if 'strain' not in self.df.columns or 'stress' not in self.df.columns:
                print("错误: Excel文件必须包含 'strain' 和 'stress' 两列。")
                return False
            # 确保数据是按应变排序的，对于循环加载，应变可能不是单调的，但通常按时间步顺序
            # self.df = self.df.sort_values(by='strain').reset_index(drop=True)
            print(f"成功加载数据: {self.excel_path}")
            return True
        except FileNotFoundError:
            print(f"错误: 文件 {self.excel_path} 不存在。")
            return False
        except Exception as e:
            print(f"加载Excel文件时发生错误: {e}")
            return False

    def validate_data_quality(self):
        """
        验证数据质量，例如检查是否有NaN值
        """
        if self.df is None:
            print("错误: 数据未加载。")
            return False
        if self.df.isnull().any().any():
            print("警告: 数据中包含NaN值，请检查。")
            self.df.dropna(inplace=True) # 简单处理：删除NaN行
        print("数据质量验证通过。")
        return True

    def prepare_training_data(self):
        """
        准备PINN训练所需的增量式数据
        将数据转换为PyTorch张量
        """
        if self.df is None:
            print("错误: 数据未加载，无法准备训练数据。")
            return None

        # 将DataFrame转换为PyTorch张量
        strain_total_exp = torch.tensor(self.df['strain'].values, dtype=torch.float32)
        stress_exp = torch.tensor(self.df['stress'].values, dtype=torch.float32)

        self.sequence_length = len(strain_total_exp)

        # 计算应变增量序列
        # prepend=torch.tensor([0.0]) 是为了使增量序列长度与总应变序列相同
        # 第一个增量是 epsilon_0 - 0 = epsilon_0
        strain_increment_input = torch.diff(strain_total_exp, prepend=torch.tensor([0.0]))
        # GRU输入需要 batch_first=True, 所以形状是 [batch_size, seq_len, input_size]
        # 这里 batch_size=1, input_size=1
        strain_increment_input = strain_increment_input.unsqueeze(0).unsqueeze(-1)

        # 同样，传递原始的应变增量张量给物理计算器 (不带batch维度)
        strain_increment = torch.diff(strain_total_exp, prepend=torch.tensor([0.0]))

        print("训练数据准备完成。")
        return {
            'strain_increment_input': strain_increment_input,  # 用于模型的GRU输入
            'strain_total_exp': strain_total_exp,              # 实验总应变
            'stress_exp': stress_exp,                          # 实验应力
            'strain_increment': strain_increment,              # 物理计算器使用的应变增量
            'sequence_length': self.sequence_length
        }

    def get_material_properties(self):
        """
        根据实验数据估算初始弹性模量 E0, 单轴抗拉强度 f_t, 和单轴抗压强度 f_c
        """
        if self.df is None:
            print("错误: 数据未加载，无法估算材料属性。")
            return {'E0_estimated': 0.0, 'f_t_estimated': 0.0, 'f_c_estimated': 0.0}

        strain = self.df['strain'].values
        stress = self.df['stress'].values

        # 1. 估算初始弹性模量 E0
        # 通常取加载初始阶段的斜率
        initial_points = 5 # 取前5个点进行线性拟合
        if len(strain) < initial_points:
            initial_points = len(strain)

        # 尝试使用正应变部分估算E0
        initial_strain_positive = strain[strain >= 0]
        initial_stress_positive = stress[strain >= 0]

        if len(initial_strain_positive) > 1:
            try:
                # 找到最接近0的应变点（避免精确0导致除以0）
                zero_idx = np.argmin(np.abs(initial_strain_positive))
                if zero_idx + initial_points <= len(initial_strain_positive):
                    E0_estimated = np.polyfit(initial_strain_positive[zero_idx:zero_idx + initial_points],
                                              initial_stress_positive[zero_idx:zero_idx + initial_points], 1)[0]
                else: # 数据点不够，用剩余的点
                    E0_estimated = np.polyfit(initial_strain_positive, initial_stress_positive, 1)[0]
            except Exception as e:
                print(f"估算E0时发生错误: {e}, 使用默认值。")
                E0_estimated = 30000.0 # 默认值
        else:
            E0_estimated = 30000.0 # 默认值
        E0_estimated = max(1000.0, E0_estimated) # 确保E0不为0或负

        # 2. 估算单轴抗拉强度 f_t (峰值拉应力)
        f_t_estimated = stress.max()
        if f_t_estimated < 0.1: # 如果没有明显的正应力，给一个默认值
            f_t_estimated = 3.0 # 经验值，例如 3 MPa

        # 3. 估算单轴抗压强度 f_c (峰值压应力，取绝对值)
        # 寻找负应力的最小值（绝对值最大）
        f_c_estimated = abs(stress.min())
        if f_c_estimated < 0.1: # 如果没有明显的负应力，给一个默认值
            f_c_estimated = 30.0 # 经验值，例如 30 MPa

        print(f"估算初始弹性模量 E0: {E0_estimated:.2f} MPa")
        print(f"估算单轴抗拉强度 f_t: {f_t_estimated:.2f} MPa")
        print(f"估算单轴抗压强度 f_c: {f_c_estimated:.2f} MPa")

        return {
            'E0_estimated': E0_estimated,
            'f_t_estimated': f_t_estimated,
            'f_c_estimated': f_c_estimated
        }

    def plot_experimental_data(self, save_path="experimental_data.png"):
        """
        绘制实验数据 (应力-应变曲线)
        """
        if self.df is None:
            print("错误: 数据未加载，无法绘图。")
            return

        plt.figure(figsize=(10, 6))
        plt.plot(self.df['strain'] * 1000, self.df['stress'], 'o-', markersize=4, label='实验数据')
        plt.xlabel('应变 (千分比)', fontsize=12)
        plt.ylabel('应力 (MPa)', fontsize=12)
        plt.title('实验应力-应变曲线', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.legend(fontsize=10)
        plt.tight_layout()
        plt.savefig(save_path, dpi=300)
        plt.close()
        print(f"实验数据图已保存至: {save_path}")

if __name__ == '__main__':
    # 示例用法
    # 请确保 tension.xlsx 文件存在，或者替换为你的循环加载数据文件
    processor = DataProcessor("tension.xlsx") 
    if processor.load_experimental_data() and processor.validate_data_quality():
        data = processor.prepare_training_data()
        properties = processor.get_material_properties()
        processor.plot_experimental_data(save_path="results/sample_experimental_data.png")

        print("\n准备好的训练数据张量形状:")
        print(f"  strain_increment_input: {data['strain_increment_input'].shape}")
        print(f"  strain_total_exp: {data['strain_total_exp'].shape}")
        print(f"  stress_exp: {data['stress_exp'].shape}")
        print(f"  strain_increment: {data['strain_increment'].shape}")
        print(f"  sequence_length: {data['sequence_length']}")
