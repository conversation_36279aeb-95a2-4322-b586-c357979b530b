5. 物理约束实现
5.1 控制方程残差

平衡方程残差：‖∇·σ + F‖²₂
本构关系残差：‖σ - C(θ):ε‖²₂
应变协调残差：‖ε - ½(∇u + (∇u)ᵀ)‖²₂
5.2 边界条件约束

Dirichlet边界：‖u_pred - u_true‖²_ΓD
Neumann边界：‖σ·n - t̄‖²_ΓN
5.3 损伤场约束

光滑性约束：λ₁‖∇θ‖₁
区间约束：λ₂[‖max(-θ,0)‖² + ‖max(θ-1,0)‖²]
6. 多任务损失函数
复合损失构成：
总损失 = α‖u_pred - u_FEM‖²（数据项） + β(平衡方程+本构关系残差)（物理项） + γ(损伤正则项)

动态权重方案：

α（数据项权重）：基于测量数据密度调整，每epoch更新
β（物理项权重）：按相对残差量级反比调整，每迭代步更新
γ（损伤项权重）：采用梯度衰减机制，分段常数更新
7. 训练策略
7.1 两阶段优化流程

预训练阶段（0-200 epoch）：

仅使用数据驱动损失（β=0）
学习率从1e-3余弦衰减到1e-4
批量固定256个采样点
物理增强阶段（201-1000 epoch）：

动态聚焦损伤区域采样
选择性残差反向传播
自适应调整β/γ权重
7.2 课程学习设置

训练阶段	损伤模式	物理权重β	噪声水平
1-200	单点局部损伤	0.1	30dB
201-500	多区域复杂损伤	0.5	20dB
501-1000	全域随机损伤	1.0	10dB
8. 结果评估体系
8.1 定量指标：

位移平均绝对误差：Σ|u_pred - u_true| / N
损伤交并比（IoU）：(预测∩真实)/(预测∪真实)
应力重构信噪比：20log(‖σ_true‖/‖σ_err‖)
8.2 可视化方案：

三维场对比：通过XY/XZ/YZ截面显示预测/真实位移场对比
损伤态势图：
设置损伤阈值θ=0.2
进行空间聚类分析
生成三维损伤概率云图
8.3 诊断能力测试：

定位精度：TP/(TP+FP+FN)（微小损伤检测能力）
跨工况验证：不同边界条件/载荷组合的泛化性测试
噪声鲁棒性：5%-20%高斯噪声下的性能衰减率