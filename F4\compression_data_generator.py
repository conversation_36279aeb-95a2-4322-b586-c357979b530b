import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class CompressionDamageDataGenerator:
    """混凝土压缩损伤实验数据生成器"""
    
    def __init__(self):
        # 材料参数
        self.E0 = 30000.0  # 初始弹性模量 (MPa)
        self.alpha = 0.12  # 压缩损伤能释放率系数
        
        # 压缩损伤参数范围
        self.param_ranges = {
            'r0_minus': (20.0, 50.0),    # 初始阈值 (MPa)
            'A_minus': (0.8, 1.2),       # 形状参数1
            'B_minus': (0.1, 0.2),       # 形状参数2
            'mu_minus': (5e10, 7e10),    # 粘性系数
            'a_minus': (4.0, 5.0)        # 非线性指数
        }
        
    def calculate_Y_minus(self, stress):
        """计算压缩损伤能释放率 Y- (式15)"""
        if stress < 0:  # 压缩应力
            # 简化为单轴情况: Y- = α*I1 + sqrt(3*J2)
            I1 = abs(stress)  # 第一不变量
            J2 = (stress**2) / 3  # 偏应力第二不变量
            return self.alpha * I1 + np.sqrt(3 * J2)
        return 0
    
    def update_threshold(self, Y_minus, r_minus_current, is_dynamic=False, mu_minus=6e10, a_minus=4.5, dt=1e-6):
        """更新损伤阈值 r-"""
        if is_dynamic:
            # 动力阈值更新 (式17)
            if Y_minus > r_minus_current:
                r_dot = mu_minus * ((Y_minus / r_minus_current) - 1)**a_minus
                return r_minus_current + r_dot * dt
        else:
            # 准静态阈值更新
            return max(r_minus_current, Y_minus)
        return r_minus_current
    
    def calculate_damage_variable(self, r_minus, r0_minus, A_minus, B_minus):
        """计算压缩损伤变量 d- (式18-19)"""
        if r_minus <= r0_minus:
            return 0.0
        
        ratio = r0_minus / r_minus
        term1 = ratio * (1 - A_minus) + A_minus
        term2 = np.exp(B_minus * (1 - r_minus / r0_minus))
        
        d_minus = 1 - term1 * term2
        return max(0.0, min(0.99, d_minus))  # 限制在[0, 0.99]
    
    def generate_single_test(self, material_type, test_id, max_strain=-0.005, num_cycles=3):
        """生成单个压缩试验数据"""
        # 随机生成材料参数
        params = {}
        for key, (min_val, max_val) in self.param_ranges.items():
            params[key] = np.random.uniform(min_val, max_val)
        
        # 生成应变历程（循环加载）
        strain_points = []
        for cycle in range(num_cycles):
            # 加载段
            loading_strain = np.linspace(0, max_strain * (cycle + 1) / num_cycles, 50)
            strain_points.extend(loading_strain[1:])  # 避免重复0点
            
            # 卸载段
            unloading_strain = np.linspace(max_strain * (cycle + 1) / num_cycles, 0, 30)
            strain_points.extend(unloading_strain[1:])  # 避免重复峰值点
        
        # 最终加载到破坏
        final_loading = np.linspace(0, max_strain, 80)
        strain_points.extend(final_loading[1:])
        
        strain_history = np.array(strain_points)
        
        # 计算应力和损伤历程
        stress_history = []
        damage_history = []
        r_minus = params['r0_minus']
        d_minus = 0.0
        max_strain_reached = 0.0  # 记录达到的最大应变幅值
        
        for i, strain in enumerate(strain_history):
            # 计算有效应力
            stress_eff = self.E0 * strain
            
            # 判断是否为加载段（应变幅值增加）
            current_strain_abs = abs(strain)
            is_loading = current_strain_abs > max_strain_reached
            
            if is_loading:
                # 加载段：更新损伤
                max_strain_reached = current_strain_abs
                
                # 计算损伤能释放率
                Y_minus = self.calculate_Y_minus(stress_eff)
                
                # 更新损伤阈值
                r_minus = self.update_threshold(Y_minus, r_minus, 
                                              is_dynamic=False,  # 准静态
                                              mu_minus=params['mu_minus'],
                                              a_minus=params['a_minus'])
                
                # 计算损伤变量
                d_minus = self.calculate_damage_variable(r_minus, params['r0_minus'],
                                                       params['A_minus'], params['B_minus'])
            # 卸载段：损伤不演化，保持当前损伤值
            
            # 计算总应力（考虑损伤的刚度退化）
            stress_total = (1 - d_minus) * stress_eff
            
            stress_history.append(stress_total)
            damage_history.append(d_minus)
        
        # 添加噪声
        noise_level = 0.02
        stress_noise = np.random.normal(0, noise_level * np.std(stress_history), len(stress_history))
        stress_history = np.array(stress_history) + stress_noise
        
        return {
            'material_type': material_type,
            'test_id': test_id,
            'strain': strain_history,
            'stress': stress_history,
            'damage': np.array(damage_history),
            'parameters': params
        }
    
    def generate_dataset(self, num_materials=4, tests_per_material=3):
        """生成完整的压缩实验数据集"""
        materials = ['C30', 'C40', 'C50', 'C60']
        all_data = []
        
        for i, material in enumerate(materials[:num_materials]):
            for j in range(tests_per_material):
                test_id = f"{material}_T{j+1:02d}"
                # 不同强度等级的最大应变略有不同
                max_strain = -0.003 - 0.001 * i
                
                test_data = self.generate_single_test(material, test_id, max_strain)
                all_data.append(test_data)
        
        return all_data
    
    def create_dataframe(self, dataset):
        """将数据转换为DataFrame格式"""
        rows = []
        
        for test in dataset:
            for i in range(len(test['strain'])):
                row = {
                    'Material': test['material_type'],
                    'Test_ID': test['test_id'],
                    'Point': i + 1,
                    'Strain': test['strain'][i],
                    'Stress_MPa': test['stress'][i],
                    'Damage_Variable': test['damage'][i],
                    'r0_minus': test['parameters']['r0_minus'],
                    'A_minus': test['parameters']['A_minus'],
                    'B_minus': test['parameters']['B_minus'],
                    'mu_minus': test['parameters']['mu_minus'],
                    'a_minus': test['parameters']['a_minus']
                }
                rows.append(row)
        
        return pd.DataFrame(rows)
    
    def create_visualizations(self, dataset, output_dir):
        """创建可视化图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('混凝土压缩损伤实验数据分析', fontsize=16, fontweight='bold')
        
        # 1. 应力-应变曲线
        ax1 = axes[0, 0]
        colors = ['red', 'blue', 'green', 'orange']
        for i, test in enumerate(dataset[:4]):  # 显示前4个试验
            ax1.plot(test['strain'], test['stress'], 
                    color=colors[i % len(colors)], 
                    label=f"{test['material_type']} - {test['test_id']}",
                    linewidth=2)
        ax1.set_xlabel('应变')
        ax1.set_ylabel('应力 (MPa)')
        ax1.set_title('压缩应力-应变曲线')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 损伤演化曲线
        ax2 = axes[0, 1]
        for i, test in enumerate(dataset[:4]):
            ax2.plot(abs(test['strain']), test['damage'], 
                    color=colors[i % len(colors)], 
                    label=f"{test['material_type']}",
                    linewidth=2)
        ax2.set_xlabel('应变幅值')
        ax2.set_ylabel('损伤变量 d-')
        ax2.set_title('压缩损伤演化曲线')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 参数分布
        ax3 = axes[1, 0]
        materials = [test['material_type'] for test in dataset]
        r0_values = [test['parameters']['r0_minus'] for test in dataset]
        
        material_types = list(set(materials))
        r0_by_material = []
        for mat in material_types:
            r0_by_material.append([r0_values[i] for i, m in enumerate(materials) if m == mat])
        
        ax3.boxplot(r0_by_material, labels=material_types)
        ax3.set_ylabel('初始阈值 r0- (MPa)')
        ax3.set_title('不同材料的初始阈值分布')
        ax3.grid(True, alpha=0.3)
        
        # 4. 损伤-应力关系
        ax4 = axes[1, 1]
        for i, test in enumerate(dataset[:4]):
            ax4.scatter(abs(test['stress']), test['damage'], 
                       color=colors[i % len(colors)], 
                       label=f"{test['material_type']}",
                       alpha=0.6, s=20)
        ax4.set_xlabel('应力幅值 (MPa)')
        ax4.set_ylabel('损伤变量 d-')
        ax4.set_title('损伤变量与应力关系')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'compression_analysis.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_html_report(self, df, dataset, output_dir):
        """生成HTML报告"""
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>混凝土压缩损伤实验数据报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }}
        .summary-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        .summary-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .summary-card h3 {{
            margin: 0 0 10px 0;
            font-size: 2em;
        }}
        .formula-box {{
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }}
        .parameter-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        .parameter-table th, .parameter-table td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        .parameter-table th {{
            background-color: #3498db;
            color: white;
        }}
        .parameter-table tr:nth-child(even) {{
            background-color: #f2f2f2;
        }}
        .image-container {{
            text-align: center;
            margin: 20px 0;
        }}
        .image-container img {{
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }}
        .download-section {{
            background-color: #e8f5e8;
            border: 2px solid #27ae60;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }}
        .download-button {{
            display: inline-block;
            background-color: #27ae60;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            font-weight: bold;
        }}
        .download-button:hover {{
            background-color: #219a52;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>混凝土压缩损伤实验数据分析报告</h1>
        
        <div class="summary-grid">
            <div class="summary-card">
                <h3>{len(df)}</h3>
                <p>数据点总数</p>
            </div>
            <div class="summary-card">
                <h3>{len(dataset)}</h3>
                <p>试验组数</p>
            </div>
            <div class="summary-card">
                <h3>{len(df['Material'].unique())}</h3>
                <p>材料类型</p>
            </div>
            <div class="summary-card">
                <h3>{datetime.now().strftime('%Y-%m-%d')}</h3>
                <p>生成日期</p>
            </div>
        </div>
        
        <h2>1. 压缩损伤理论公式</h2>
        
        <h3>1.1 压缩损伤能释放率</h3>
        <div class="formula-box">
            Y⁻ = α·I₁ + √(3·J₂)  (式15)<br>
            其中：α = 0.12，I₁为第一不变量，J₂为偏应力第二不变量
        </div>
        
        <h3>1.2 损伤阈值演化</h3>
        <div class="formula-box">
            ṙ⁻ = μ⁻ · ⟨(Y⁻/r⁻) - 1⟩^(a⁻)  (式17)<br>
            准静态：r⁻_new = max(r⁻_old, Y⁻)
        </div>
        
        <h3>1.3 损伤变量计算</h3>
        <div class="formula-box">
            d⁻ = 1 - [(r₀⁻/r⁻)(1-A⁻) + A⁻] · exp[B⁻(1-r⁻/r₀⁻)]  (式18-19)
        </div>
        
        <h2>2. 模型参数范围</h2>
        <table class="parameter-table">
            <tr>
                <th>参数</th>
                <th>符号</th>
                <th>物理意义</th>
                <th>取值范围</th>
            </tr>
            <tr>
                <td>初始阈值</td>
                <td>r₀⁻</td>
                <td>线弹性极限强度</td>
                <td>20.0 - 50.0 MPa</td>
            </tr>
            <tr>
                <td>形状参数1</td>
                <td>A⁻</td>
                <td>初始损伤演化速率</td>
                <td>0.8 - 1.2</td>
            </tr>
            <tr>
                <td>形状参数2</td>
                <td>B⁻</td>
                <td>损伤饱和趋势</td>
                <td>0.1 - 0.2</td>
            </tr>
            <tr>
                <td>粘性系数</td>
                <td>μ⁻</td>
                <td>应变率相关阈值更新速率</td>
                <td>5×10¹⁰ - 7×10¹⁰</td>
            </tr>
            <tr>
                <td>非线性指数</td>
                <td>a⁻</td>
                <td>阈值演化的非线性程度</td>
                <td>4.0 - 5.0</td>
            </tr>
        </table>
        
        <h2>3. 实验数据可视化</h2>
        <div class="image-container">
            <img src="compression_analysis.png" alt="压缩损伤分析图表">
        </div>
        
        <h2>4. 数据统计信息</h2>
        <table class="parameter-table">
            <tr>
                <th>统计项</th>
                <th>应变范围</th>
                <th>应力范围 (MPa)</th>
                <th>损伤变量范围</th>
            </tr>
            <tr>
                <td>最小值</td>
                <td>{df['Strain'].min():.6f}</td>
                <td>{df['Stress_MPa'].min():.2f}</td>
                <td>{df['Damage_Variable'].min():.4f}</td>
            </tr>
            <tr>
                <td>最大值</td>
                <td>{df['Strain'].max():.6f}</td>
                <td>{df['Stress_MPa'].max():.2f}</td>
                <td>{df['Damage_Variable'].max():.4f}</td>
            </tr>
            <tr>
                <td>平均值</td>
                <td>{df['Strain'].mean():.6f}</td>
                <td>{df['Stress_MPa'].mean():.2f}</td>
                <td>{df['Damage_Variable'].mean():.4f}</td>
            </tr>
        </table>
        
        <h2>5. 数据下载</h2>
        <div class="download-section">
            <h3>📊 实验数据文件</h3>
            <p>以下文件包含完整的压缩损伤实验数据，可用于进一步分析和建模：</p>
            <a href="compression_experimental_data.xlsx" class="download-button">📥 下载 Excel 文件</a>
            <a href="compression_experimental_data.csv" class="download-button">📥 下载 CSV 文件</a>
            <br><br>
            <p><strong>文件路径：</strong></p>
            <p>📁 Excel: <code>d:/column/F4/compression_results/compression_experimental_data.xlsx</code></p>
            <p>📁 CSV: <code>d:/column/F4/compression_results/compression_experimental_data.csv</code></p>
        </div>
        
        <h2>6. 应用建议</h2>
        <ul>
            <li><strong>参数标定：</strong>使用遗传算法或粒子群优化算法拟合实验数据</li>
            <li><strong>有限元分析：</strong>将损伤本构关系嵌入ABAQUS、ANSYS等软件</li>
            <li><strong>结构分析：</strong>用于混凝土结构的非线性分析和损伤评估</li>
            <li><strong>疲劳分析：</strong>结合循环加载数据进行疲劳损伤预测</li>
        </ul>
        
        <footer style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #7f8c8d;">
            <p>报告生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
            <p>基于混凝土弹塑性损伤本构模型理论</p>
        </footer>
    </div>
</body>
</html>
        """
        
        with open(os.path.join(output_dir, 'compression_damage_report.html'), 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def run_generation(self):
        """运行完整的数据生成流程"""
        print("开始生成混凝土压缩损伤实验数据...")
        
        # 创建输出目录
        output_dir = 'd:/column/F4/compression_results'
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成数据集
        dataset = self.generate_dataset(num_materials=4, tests_per_material=3)
        print(f"生成了 {len(dataset)} 组试验数据")
        
        # 转换为DataFrame
        df = self.create_dataframe(dataset)
        print(f"总共 {len(df)} 个数据点")
        
        # 保存数据文件
        excel_path = os.path.join(output_dir, 'compression_experimental_data.xlsx')
        csv_path = os.path.join(output_dir, 'compression_experimental_data.csv')
        
        df.to_excel(excel_path, index=False)
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        
        # 创建可视化
        self.create_visualizations(dataset, output_dir)
        
        # 生成HTML报告
        self.generate_html_report(df, dataset, output_dir)
        
        print(f"\n数据生成完成！")
        print(f"Excel文件路径: {excel_path}")
        print(f"CSV文件路径: {csv_path}")
        print(f"HTML报告路径: {os.path.join(output_dir, 'compression_damage_report.html')}")
        print(f"分析图表路径: {os.path.join(output_dir, 'compression_analysis.png')}")
        
        # 输出统计信息
        print(f"\n数据统计:")
        print(f"- 数据点总数: {len(df)}")
        print(f"- 材料类型: {len(df['Material'].unique())} 种")
        print(f"- 试验组数: {len(dataset)} 组")
        print(f"- 应变范围: {df['Strain'].min():.6f} 到 {df['Strain'].max():.6f}")
        print(f"- 应力范围: {df['Stress_MPa'].min():.2f} 到 {df['Stress_MPa'].max():.2f} MPa")
        print(f"- 损伤变量范围: {df['Damage_Variable'].min():.4f} 到 {df['Damage_Variable'].max():.4f}")
        
        return excel_path, csv_path

if __name__ == "__main__":
    generator = CompressionDamageDataGenerator()
    excel_path, csv_path = generator.run_generation()