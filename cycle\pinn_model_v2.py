"""
基于增量式输入的混凝土损伤参数识别PINN模型 - 改进版
使用物理引导的塑性应变建模
支持受拉和受压状态的统一建模
"""

import torch
import torch.nn as nn
import numpy as np

class DamagePINNV2(nn.Module):
    """
    混凝土损伤PINN模型 - 改进版
    基于GRU架构，输入应变增量序列，输出应力、损伤、塑性应变增量系数
    """
    
    def __init__(self, input_size=1, hidden_size=64, num_layers=2, output_size=3):
        super(DamagePINNV2, self).__init__()
        
        # GRU层 - 用于处理增量式序列输入
        self.gru = nn.GRU(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=0.1 if num_layers > 1 else 0
        )
        
        # 全连接层 - 输出三个物理量
        # output_size 仍然为3: [名义应力, 损伤通用预测, 塑性应变系数通用预测]
        self.fc_layers = nn.Sequential(
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_size, output_size)
        )
        
        # 激活函数确保物理约束
        self.sigmoid = nn.Sigmoid()  # 用于损伤变量 [0,1]
        
    def forward(self, strain_increment_seq):
        """
        前向传播
        
        Args:
            strain_increment_seq: 应变增量序列 [batch_size, seq_len, 1]
            
        Returns:
            sigma_hat_seq: 预测的名义应力序列 [batch_size, seq_len]
            d_hat_seq: 预测的通用损伤变量序列 [batch_size, seq_len] (需要通过物理损失在拉压区拟合d+和d-)
            xi_hat_seq: 预测的通用塑性应变系数序列 [batch_size, seq_len] (需要通过物理损失在拉压区拟合xi+和xi-)
        """
        batch_size, seq_len, _ = strain_increment_seq.shape
        
        # GRU处理序列
        gru_out, _ = self.gru(strain_increment_seq)  # [batch_size, seq_len, hidden_size]
        
        # 全连接层输出
        output = self.fc_layers(gru_out)  # [batch_size, seq_len, 3]
        
        # 分离三个输出并应用物理约束
        sigma_hat_seq = output[:, :, 0]  # 名义应力可以为负
        d_hat_seq = self.sigmoid(output[:, :, 1])  # 通用损伤预测 [0,1]
        
        # 输出塑性应变通用系数xi_hat，使用sigmoid确保在合理范围内，并缩放到小数值
        xi_hat_seq = self.sigmoid(output[:, :, 2]) * 0.1 # xi_hat在[0, 0.1]范围内
        
        # 注意: ep_seq (累积塑性应变) 将在 PhysicsCalculatorV2 中根据 xi_hat_seq, 
        # strain_increment 和拉压状态进行计算。这里只输出系数。
        
        return sigma_hat_seq, d_hat_seq, xi_hat_seq


class PhysicsCalculatorV2:
    """
    物理约束计算器 - 改进版
    支持受拉和受压状态下的统一损伤和塑性演化
    """
    
    def __init__(self, E0=30000.0, f_t=3.0, f_c=30.0): # 添加 f_c
        self.E0 = E0        # 初始弹性模量
        self.f_t = f_t      # 单轴抗拉强度
        self.f_c = f_c      # 单轴抗压强度 (取绝对值，但公式中使用其正值)
        # 初始压缩损伤阈值 r_0_minus。参考文献中提及 (1-alpha)*f_c
        # 为简化，这里直接使用 f_c，如果需要，可添加一个 alpha 参数
        self.r0_minus = f_c 
    
    def calculate_physics_constraints(self, strain_increment_seq, 
                                      A_plus, B_plus, xi_plus, 
                                      A_minus, B_minus, xi_minus): # 接收所有参数
        """
        按增量步计算物理约束目标 (d+, d-, ep_phy)
        
        Args:
            strain_increment_seq: 应变增量序列 [seq_len]
            A_plus, B_plus: 受拉损伤参数
            xi_plus: 受拉塑性应变系数
            A_minus, B_minus: 受压损伤参数
            xi_minus: 受压塑性应变系数
            
        Returns:
            d_plus_phy_seq: 物理受拉损伤序列
            d_minus_phy_seq: 物理受压损伤序列
            ep_phy_seq: 物理塑性应变序列
        """
        # 初始化状态变量
        epsilon_total = torch.tensor(0.0, device=strain_increment_seq.device) # 累积总应变
        ep_phy = torch.tensor(0.0, device=strain_increment_seq.device)        # 累积物理塑性应变
        
        d_plus_phy = torch.tensor(0.0, device=strain_increment_seq.device)     # 累积物理受拉损伤
        d_minus_phy = torch.tensor(0.0, device=strain_increment_seq.device)    # 累积物理受压损伤
        
        r_max_plus_phy = torch.tensor(self.f_t, device=strain_increment_seq.device) # 历史最大拉伸损伤阈值
        r_max_minus_phy = torch.tensor(self.r0_minus, device=strain_increment_seq.device) # 历史最大压缩损伤阈值

        # 用于存储每一步计算结果的列表
        d_plus_phy_seq = []
        d_minus_phy_seq = []
        ep_phy_seq = []
        
        # 按增量步进行循环积分
        for delta_epsilon in strain_increment_seq:
            # 1. 更新总应变状态
            epsilon_total += delta_epsilon
            
            # 2. 计算当前有效应变 (弹性应变)
            # 这里的有效应变是基于总应变和累积塑性应变
            current_elastic_strain = epsilon_total - ep_phy
            
            # 3. 损伤演化逻辑 (受拉和受压独立判断)
            # 受拉损伤演化
            if current_elastic_strain > 1e-7: # 应变大于0，处于拉伸状态 (增加一个小量避免浮点误差)
                Y_plus_current = self.E0 * current_elastic_strain
                if Y_plus_current > r_max_plus_phy: # 突破历史最大拉伸损伤阈值
                    r_max_plus_phy = Y_plus_current
                    # 应用受拉损伤演化公式 (公式中的 ft 是 r0_plus)
                    term1_plus = self.f_t / r_max_plus_phy * (1 - A_plus)
                    term2_plus = A_plus * torch.exp(B_plus * (1 - r_max_plus_phy / self.f_t))
                    d_plus_phy = 1 - (term1_plus + term2_plus)
                    d_plus_phy = torch.clamp(d_plus_phy, 0.0, 1.0)
                # 否则 d_plus_phy 保持不变 (卸载或未达到新阈值)
            # 受压损伤演化
            elif current_elastic_strain < -1e-7: # 应变小于0，处于压缩状态
                # 压应力为负，驱动力取绝对值
                Y_minus_current = self.E0 * abs(current_elastic_strain) 
                if Y_minus_current > r_max_minus_phy: # 突破历史最大压缩损伤阈值
                    r_max_minus_phy = Y_minus_current
                    # 应用受压损伤演化公式 (公式中的 fc 是 r0_minus)
                    term1_minus = self.f_c / r_max_minus_phy * (1 - A_minus) # 使用 f_c
                    term2_minus = A_minus * torch.exp(B_minus * (1 - r_max_minus_phy / self.f_c)) # 使用 f_c
                    d_minus_phy = 1 - (term1_minus + term2_minus)
                    d_minus_phy = torch.clamp(d_minus_phy, 0.0, 1.0)
                # 否则 d_minus_phy 保持不变

            # 4. 塑性应变演化
            # 塑性应变增量取决于应变增量的方向和对应的塑性系数
            delta_ep = torch.tensor(0.0, device=strain_increment_seq.device)
            if delta_epsilon > 0: # 拉伸加载或卸载
                # 如果当前是拉伸有效应变，且在加载
                # 这里的塑性应变系数 xi_plus_effective 可以考虑损伤影响，简化后直接用 xi_plus
                delta_ep = xi_plus * delta_epsilon # xi_plus > 0, delta_epsilon > 0
            elif delta_epsilon < 0: # 压缩加载或卸载
                # 如果当前是压缩有效应变，且在加载
                # 塑性应变系数 xi_minus 应该定义为正值，因为 delta_epsilon 本身为负
                delta_ep = xi_minus * delta_epsilon # xi_minus > 0, delta_epsilon < 0, 导致 delta_ep < 0
            
            ep_phy += delta_ep # 累积塑性应变

            # 5. 记录当前步的状态
            d_plus_phy_seq.append(d_plus_phy.clone())
            d_minus_phy_seq.append(d_minus_phy.clone())
            ep_phy_seq.append(ep_phy.clone())
        
        return torch.stack(d_plus_phy_seq), torch.stack(d_minus_phy_seq), torch.stack(ep_phy_seq)
    
    def calculate_stress_from_state(self, strain_total_seq, d_seq, ep_seq, current_elastic_strain_dir):
        """
        根据本构关系计算应力。
        这里的 d_seq 是神经网络预测的通用损伤，需要根据当前弹性应变方向来应用。
        """
        # 根据当前弹性应变方向（由 `current_elastic_strain_dir` 指示）
        # 选择使用 d_plus 还是 d_minus，但模型预测的是一个通用的 d_hat_seq
        # 所以这里的 d_seq 就是 d_hat_seq。
        # stress = (1 - d_seq) * self.E0 * (strain_total_seq - ep_seq)
        # 这个函数可能不再直接用于损失计算中的 L_stress，因为L_stress直接用预测值
        # 而是用于预测阶段的可视化，此时 d_seq 应该是模型预测的 d_hat_seq。
        
        # L_stress 的计算在 LossCalculator 中直接使用 (1-d_hat) * E0 * (epsilon_hat - ep_hat)
        # 所以这里的函数可能主要用于预测时的辅助计算或验证
        return (1 - d_seq) * self.E0 * (strain_total_seq - ep_seq)


class LossCalculator:
    """
    损失函数计算器
    实现框架中定义的四部分损失
    现在支持区分受拉和受压损伤损失
    """
    
    def __init__(self, lambda_data=1.0, lambda_stress=1.0, 
                 lambda_damage_plus=0.8, lambda_damage_minus=0.8, # 区分拉压损伤权重
                 lambda_plastic=0.5):
        self.lambda_data = lambda_data
        self.lambda_stress = lambda_stress
        self.lambda_damage_plus = lambda_damage_plus
        self.lambda_damage_minus = lambda_damage_minus
        self.lambda_plastic = lambda_plastic
    
    def calculate_total_loss(self, sigma_hat_seq, d_hat_seq, ep_hat_seq, 
                           stress_exp_seq, strain_total_exp_seq, # 传入实验总应变用于判断拉压区域
                           d_plus_phy_seq, d_minus_phy_seq, ep_phy_seq, E0):
        """
        计算总损失
        """
        # L_data: 数据拟合损失
        loss_data = torch.mean((sigma_hat_seq - stress_exp_seq)**2)
        
        # L_stress: 本构自洽损失
        # 这里的 d_hat_seq 是模型预测的通用损伤变量
        stress_physics = (1 - d_hat_seq) * E0 * (strain_total_exp_seq - ep_hat_seq)
        loss_stress = torch.mean((sigma_hat_seq - stress_physics)**2)
        
        # L_damage: 损伤物理损失 - 区分受拉和受压部分
        # 创建掩码来区分拉伸和压缩区域
        # 根据实验应变的正负来判断当前点是处于拉伸还是压缩
        # 注意：这里也可以根据 stress_exp_seq 的正负来判断，具体取决于你的物理定义。
        # 假设：应变大于0为拉伸区，应变小于0为压缩区
        tensile_mask = (strain_total_exp_seq >= 0).float()
        compressive_mask = (strain_total_exp_seq < 0).float()

        # 拉伸损伤损失：在拉伸区，模型预测的通用损伤 d_hat_seq 应接近物理拉伸损伤 d_plus_phy_seq
        loss_damage_plus = torch.mean((d_hat_seq - d_plus_phy_seq)**2 * tensile_mask)
        # 压缩损伤损失：在压缩区，模型预测的通用损伤 d_hat_seq 应接近物理压缩损伤 d_minus_phy_seq
        loss_damage_minus = torch.mean((d_hat_seq - d_minus_phy_seq)**2 * compressive_mask)
        
        # 总损伤损失是拉伸和压缩损伤损失的加权和
        total_loss_damage = self.lambda_damage_plus * loss_damage_plus + \
                            self.lambda_damage_minus * loss_damage_minus
        
        # L_plastic: 塑性物理损失
        # ep_hat_seq 是神经网络预测的累积塑性应变，直接与物理累积塑性应变 ep_phy_seq 对比
        loss_plastic = torch.mean((ep_hat_seq - ep_phy_seq)**2)
        
        # 总损失
        total_loss = (self.lambda_data * loss_data + 
                     self.lambda_stress * loss_stress + 
                     total_loss_damage +  # 使用合并后的总损伤损失
                     self.lambda_plastic * loss_plastic)
        
        return total_loss, {
            'loss_data': loss_data.item(),
            'loss_stress': loss_stress.item(), 
            'loss_damage_plus': loss_damage_plus.item(),   # 记录细分损失
            'loss_damage_minus': loss_damage_minus.item(), # 记录细分损失
            'loss_plastic': loss_plastic.item(),
            'total_loss': total_loss.item()
        } 
