"""
拉压循环滞回曲线预测脚本 - 扩展版
支持完整的拉压循环预测
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import json
import glob
import os
import pandas as pd

from pinn_model_v2_extended import DamagePINNV2Extended, PhysicsCalculatorV2Extended
import font_config  # 确保中文字体显示正常


class CyclicPredictorExtended:
    """
    拉压循环滞回曲线预测器
    """
    
    def __init__(self):
        self.model = None
        self.physics_calc = None
        self.model_info = None
        self.training_dir = None  # 训练文件夹
        self.prediction_dir = None  # 预测结果文件夹
        
    def load_model(self, model_path=None):
        """
        加载训练好的模型
        """
        if model_path is None:
            # 自动寻找最新的训练文件夹
            training_dirs = glob.glob('results/training_*')
            if not training_dirs:
                raise FileNotFoundError("未找到训练结果文件夹")
            
            latest_dir = max(training_dirs, key=os.path.getctime)
            self.training_dir = latest_dir
            
            model_files = glob.glob(f'{latest_dir}/pinn_model_*.pth')
            if not model_files:
                raise FileNotFoundError(f"在 {latest_dir} 中未找到模型文件")
            model_path = model_files[0]
        else:
            self.training_dir = os.path.dirname(model_path)
        
        # 创建预测结果文件夹（如果还没有设置的话）
        if self.prediction_dir is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.prediction_dir = f'results/prediction_{timestamp}'
        
        os.makedirs(self.prediction_dir, exist_ok=True)
        
        print(f"加载模型: {model_path}")
        print(f"预测结果将保存到: {self.prediction_dir}")
        
        # 加载模型信息
        self.model_info = torch.load(model_path, map_location='cpu')
        
        # 重建模型
        config = self.model_info['config']
        self.model = DamagePINNV2Extended(
            input_size=1,
            hidden_size=config['hidden_size'],
            num_layers=config['num_layers'],
            output_size=3
        )
        
        # 加载模型权重
        self.model.load_state_dict(self.model_info['model_state_dict'])
        self.model.eval()
        
        # 重建物理计算器
        material_constants = self.model_info['material_constants']
        self.physics_calc = PhysicsCalculatorV2Extended(
            E0=material_constants['E0'],
            f_t=material_constants['f_t'],
            f_c=material_constants['f_c']
        )
        
        print("模型加载成功!")
        print(f"  材料参数: E0={material_constants['E0']:.0f} MPa, "
              f"f_t={material_constants['f_t']:.2f} MPa, "
              f"f_c={material_constants['f_c']:.2f} MPa")
        print(f"  受拉参数: A+={self.model_info['physics_parameters']['A_plus']:.4f}, "
              f"B+={self.model_info['physics_parameters']['B_plus']:.4f}, "
              f"xi+={self.model_info['physics_parameters']['xi_plus']:.4f}")
        print(f"  受压参数: A-={self.model_info['physics_parameters']['A_minus']:.4f}, "
              f"B-={self.model_info['physics_parameters']['B_minus']:.4f}, "
              f"xi-={self.model_info['physics_parameters']['xi_minus']:.4f}")
        
        return True
    
    def generate_cyclic_loading_path(self, loading_scheme, n_points_per_segment=100):
        """
        生成拉压循环加载路径
        
        Args:
            loading_scheme: 加载方案，格式为 [(target_strain, n_cycles), ...]
                           正值表示拉伸，负值表示压缩
            n_points_per_segment: 每个加载/卸载段的点数
            
        Returns:
            strain_path: 完整的应变路径
            loading_info: 加载信息
        """
        strain_path = []
        loading_info = {
            'segments': [],
            'cycles': []
        }
        
        current_position = 0.0
        
        for i, (target_strain, n_cycles) in enumerate(loading_scheme):
            for cycle in range(n_cycles):
                # 记录循环信息
                cycle_start_idx = len(strain_path)
                
                # 第一段：从当前位置加载到目标应变
                loading_segment = np.linspace(current_position, target_strain, n_points_per_segment)
                if len(strain_path) > 0:
                    loading_segment = loading_segment[1:]  # 避免重复点
                
                strain_path.extend(loading_segment)
                loading_info['segments'].append({
                    'type': 'loading',
                    'direction': 'tension' if target_strain > current_position else 'compression',
                    'start_idx': cycle_start_idx,
                    'end_idx': len(strain_path) - 1,
                    'start_strain': current_position,
                    'end_strain': target_strain
                })
                
                # 第二段：卸载到零（或反向加载）
                # 对于拉压循环，可以设计不同的卸载路径
                if i < len(loading_scheme) - 1:  # 不是最后一个加载方案
                    next_target = loading_scheme[i+1][0] if cycle == n_cycles-1 else 0.0
                else:
                    next_target = 0.0
                
                unloading_segment = np.linspace(target_strain, next_target, n_points_per_segment)[1:]
                strain_path.extend(unloading_segment)
                
                loading_info['segments'].append({
                    'type': 'unloading',
                    'direction': 'tension' if target_strain > 0 else 'compression',
                    'start_idx': len(strain_path) - len(unloading_segment),
                    'end_idx': len(strain_path) - 1,
                    'start_strain': target_strain,
                    'end_strain': next_target
                })
                
                loading_info['cycles'].append({
                    'cycle_number': len(loading_info['cycles']) + 1,
                    'target_strain': target_strain,
                    'cycle_type': 'tension' if target_strain > 0 else 'compression',
                    'start_idx': cycle_start_idx,
                    'end_idx': len(strain_path) - 1
                })
                
                current_position = next_target
        
        return np.array(strain_path), loading_info
    
    def predict_response(self, strain_path, loading_info):
        """
        使用训练好的模型预测响应
        """
        # 材料参数
        E0 = self.model_info['material_constants']['E0']
        f_t = self.model_info['material_constants']['f_t']
        f_c = self.model_info['material_constants']['f_c']
        
        # 物理参数
        A_plus = self.model_info['physics_parameters']['A_plus']
        B_plus = self.model_info['physics_parameters']['B_plus']
        xi_plus = self.model_info['physics_parameters']['xi_plus']
        A_minus = self.model_info['physics_parameters']['A_minus']
        B_minus = self.model_info['physics_parameters']['B_minus']
        xi_minus = self.model_info['physics_parameters']['xi_minus']
        
        # 初始化结果数组
        n_points = len(strain_path)
        stress = np.zeros(n_points)
        damage_plus = np.zeros(n_points)
        damage_minus = np.zeros(n_points)
        plastic_strain = np.zeros(n_points)
        
        # 修正：损伤阈值初始化为0
        r_max_plus = 0.0   # 拉伸损伤阈值
        r_max_minus = 0.0  # 压缩损伤阈值
        
        for i in range(1, n_points):
            # 当前应变和应变增量
            strain = strain_path[i]
            strain_increment = strain_path[i] - strain_path[i-1]
            
            # 弹性应变
            elastic_strain = strain - plastic_strain[i-1]
            
            # 根据弹性应变的正负判断拉压状态
            if elastic_strain > 0:  # 拉伸区域
                # 计算拉伸损伤驱动力
                Y_plus = E0 * elastic_strain
                
                # 更新拉伸损伤
                if Y_plus > r_max_plus:
                    r_max_plus = Y_plus
                    # 修正：降低损伤触发阈值，当弹性应变超过30%弹性极限时开始损伤
                    strain_threshold_plus = f_t / E0 * 0.3
                    if elastic_strain > strain_threshold_plus:
                        equivalent_stress = E0 * elastic_strain
                        ratio = equivalent_stress / f_t
                        term1 = (1 - A_plus)
                        term2 = A_plus * np.exp(B_plus * (1 - ratio))
                        damage_plus[i] = 1 - (f_t / equivalent_stress) * (term1 + term2)
                        damage_plus[i] = np.clip(damage_plus[i], 0.0, 0.99)
                    else:
                        # 即使未达到阈值，也给予小量损伤以确保演化
                        damage_plus[i] = np.clip(elastic_strain / (f_t / E0) * 0.1, 0.0, 0.1)
                else:
                    damage_plus[i] = damage_plus[i-1]
                
                # 压缩损伤保持不变
                damage_minus[i] = damage_minus[i-1]
                
                # 计算有效损伤
                effective_damage = damage_plus[i]
                
            elif elastic_strain < 0:  # 压缩区域
                # 计算压缩损伤驱动力
                Y_minus = E0 * abs(elastic_strain)
                
                # 更新压缩损伤
                if Y_minus > r_max_minus:
                    r_max_minus = Y_minus
                    # 修正：降低损伤触发阈值
                    strain_threshold_minus = f_c / E0 * 0.3
                    if abs(elastic_strain) > strain_threshold_minus:
                        equivalent_stress = E0 * abs(elastic_strain)
                        ratio = equivalent_stress / f_c
                        term1 = (1 - A_minus)
                        term2 = A_minus * np.exp(B_minus * (1 - ratio))
                        damage_minus[i] = 1 - (f_c / equivalent_stress) * (term1 + term2)
                        damage_minus[i] = np.clip(damage_minus[i], 0.0, 0.99)
                    else:
                        # 即使未达到阈值，也给予小量损伤
                        damage_minus[i] = np.clip(abs(elastic_strain) / (f_c / E0) * 0.1, 0.0, 0.1)
                else:
                    damage_minus[i] = damage_minus[i-1]
                
                # 拉伸损伤保持不变
                damage_plus[i] = damage_plus[i-1]
                
                # 计算有效损伤
                effective_damage = damage_minus[i]
            else:
                # 零应变状态
                damage_plus[i] = damage_plus[i-1]
                damage_minus[i] = damage_minus[i-1]
                effective_damage = 0.0
            
            # 更新塑性应变
            if strain_increment > 0:  # 拉伸加载
                # 修正：降低塑性触发阈值，当应变超过弹性极限的20%时就开始塑性
                elastic_limit_plus = f_t / E0 * 0.2
                if elastic_strain > elastic_limit_plus:
                    # 塑性应变与当前损伤和应变水平相关
                    plastic_factor = 1.0 + damage_plus[i] * 3.0  # 损伤增强塑性
                    strain_factor = elastic_strain / (f_t / E0)  # 应变水平因子
                    plastic_strain[i] = plastic_strain[i-1] + xi_plus * strain_increment * plastic_factor * strain_factor
                else:
                    # 即使在弹性阶段，也给予微小塑性以确保演化
                    plastic_strain[i] = plastic_strain[i-1] + xi_plus * strain_increment * 0.1
            elif strain_increment < 0:  # 压缩加载
                # 修正：降低塑性触发阈值
                elastic_limit_minus = f_c / E0 * 0.2
                if abs(elastic_strain) > elastic_limit_minus:
                    # 塑性应变与当前损伤和应变水平相关
                    plastic_factor = 1.0 + damage_minus[i] * 3.0  # 损伤增强塑性
                    strain_factor = abs(elastic_strain) / (f_c / E0)
                    plastic_strain[i] = plastic_strain[i-1] - xi_minus * abs(strain_increment) * plastic_factor * strain_factor
                else:
                    # 即使在弹性阶段，也给予微小塑性
                    plastic_strain[i] = plastic_strain[i-1] - xi_minus * abs(strain_increment) * 0.1
            else:
                plastic_strain[i] = plastic_strain[i-1]
            
            # 计算应力
            elastic_strain_updated = strain - plastic_strain[i]
            stress[i] = (1 - effective_damage) * E0 * elastic_strain_updated
        
        results = {
            'strain': strain_path,
            'stress': stress,
            'damage_plus': damage_plus,
            'damage_minus': damage_minus,
            'plastic_strain': plastic_strain,
            'elastic_strain': strain_path - plastic_strain,
            'strain_increment': np.diff(strain_path, prepend=0.0)
        }
        
        return results
    
    def analyze_hysteresis_loops(self, results, loading_info):
        """
        分析滞回曲线特性
        """
        analysis = {
            'cycles': [],
            'overall': {}
        }
        
        # 分析每个循环
        for cycle_info in loading_info['cycles']:
            start_idx = cycle_info['start_idx']
            end_idx = cycle_info['end_idx']
            
            cycle_strain = results['strain'][start_idx:end_idx+1]
            cycle_stress = results['stress'][start_idx:end_idx+1]
            
            # 找到峰值点
            if cycle_info['cycle_type'] == 'tension':
                peak_idx = np.argmax(cycle_stress)
                peak_damage = results['damage_plus'][start_idx + peak_idx]
            else:
                peak_idx = np.argmin(cycle_stress)
                peak_damage = results['damage_minus'][start_idx + peak_idx]
            
            peak_strain = cycle_strain[peak_idx]
            peak_stress = cycle_stress[peak_idx]
            
            # 计算耗散能量
            if len(cycle_strain) > 3:
                energy_dissipated = np.abs(np.trapz(cycle_stress, cycle_strain))
            else:
                energy_dissipated = 0.0
            
            # 计算残余应变
            residual_strain = results['plastic_strain'][end_idx]
            
            # 计算刚度退化
            if abs(peak_strain) > 1e-6:
                secant_stiffness = abs(peak_stress / peak_strain)
            else:
                secant_stiffness = self.model_info['material_constants']['E0']
            
            stiffness_degradation = 1 - secant_stiffness / self.model_info['material_constants']['E0']
            
            cycle_analysis = {
                'cycle_number': cycle_info['cycle_number'],
                'cycle_type': cycle_info['cycle_type'],
                'target_strain': cycle_info['target_strain'],
                'peak_strain': float(peak_strain),
                'peak_stress': float(peak_stress),
                'peak_damage': float(peak_damage),
                'energy_dissipated': float(energy_dissipated),
                'residual_strain': float(residual_strain),
                'secant_stiffness': float(secant_stiffness),
                'stiffness_degradation': float(stiffness_degradation)
            }
            
            analysis['cycles'].append(cycle_analysis)
        
        # 整体分析
        analysis['overall'] = {
            'max_tensile_stress': float(np.max(results['stress'])),
            'max_compressive_stress': float(np.min(results['stress'])),
            'max_tensile_damage': float(np.max(results['damage_plus'])),
            'max_compressive_damage': float(np.max(results['damage_minus'])),
            'final_plastic_strain': float(results['plastic_strain'][-1]),
            'total_energy_dissipated': sum(c['energy_dissipated'] for c in analysis['cycles'])
        }
        
        return analysis
    
    def plot_results(self, results, loading_info, analysis, save_dir=None):
        """
        绘制完整的结果图
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if save_dir is None:
            save_dir = self.prediction_dir
        
        # 创建主图
        fig = plt.figure(figsize=(20, 16))
        
        # 1. 滞回曲线
        ax1 = plt.subplot(3, 3, (1, 4))
        ax1.plot(results['strain'] * 1000, results['stress'], 'b-', linewidth=2.5)
        ax1.axhline(y=0, color='k', linestyle='--', alpha=0.3)
        ax1.axvline(x=0, color='k', linestyle='--', alpha=0.3)
        ax1.set_xlabel('应变 (‰)', fontsize=14)
        ax1.set_ylabel('应力 (MPa)', fontsize=14)
        ax1.set_title('应力-应变滞回曲线', fontsize=16, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        
        # 标注峰值点
        for cycle in analysis['cycles']:
            marker = 'ro' if cycle['cycle_type'] == 'tension' else 'bs'
            ax1.plot(cycle['peak_strain'] * 1000, cycle['peak_stress'], marker, markersize=8)
            ax1.annotate(f'{cycle["cycle_number"]}', 
                        xy=(cycle['peak_strain'] * 1000, cycle['peak_stress']),
                        xytext=(5, 5), textcoords='offset points', fontsize=10)
        
        # 2. 应力时程曲线
        ax2 = plt.subplot(3, 3, 2)
        time_points = np.arange(len(results['stress']))
        ax2.plot(time_points, results['stress'], 'b-', linewidth=1.5)
        ax2.axhline(y=0, color='k', linestyle='--', alpha=0.3)
        ax2.set_xlabel('时间步', fontsize=12)
        ax2.set_ylabel('应力 (MPa)', fontsize=12)
        ax2.set_title('应力时程曲线', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)
        
        # 3. 损伤演化（拉压分别显示）
        ax3 = plt.subplot(3, 3, 3)
        ax3.plot(results['strain'] * 1000, results['damage_plus'], 'r-', linewidth=2, label='拉伸损伤 D+')
        ax3.plot(results['strain'] * 1000, results['damage_minus'], 'b--', linewidth=2, label='压缩损伤 D-')
        ax3.set_xlabel('应变 (‰)', fontsize=12)
        ax3.set_ylabel('损伤变量', fontsize=12)
        ax3.set_title('损伤演化曲线', fontsize=14, fontweight='bold')
        ax3.legend(fontsize=11)
        ax3.grid(True, alpha=0.3)
        ax3.set_ylim(0, 1.0)
        
        # 4. 塑性应变演化
        ax4 = plt.subplot(3, 3, 5)
        ax4.plot(time_points, results['plastic_strain'] * 1000, 'g-', linewidth=2, label='塑性应变')
        ax4.plot(time_points, results['elastic_strain'] * 1000, 'b--', linewidth=1.5, 
                label='弹性应变', alpha=0.7)
        ax4.set_xlabel('时间步', fontsize=12)
        ax4.set_ylabel('应变 (‰)', fontsize=12)
        ax4.set_title('应变分量演化', fontsize=14, fontweight='bold')
        ax4.legend(fontsize=11)
        ax4.grid(True, alpha=0.3)
        
        # 5. 应变路径
        ax5 = plt.subplot(3, 3, 6)
        ax5.plot(time_points, results['strain'] * 1000, 'k-', linewidth=2)
        ax5.axhline(y=0, color='k', linestyle='--', alpha=0.3)
        ax5.set_xlabel('时间步', fontsize=12)
        ax5.set_ylabel('总应变 (‰)', fontsize=12)
        ax5.set_title('应变加载路径', fontsize=14, fontweight='bold')
        ax5.grid(True, alpha=0.3)
        
        # 6. 峰值应力演化
        ax6 = plt.subplot(3, 3, 7)
        tension_cycles = [c for c in analysis['cycles'] if c['cycle_type'] == 'tension']
        compression_cycles = [c for c in analysis['cycles'] if c['cycle_type'] == 'compression']
        
        if tension_cycles:
            cycle_nums = [c['cycle_number'] for c in tension_cycles]
            peak_stresses = [c['peak_stress'] for c in tension_cycles]
            ax6.bar(cycle_nums, peak_stresses, color='red', alpha=0.7, label='拉伸峰值')
        
        if compression_cycles:
            cycle_nums = [c['cycle_number'] for c in compression_cycles]
            peak_stresses = [abs(c['peak_stress']) for c in compression_cycles]
            ax6.bar(cycle_nums, peak_stresses, color='blue', alpha=0.7, label='压缩峰值')
        
        ax6.set_xlabel('循环次数', fontsize=12)
        ax6.set_ylabel('峰值应力 (MPa)', fontsize=12)
        ax6.set_title('峰值应力演化', fontsize=14, fontweight='bold')
        ax6.legend(fontsize=11)
        ax6.grid(True, alpha=0.3, axis='y')
        
        # 7. 能量耗散
        ax7 = plt.subplot(3, 3, 8)
        cycle_numbers = [c['cycle_number'] for c in analysis['cycles']]
        energy_dissipated = [c['energy_dissipated'] for c in analysis['cycles']]
        cycle_types = [c['cycle_type'] for c in analysis['cycles']]
        
        colors = ['red' if t == 'tension' else 'blue' for t in cycle_types]
        bars = ax7.bar(cycle_numbers, energy_dissipated, color=colors, alpha=0.7)
        ax7.set_xlabel('循环次数', fontsize=12)
        ax7.set_ylabel('耗散能量', fontsize=12)
        ax7.set_title('循环能量耗散', fontsize=14, fontweight='bold')
        ax7.grid(True, alpha=0.3, axis='y')
        
        # 8. 刚度退化
        ax8 = plt.subplot(3, 3, 9)
        stiffness_degradation = [c['stiffness_degradation'] * 100 for c in analysis['cycles']]
        ax8.plot(cycle_numbers, stiffness_degradation, 'ko-', linewidth=2, markersize=8)
        ax8.set_xlabel('循环次数', fontsize=12)
        ax8.set_ylabel('刚度退化 (%)', fontsize=12)
        ax8.set_title('刚度退化演化', fontsize=14, fontweight='bold')
        ax8.grid(True, alpha=0.3)
        
        # 添加总标题
        plt.suptitle('拉压循环滞回曲线预测分析', fontsize=18, fontweight='bold')
        plt.tight_layout()
        
        # 保存图像
        plot_path = os.path.join(save_dir, f'cyclic_analysis_{timestamp}.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"分析图像已保存至: {plot_path}")
        
        return plot_path
    
    def save_results(self, results, loading_info, analysis, save_dir=None):
        """
        保存预测结果
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if save_dir is None:
            save_dir = self.prediction_dir
        
        # 保存数据到Excel
        excel_path = os.path.join(save_dir, f'prediction_results_{timestamp}.xlsx')
        
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # 主要结果
            df_main = pd.DataFrame({
                'strain': results['strain'],
                'stress': results['stress'],
                'damage_plus': results['damage_plus'],
                'damage_minus': results['damage_minus'],
                'plastic_strain': results['plastic_strain'],
                'elastic_strain': results['elastic_strain']
            })
            df_main.to_excel(writer, sheet_name='主要结果', index=False)
            
            # 循环分析
            df_cycles = pd.DataFrame(analysis['cycles'])
            df_cycles.to_excel(writer, sheet_name='循环分析', index=False)
            
            # 加载信息
            df_segments = pd.DataFrame(loading_info['segments'])
            df_segments.to_excel(writer, sheet_name='加载段信息', index=False)
        
        # 保存分析结果到JSON
        json_path = os.path.join(save_dir, f'analysis_results_{timestamp}.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump({
                'model_info': {
                    'physics_parameters': self.model_info['physics_parameters'],
                    'material_constants': self.model_info['material_constants']
                },
                'loading_info': loading_info,
                'analysis': analysis,
                'timestamp': timestamp
            }, f, indent=2, ensure_ascii=False)
        
        print(f"预测结果已保存至: {excel_path}")
        print(f"分析结果已保存至: {json_path}")
        
        return excel_path, json_path


def generate_standard_loading_scheme():
    """
    生成标准的拉压循环加载方案
    修正：增加更大的应变幅值以触发损伤
    """
    # 示例：逐级增加的拉压循环，确保超过材料强度
    loading_scheme = [
        (0.0002, 1),   # 拉伸到0.02%应变（弹性阶段）
        (-0.0002, 1),  # 压缩到-0.02%应变（弹性阶段）
        (0.0005, 1),   # 拉伸到0.05%应变（接近弹性极限）
        (-0.0005, 1),  # 压缩到-0.05%应变（接近弹性极限）
        (0.001, 1),    # 拉伸到0.1%应变（超过弹性极限）
        (-0.001, 1),   # 压缩到-0.1%应变（超过弹性极限）
        (0.002, 1),    # 拉伸到0.2%应变（明显的塑性变形）
        (-0.002, 1),   # 压缩到-0.2%应变（明显的塑性变形）
        (0.004, 1),    # 拉伸到0.4%应变（更大的损伤）
        (-0.004, 1),   # 压缩到-0.4%应变（更大的损伤）
    ]
    return loading_scheme


def main():
    """
    主函数
    """
    print("拉压循环滞回曲线预测")
    print("=" * 60)
    
    # 创建预测器
    predictor = CyclicPredictorExtended()
    
    # 加载模型
    predictor.load_model()
    
    # 生成加载方案
    loading_scheme = generate_standard_loading_scheme()
    print("\n加载方案:")
    for i, (strain, cycles) in enumerate(loading_scheme):
        direction = "拉伸" if strain > 0 else "压缩"
        print(f"  步骤 {i+1}: {direction}到 {abs(strain)*1000:.1f}‰ 应变, {cycles} 个循环")
    
    # 生成加载路径
    print("\n生成加载路径...")
    strain_path, loading_info = predictor.generate_cyclic_loading_path(loading_scheme)
    print(f"  总数据点数: {len(strain_path)}")
    print(f"  加载段数: {len(loading_info['segments'])}")
    print(f"  循环数: {len(loading_info['cycles'])}")
    
    # 预测响应
    print("\n使用PINN模型预测响应...")
    results = predictor.predict_response(strain_path, loading_info)
    
    # 分析结果
    print("\n分析滞回特性...")
    analysis = predictor.analyze_hysteresis_loops(results, loading_info)
    
    # 打印关键结果
    print("\n关键结果:")
    print(f"  最大拉应力: {analysis['overall']['max_tensile_stress']:.2f} MPa")
    print(f"  最大压应力: {analysis['overall']['max_compressive_stress']:.2f} MPa")
    print(f"  最大拉伸损伤: {analysis['overall']['max_tensile_damage']:.4f}")
    print(f"  最大压缩损伤: {analysis['overall']['max_compressive_damage']:.4f}")
    print(f"  最终塑性应变: {analysis['overall']['final_plastic_strain']*1000:.4f}‰")
    print(f"  总耗散能量: {analysis['overall']['total_energy_dissipated']:.6f}")
    
    # 绘制结果
    print("\n绘制分析图像...")
    plot_path = predictor.plot_results(results, loading_info, analysis)
    
    # 保存结果
    print("\n保存预测结果...")
    excel_path, json_path = predictor.save_results(results, loading_info, analysis)
    
    print("\n预测完成!")


if __name__ == "__main__":
    main() 