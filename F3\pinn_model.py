# -*- coding: utf-8 -*-
"""
PINN模型模块

实现混凝土损伤本构的物理信息神经网络模型
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
from font_config import configure_chinese_font

class ConstitutivePINN(nn.Module):
    """
    混凝土损伤本构PINN模型
    """
    
    def __init__(self, hidden_size=10, num_layers=3):
        super(ConstitutivePINN, self).__init__()
        
        # 网络结构
        layers = []
        layers.append(nn.Linear(1, hidden_size))  # 输入层：应变
        layers.append(nn.ReLU())
        
        for _ in range(num_layers - 1):
            layers.append(nn.Linear(hidden_size, hidden_size))
            layers.append(nn.ReLU())
        
        layers.append(nn.Linear(hidden_size, 2))  # 输出层：应力和损伤变量
        
        self.network = nn.Sequential(*layers)
        
        # 材料参数（可学习参数）
        self.E0 = nn.Parameter(torch.tensor(30000.0))  # 初始弹性模量 (MPa)
        self.r0_plus = nn.Parameter(torch.tensor(2.41))   # 拉伸损伤阈值
        self.r0_minus = nn.Parameter(torch.tensor(10.0))  # 压缩损伤阈值
        self.A_plus = nn.Parameter(torch.tensor(0.2))     # 拉伸损伤参数A
        self.A_minus = nn.Parameter(torch.tensor(0.2))    # 压缩损伤参数A
        self.B_plus = nn.Parameter(torch.tensor(1.0))     # 拉伸损伤参数B
        self.B_minus = nn.Parameter(torch.tensor(1.0))    # 压缩损伤参数B
        
        # 配置中文字体
        configure_chinese_font()
    
    def forward(self, strain):
        """
        前向传播
        
        Args:
            strain: 应变张量 [batch_size, 1]
            
        Returns:
            stress: 应力张量 [batch_size, 1]
            damage: 损伤变量张量 [batch_size, 1]
        """
        # 网络输出
        output = self.network(strain)
        stress_raw = output[:, 0:1]
        damage_raw = output[:, 1:2]
        
        # 确保损伤变量在[0,1]范围内
        damage = torch.sigmoid(damage_raw)
        
        # 应力输出（可以是正负值）
        stress = stress_raw
        
        return stress, damage
    
    def physics_loss(self, strain, stress_pred, damage_pred):
        """
        计算物理约束损失
        
        Args:
            strain: 应变张量
            stress_pred: 预测应力张量
            damage_pred: 预测损伤变量张量
            
        Returns:
            physics_loss: 物理约束损失
        """
        # 本构关系约束：σ = (1-d) * E * ε
        stress_constitutive = (1 - damage_pred) * self.E0 * strain
        constitutive_loss = torch.mean((stress_pred - stress_constitutive) ** 2)
        
        # 损伤演化约束
        # 简化的损伤演化：d = 1 - exp(-k * <ε - ε0>+)
        strain_threshold = 0.0001  # 损伤起始应变
        k_damage = 1000.0  # 损伤演化参数
        
        strain_effective = torch.clamp(strain - strain_threshold, min=0.0)
        damage_theoretical = 1 - torch.exp(-k_damage * strain_effective)
        damage_evolution_loss = torch.mean((damage_pred - damage_theoretical) ** 2)
        
        # 损伤单调性约束（损伤只能增加）
        if strain.shape[0] > 1:
            damage_diff = damage_pred[1:] - damage_pred[:-1]
            strain_diff = strain[1:] - strain[:-1]
            # 当应变增加时，损伤不应减少
            monotonicity_loss = torch.mean(torch.clamp(-damage_diff * torch.clamp(strain_diff, min=0), min=0) ** 2)
        else:
            monotonicity_loss = torch.tensor(0.0)
        
        total_physics_loss = constitutive_loss + 0.1 * damage_evolution_loss + 0.01 * monotonicity_loss
        
        return total_physics_loss, {
            'constitutive': constitutive_loss.item(),
            'damage_evolution': damage_evolution_loss.item(),
            'monotonicity': monotonicity_loss.item()
        }
    
    def compute_damage_variables(self, strain):
        """
        根据损伤本构理论计算损伤变量
        
        Args:
            strain: 应变张量
            
        Returns:
            damage_plus: 拉伸损伤变量
            damage_minus: 压缩损伤变量
        """
        # 分离拉伸和压缩应变
        strain_plus = torch.clamp(strain, min=0.0)
        strain_minus = torch.clamp(-strain, min=0.0)
        
        # 计算损伤能释放率（简化）
        Y_plus = self.E0 * strain_plus
        Y_minus = self.E0 * strain_minus
        
        # 损伤阈值更新（简化为当前最大值）
        r_plus = torch.clamp(Y_plus, min=self.r0_plus)
        r_minus = torch.clamp(Y_minus, min=self.r0_minus)
        
        # 计算损伤变量
        damage_plus = 1 - ((self.r0_plus / r_plus) * (1 - self.A_plus) + self.A_plus) * \
                      torch.exp(self.B_plus * (1 - r_plus / self.r0_plus))
        
        damage_minus = 1 - ((self.r0_minus / r_minus) * (1 - self.A_minus) + self.A_minus) * \
                       torch.exp(self.B_minus * (1 - r_minus / self.r0_minus))
        
        # 确保损伤变量在合理范围内
        damage_plus = torch.clamp(damage_plus, min=0.0, max=0.99)
        damage_minus = torch.clamp(damage_minus, min=0.0, max=0.99)
        
        return damage_plus, damage_minus

class PINNTrainer:
    """
    PINN训练器
    """
    
    def __init__(self, model, learning_rate=0.001):
        self.model = model
        self.optimizer = optim.Adam(model.parameters(), lr=learning_rate)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, patience=500, factor=0.5)
        
        self.train_losses = []
        self.physics_losses = []
        self.data_losses = []
        
    def train_step(self, strain_data, stress_data, physics_weight=1.0, data_weight=1.0):
        """
        单步训练
        
        Args:
            strain_data: 应变数据
            stress_data: 应力数据
            physics_weight: 物理约束权重
            data_weight: 数据拟合权重
            
        Returns:
            total_loss: 总损失
        """
        self.optimizer.zero_grad()
        
        # 前向传播
        stress_pred, damage_pred = self.model(strain_data)
        
        # 数据拟合损失
        data_loss = torch.mean((stress_pred - stress_data) ** 2)
        
        # 物理约束损失
        physics_loss, physics_components = self.model.physics_loss(strain_data, stress_pred, damage_pred)
        
        # 总损失
        total_loss = data_weight * data_loss + physics_weight * physics_loss
        
        # 反向传播
        total_loss.backward()
        self.optimizer.step()
        
        # 记录损失
        self.train_losses.append(total_loss.item())
        self.physics_losses.append(physics_loss.item())
        self.data_losses.append(data_loss.item())
        
        return total_loss.item(), data_loss.item(), physics_loss.item(), physics_components
    
    def train(self, strain_data, stress_data, epochs=5000, physics_weight=1.0, data_weight=1.0, 
              print_interval=500, save_path=None):
        """
        训练模型
        
        Args:
            strain_data: 应变数据张量
            stress_data: 应力数据张量
            epochs: 训练轮数
            physics_weight: 物理约束权重
            data_weight: 数据拟合权重
            print_interval: 打印间隔
            save_path: 模型保存路径
        """
        print(f"开始训练，共 {epochs} 轮")
        print(f"数据点数: {len(strain_data)}")
        print(f"物理约束权重: {physics_weight}, 数据拟合权重: {data_weight}")
        
        for epoch in range(epochs):
            total_loss, data_loss, physics_loss, physics_components = self.train_step(
                strain_data, stress_data, physics_weight, data_weight
            )
            
            # 学习率调度
            self.scheduler.step(total_loss)
            
            if (epoch + 1) % print_interval == 0:
                current_lr = self.optimizer.param_groups[0]['lr']
                print(f"Epoch {epoch+1}/{epochs}:")
                print(f"  总损失: {total_loss:.6f}")
                print(f"  数据损失: {data_loss:.6f}")
                print(f"  物理损失: {physics_loss:.6f}")
                print(f"  - 本构约束: {physics_components['constitutive']:.6f}")
                print(f"  - 损伤演化: {physics_components['damage_evolution']:.6f}")
                print(f"  - 单调性: {physics_components['monotonicity']:.6f}")
                print(f"  学习率: {current_lr:.2e}")
                print(f"  材料参数: E0={self.model.E0.item():.1f}, r0+={self.model.r0_plus.item():.3f}")
                print("-" * 50)
        
        print("训练完成！")
        
        # 保存模型
        if save_path:
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'train_losses': self.train_losses,
                'physics_losses': self.physics_losses,
                'data_losses': self.data_losses
            }, save_path)
            print(f"模型已保存至: {save_path}")
    
    def plot_training_history(self, save_path=None):
        """
        绘制训练历史
        """
        plt.figure(figsize=(15, 5))
        
        plt.subplot(1, 3, 1)
        plt.plot(self.train_losses, 'b-', linewidth=1)
        plt.xlabel('训练步数')
        plt.ylabel('总损失')
        plt.title('训练损失曲线')
        plt.yscale('log')
        plt.grid(True)
        
        plt.subplot(1, 3, 2)
        plt.plot(self.data_losses, 'g-', linewidth=1, label='数据损失')
        plt.plot(self.physics_losses, 'r-', linewidth=1, label='物理损失')
        plt.xlabel('训练步数')
        plt.ylabel('损失值')
        plt.title('损失分解')
        plt.yscale('log')
        plt.legend()
        plt.grid(True)
        
        plt.subplot(1, 3, 3)
        # 绘制最后1000步的损失
        start_idx = max(0, len(self.train_losses) - 1000)
        plt.plot(range(start_idx, len(self.train_losses)), 
                self.train_losses[start_idx:], 'b-', linewidth=1)
        plt.xlabel('训练步数')
        plt.ylabel('总损失')
        plt.title('最后1000步损失')
        plt.grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"训练历史图已保存至: {save_path}")
        
        plt.show()

# 测试代码
if __name__ == "__main__":
    # 创建模型
    model = ConstitutivePINN(hidden_size=20, num_layers=4)
    trainer = PINNTrainer(model, learning_rate=0.001)
    
    # 生成测试数据
    strain_test = torch.linspace(0, 0.01, 1000).reshape(-1, 1)
    stress_test = 30000 * strain_test * (1 - 0.5 * torch.clamp(strain_test - 0.001, min=0) / 0.009)
    
    print("开始测试训练...")
    trainer.train(strain_test, stress_test, epochs=1000, print_interval=200)
    
    # 绘制训练历史
    trainer.plot_training_history('test_training_history.png')
    
    print("测试完成！")