import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, List, Optional

class ConcreteTensionPINN(nn.Module):
    """
    物理信息神经网络（PINN）用于混凝土拉伸损伤曲线识别
    
    核心功能：
    1. 输入应变序列，输出应力、损伤变量、塑性应变
    2. 内置物理约束和损伤阈值判断机制
    3. 通过LSTM捕捉历史依赖性
    """
    
    def __init__(self, 
                 E0: float = 30000.0,  # 初始弹性模量 (MPa)
                 ft: float = 3.0,      # 抗拉强度 (MPa)
                 hidden_size: int = 64,
                 num_layers: int = 2,
                 device: str = 'cpu'):
        """
        初始化PINN模型
        
        Args:
            E0: 初始弹性模量
            ft: 抗拉强度
            hidden_size: LSTM隐藏层大小
            num_layers: LSTM层数
            device: 计算设备
        """
        super(ConcreteTensionPINN, self).__init__()
        
        # 固定物理参数
        self.E0 = E0
        self.ft = ft
        self.r0 = ft  # 初始损伤阈值
        self.device = device
        
        # 可训练的物理参数
        self.A_plus = nn.Parameter(torch.tensor(0.5, device=device))  # 损伤演化形状参数
        self.B_plus = nn.Parameter(torch.tensor(1.0, device=device))  # 损伤演化速率参数
        self.xi = nn.Parameter(torch.tensor(0.1, device=device))      # 塑性应变比例系数
        
        # LSTM网络用于捕捉历史依赖性
        self.lstm = nn.LSTM(
            input_size=1,      # 输入：应变
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=0.1 if num_layers > 1 else 0.0
        )
        
        # 输出层：预测应力、损伤、塑性应变
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_size, hidden_size),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_size, 3)  # 输出：[应力, 损伤, 塑性应变]
        )
        
        # 初始化网络权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化网络权重"""
        for name, param in self.named_parameters():
            if 'weight' in name:
                if 'lstm' in name:
                    nn.init.xavier_uniform_(param)
                else:
                    nn.init.kaiming_normal_(param)
            elif 'bias' in name:
                nn.init.constant_(param, 0)
    
    def compute_damage_evolution(self, r: torch.Tensor) -> torch.Tensor:
        """
        计算损伤演化
        
        Args:
            r: 损伤阈值
            
        Returns:
            损伤变量 d
        """
        # 确保参数在合理范围内
        A_plus = torch.clamp(self.A_plus, 0.0, 1.0)
        B_plus = torch.clamp(self.B_plus, 0.1, 10.0)
        
        # 损伤演化公式
        term1 = self.r0 / r * (1 - A_plus)
        term2 = A_plus * torch.exp(B_plus * (1 - r / self.r0))
        damage = 1 - (term1 + term2)
        
        # 确保损伤在[0, 1]范围内
        damage = torch.clamp(damage, 0.0, 1.0)
        
        return damage
    
    def forward_step(self, 
                     strain: torch.Tensor,
                     prev_state: dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, dict]:
        """
        单步前向传播
        
        Args:
            strain: 当前应变
            prev_state: 前一步的状态信息
            
        Returns:
            stress: 当前应力
            damage: 当前损伤
            plastic_strain: 当前塑性应变
            current_state: 当前状态信息
        """
        # 获取前一步状态，确保都是Tensor类型
        prev_r = prev_state.get('r_max', torch.tensor(self.r0, device=self.device, dtype=torch.float32))
        prev_plastic = prev_state.get('plastic_strain', torch.tensor(0.0, device=self.device, dtype=torch.float32))
        prev_strain = prev_state.get('strain', torch.tensor(0.0, device=self.device, dtype=torch.float32))
        lstm_state = prev_state.get('lstm_state', None)
        
        # 确保张量在正确的设备上
        if isinstance(strain, (int, float)):
            strain = torch.tensor(strain, device=self.device, dtype=torch.float32)
        
        # 确保输入参数是Tensor类型
        if not isinstance(prev_r, torch.Tensor):
            prev_r = torch.tensor(prev_r, device=self.device, dtype=torch.float32)
        if not isinstance(prev_plastic, torch.Tensor):
            prev_plastic = torch.tensor(prev_plastic, device=self.device, dtype=torch.float32)
        if not isinstance(prev_strain, torch.Tensor):
            prev_strain = torch.tensor(prev_strain, device=self.device, dtype=torch.float32)
        
        # 塑性应变更新
        xi_clamped = torch.clamp(self.xi, 0.0, 1.0)
        if prev_state:
            delta_strain = strain - prev_strain
            plastic_strain = prev_plastic + xi_clamped * delta_strain
        else:
            plastic_strain = torch.tensor(0.0, device=self.device, dtype=torch.float32)
        
        # 计算损伤驱动力
        Y = self.E0 * (strain - plastic_strain)
        
        # 损伤阈值更新（不可逆性）
        r_max = torch.maximum(prev_r, Y)
        
        # 判断是否需要更新损伤（核心逻辑）
        if Y > prev_r:
            # 超过阈值，更新损伤
            damage = self.compute_damage_evolution(r_max)
        else:
            # 未超过阈值，保持前一步损伤
            damage = prev_state.get('damage', torch.tensor(0.0, device=self.device, dtype=torch.float32))
            if not isinstance(damage, torch.Tensor):
                damage = torch.tensor(damage, device=self.device, dtype=torch.float32)
        
        # 通过LSTM更新隐藏状态
        strain_input = strain.unsqueeze(0).unsqueeze(0)  # [1, 1, 1]
        lstm_out, lstm_state = self.lstm(strain_input, lstm_state)
        
        # 网络预测（用于学习和校正）
        nn_output = self.output_layer(lstm_out.squeeze(0).squeeze(0))  # 确保输出是[3]维度
        stress_pred = nn_output[0]
        damage_pred = nn_output[1] 
        plastic_pred = nn_output[2]
        
        # 物理约束的应力计算
        stress_physics = self.E0 * (1 - damage) * (strain - plastic_strain)
        
        # 融合物理计算和网络预测
        stress = stress_physics  # 主要使用物理约束
        
        # 构建当前状态
        current_state = {
            'r_max': r_max,
            'plastic_strain': plastic_strain,
            'strain': strain,
            'damage': damage,
            'lstm_state': lstm_state,
            'stress_pred': stress_pred,
            'damage_pred': damage_pred,
            'plastic_pred': plastic_pred
        }
        
        return stress, damage, plastic_strain, current_state
    
    def forward(self, strain_sequence: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, List[dict]]:
        """
        完整的前向传播过程
        
        Args:
            strain_sequence: 应变序列 [batch_size, seq_len] 或 [seq_len]
            
        Returns:
            stress_sequence: 应力序列
            damage_sequence: 损伤序列
            plastic_sequence: 塑性应变序列
            state_history: 状态历史
        """
        if strain_sequence.dim() == 1:
            strain_sequence = strain_sequence.unsqueeze(0)  # [1, seq_len]
        
        batch_size, seq_len = strain_sequence.shape
        
        # 初始化输出张量
        stress_sequence = torch.zeros(batch_size, seq_len, device=self.device)
        damage_sequence = torch.zeros(batch_size, seq_len, device=self.device)
        plastic_sequence = torch.zeros(batch_size, seq_len, device=self.device)
        
        state_history = []
        
        # 逐步计算每个时间步
        for i in range(seq_len):
            batch_states = []
            
            for b in range(batch_size):
                # 获取前一步状态
                if i == 0:
                    prev_state = {}
                else:
                    prev_state = state_history[i-1][b] if i > 0 and len(state_history) > i-1 else {}
                
                # 单步前向传播
                stress, damage, plastic, current_state = self.forward_step(
                    strain_sequence[b, i], prev_state
                )
                
                # 存储结果
                stress_sequence[b, i] = stress
                damage_sequence[b, i] = damage
                plastic_sequence[b, i] = plastic
                
                batch_states.append(current_state)
            
            state_history.append(batch_states)
        
        return stress_sequence, damage_sequence, plastic_sequence, state_history
    
    def predict_single_point(self, strain: float, state_history: Optional[List[dict]] = None) -> dict:
        """
        预测单个应变点的状态
        
        Args:
            strain: 目标应变值
            state_history: 历史状态（可选）
            
        Returns:
            包含应力、损伤、塑性应变的状态字典
        """
        self.eval()
        
        with torch.no_grad():
            strain_tensor = torch.tensor(strain, device=self.device, dtype=torch.float32)
            
            # 如果没有历史状态，从初始状态开始
            if state_history is None or len(state_history) == 0:
                prev_state = {}
            else:
                prev_state = state_history[-1][0]  # 取最后一个状态
            
            stress, damage, plastic, current_state = self.forward_step(strain_tensor, prev_state)
            
            return {
                'strain': strain,
                'stress': stress.item(),
                'damage': damage.item(),
                'plastic_strain': plastic.item(),
                'r_max': current_state['r_max'].item(),
                'is_damage_updated': current_state['r_max'] > prev_state.get('r_max', self.r0)
            }
    
    def get_material_parameters(self) -> dict:
        """获取当前的材料参数"""
        return {
            'A_plus': self.A_plus.item(),
            'B_plus': self.B_plus.item(),
            'xi': self.xi.item(),
            'E0': self.E0,
            'ft': self.ft
        } 