# PINN滞回曲线模型设计思路

## 1. 数据预处理与输入设计

### 输入特征
- **位移归一化**：将实验位移数据 $x$ 归一化至 $[-1, 1]$ 范围：
  $$
  x_{\text{norm}} = \frac{x - x_{\min}}{x_{\max} - x_{\min}} \times 2 - 1
  $$

- **静态参数标准化**：对柱的基础参数进行Z-Score标准化：
  $$
  \text{Input} = \left[ x_{\text{norm}}, \, L/D, \, P/f_cA_g, \, \rho_l, \, \rho_t \right]
  $$
  - 包含参数：归一化位移 $x_{\text{norm}}$、长细比 $L/D$、轴压比 $P/f_cA_g$、纵向配筋率 $\rho_l$、横向配筋率 $\rho_t$

### 输出目标
- 归一化恢复力 $F_{\text{norm}}$：
  $$
  F_{\text{norm}} = \frac{F - F_{\min}}{F_{\max} - F_{\min}} \times 2 - 1
  $$

### 数据组织
- 按滞回循环顺序排列数据，保留加载-卸载路径的时序性
- 数据集划分：
  - 训练集：前80%的滞回循环
  - 测试集：后20%的滞回循环

## 2. 物理约束建模

### 核心方程（Bouc-Wen模型）
1. **恢复力公式**：
   $$
   F = \alpha k x + (1 - \alpha)k z
   $$
   - $\alpha$: 线性刚度占比（$0 \leq \alpha \leq 1$）
   - $z$: 滞回位移变量，描述非线性行为

2. **滞回变量微分方程**：
   $$
   \frac{dz}{dx} = A - \beta \cdot \text{sign}(\Delta x) |z|^n - \gamma |z|^n
   $$
   - $\Delta x$: 位移差分近似速度符号（$\Delta x = x_{t+1} - x_t$）
   - $A, \beta, \gamma, n$: 控制滞回环形状的参数

## 3. 神经网络架构

### 网络结构
- **输入层**：5维（归一化位移 + 4个静态参数）
- **输出层**：2维（预测力 $F_{\text{pred}}$ 和滞回变量 $z$）
- **隐藏层**：
  - 4~6层全连接网络，每层50~100个神经元
  - 激活函数：**Swish**（兼顾非线性与光滑性）：
    $$
    \text{Swish}(x) = x \cdot \sigma(x), \quad \sigma(x) = \frac{1}{1 + e^{-x}}
    $$

### 参数学习
- **全局可训练变量**：Bouc-Wen参数 $\alpha, \beta, \gamma, A, n, k$
- **静态参数影响**：通过输入特征直接调制网络权重，增强对不同柱特性的适应性

## 4. 损失函数设计

### 总损失函数
$$
\mathcal{L} = \lambda_1 \cdot \text{MSE}(F_{\text{pred}}, F_{\text{exp}}) + \lambda_2 \cdot \left( \text{MSE}(\text{Residual}_1) + \text{MSE}(\text{Residual}_2) \right)
$$

### 损失项定义
1. **数据匹配损失**：
   $$
   \text{Residual}_1 = F_{\text{pred}} - F_{\text{exp}}
   $$

2. **物理残差损失**：
   - 恢复力残差：
     $$
     \text{Residual}_2^{(1)} = F_{\text{pred}} - \left( \alpha k x + (1 - \alpha)k z \right)
     $$
   - 滞回变量残差：
     $$
     \text{Residual}_2^{(2)} = \frac{dz}{dx} - \left( A - \beta \cdot \text{sign}(\Delta x) |z|^n - \gamma |z|^n \right)
     $$

### 权重分配
- 初始建议：$\lambda_1 = 1.0, \, \lambda_2 = 0.5$，根据训练动态调整

## 5. 训练策略

### 优化器
- **两阶段优化**：
  1. **Adam优化器**：初始学习率 $10^{-3} \sim 10^{-4}$，快速收敛
  2. **L-BFGS优化器**：精细调参，提升物理约束满足度

### 正则化
- **L2正则化**：系数 $10^{-4} \sim 10^{-5}$，防止过拟合
- **梯度裁剪**：限制最大梯度范数为1.0，避免微分项导致的梯度爆炸

### 学习率调整
- **动态衰减**：监控损失平台期，使用 `ReduceLROnPlateau` 自动降低学习率

## 6. 预测与验证

### 预测步骤
1. 输入位移序列 $x(t)$ 和静态参数，网络输出 $F_{\text{pred}}(t)$ 和 $z(t)$
2. 反归一化 $F_{\text{pred}}$ 得到实际力值

### 验证指标
- **定量指标**：
  $$
  \text{MSE} = \frac{1}{N} \sum_{i=1}^N (F_{\text{pred}}^{(i)} - F_{\text{exp}}^{(i)})^2, \quad \text{MAE} = \frac{1}{N} \sum_{i=1}^N |F_{\text{pred}}^{(i)} - F_{\text{exp}}^{(i)})|\n  $$
  - **R²决定系数**：衡量模型解释方差的比例
  - **能量误差**：评估滞回环面积差异
- **定性指标**：绘制实验与预测滞回曲线，检查以下特征：
  - 包络线形状
  - 捏缩效应（Pinching Effect）
  - 刚度退化（Stiffness Degradation）
  - 强度退化（Strength Degradation）

### 模型不确定性分析
1. **参数敏感性分析**：通过扰动Bouc-Wen参数评估模型鲁棒性
2. **蒙特卡洛模拟**：考虑输入参数变异性的预测区间估计
3. **误差传播分析**：量化数据测量误差对预测结果的影响

## 7. 关键公式总结

$$
\boxed{
\begin{aligned}
&\text{输入：} \quad \left[ x_{\text{norm}}, \, L/D, \, P/f_cA_g, \, \rho_l, \, \rho_t \right] \\
&\text{输出：} \quad F_{\text{pred}}, \, z \\
&\text{物理约束：} \\
&F = \alpha k x + (1 - \alpha)k z, \\
&\frac{dz}{dx} = A - \beta \cdot \text{sign}(\Delta x) |z|^n - \gamma |z|^n, \\
&\mathcal{L} = \lambda_1 \cdot \text{MSE}(F_{\text{pred}}, F_{\text{exp}}) + \lambda_2 \cdot \left( \text{MSE}(\text{Residual}_2^{(1)}) + \text{MSE}(\text{Residual}_2^{(2)}) \right)
\end{aligned}
}
$$

## 优势
- **数据高效**：物理约束弥补小样本缺陷
- **可解释性**：Bouc-Wen参数与静态参数关联，模型具备物理意义
- **泛化能力**：适应不同几何与材料特性的柱