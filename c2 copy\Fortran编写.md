-----

**第二阶段：VUMAT子程序Fortran编码（Fortran环境）**

**目标：** 将PINN识别的参数和物理演化逻辑转化为Fortran VUMAT子程序。

**步骤 2.1：从JSON文件读取识别参数**

  * **AI指令**：
      * 读取上一步生成的`identified_parameters.json`文件，提取出PINN识别的`physics_parameters` ($A^{\\pm}, B^{\\pm}, \\xi^{\\pm}$) 和`material_constants` ($E\_0, f\_t, f\_c$)。
      * **输出**：将这些参数值以Fortran可以接受的`REAL*8`浮点数形式存储，用于后续VUMAT代码的编写。

**步骤 2.2：编写VUMAT子程序框架`vumat_concrete.f`**

  * **AI指令**：
      * 创建一个名为`vumat_concrete.f`的Fortran文件。
      * 根据Abaqus VUMAT的通用模板，填充子程序的基本结构和参数声明。
      * **核心参数声明**：
          * `PROPS(NPROPS)`：用于接收$E\_0, f\_t, f\_c, A^{\\pm}, B^{\\pm}, \\xi^{\\pm}$共9个参数。
          * `STATEV(NSTATV)`：声明至少5个状态变量（累积塑性应变、d+, d-, r\_max+, r\_max-）。
          * `STRESS(NTENS)`：柯西应力张量。
          * `DSTRAN(NTENS)`：总应变增量。
          * `DDSDDE(NTENS, NTENS)`：算法一致性切线模量。
      * **Fortran代码框架**：使用之前提供的VUMAT框架作为基础，并根据模型特点进行初步填充。
      * **输出**：生成`vumat_concrete.f`文件。

**步骤 2.3：Fortran化物理演化逻辑**

  * **AI指令**：
      * 将Python `PhysicsCalculatorV2`中的物理演化逻辑（应变更新、损伤驱动力计算、历史最大值更新、损伤演化、塑性应变演化、应力计算）精确地翻译成Fortran代码，并填充到`vumat_concrete.f`的“Incremental Constitutive Model Calculation”部分。
      * **关键点**：
          * **张量操作**：确保应力、应变张量（`STRESS`, `DSTRAN`）的正确处理，包括分量索引和多轴情况下的计算。
          * **数学函数**：使用Fortran的`EXP`, `MAX`, `MIN`, `SQRT`等内置函数。
          * **状态变量更新**：正确读取和更新`STATEV`数组中的各个物理量。
          * **数值稳定性**：特别关注损伤和塑性演化中的迭代或条件判断，确保数值收敛性和稳定性。
      * **输出**：更新`vumat_concrete.f`文件，包含Fortran化的本构演化逻辑。

**步骤 2.4：实现算法一致性切线模量（DDSDDE）**

  * **AI指令**：
      * 根据吴建营、李杰论文中推导的“算法一致性切线模量”公式，将其精确地用Fortran代码实现，并填充到`vumat_concrete.f`的“Update DDSDDE”部分。
      * **关键点**：
          * 确保DDSDDE矩阵的维度和分量索引正确。
          * 解析表达式的Fortran转换要准确无误。
      * **输出**：更新`vumat_concrete.f`文件，包含完整的DDSDDE计算。

-----
