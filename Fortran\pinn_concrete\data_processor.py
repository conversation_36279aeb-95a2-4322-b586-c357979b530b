import pandas as pd
import numpy as np
import torch
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
import os

class DataProcessor:
    def __init__(self, data_path, L0=100.0, A=10000.0):
        """
        初始化数据处理器
        
        参数:
        data_path: Excel数据文件路径
        L0: 试件初始长度(mm)
        A: 横截面积(mm²)
        """
        self.data_path = data_path
        self.L0 = L0  # 试件初始长度，单位mm
        self.A = A    # 横截面积，单位mm²
        self.raw_data = None
        self.strain = None
        self.stress = None
        self.strain_normalized = None
        self.stress_normalized = None
        self.strain_mean = None
        self.strain_std = None
        self.stress_mean = None
        self.stress_std = None
        
    def load_data(self):
        """
        加载Excel数据并转换为应变和应力
        """
        try:
            # 读取Excel数据
            self.raw_data = pd.read_excel(self.data_path)
            print(f"数据加载成功，共{len(self.raw_data)}行")
            print(f"数据列名: {self.raw_data.columns.tolist()}")
            
            # 提取力和位移数据
            force = self.raw_data.iloc[:, 0].values  # 力(KN)
            displacement = self.raw_data.iloc[:, 1].values  # 位移(mm)
            
            # 转换为应变和应力
            # 应变 = 位移/初始长度
            self.strain = displacement / self.L0
            # 应力 = 力/横截面积 (KN转换为N，除以mm²得到MPa)
            self.stress = force * 1000 / self.A
            
            print(f"应变范围: [{np.min(self.strain):.6f}, {np.max(self.strain):.6f}]")
            print(f"应力范围: [{np.min(self.stress):.6f}, {np.max(self.stress):.6f}] MPa")
            
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def normalize_data(self):
        """
        对应变和应力数据进行归一化处理
        """
        if self.strain is None or self.stress is None:
            print("请先加载数据")
            return False
        
        # 计算均值和标准差
        self.strain_mean = np.mean(self.strain)
        self.strain_std = np.std(self.strain)
        self.stress_mean = np.mean(self.stress)
        self.stress_std = np.std(self.stress)
        
        # 归一化处理
        self.strain_normalized = (self.strain - self.strain_mean) / self.strain_std
        self.stress_normalized = (self.stress - self.stress_mean) / self.stress_std
        
        print("数据归一化完成")
        return True
    
    def create_dataloader(self, batch_size=32, train_ratio=0.8):
        """
        创建PyTorch数据加载器
        
        参数:
        batch_size: 批次大小
        train_ratio: 训练集比例
        
        返回:
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        """
        if self.strain_normalized is None or self.stress_normalized is None:
            print("请先归一化数据")
            return None, None
        
        # 转换为PyTorch张量
        strain_tensor = torch.FloatTensor(self.strain_normalized.reshape(-1, 1))
        stress_tensor = torch.FloatTensor(self.stress_normalized.reshape(-1, 1))
        
        # 创建数据集
        dataset = TensorDataset(strain_tensor, stress_tensor)
        
        # 划分训练集和验证集
        train_size = int(len(dataset) * train_ratio)
        val_size = len(dataset) - train_size
        train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
        
        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        
        print(f"训练集大小: {train_size}, 验证集大小: {val_size}")
        return train_loader, val_loader
    
    def denormalize_stress(self, normalized_stress):
        """
        将归一化的应力值转换回原始应力值
        """
        return normalized_stress * self.stress_std + self.stress_mean
    
    def denormalize_strain(self, normalized_strain):
        """
        将归一化的应变值转换回原始应变值
        """
        return normalized_strain * self.strain_std + self.strain_mean
    
    def plot_data(self, save_path=None):
        """
        绘制原始数据
        
        参数:
        save_path: 保存图像的路径，如果为None则显示图像
        """
        # 配置中文字体
        from font_config import configure_chinese_font
        configure_chinese_font()
        
        # 绘制应力-应变曲线
        plt.figure(figsize=(10, 6))
        plt.plot(self.strain, self.stress, 'bo-', linewidth=2, markersize=4)
        plt.xlabel('应变 ε')
        plt.ylabel('应力 σ (MPa)')
        plt.title('混凝土单轴应力-应变曲线')
        plt.grid(True)
        
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"原始数据图像已保存至 {save_path}")
        else:
            plt.show()
        
        return True

# 测试代码
if __name__ == "__main__":
    # 创建数据处理器实例
    data_processor = DataProcessor("../data.xlsx")
    
    # 加载数据
    if data_processor.load_data():
        # 归一化数据
        data_processor.normalize_data()
        
        # 创建数据加载器
        train_loader, val_loader = data_processor.create_dataloader(batch_size=32)
        
        # 绘制原始数据
        data_processor.plot_data(save_path="./results/raw_data.png")