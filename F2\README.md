# PINN混凝土单轴本构模型

本项目基于物理信息神经网络（Physics-Informed Neural Network, PINN）实现了混凝土单轴加载下的非线性应力-应变及损伤演化关系建模。

## 项目概述

混凝土柱几何参数：
- 标距长度：800mm
- 截面：圆形，直径为400mm

本项目实现了以下功能：
- 输入：总应变（ε）
- 输出：应力（σ）与损伤变量（d）
- 物理约束：结合本构关系与损伤演化规律
- 训练数据：实验数据（力-位移数据）

## 项目结构

```
F2/
├── README.md               # 项目说明文档
├── data.xlsx               # 实验数据（力-位移数据）
├── data_processor.py       # 数据处理模块
├── pinn_model.py           # PINN模型定义
├── train.py                # 模型训练脚本
├── predict.py              # 模型预测脚本
├── checkpoints/            # 模型保存目录
└── *.png                   # 生成的图表
```

## 物理约束

### 本构关系

```
σ = (1 - d) * E * ε
```

其中：
- σ：应力
- d：损伤变量
- E：弹性模量
- ε：应变

### 损伤演化规律

```
d = 1 - exp(-k * (ε - ε0)^+)
```

其中：
- d：损伤变量
- k：材料参数
- ε0：应变阈值
- (ε - ε0)^+：表示max(ε - ε0, 0)

## 使用方法

### 1. 数据处理

```python
from data_processor import DataProcessor

# 初始化数据处理器
processor = DataProcessor('data.xlsx')

# 加载数据
processor.load_data()

# 转换为应力-应变数据
processor.convert_to_stress_strain()

# 归一化数据
processor.normalize_data()

# 绘制原始数据
processor.plot_raw_data()
processor.plot_stress_strain()
```

### 2. 训练模型

```bash
python train.py
```

### 3. 预测

```bash
python predict.py --model checkpoints/pinn_model_epoch_final.pth --min_strain 0.0 --max_strain 0.01
```

## 模型结构

- 输入层：1个神经元（应变ε）
- 隐藏层：1层，10个神经元，ReLU激活
- 输出层：2个神经元（应力σ，损伤变量d）

## 损失函数

总损失 = 数据损失 + λ1 * 物理损失 + λ2 * 损伤损失

- 数据损失：网络输出应力与实验应力的均方误差
- 物理损失：网络输出应力与本构公式计算应力的均方误差
- 损伤损失：网络输出损伤与演化公式损伤的均方误差

## 结果可视化

训练过程中会生成以下图表：
- 训练历史图表（损失函数变化）
- 应力-应变曲线对比图
- 损伤演化曲线
- 本构关系验证图

## 参考

本项目基于pinn_stress_strain_script.md中的设计实现，使用了PyTorch框架构建PINN模型。