# 混凝土损伤本构AI建模项目

基于物理信息神经网络(PINN)的混凝土单轴加载损伤本构模型

## 项目概述

本项目利用物理信息神经网络(Physics-Informed Neural Networks, PINN)方法，基于循环加卸载试验数据，建立混凝土单轴加载下的弹塑性损伤本构模型。项目实现了损伤变量与应力应变关系的高精度拟合与预测。

## 理论基础

### 损伤本构模型

- **总应力关系**：$\sigma_{\text{tot}} = (1-d^+)\sigma^+ + (1-d^-)\sigma^- + \sigma_{\text{vis}}$
- **有效应力**：$\sigma = C_0 : (\varepsilon - \varepsilon^p)$
- **损伤变量**：$d^\pm = 1 - \left( \frac{r_0^\pm}{r^\pm}(1-A^\pm) + A^\pm \right) \exp\left(B^\pm\left(1 - \frac{r^\pm}{r_0^\pm}\right)\right)$

### PINN网络结构

- **输入**：应变 $\epsilon$
- **输出**：应力 $\sigma$ 和损伤变量 $d$
- **物理约束**：
  - 本构关系：$\sigma = (1-d) E \epsilon$
  - 损伤演化：$d = 1 - e^{-k (\epsilon - \epsilon_0)^+}$
  - 损伤单调性约束

## 项目结构

```
F3/
├── data.xlsx              # 试验数据文件
├── data_processor.py       # 数据处理模块
├── pinn_model.py          # PINN模型定义
├── train.py               # 训练脚本
├── predict.py             # 预测脚本
├── font_config.py         # 中文字体配置
├── requirements.txt       # 依赖包列表
├── README.md             # 项目说明文档
└── 项目说明.md            # 详细理论说明
```

## 安装与环境配置

### 1. 环境要求

- Python 3.7+
- PyTorch 1.9+
- 其他依赖见 `requirements.txt`

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 数据准备

将循环加卸载试验数据保存为Excel格式，命名为 `data.xlsx`，包含以下列：
- 位移或应变数据
- 力或应力数据

## 使用方法

### 1. 数据处理和模型训练

```bash
python train.py
```

训练脚本将自动完成：
- 数据加载与预处理
- 循环段识别
- PINN模型训练
- 结果可视化
- 模型保存

### 2. 模型预测

```bash
python predict.py
```

预测脚本功能：
- 加载训练好的模型
- 单点预测
- 范围预测
- 循环加载预测
- 材料响应分析

### 3. 数据处理（独立使用）

```python
from data_processor import DataProcessor

# 创建数据处理器
processor = DataProcessor('data.xlsx')

# 加载和预处理数据
processor.load_data()
processor.preprocess_data()

# 绘制原始数据
processor.plot_raw_data('raw_data.png')

# 获取训练数据
training_data = processor.get_training_data()
```

### 4. PINN模型（独立使用）

```python
from pinn_model import ConstitutivePINN, PINNTrainer
import torch

# 创建模型
model = ConstitutivePINN(hidden_size=32, num_layers=4)
trainer = PINNTrainer(model, learning_rate=0.001)

# 准备数据
strain_data = torch.tensor(strain_values, dtype=torch.float32).reshape(-1, 1)
stress_data = torch.tensor(stress_values, dtype=torch.float32).reshape(-1, 1)

# 训练模型
trainer.train(strain_data, stress_data, epochs=10000)

# 绘制训练历史
trainer.plot_training_history('training_history.png')
```

## 输出文件说明

### 训练阶段输出

- `raw_data_analysis.png` - 原始数据分析图
- `training_history.png` - 训练损失曲线
- `prediction_results.png` - 预测结果对比
- `extended_predictions.png` - 扩展预测结果
- `concrete_pinn_model.pth` - 训练好的模型文件
- `extended_predictions.xlsx` - 扩展预测数据

### 预测阶段输出

- `material_response_analysis.png` - 材料响应分析
- `cyclic_loading_prediction.png` - 循环加载预测
- `cyclic_loading_prediction.xlsx` - 循环加载预测数据

## 模型参数

### 网络参数

- 隐藏层大小：32
- 隐藏层数：4
- 激活函数：ReLU
- 优化器：Adam
- 学习率：0.001

### 材料参数（可学习）

- `E0`：初始弹性模量 (MPa)
- `r0_plus`：拉伸损伤阈值
- `r0_minus`：压缩损伤阈值
- `A_plus/A_minus`：损伤参数A
- `B_plus/B_minus`：损伤参数B

### 训练参数

- 训练轮数：10000
- 物理约束权重：1.0
- 数据拟合权重：10.0
- 学习率衰减：ReduceLROnPlateau

## 性能评估

模型性能通过以下指标评估：

- **均方误差 (MSE)**：预测精度
- **均方根误差 (RMSE)**：预测误差大小
- **平均绝对误差 (MAE)**：平均偏差
- **决定系数 (R²)**：拟合优度

## 扩展功能

### 1. 材料响应分析

- 应力-应变关系
- 损伤演化曲线
- 切线模量变化
- 有效应力分析
- 能量耗散计算

### 2. 循环加载预测

- 多循环加载路径
- 损伤累积分析
- 滞回曲线预测

### 3. 参数敏感性分析

- 材料参数影响
- 网络结构优化
- 物理约束权重调节

## 注意事项

1. **数据质量**：确保试验数据的质量和完整性
2. **参数调节**：根据具体材料调整初始参数
3. **收敛性**：监控训练过程，确保模型收敛
4. **物理合理性**：验证预测结果的物理合理性

## 故障排除

### 常见问题

1. **数据加载失败**
   - 检查Excel文件格式
   - 确认文件路径正确
   - 验证数据列名

2. **训练不收敛**
   - 调整学习率
   - 修改网络结构
   - 调节损失函数权重

3. **中文字体显示问题**
   - 运行 `font_config.py` 测试字体
   - 安装系统中文字体

4. **内存不足**
   - 减少数据点数
   - 降低网络规模
   - 使用批处理训练

## 参考文献

1. Raissi, M., Perdikaris, P., & Karniadakis, G. E. (2019). Physics-informed neural networks: A deep learning framework for solving forward and inverse problems involving nonlinear partial differential equations. Journal of Computational Physics, 378, 686-707.

2. 混凝土损伤本构理论相关文献

## 联系方式

如有问题或建议，请联系项目维护者。

## 许可证

本项目采用MIT许可证。