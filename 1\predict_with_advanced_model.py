import os
import torch
import numpy as np
import pandas as pd
from column_hysteresis_model import (
    load_excel_data,
    preprocess_data,
    ColumnHysteresisPINN
)

# 设备配置
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 1. 加载Excel数据
excel_path = os.path.join(os.path.dirname(__file__), "column1.xlsx")
static_params, dynamic_data = load_excel_data(excel_path)

# 2. 数据预处理
X, y, scalers, original_data = preprocess_data(static_params, dynamic_data)

# 3. 创建模型（结构要与训练时一致）
input_dim = X.shape[1]
model = ColumnHysteresisPINN(input_dim, hidden_dims=[128, 128, 128, 128, 128]).to(device)

# 4. 加载训练好的权重
model_path = os.path.join(os.path.dirname(__file__), "advanced_column_hysteresis_model.pth")
model.load_state_dict(torch.load(model_path, map_location=device))
model.eval()

# 5. 预测
with torch.no_grad():
    y_pred = model(X)  # 归一化的预测力

# 6. 反归一化为真实力值
force_pred = scalers['force'].inverse_transform(y_pred.cpu().numpy())

# 7. 可选：保存预测结果到Excel
result_df = pd.DataFrame({
    "Displacement": original_data['disp'],
    "True_Force": original_data['force'],
    "Pred_Force": force_pred.flatten()
})
result_df.to_excel(os.path.join(os.path.dirname(__file__), "prediction_results.xlsx"), index=False)
print("预测结果已保存到 prediction_results.xlsx")

# 8. 可选：简单可视化
import matplotlib.pyplot as plt
plt.figure(figsize=(10, 6))
plt.plot(original_data['disp'], original_data['force'], label='True Force', linewidth=2)
plt.plot(original_data['disp'], force_pred, label='Predicted Force', linestyle='--')
plt.xlabel('Displacement (mm)')
plt.ylabel('Force (kN)')
plt.title('Force-Displacement Prediction')
plt.legend()
plt.grid(True)
plt.tight_layout()

# 保存图片
plt.savefig(os.path.join(os.path.dirname(__file__), "prediction_plot.png"), dpi=300, bbox_inches='tight')

plt.show()

# 假设 area 和 length0 已知
area = np.pi * (400 / 2) ** 2  # 400mm直径的圆截面，单位mm^2
length0 = 800  # mm

# 计算应力和应变
stress = force_pred.flatten() / area  # 单位：kN/mm^2
strain = original_data['disp'] / length0  # 单位：无量纲

# 保存应力-应变数据
stress_strain_df = pd.DataFrame({
    "Strain": strain,
    "Stress": stress
})
stress_strain_df.to_excel(os.path.join(os.path.dirname(__file__), "stress_strain_results.xlsx"), index=False)

# 可视化应力-应变曲线
plt.figure(figsize=(8, 6))
plt.plot(strain, stress, label='Predicted Stress-Strain')
plt.xlabel('Strain')
plt.ylabel('Stress (kN/mm²)')
plt.title('Predicted Stress-Strain Curve')
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.savefig(os.path.join(os.path.dirname(__file__), "stress_strain_plot.png"), dpi=300, bbox_inches='tight')
plt.show()