# 混凝土拉伸损伤曲线PINN识别模型

## 模型特点

1. **物理信息神经网络(PINN)**: 结合了数据驱动和物理约束的深度学习模型
2. **LSTM架构**: 捕捉材料损伤的历史依赖性和路径相关性
3. **端到端训练**: 同时优化神经网络参数和物理材料参数
4. **多重损失函数**: 数据拟合、本构关系、损伤演化、塑性累积四重约束

## 使用方法

### 1. 环境要求
```bash
pip install torch pandas numpy matplotlib openpyxl
```

### 2. 运行模型
```bash
python concrete_pinn_model.py
```

### 3. 数据格式
Excel文件应包含两列数据：
- 第一列：应变 (strain)
- 第二列：应力 (stress, MPa)

## 输出结果

1. **训练过程**: 显示损失函数收敛过程和参数识别结果
2. **物理参数**: A+, B+, xi 三个待识别材料参数
3. **预测曲线**: 应力-应变曲线和损伤-应变曲线对比图
4. **保存模型**: concrete_pinn_model.pth 包含训练好的模型和参数

## 模型参数

### 固定参数
- E0: 初始弹性模量 (默认30000 MPa)
- ft: 抗拉强度 (默认3.0 MPa)

### 待识别参数
- A+: 损伤演化曲线形状参数
- B+: 损伤演化曲线速率参数
- xi: 塑性应变发展比例系数

### 损失权重
- lambda_data: 数据拟合损失权重 (1.0)
- lambda_stress: 本构关系损失权重 (0.8)
- lambda_damage: 损伤演化损失权重 (0.5)
- lambda_plastic: 塑性累积损失权重 (0.5)

## 自定义使用

```python
from concrete_pinn_model import ConcretePINN, ConcretePINNTrainer, load_data

# 加载数据
strain_exp, stress_exp = load_data('your_data.xlsx')

# 创建模型
model = ConcretePINN()
trainer = ConcretePINNTrainer(model, E0=30000.0, ft=3.0)

# 训练
trainer.train(strain_exp, stress_exp, num_epochs=2000)

# 预测
stress_pred, damage_pred, plastic_pred = trainer.predict(strain_exp)
``` 