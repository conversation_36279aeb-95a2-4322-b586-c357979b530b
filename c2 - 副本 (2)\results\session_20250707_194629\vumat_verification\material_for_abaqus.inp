** ============================================
** Auto-generated Material Card for Abaqus (V2.6)
** Generated by: generate_material_card.py
** Session: session_20250707_194629
** Final: Pure VUMAT definition - all material behavior in subroutine
** ============================================
**
*MATERIAL, NAME=CONCRETE_VUMAT
** Material density (required for Abaqus/Explicit)
*DENSITY
2.40e-09
** User-defined material properties for the VUMAT subroutine
** VUMAT must provide complete material behavior including initial stiffness
*USER MATERIAL, CONSTANTS=10
** PROPS(1-10): E0, ft, fc, A+, B+, xi+, A-, B-, xi-, nu
10000.00, 3.670431, 10.00, 0.854570, 1.693629, 0.500000, 2.000000, 1.231858, 0.500224, 0.200000
*DEPVAR
5
** State variables:
** SDV(1): Damage_plus (d+)
** SDV(2): Damage_minus (d-)
** SDV(3): R_max_plus
** SDV(4): R_max_minus
** SDV(5): Plastic_strain (ep)
