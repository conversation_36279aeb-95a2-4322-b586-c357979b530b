import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# 导入主模型
from concrete_tension_pinn import ConcreteTensionPINN, ConcreteTensionTrainer, PhysicsConstraints

def test_model_basic():
    """
    测试模型的基本功能
    """
    print("=" * 50)
    print("测试PINN模型基本功能")
    print("=" * 50)
    
    # 创建模型
    model = ConcreteTensionPINN(input_size=1, hidden_size=64, num_layers=2, output_size=3)
    print(f"✓ 模型创建成功，参数数量: {sum(p.numel() for p in model.parameters())}")
    
    # 测试前向传播
    batch_size, seq_len = 1, 50
    test_input = torch.randn(batch_size, seq_len, 1)
    
    with torch.no_grad():
        stress, damage, plastic = model(test_input)
    
    print(f"✓ 前向传播测试通过")
    print(f"  输入形状: {test_input.shape}")
    print(f"  应力输出形状: {stress.shape}")
    print(f"  损伤输出形状: {damage.shape}")
    print(f"  塑性应变输出形状: {plastic.shape}")
    
    # 检查输出范围
    print(f"  损伤变量范围: [{damage.min().item():.4f}, {damage.max().item():.4f}]")
    print(f"  应力范围: [{stress.min().item():.4f}, {stress.max().item():.4f}]")
    
    return True

def test_physics_constraints():
    """
    测试物理约束计算
    """
    print("\n" + "=" * 50)
    print("测试物理约束计算")
    print("=" * 50)
    
    physics = PhysicsConstraints()
    
    # 创建测试数据
    strain_seq = torch.linspace(0, 0.003, 50).unsqueeze(0).unsqueeze(-1)
    plastic_seq = torch.zeros_like(strain_seq)
    
    E0 = torch.tensor(30000.0)
    ft = torch.tensor(3.0)
    A_plus = torch.tensor(0.5)
    B_plus = torch.tensor(1.0)
    xi = torch.tensor(0.1)
    
    # 测试损伤阈值计算
    r_seq = physics.compute_damage_threshold(strain_seq, plastic_seq, E0, ft)
    print(f"✓ 损伤阈值计算成功，范围: [{r_seq.min().item():.2f}, {r_seq.max().item():.2f}]")
    
    # 测试物理损伤计算
    damage_phy = physics.compute_physical_damage(r_seq, ft, A_plus, B_plus)
    print(f"✓ 物理损伤计算成功，范围: [{damage_phy.min().item():.4f}, {damage_phy.max().item():.4f}]")
    
    # 测试塑性应变计算
    plastic_phy = physics.compute_physical_plastic_strain(strain_seq, xi)
    print(f"✓ 塑性应变计算成功，范围: [{plastic_phy.min().item():.6f}, {plastic_phy.max().item():.6f}]")
    
    return True

def test_data_loading():
    """
    测试数据加载功能
    """
    print("\n" + "=" * 50)
    print("测试数据加载功能")
    print("=" * 50)
    
    model = ConcreteTensionPINN()
    trainer = ConcreteTensionTrainer(model)
    
    # 测试示例数据生成
    strain_data, stress_data = trainer.generate_sample_data(n_points=100)
    print(f"✓ 示例数据生成成功")
    print(f"  数据点数量: {len(strain_data)}")
    print(f"  应变范围: [{strain_data.min():.6f}, {strain_data.max():.6f}]")
    print(f"  应力范围: [{stress_data.min():.3f}, {stress_data.max():.3f}] MPa")
    
    # 测试数据准备
    strain_tensor, stress_tensor = trainer.prepare_training_data(strain_data, stress_data)
    print(f"✓ 数据张量转换成功")
    print(f"  应变张量形状: {strain_tensor.shape}")
    print(f"  应力张量形状: {stress_tensor.shape}")
    
    return strain_data, stress_data

def test_training_step():
    """
    测试单步训练
    """
    print("\n" + "=" * 50)
    print("测试单步训练")
    print("=" * 50)
    
    model = ConcreteTensionPINN(input_size=1, hidden_size=32, num_layers=2)
    trainer = ConcreteTensionTrainer(model)
    
    # 生成测试数据
    strain_data, stress_data = trainer.generate_sample_data(n_points=50)
    strain_tensor, stress_tensor = trainer.prepare_training_data(strain_data, stress_data)
    
    # 执行一步训练
    initial_loss = None
    for i in range(5):
        losses = trainer.train_epoch(strain_tensor, stress_tensor)
        if i == 0:
            initial_loss = losses['total'].item()
        print(f"  步骤 {i+1}: 总损失 = {losses['total'].item():.6f}")
    
    print(f"✓ 训练步骤测试成功")
    print(f"  初始损失: {initial_loss:.6f}")
    print(f"  最终损失: {losses['total'].item():.6f}")
    
    return True

def test_prediction():
    """
    测试预测功能
    """
    print("\n" + "=" * 50)
    print("测试预测功能")
    print("=" * 50)
    
    model = ConcreteTensionPINN(input_size=1, hidden_size=32, num_layers=2)
    trainer = ConcreteTensionTrainer(model)
    
    # 生成测试数据
    strain_data, _ = trainer.generate_sample_data(n_points=30)
    
    # 进行预测
    predictions = trainer.predict(strain_data)
    
    print(f"✓ 预测功能测试成功")
    print(f"  预测应力范围: [{predictions['stress_pred'].min():.3f}, {predictions['stress_pred'].max():.3f}]")
    print(f"  预测损伤范围: [{predictions['damage_pred'].min():.4f}, {predictions['damage_pred'].max():.4f}]")
    print(f"  预测塑性应变范围: [{predictions['plastic_pred'].min():.6f}, {predictions['plastic_pred'].max():.6f}]")
    
    return True

def test_parameter_learning():
    """
    测试参数学习能力
    """
    print("\n" + "=" * 50)
    print("测试参数学习能力")
    print("=" * 50)
    
    model = ConcreteTensionPINN(input_size=1, hidden_size=32, num_layers=2)
    trainer = ConcreteTensionTrainer(model)
    
    # 记录初始参数
    initial_A = model.A_plus.item()
    initial_B = model.B_plus.item()
    initial_xi = model.xi.item()
    
    print(f"初始参数:")
    print(f"  A+ = {initial_A:.4f}")
    print(f"  B+ = {initial_B:.4f}")
    print(f"  ξ  = {initial_xi:.4f}")
    
    # 生成数据并训练几步
    strain_data, stress_data = trainer.generate_sample_data(n_points=50)
    
    # 简短训练
    for epoch in range(20):
        strain_tensor, stress_tensor = trainer.prepare_training_data(strain_data, stress_data)
        losses = trainer.train_epoch(strain_tensor, stress_tensor)
        
        if (epoch + 1) % 5 == 0:
            print(f"  Epoch {epoch+1}: 损失 = {losses['total'].item():.6f}")
    
    # 记录最终参数
    final_A = model.A_plus.item()
    final_B = model.B_plus.item()
    final_xi = model.xi.item()
    
    print(f"\n最终参数:")
    print(f"  A+ = {final_A:.4f} (变化: {final_A - initial_A:+.4f})")
    print(f"  B+ = {final_B:.4f} (变化: {final_B - initial_B:+.4f})")
    print(f"  ξ  = {final_xi:.4f} (变化: {final_xi - initial_xi:+.4f})")
    
    print(f"✓ 参数学习测试成功")
    
    return True

def main():
    """
    运行所有测试
    """
    print("开始PINN模型功能测试...")
    
    try:
        # 基本功能测试
        test_model_basic()
        
        # 物理约束测试
        test_physics_constraints()
        
        # 数据加载测试
        test_data_loading()
        
        # 训练步骤测试
        test_training_step()
        
        # 预测功能测试
        test_prediction()
        
        # 参数学习测试
        test_parameter_learning()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试通过！PINN模型功能正常")
        print("=" * 50)
        
        # 设备信息
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"\n系统信息:")
        print(f"  PyTorch版本: {torch.__version__}")
        print(f"  计算设备: {device}")
        if torch.cuda.is_available():
            print(f"  GPU设备: {torch.cuda.get_device_name(0)}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 模型已准备就绪，可以开始正式训练！")
        print("运行命令: python concrete_tension_pinn.py")
    else:
        print("\n❌ 请检查错误信息并修复问题")