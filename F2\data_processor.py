import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

class DataProcessor:
    def __init__(self, file_path, sheet_name='力和位移数据'):
        """初始化数据处理器
        
        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称
        """
        self.file_path = file_path
        self.sheet_name = sheet_name
        self.raw_data = None
        self.strain_data = None
        self.stress_data = None
        self.normalized_strain = None
        self.normalized_stress = None
        self.strain_max = None
        self.stress_max = None
        
        # 混凝土柱参数
        self.length = 800  # 标距长度，单位mm
        self.diameter = 400  # 直径，单位mm
        self.area = np.pi * (self.diameter/2)**2  # 截面积，单位mm²
        
    def load_data(self):
        """加载Excel数据"""
        try:
            df = pd.read_excel(self.file_path, sheet_name=self.sheet_name)
            self.raw_data = df
            print(f"数据加载成功，共{len(df)}行")
            return df
        except Exception as e:
            print(f"数据加载失败: {e}")
            return None
    
    def convert_to_stress_strain(self):
        """将力-位移数据转换为应力-应变数据"""
        if self.raw_data is None:
            print("请先加载数据")
            return None
        
        # 提取力和位移数据
        force_kn = self.raw_data['力（KN）'].values  # 单位：KN
        displacement_mm = self.raw_data['位移（mm）'].values  # 单位：mm
        
        # 转换力为牛顿 (1 KN = 1000 N)
        force_n = force_kn * 1000
        
        # 计算应力 (σ = F/A)，单位：MPa (N/mm²)
        stress = force_n / self.area
        
        # 计算应变 (ε = ΔL/L)
        strain = displacement_mm / self.length
        
        self.strain_data = strain
        self.stress_data = stress
        
        return strain, stress
    
    def normalize_data(self):
        """归一化应力和应变数据"""
        if self.strain_data is None or self.stress_data is None:
            print("请先转换为应力-应变数据")
            return None
        
        # 获取最大值用于归一化
        self.strain_max = np.max(np.abs(self.strain_data))
        self.stress_max = np.max(np.abs(self.stress_data))
        
        # 归一化
        self.normalized_strain = self.strain_data / self.strain_max
        self.normalized_stress = self.stress_data / self.stress_max
        
        return self.normalized_strain, self.normalized_stress
    
    def denormalize_stress(self, normalized_stress):
        """将归一化的应力值转换回实际应力值"""
        return normalized_stress * self.stress_max
    
    def denormalize_strain(self, normalized_strain):
        """将归一化的应变值转换回实际应变值"""
        return normalized_strain * self.strain_max
    
    def prepare_training_data(self, test_size=0.2, random_state=42, include_delta=True):
        """准备训练数据和测试数据
        
        Args:
            test_size: 测试集比例
            random_state: 随机种子
            include_delta: 是否包含应变增量
            
        Returns:
            X_train, X_test, y_train, y_test: 训练集和测试集
        """
        if self.normalized_strain is None or self.normalized_stress is None:
            print("请先归一化数据")
            return None
        
        if include_delta:
            # 计算应变增量
            delta_strain = np.zeros_like(self.normalized_strain)
            delta_strain[1:] = self.normalized_strain[1:] - self.normalized_strain[:-1]
            
            # 创建输入和输出数据 - 包含应变和应变增量
            X = np.column_stack((self.normalized_strain, delta_strain))  # 输入：[应变, 应变增量]
            y = self.normalized_stress.reshape(-1, 1)  # 输出：应力
        else:
            # 创建输入和输出数据 - 只包含应变
            X = self.normalized_strain.reshape(-1, 1)  # 输入：应变
            y = self.normalized_stress.reshape(-1, 1)  # 输出：应力
        
        # 随机分割训练集和测试集
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state
        )
        
        return X_train, X_test, y_train, y_test
    
    def plot_raw_data(self):
        """绘制原始力-位移数据"""
        if self.raw_data is None:
            print("请先加载数据")
            return
        
        plt.figure(figsize=(10, 6))
        plt.plot(self.raw_data['位移（mm）'], self.raw_data['力（KN）'], 'o-', markersize=2)
        plt.xlabel('位移 (mm)')
        plt.ylabel('力 (KN)')
        plt.title('力-位移曲线')
        plt.grid(True)
        plt.savefig('d:/column/F2/raw_data.png')
        plt.close()
        print("原始数据图表已保存")
    
    def plot_stress_strain(self):
        """绘制应力-应变曲线"""
        if self.strain_data is None or self.stress_data is None:
            print("请先转换为应力-应变数据")
            return
        
        plt.figure(figsize=(10, 6))
        plt.plot(self.strain_data, self.stress_data, 'o-', markersize=2)
        plt.xlabel('应变 (ε)')
        plt.ylabel('应力 (MPa)')
        plt.title('应力-应变曲线')
        plt.grid(True)
        plt.savefig('d:/column/F2/stress_strain.png')
        plt.close()
        print("应力-应变曲线已保存")

# 测试代码
if __name__ == "__main__":
    file_path = 'd:/column/F2/data.xlsx'
    processor = DataProcessor(file_path)
    processor.load_data()
    processor.convert_to_stress_strain()
    processor.normalize_data()
    processor.plot_raw_data()
    processor.plot_stress_strain()